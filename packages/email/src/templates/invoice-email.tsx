import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Hr,
  Html,
  <PERSON>,
  Section,
  Text,
} from "@react-email/components";

interface InvoiceEmailProps {
  recipientName: string;
  invoiceNumber: string;
  amount: string;
  dueDate: string;
  pdfUrl: string;
  customMessage?: string;
}

export default function InvoiceEmail({
  recipientName,
  invoiceNumber,
  amount,
  dueDate,
  pdfUrl,
  customMessage,
}: InvoiceEmailProps) {
  return (
    <Html>
      <Head />
      <Body style={main}>
        <Container style={container}>
          <Section style={header}>
            <Text style={headerText}>MODA</Text>
          </Section>

          <Text style={heading}>
            {recipientName ? `Hi ${recipientName},` : "Hi there,"}
          </Text>

          <Text style={paragraph}>
            Thank you for your recent purchase! Please find your invoice attached to this email.
          </Text>

          {customMessage && (
            <Section style={messageBox}>
              <Text style={messageText}>{customMessage}</Text>
            </Section>
          )}

          <Section style={invoiceDetails}>
            <Text style={detailsHeading}>Invoice Details:</Text>
            <Text style={detailItem}>
              <strong>Invoice Number:</strong> {invoiceNumber}
            </Text>
            <Text style={detailItem}>
              <strong>Amount:</strong> ${amount}
            </Text>
            <Text style={detailItem}>
              <strong>Due Date:</strong> {dueDate}
            </Text>
          </Section>

          <Section style={buttonContainer}>
            <Button href={pdfUrl} style={button}>
              Download Invoice PDF
            </Button>
          </Section>

          <Text style={paragraph}>
            If you have any questions about this invoice, please don't hesitate to contact us.
          </Text>

          <Hr style={hr} />

          <Text style={footer}>
            This invoice was generated by MODA. If you believe you received this email in error, please contact us.
          </Text>

          <Text style={footer}>
            Payment terms: Net 30 days from invoice date.
          </Text>
        </Container>
      </Body>
    </Html>
  );
}

const main = {
  backgroundColor: "#ffffff",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  maxWidth: "560px",
};

const header = {
  backgroundColor: "#000000",
  padding: "20px",
  textAlign: "center" as const,
  marginBottom: "32px",
};

const headerText = {
  color: "#ffffff",
  fontSize: "24px",
  fontWeight: "bold",
  margin: "0",
};

const heading = {
  fontSize: "24px",
  letterSpacing: "-0.5px",
  lineHeight: "1.3",
  fontWeight: "400",
  color: "#484848",
  padding: "17px 0 0",
};

const paragraph = {
  margin: "0 0 15px",
  fontSize: "15px",
  lineHeight: "1.4",
  color: "#3c4149",
};

const messageBox = {
  backgroundColor: "#f8f9fa",
  border: "1px solid #e9ecef",
  borderRadius: "4px",
  padding: "16px",
  margin: "16px 0",
};

const messageText = {
  margin: "0",
  fontSize: "15px",
  lineHeight: "1.4",
  color: "#495057",
  fontStyle: "italic",
};

const invoiceDetails = {
  backgroundColor: "#f8f9fa",
  border: "1px solid #e9ecef",
  borderRadius: "4px",
  padding: "20px",
  margin: "20px 0",
};

const detailsHeading = {
  fontSize: "16px",
  fontWeight: "bold",
  color: "#212529",
  margin: "0 0 12px 0",
};

const detailItem = {
  margin: "0 0 8px 0",
  fontSize: "14px",
  lineHeight: "1.4",
  color: "#495057",
};

const buttonContainer = {
  textAlign: "center" as const,
  margin: "32px 0",
};

const button = {
  backgroundColor: "#007ee6",
  borderRadius: "4px",
  color: "#fff",
  fontSize: "15px",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "inline-block",
  padding: "14px 28px",
  fontWeight: "bold",
};

const hr = {
  borderColor: "#e6ebf1",
  margin: "20px 0",
};

const footer = {
  color: "#8898aa",
  fontSize: "12px",
  lineHeight: "16px",
  marginTop: "12px",
};