import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Button,
  Hr,
} from "@react-email/components";

interface SellerApplicationReceivedProps {
  name: string;
  applicationId: string;
  businessName: string;
}

export default function SellerApplicationReceived({
  name = "<PERSON>",
  applicationId = "HV-12345678",
  businessName = "Luxury Goods LLC",
}: SellerApplicationReceivedProps) {
  return (
    <Html>
      <Head />
      <Preview>Your seller application has been received</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={header}>
            <Heading style={h1}>MODA</Heading>
          </Section>
          
          <Section style={content}>
            <Heading style={h2}>Application Received</Heading>
            
            <Text style={text}>Dear {name},</Text>
            
            <Text style={text}>
              Thank you for submitting your seller application to MODA. We have received your application and it is now under review.
            </Text>
            
            <Section style={applicationDetails}>
              <Text style={detailLabel}>Application Details:</Text>
              <Text style={detail}><strong>Application ID:</strong> {applicationId}</Text>
              <Text style={detail}><strong>Business Name:</strong> {businessName}</Text>
              <Text style={detail}><strong>Submitted:</strong> {new Date().toLocaleDateString()}</Text>
              <Text style={detail}><strong>Status:</strong> Under Review</Text>
            </Section>
            
            <Text style={text}>
              Our team will review your application within 2-3 business days. You will receive an email notification once the review is complete.
            </Text>
            
            <Text style={text}>
              During the review process, we may contact you if we need additional information or documentation.
            </Text>
            
            <Button style={button} href={`${process.env.APP_URL}/seller/dashboard`}>
              Check Application Status
            </Button>
            
            <Hr style={hr} />
            
            <Text style={footer}>
              If you have any questions, please contact our seller support team at{" "}
              <a href="mailto:<EMAIL>" style={link}>
                <EMAIL>
              </a>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

const main = {
  backgroundColor: "#f6f9fc",
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: "#ffffff",
  margin: "0 auto",
  padding: "20px 0 48px",
  marginBottom: "64px",
};

const header = {
  padding: "32px 24px",
  backgroundColor: "#000000",
  textAlign: "center" as const,
};

const content = {
  padding: "24px",
};

const h1 = {
  color: "#ffffff",
  fontSize: "24px",
  fontWeight: "600",
  margin: "0",
};

const h2 = {
  color: "#333333",
  fontSize: "20px",
  fontWeight: "600",
  margin: "0 0 16px",
};

const text = {
  color: "#333333",
  fontSize: "16px",
  lineHeight: "24px",
  margin: "0 0 16px",
};

const applicationDetails = {
  backgroundColor: "#f8f9fa",
  padding: "16px",
  borderRadius: "8px",
  margin: "24px 0",
};

const detailLabel = {
  color: "#333333",
  fontSize: "16px",
  fontWeight: "600",
  margin: "0 0 8px",
};

const detail = {
  color: "#666666",
  fontSize: "14px",
  margin: "0 0 4px",
};

const button = {
  backgroundColor: "#000000",
  borderRadius: "8px",
  color: "#ffffff",
  fontSize: "16px",
  fontWeight: "600",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  padding: "12px 24px",
  margin: "24px 0",
};

const hr = {
  borderColor: "#e6ebf1",
  margin: "32px 0",
};

const footer = {
  color: "#666666",
  fontSize: "14px",
  lineHeight: "20px",
  margin: "0",
};

const link = {
  color: "#000000",
  textDecoration: "underline",
};