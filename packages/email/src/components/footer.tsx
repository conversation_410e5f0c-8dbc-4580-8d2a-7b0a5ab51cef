import { Hr, Img, Link, Row, Section, Text, Column } from "@react-email/components";

export function Footer() {
   return (
      <Section className="w-full text-center font-sans mt-8 mb-2">
         <Hr />
         <Text className="text-sm font-light text-neutral-400 mt-6 mb-4">Join the Elite Moda Club.</Text>
         <Row>
            <Column className="align-middle">
               <table role="presentation" style={{ margin: "0 auto", borderSpacing: 0, borderCollapse: "collapse" }}>
                  <tr>
                     <td style={{ paddingRight: 12 }}>
                        <Link href="https://www.facebook.com/profile.php?id=61551698345239&mibextid=LQQJ4d">
                           <svg
                              width="18"
                              height="18"
                              viewBox="0 0 18 18"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              aria-label="Facebook"
                              style={{ display: "block" }}
                           >
                              <rect width="18" height="18" rx="4" fill="#1877F2"/>
                              <path
                                 d="M12.5 9.00002C12.5 6.51474 10.4853 4.50002 8 4.50002C5.51472 4.50002 3.5 6.51474 3.5 9.00002C3.5 11.4853 5.51472 13.5 8 13.5C10.4853 13.5 12.5 11.4853 12.5 9.00002Z"
                                 fill="#1877F2"
                              />
                              <path
                                 d="M10.5 9.00002H9.25V13H7.5V9.00002H6.5V7.5H7.5V6.75C7.5 5.92157 8.17157 5.25002 9 5.25002H10.5V7.00002H9.5C9.22386 7.00002 9 7.22388 9 7.50002V7.5H10.5V9.00002Z"
                                 fill="white"
                              />
                           </svg>
                        </Link>
                     </td>
                     <td>
                        <Link href="https://instagram.com/modaclubs">
                           <svg
                              width="22"
                              height="22"
                              viewBox="0 0 22 22"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              aria-label="Instagram"
                              style={{ display: "block" }}
                           >
                              <rect width="22" height="22" rx="6" fill="#fff" />
                              <rect x="2" y="2" width="18" height="18" rx="5" fill="#fff" stroke="#302923" strokeWidth="1.5"/>
                              <circle cx="11" cy="11" r="4.2" stroke="#302923" strokeWidth="1.5" fill="none"/>
                              <circle cx="15.2" cy="6.8" r="1" fill="#302923"/>
                           </svg>
                        </Link>
                     </td>
                  </tr>
               </table>
            </Column>
         </Row>
      </Section>
   );
}
