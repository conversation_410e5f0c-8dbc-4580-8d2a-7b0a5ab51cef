import { query, mutation, internalMutation } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

// Public queries for staff directory
export const getPublicStaff = query({
  args: {
    department: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  returns: v.array(v.object({
    _id: v.id("staff"),
    _creationTime: v.number(),
    name: v.string(),
    role: v.string(),
    imageUrl: v.optional(v.string()),
    bio: v.optional(v.string()),
    department: v.optional(v.string()),
    linkedinUrl: v.optional(v.string()),
    sortOrder: v.optional(v.number()),
  })),
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("staff")
      .withIndex("by_public", (q) => q.eq("isPublic", true))
      .filter((q) => q.eq(q.field("isActive"), true));

    if (args.department) {
      query = query.filter((q) => q.eq(q.field("department"), args.department));
    }

    const staff = await query
      .order("asc")
      .take(args.limit || 50);

    // Sort by sortOrder if available, then by name
    const sortedStaff = staff.sort((a, b) => {
      if (a.sortOrder !== undefined && b.sortOrder !== undefined) {
        return a.sortOrder - b.sortOrder;
      }
      if (a.sortOrder !== undefined) return -1;
      if (b.sortOrder !== undefined) return 1;
      return a.name.localeCompare(b.name);
    });

    return sortedStaff.map(member => ({
      _id: member._id,
      _creationTime: member._creationTime,
      name: member.name,
      role: member.role,
      imageUrl: member.imageUrl,
      bio: member.bio,
      department: member.department,
      linkedinUrl: member.linkedinUrl,
      sortOrder: member.sortOrder,
    }));
  },
});

export const getStaffDepartments = query({
  args: {},
  returns: v.array(v.string()),
  handler: async (ctx) => {
    const staff = await ctx.db
      .query("staff")
      .withIndex("by_public", (q) => q.eq("isPublic", true))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    const departments = [...new Set(
      staff
        .map(member => member.department)
        .filter((dept): dept is string => dept !== undefined)
    )];

    return departments.sort();
  },
});

// Admin queries and mutations
export const getAllStaff = query({
  args: {
    department: v.optional(v.string()),
    includeInactive: v.optional(v.boolean()),
  },
  returns: v.array(v.object({
    _id: v.id("staff"),
    _creationTime: v.number(),
    name: v.string(),
    role: v.string(),
    imageUrl: v.optional(v.string()),
    bio: v.optional(v.string()),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    linkedinUrl: v.optional(v.string()),
    department: v.optional(v.string()),
    startDate: v.optional(v.number()),
    isActive: v.boolean(),
    isPublic: v.boolean(),
    sortOrder: v.optional(v.number()),
    createdById: v.id("users"),
    updatedAt: v.number(),
  })),
  handler: async (ctx, args) => {
    // TODO: Add auth check for admin users
    let staff;

    if (args.department) {
      const allStaff = await ctx.db
        .query("staff")
        .withIndex("by_department", (q) => q.eq("department", args.department))
        .collect();
      
      staff = args.includeInactive 
        ? allStaff 
        : allStaff.filter(s => s.isActive);
    } else {
      const allStaff = await ctx.db.query("staff").collect();
      staff = args.includeInactive 
        ? allStaff 
        : allStaff.filter(s => s.isActive);
    }

    return staff.sort((a, b) => {
      if (a.sortOrder !== undefined && b.sortOrder !== undefined) {
        return a.sortOrder - b.sortOrder;
      }
      if (a.sortOrder !== undefined) return -1;
      if (b.sortOrder !== undefined) return 1;
      return a.name.localeCompare(b.name);
    });
  },
});

export const getStaffStats = query({
  args: {},
  returns: v.object({
    totalStaff: v.number(),
    activeStaff: v.number(),
    publicStaff: v.number(),
    staffByDepartment: v.record(v.string(), v.number()),
  }),
  handler: async (ctx) => {
    // TODO: Add auth check for admin users
    const allStaff = await ctx.db.query("staff").collect();

    const stats = {
      totalStaff: allStaff.length,
      activeStaff: allStaff.filter(s => s.isActive).length,
      publicStaff: allStaff.filter(s => s.isPublic && s.isActive).length,
      staffByDepartment: {} as Record<string, number>,
    };

    // Count staff by department
    allStaff
      .filter(s => s.isActive && s.department)
      .forEach(staff => {
        const dept = staff.department!;
        stats.staffByDepartment[dept] = (stats.staffByDepartment[dept] || 0) + 1;
      });

    return stats;
  },
});

export const createStaff = mutation({
  args: {
    name: v.string(),
    role: v.string(),
    imageUrl: v.optional(v.string()),
    bio: v.optional(v.string()),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    linkedinUrl: v.optional(v.string()),
    department: v.optional(v.string()),
    startDate: v.optional(v.number()),
    isPublic: v.boolean(),
    sortOrder: v.optional(v.number()),
    createdById: v.id("users"),
  },
  returns: v.id("staff"),
  handler: async (ctx, args) => {
    // TODO: Add auth check for admin users
    const now = Date.now();

    return await ctx.db.insert("staff", {
      name: args.name,
      role: args.role,
      imageUrl: args.imageUrl,
      bio: args.bio,
      email: args.email,
      phone: args.phone,
      linkedinUrl: args.linkedinUrl,
      department: args.department,
      startDate: args.startDate,
      isActive: true,
      isPublic: args.isPublic,
      sortOrder: args.sortOrder,
      createdById: args.createdById,
      updatedAt: now,
    });
  },
});

export const updateStaff = mutation({
  args: {
    staffId: v.id("staff"),
    name: v.optional(v.string()),
    role: v.optional(v.string()),
    imageUrl: v.optional(v.string()),
    bio: v.optional(v.string()),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    linkedinUrl: v.optional(v.string()),
    department: v.optional(v.string()),
    startDate: v.optional(v.number()),
    isActive: v.optional(v.boolean()),
    isPublic: v.optional(v.boolean()),
    sortOrder: v.optional(v.number()),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // TODO: Add auth check for admin users
    const { staffId, ...updates } = args;

    const existingStaff = await ctx.db.get(staffId);
    if (!existingStaff) {
      throw new Error("Staff member not found");
    }

    await ctx.db.patch(staffId, {
      ...updates,
      updatedAt: Date.now(),
    });

    return null;
  },
});

export const deleteStaff = mutation({
  args: {
    staffId: v.id("staff"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // TODO: Add auth check for admin users
    const existingStaff = await ctx.db.get(args.staffId);
    if (!existingStaff) {
      throw new Error("Staff member not found");
    }

    await ctx.db.delete(args.staffId);
    return null;
  },
});

// Utility function to reorder staff
export const reorderStaff = mutation({
  args: {
    staffUpdates: v.array(v.object({
      staffId: v.id("staff"),
      sortOrder: v.number(),
    })),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // TODO: Add auth check for admin users
    const now = Date.now();

    for (const update of args.staffUpdates) {
      await ctx.db.patch(update.staffId, {
        sortOrder: update.sortOrder,
        updatedAt: now,
      });
    }

    return null;
  },
});
