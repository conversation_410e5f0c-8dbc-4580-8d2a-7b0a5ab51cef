import Stripe from 'stripe';

// Initialize Stripe conditionally to prevent build errors
let stripe: Stripe | null = null;

if (process.env.STRIPE_SECRET_KEY) {
  stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
    apiVersion: '2025-07-30.basil',
    typescript: true,
    // Add some configuration to help with rate limiting
    maxNetworkRetries: 3,
    timeout: 30000, // 30 second timeout
  });
}

// Stripe Connect configuration
export const STRIPE_CONFIG = {
  publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || '',
  secretKey: process.env.STRIPE_SECRET_KEY || '',
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
  platformFeePercentage: parseFloat(process.env.PLATFORM_FEE_PERCENTAGE || '5.0'),
  applicationFeePercentage: parseFloat(process.env.STRIPE_APPLICATION_FEE_PERCENTAGE || '2.9'),
  appUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
};

// Helper function to get Stripe instance
export function getStripe(): Stripe {
  if (!stripe) {
    throw new Error('Stripe not initialized. Make sure STRIPE_SECRET_KEY is set.');
  }
  return stripe;
}

// Helper function to calculate platform fees
export function calculateFees(amount: number) {
  const platformFee = Math.round(amount * (STRIPE_CONFIG.platformFeePercentage / 100));
  const stripeFee = Math.round(amount * (STRIPE_CONFIG.applicationFeePercentage / 100)) + 30; // $0.30 fixed fee
  const sellerAmount = amount - platformFee - stripeFee;
  
  return {
    totalAmount: amount,
    platformFee,
    stripeFee,
    sellerAmount,
  };
}

// Helper function to create Stripe Connect Express account
export async function createConnectAccount(email: string, country: string = 'US') {
  try {
    const account = await getStripe().accounts.create({
      type: 'express',
      country,
      email,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      business_type: 'individual',
      settings: {
        payouts: {
          schedule: {
            interval: 'weekly',
            weekly_anchor: 'friday',
          },
        },
      },
    });

    return account;
  } catch (error) {
    console.error('Error creating Stripe Connect account:', error);
    throw error;
  }
}

// Helper function to create account link for onboarding
export async function createAccountLink(accountId: string, refreshUrl: string, returnUrl: string) {
  try {
    const accountLink = await getStripe().accountLinks.create({
      account: accountId,
      refresh_url: refreshUrl,
      return_url: returnUrl,
      type: 'account_onboarding',
    });

    return accountLink;
  } catch (error) {
    console.error('Error creating account link:', error);
    throw error;
  }
}

// Helper function to retrieve account status
export async function getAccountStatus(accountId: string) {
  try {
    const account = await getStripe().accounts.retrieve(accountId);
    
    return {
      id: account.id,
      charges_enabled: account.charges_enabled,
      payouts_enabled: account.payouts_enabled,
      details_submitted: account.details_submitted,
      requirements: account.requirements,
      business_profile: account.business_profile,
    };
  } catch (error) {
    console.error('Error retrieving account status:', error);
    throw error;
  }
}

// Helper function to create payment intent with Connect transfer
export async function createPaymentIntentWithTransfer(
  amount: number,
  currency: string = 'usd',
  connectedAccountId: string,
  metadata: Record<string, string> = {}
) {
  try {
    const fees = calculateFees(amount);
    
    const paymentIntent = await getStripe().paymentIntents.create({
      amount: Math.round(fees.totalAmount * 100), // Convert to cents
      currency,
      application_fee_amount: Math.round(fees.platformFee * 100),
      transfer_data: {
        destination: connectedAccountId,
      },
      metadata: {
        ...metadata,
        platform_fee: fees.platformFee.toString(),
        seller_amount: fees.sellerAmount.toString(),
      },
    });

    return {
      paymentIntent,
      fees,
    };
  } catch (error) {
    console.error('Error creating payment intent:', error);
    throw error;
  }
}

// Helper function to create refund
export async function createRefund(
  paymentIntentId: string,
  amount?: number,
  reason: 'duplicate' | 'fraudulent' | 'requested_by_customer' = 'requested_by_customer'
) {
  try {
    const refund = await getStripe().refunds.create({
      payment_intent: paymentIntentId,
      amount: amount ? Math.round(amount * 100) : undefined,
      reason,
    });

    return refund;
  } catch (error) {
    console.error('Error creating refund:', error);
    throw error;
  }
}

export default getStripe;
