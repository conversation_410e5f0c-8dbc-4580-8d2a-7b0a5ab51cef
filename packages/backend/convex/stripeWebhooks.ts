import { v } from "convex/values";
import { mutation } from "./_generated/server";
import { ConvexError } from "convex/values";
import type { Id } from "./_generated/dataModel";
import getStripe from "./lib/stripe";

/**
 * Process incoming Stripe webhook events
 */
export const processWebhookEvent = mutation({
  args: {
    signature: v.string(),
    payload: v.string(),
  },
  handler: async (ctx, args) => {
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;
    
    try {
      // Verify webhook signature
      const event = await getStripe().webhooks.constructEventAsync(
        args.payload,
        args.signature,
        webhookSecret
      );

      // Check if we've already processed this event (idempotency)
      const existingEvent = await ctx.db
        .query("stripeEvents")
        .withIndex("by_stripeEventId", (q) => q.eq("stripeEventId", event.id))
        .first();

      if (existingEvent) {
        console.log(`Event ${event.id} already processed`);
        return { success: true, message: "Event already processed" };
      }

      // Store the event for tracking
      await ctx.db.insert("stripeEvents", {
        stripeEventId: event.id,
        eventType: event.type,
        eventData: event.data,
        processed: false,
        receivedAt: Date.now(),
      });

      try {
        // Process different event types
        switch (event.type) {
          case 'payment_intent.succeeded':
            await handlePaymentSucceeded(ctx, event.data);
            break;
            
          case 'payment_intent.payment_failed':
            await handlePaymentFailed(ctx, event.data);
            break;
            
          case 'account.updated':
            await handleAccountUpdated(ctx, event.data);
            break;
            
          case 'transfer.created':
            await handleTransferCreated(ctx, event.data);
            break;
            
          case 'checkout.session.completed':
            await handleCheckoutCompleted(ctx, event.data);
            break;
            
          case 'customer.subscription.created':
          case 'customer.subscription.updated':
          case 'customer.subscription.deleted':
            await handleSubscriptionEvent(ctx, event.data);
            break;
            
          default:
            console.log(`Unhandled event type: ${event.type}`);
        }

        // Mark event as processed
        const storedEvent = await ctx.db
          .query("stripeEvents")
          .withIndex("by_stripeEventId", (q) => q.eq("stripeEventId", event.id))
          .first();
          
        if (storedEvent) {
          await ctx.db.patch(storedEvent._id, {
            processed: true,
            processedAt: Date.now(),
          });
        }

        return { success: true, message: "Event processed successfully" };
      } catch (error) {
        console.error(`Error processing event ${event.id}:`, error);
        
        // Mark event as failed
        const storedEvent = await ctx.db
          .query("stripeEvents")
          .withIndex("by_stripeEventId", (q) => q.eq("stripeEventId", event.id))
          .first();
          
        if (storedEvent) {
          await ctx.db.patch(storedEvent._id, {
            processed: false,
            processingError: error instanceof Error ? error.message : "Unknown error",
            processedAt: Date.now(),
          });
        }

        throw new ConvexError("Failed to process webhook event");
      }
    } catch (error) {
      console.error("Webhook signature verification failed:", error);
      return { success: false, error: "Invalid webhook signature" };
    }
  },
});

/**
 * Handle successful payment
 */
async function handlePaymentSucceeded(ctx: any, eventData: any) {
  const paymentIntent = eventData.object;
  const orderId = paymentIntent.metadata?.orderId;

  if (!orderId) {
    console.log("No order ID found in payment intent metadata");
    return;
  }

  // Update order status
  const order = await ctx.db.get(orderId as Id<"orders">);
  if (!order) return;

  const updateData: any = {
    stripePaymentIntentId: paymentIntent.id,
    updatedAt: Date.now(),
  };

  if (paymentIntent.amount) {
    updateData.orderStatus = "paid";
    updateData.paidDate = Date.now();
  }

  await ctx.db.patch(orderId as Id<"orders">, updateData);
  console.log(`Payment succeeded for order ${orderId}`);
}

/**
 * Handle failed payment
 */
async function handlePaymentFailed(ctx: any, eventData: any) {
  const paymentIntent = eventData.object;
  const orderId = paymentIntent.metadata?.orderId;

  if (!orderId) {
    console.log("No order ID found in payment intent metadata");
    return;
  }

  // Update order status
  const order = await ctx.db.get(orderId as Id<"orders">);
  if (!order) return;

  await ctx.db.patch(orderId as Id<"orders">, {
    stripePaymentIntentId: paymentIntent.id,
    orderStatus: "cancelled",
    cancelledDate: Date.now(),
    cancellationReason: paymentIntent.last_payment_error?.message || "Payment failed",
    updatedAt: Date.now(),
  });

  console.log(`Payment failed for order ${orderId}`);
}

/**
 * Handle Stripe Connect account updates
 */
async function handleAccountUpdated(ctx: any, eventData: any) {
  const account = eventData.object;
  
  // Find seller by Stripe account ID
  const sellerProfile = await ctx.db
    .query("sellerProfiles")
    .filter((q: any) => q.eq(q.field("stripeConnectAccountId"), account.id))
    .first();

  if (!sellerProfile) {
    console.log(`No seller found for Stripe account ${account.id}`);
    return;
  }

  // Update seller's Stripe status
  const isEnabled = account.charges_enabled && account.payouts_enabled && account.details_submitted;
  const status = isEnabled ? "enabled" : (account.requirements?.currently_due?.length || 0) > 0 ? "restricted" : "pending";

  await ctx.db.patch(sellerProfile._id, {
    stripeAccountStatus: status,
    stripeOnboardingComplete: isEnabled,
    stripeChargesEnabled: account.charges_enabled,
    stripePayoutsEnabled: account.payouts_enabled,
    stripeDetailsSubmitted: account.details_submitted,
    stripeRequirements: account.requirements?.currently_due || [],
    updatedAt: Date.now(),
  });

  console.log(`Updated Stripe status for seller ${sellerProfile.userId}`);
}

/**
 * Handle transfer creation
 */
async function handleTransferCreated(ctx: any, eventData: any) {
  const transfer = eventData.object;
  const orderId = transfer.metadata?.orderId;

  if (orderId) {
    const order = await ctx.db.get(orderId as Id<"orders">);
    if (order) {
      await ctx.db.patch(orderId as Id<"orders">, {
        stripeTransferId: transfer.id,
        sellerEarnings: transfer.amount / 100,
        updatedAt: Date.now(),
      });
    }
  }

  console.log(`Transfer created: ${transfer.id}`);
}

/**
 * Handle successful transfer
 */
async function handleTransferPaid(ctx: any, eventData: any) {
  const transfer = eventData.object;
  const orderId = transfer.metadata?.orderId;

  if (orderId) {
    const order = await ctx.db.get(orderId as Id<"orders">);
    if (order) {
      await ctx.db.patch(orderId as Id<"orders">, {
        stripeTransferId: transfer.id,
        sellerEarnings: transfer.amount / 100,
        updatedAt: Date.now(),
      });
    }
  }

  console.log(`Transfer paid: ${transfer.id}`);
}

/**
 * Handle failed transfer
 */
async function handleTransferFailed(ctx: any, eventData: any) {
  const transfer = eventData.object;
  const orderId = transfer.metadata?.orderId;

  if (orderId) {
    const order = await ctx.db.get(orderId as Id<"orders">);
    if (order) {
      await ctx.db.patch(orderId as Id<"orders">, {
        stripeTransferId: transfer.id,
        sellerEarnings: transfer.amount / 100,
        updatedAt: Date.now(),
      });
    }
  }

  console.log(`Transfer failed: ${transfer.id}`);
}

/**
 * Handle checkout session completed
 */
async function handleCheckoutCompleted(ctx: any, eventData: any) {
  const checkoutSession = eventData.object;
  const orderId = checkoutSession.metadata?.orderId;

  if (!orderId) {
    console.log("No order ID found in checkout session metadata");
    return;
  }

  // Update order status
  const order = await ctx.db.get(orderId as Id<"orders">);
  if (!order) return;

  await ctx.db.patch(orderId as Id<"orders">, {
    stripePaymentIntentId: checkoutSession.payment_intent,
    orderStatus: "paid",
    paidDate: Date.now(),
    updatedAt: Date.now(),
  });

  console.log(`Checkout session completed for order ${orderId}`);
}

/**
 * Handle subscription events
 */
async function handleSubscriptionEvent(ctx: any, eventData: any) {
  const subscription = eventData.object;
  const orderId = subscription.metadata?.orderId;

  if (!orderId) {
    console.log("No order ID found in subscription metadata");
    return;
  }

  // Update order status based on subscription event type
  const order = await ctx.db.get(orderId as Id<"orders">);
  if (!order) return;

  if (subscription.status === 'active') {
    await ctx.db.patch(orderId as Id<"orders">, {
      stripePaymentIntentId: subscription.latest_invoice.payment_intent,
      orderStatus: "paid",
      paidDate: Date.now(),
      updatedAt: Date.now(),
    });
    console.log(`Subscription active for order ${orderId}`);
  } else if (subscription.status === 'canceled') {
    await ctx.db.patch(orderId as Id<"orders">, {
      stripePaymentIntentId: subscription.latest_invoice.payment_intent,
      orderStatus: "cancelled",
      cancelledDate: Date.now(),
      cancellationReason: "Subscription cancelled",
      updatedAt: Date.now(),
    });
    console.log(`Subscription cancelled for order ${orderId}`);
  } else if (subscription.status === 'unpaid') {
    await ctx.db.patch(orderId as Id<"orders">, {
      stripePaymentIntentId: subscription.latest_invoice.payment_intent,
      orderStatus: "payment_failed",
      cancelledDate: Date.now(),
      cancellationReason: subscription.latest_invoice.last_payment_error?.message || "Subscription unpaid",
      updatedAt: Date.now(),
    });
    console.log(`Subscription unpaid for order ${orderId}`);
  }
}
