import { v } from "convex/values";
import { mutation, query, action, internalMutation } from "./_generated/server";
import { ConvexError } from "convex/values";
import { 
  getAuthUser, 
} from "./lib/auth_utils";
import { createConnectAccount as createStripeAccount, createAccountLink, getAccountStatus, STRIPE_CONFIG } from "./lib/stripe";

/**
 * Create Stripe Connect account for approved seller
 * This is called when a seller application is approved
 */
export const createConnectAccount = action({
  args: {
    userId: v.id("users"),
    email: v.string(),
    country: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<{
    success: boolean;
    accountId: string;
    message: string;
  }> => {
    try {
      // Create Stripe Connect Express account
      const account = await createStripeAccount(args.email, args.country || 'US');
      
      // Store account information in database
      // TODO: Fix internal API reference after regeneration
      // await ctx.runMutation(internal.stripeConnect.storeConnectAccount, {
      //   userId: args.userId,
      //   stripeAccountId: account.id,
      //   email: args.email,
      // });

      return {
        success: true,
        accountId: account.id,
        message: "Stripe Connect account created successfully",
      };
    } catch (error) {
      console.error("Error creating Stripe Connect account:", error);
      throw new ConvexError("Failed to create Stripe Connect account");
    }
  },
});

/**
 * Internal mutation to store Stripe Connect account info
 */
export const storeConnectAccount = internalMutation({
  args: {
    userId: v.id("users"),
    stripeAccountId: v.string(),
    email: v.string(),
  },
  handler: async (ctx, args): Promise<{ success: boolean }> => {
    // Update seller profile with Stripe account info
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();

    if (!sellerProfile) {
      throw new ConvexError("Seller profile not found");
    }

    await ctx.db.patch(sellerProfile._id, {
      stripeConnectAccountId: args.stripeAccountId,
      stripeAccountStatus: "pending",
      stripeOnboardingComplete: false,
      stripeChargesEnabled: false,
      stripePayoutsEnabled: false,
      stripeDetailsSubmitted: false,
      updatedAt: Date.now(),
    });

    // Create onboarding tracking record
    await ctx.db.insert("stripeOnboarding", {
      userId: args.userId,
      stripeAccountId: args.stripeAccountId,
      status: "pending",
      refreshCount: 0,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

/**
 * Generate onboarding link for seller
 */
export const generateOnboardingLink = mutation({
  args: {
    userId: v.optional(v.id("users")),
    stripeAccountId: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<{
    success: boolean;
    url: string;
    expiresAt: number;
  }> => {
    const user = await getAuthUser(ctx);
    if (!user) {
      throw new ConvexError("Authentication required");
    }

    const userId = args.userId || user._id;
    
    // Get seller profile
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .first();

    if (!sellerProfile?.stripeConnectAccountId) {
      throw new ConvexError("Stripe Connect account not found");
    }
    
    try {
      const refreshUrl = `${STRIPE_CONFIG.appUrl}/seller/onboarding/refresh`;
      const returnUrl = `${STRIPE_CONFIG.appUrl}/seller/onboarding/complete`;
      
      const accountLink = await createAccountLink(
        sellerProfile.stripeConnectAccountId,
        refreshUrl,
        returnUrl
      );

      // Update onboarding tracking
      const onboarding = await ctx.db
        .query("stripeOnboarding")
        .withIndex("by_userId", (q) => q.eq("userId", userId))
        .first();

      if (onboarding) {
        await ctx.db.patch(onboarding._id, {
          onboardingUrl: accountLink.url,
          onboardingExpiresAt: accountLink.expires_at * 1000,
          status: "in_progress",
          refreshCount: onboarding.refreshCount + 1,
          lastRefreshAt: Date.now(),
          updatedAt: Date.now(),
        });
      } else {
        // Create new onboarding record if none exists
        await ctx.db.insert("stripeOnboarding", {
          userId,
          stripeAccountId: sellerProfile.stripeConnectAccountId,
          onboardingUrl: accountLink.url,
          onboardingExpiresAt: accountLink.expires_at * 1000,
          status: "in_progress",
          refreshCount: 1,
          lastRefreshAt: Date.now(),
          updatedAt: Date.now(),
        });
      }

      return {
        success: true,
        url: accountLink.url,
        expiresAt: accountLink.expires_at,
      };
    } catch (error) {
      console.error("Error generating onboarding link:", error);
      throw new ConvexError("Failed to generate onboarding link");
    }
  },
});

/**
 * Internal query to get seller profile
 */
export const getSellerProfile = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();
  },
});

/**
 * Internal mutation to update onboarding link
 */
export const updateOnboardingLink = internalMutation({
  args: {
    userId: v.id("users"),
    onboardingUrl: v.string(),
    expiresAt: v.number(),
  },
  handler: async (ctx, args): Promise<{ success: boolean }> => {
    const onboarding = await ctx.db
      .query("stripeOnboarding")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();

    if (onboarding) {
      await ctx.db.patch(onboarding._id, {
        onboardingUrl: args.onboardingUrl,
        onboardingExpiresAt: args.expiresAt,
        status: "in_progress",
        refreshCount: onboarding.refreshCount + 1,
        lastRefreshAt: Date.now(),
        updatedAt: Date.now(),
      });
    }

    return { success: true };
  },
});

/**
 * Check and update Stripe account status
 */
export const updateAccountStatus = mutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args): Promise<{
    success: boolean;
    status: any;
  }> => {
    // Get seller profile directly since we're in a mutation
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();

    if (!sellerProfile?.stripeConnectAccountId) {
      throw new ConvexError("Stripe Connect account not found");
    }
    
    try {
      const accountStatus = await getAccountStatus(sellerProfile.stripeConnectAccountId);
      
      // Update seller profile with current status
      const isEnabled = accountStatus.charges_enabled && accountStatus.payouts_enabled && accountStatus.details_submitted;
      const status = isEnabled ? "enabled" : (accountStatus.requirements?.currently_due?.length || 0) > 0 ? "restricted" : "pending";

      await ctx.db.patch(sellerProfile._id, {
        stripeAccountStatus: status,
        stripeOnboardingComplete: isEnabled,
        stripeChargesEnabled: accountStatus.charges_enabled,
        stripePayoutsEnabled: accountStatus.payouts_enabled,
        stripeDetailsSubmitted: accountStatus.details_submitted,
        stripeRequirements: accountStatus.requirements?.currently_due || [],
        updatedAt: Date.now(),
      });

      // Update onboarding status if complete
      if (isEnabled) {
        const onboarding = await ctx.db
          .query("stripeOnboarding")
          .withIndex("by_userId", (q) => q.eq("userId", args.userId))
          .first();

        if (onboarding) {
          await ctx.db.patch(onboarding._id, {
            status: "completed",
            completedAt: Date.now(),
            updatedAt: Date.now(),
          });
        }
      }

      return {
        success: true,
        status: accountStatus,
      };
    } catch (error) {
      console.error("Error updating account status:", error);
      throw new ConvexError("Failed to update account status");
    }
  },
});

/**
 * Internal mutation to update seller Stripe status
 */
export const updateSellerStripeStatus = internalMutation({
  args: {
    userId: v.id("users"),
    chargesEnabled: v.boolean(),
    payoutsEnabled: v.boolean(),
    detailsSubmitted: v.boolean(),
    requirements: v.array(v.string()),
  },
  handler: async (ctx, args): Promise<{ success: boolean }> => {
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();

    if (!sellerProfile) {
      throw new ConvexError("Seller profile not found");
    }

    const isEnabled = args.chargesEnabled && args.payoutsEnabled && args.detailsSubmitted;
    const status = isEnabled ? "enabled" : args.requirements.length > 0 ? "restricted" : "pending";

    await ctx.db.patch(sellerProfile._id, {
      stripeAccountStatus: status,
      stripeOnboardingComplete: isEnabled,
      stripeChargesEnabled: args.chargesEnabled,
      stripePayoutsEnabled: args.payoutsEnabled,
      stripeDetailsSubmitted: args.detailsSubmitted,
      stripeRequirements: args.requirements,
      updatedAt: Date.now(),
    });

    // Update onboarding status if complete
    if (isEnabled) {
      const onboarding = await ctx.db
        .query("stripeOnboarding")
        .withIndex("by_userId", (q) => q.eq("userId", args.userId))
        .first();

      if (onboarding) {
        await ctx.db.patch(onboarding._id, {
          status: "completed",
          completedAt: Date.now(),
          updatedAt: Date.now(),
        });
      }
    }

    return { success: true };
  },
});

/**
 * Get seller's Stripe Connect status
 */
export const getConnectStatus = query({
  args: {
    userId: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      return null;
    }

    const userId = args.userId || user._id;
    
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .first();

    if (!sellerProfile) {
      return null;
    }

    const onboarding = await ctx.db
      .query("stripeOnboarding")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .first();

    return {
      hasStripeAccount: !!sellerProfile.stripeConnectAccountId,
      accountId: sellerProfile.stripeConnectAccountId,
      status: sellerProfile.stripeAccountStatus,
      onboardingComplete: sellerProfile.stripeOnboardingComplete,
      chargesEnabled: sellerProfile.stripeChargesEnabled,
      payoutsEnabled: sellerProfile.stripePayoutsEnabled,
      detailsSubmitted: sellerProfile.stripeDetailsSubmitted,
      requirements: sellerProfile.stripeRequirements || [],
      onboarding: onboarding ? {
        status: onboarding.status,
        url: onboarding.onboardingUrl,
        expiresAt: onboarding.onboardingExpiresAt,
        refreshCount: onboarding.refreshCount,
      } : null,
    };
  },
});
