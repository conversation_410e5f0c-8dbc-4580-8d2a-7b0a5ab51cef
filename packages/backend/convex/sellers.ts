import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { ConvexError } from "convex/values";
import { requireAuth, requireAdmin } from "./lib/auth_utils";
import { betterAuthComponent } from "./auth";
import { calculateSellerRevenue } from "./lib/utils";
import type { Id } from "./_generated/dataModel";

// Get seller status for current user
export const getSellerStatus = query({
  args: {},
  handler: async (ctx) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) {
      return {
        hasProfile: false,
        verificationStatus: "pending" as const,
        canSell: false,
      };
    }

    const userId = userMetadata.userId as Id<"users">;
    const user = await ctx.db.get(userId);
    
    if (!user || user.userType !== "seller") {
      return {
        hasProfile: false,
        verificationStatus: "pending" as const,
        canSell: false,
      };
    }

    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .first();

    const isSubscriptionActive = user.subscriptionStatus === "active" && 
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

    return {
      hasProfile: !!sellerProfile,
      verificationStatus: sellerProfile?.verificationStatus || "pending",
      canSell: isSubscriptionActive && sellerProfile?.verificationStatus === "approved",
    };
  },
});

// Create seller profile
export const createProfile = mutation({
  args: {
    businessName: v.optional(v.string()),
    businessType: v.optional(v.string()),
    taxId: v.optional(v.string()),
    address: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
    }),
    phone: v.string(),
  },
  handler: async (ctx, args) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) throw new Error("Not authenticated");

    const userId = userMetadata.userId as Id<"users">;
    const user = await ctx.db.get(userId);
    if (!user) throw new Error("User not found");

    // Check if seller profile already exists
    const existingProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .first();

    if (existingProfile) {
      throw new Error("Seller profile already exists");
    }

    const now = Date.now();

    // Create seller profile
    const profileId = await ctx.db.insert("sellerProfiles", {
      userId,
      businessName: args.businessName,
      businessType: args.businessType,
      taxId: args.taxId,
      address: args.address,
      phone: args.phone,
      verificationStatus: "pending",
      applicationDate: now,
      updatedAt: now,
    });

    // Update user type to seller if not already
    if (user.userType !== "seller") {
      await ctx.db.patch(userId, {
        userType: "seller",
        updatedAt: now,
      });
    }

    // Log seller application
    await ctx.db.insert("analytics", {
      eventType: "seller_application",
      userId,
      timestamp: now,
      metadata: {
        category: "seller",
      },
    });

    return { success: true, profileId };
  },
});

// Update seller profile
export const updateProfile = mutation({
  args: {
    businessName: v.optional(v.string()),
    businessType: v.optional(v.string()),
    taxId: v.optional(v.string()),
    address: v.optional(v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
    })),
    phone: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) throw new Error("Not authenticated");

    const userId = userMetadata.userId as Id<"users">;
    const user = await ctx.db.get(userId);
    if (!user || user.userType !== "seller") {
      throw new Error("Not authorized as seller");
    }

    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .first();

    if (!sellerProfile) {
      throw new Error("Seller profile not found");
    }

    // Update profile
    await ctx.db.patch(sellerProfile._id, {
      ...args,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Update seller company information for invoices and branding
export const updateCompanyInfo = mutation({
  args: {
    companyLogo: v.optional(v.id("_storage")),
    website: v.optional(v.string()),
    companyEmail: v.optional(v.string()),
    companyPhone: v.optional(v.string()),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) throw new Error("Not authenticated");

    const userId = userMetadata.userId as Id<"users">;
    const user = await ctx.db.get(userId);
    if (!user || user.userType !== "seller") {
      throw new Error("Not authorized as seller");
    }

    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .first();

    if (!sellerProfile) {
      throw new Error("Seller profile not found");
    }

    // Update company information
    await ctx.db.patch(sellerProfile._id, {
      ...args,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Get seller company information for invoices
export const getCompanyInfo = query({
  args: {},
  returns: v.union(
    v.object({
      businessName: v.optional(v.string()),
      companyLogo: v.optional(v.id("_storage")),
      website: v.optional(v.string()),
      companyEmail: v.optional(v.string()),
      companyPhone: v.optional(v.string()),
      address: v.object({
        street: v.string(),
        city: v.string(),
        state: v.string(),
        zipCode: v.string(),
        country: v.string(),
      }),
      phone: v.string(),
    }),
    v.null()
  ),
  handler: async (ctx) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) return null;

    const userId = userMetadata.userId as Id<"users">;
    const user = await ctx.db.get(userId);
    
    if (!user || user.userType !== "seller") return null;

    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .first();

    if (!sellerProfile) return null;

    return {
      businessName: sellerProfile.businessName,
      companyLogo: sellerProfile.companyLogo,
      website: sellerProfile.website,
      companyEmail: sellerProfile.companyEmail,
      companyPhone: sellerProfile.companyPhone,
      address: sellerProfile.address,
      phone: sellerProfile.phone,
    };
  },
});

// Get seller profile
export const getProfile = query({
  args: {},
  handler: async (ctx) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) return null;

    const userId = userMetadata.userId as Id<"users">;
    const user = await ctx.db.get(userId);
    
    if (!user || user.userType !== "seller") return null;

    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .first();

    return sellerProfile;
  },
});

// Upload verification document
export const uploadVerificationDocument = mutation({
  args: {
    type: v.string(),
    url: v.string(),
  },
  handler: async (ctx, { type, url }) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) throw new Error("Not authenticated");

    const userId = userMetadata.userId as Id<"users">;
    const user = await ctx.db.get(userId);
    if (!user || user.userType !== "seller") {
      throw new Error("Not authorized as seller");
    }

    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .first();

    if (!sellerProfile) {
      throw new Error("Seller profile not found");
    }

    const document = {
      type,
      url,
      uploadedAt: Date.now(),
    };

    const existingDocs = sellerProfile.verificationDocuments || [];
    const updatedDocs = [...existingDocs, document];

    await ctx.db.patch(sellerProfile._id, {
      verificationDocuments: updatedDocs,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Get seller analytics (for sellers to view their own data)
export const getSellerAnalytics = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, { limit = 50 }) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) throw new Error("Not authenticated");

    const userId = userMetadata.userId as Id<"users">;
    const user = await ctx.db.get(userId);
    if (!user || user.userType !== "seller") {
      throw new Error("Not authorized as seller");
    }

    const events = await ctx.db
      .query("analytics")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .order("desc")
      .take(limit);

    return events;
  },
});

// Get seller dashboard stats
export const getDashboardStats = query({
  args: {},
  handler: async (ctx) => {
    const userMetadata = await betterAuthComponent.getAuthUser(ctx);
    if (!userMetadata) throw new Error("Not authenticated");

    const userId = userMetadata.userId as Id<"users">;
    const user = await ctx.db.get(userId);
    if (!user || user.userType !== "seller") {
      throw new Error("Not authorized as seller");
    }

    // Get seller's products
    const products = await ctx.db
      .query("products")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", userId))
      .collect();

    // Get seller's orders
    const orders = await ctx.db
      .query("orders")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", userId))
      .collect();

    // Calculate stats
    const totalProducts = products.length;
    const activeProducts = products.filter(p => p.status === "active").length;
    const soldProducts = products.filter(p => p.status === "sold").length;
    
    const totalOrders = orders.length;
    const completedOrders = orders.filter(o => o.orderStatus === "delivered").length;
    const pendingOrders = orders.filter(o => 
      ["pending", "confirmed", "shipped"].includes(o.orderStatus)
    ).length;

    const totalRevenue = calculateSellerRevenue(
      orders.filter(o => o.orderStatus === "delivered")
    );

    const thisMonthStart = new Date();
    thisMonthStart.setDate(1);
    thisMonthStart.setHours(0, 0, 0, 0);

    const thisMonthOrders = orders.filter(o => 
      o.orderDate >= thisMonthStart.getTime() && o.orderStatus === "delivered"
    );
    const monthlyRevenue = calculateSellerRevenue(thisMonthOrders);

    return {
      totalProducts,
      activeProducts,
      soldProducts,
      totalOrders,
      completedOrders,
      pendingOrders,
      totalRevenue,
      monthlyRevenue,
    };
  },
});
