import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { ConvexError } from "convex/values";

// Generate a unique request number for middleman requests
function generateRequestNumber(): string {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `HV-MM-${timestamp}-${random}`;
}

// Submit a new middleman service request
export const submitMiddlemanRequest = mutation({
  args: {
    // Product Details
    productName: v.string(),
    productBrand: v.string(),
    productModel: v.optional(v.string()),
    productDescription: v.string(),
    productCondition: v.union(
      v.literal("new"),
      v.literal("like_new"),
      v.literal("excellent"),
      v.literal("good"),
      v.literal("fair")
    ),
    productValue: v.string(),
    currency: v.string(),
    productInclusions: v.string(),
    productImages: v.optional(v.array(v.string())),
    
    // Seller Information
    sellerName: v.string(),
    sellerEmail: v.string(),
    sellerPhone: v.optional(v.string()),
    sellerAddress: v.string(),
    
    // Buyer Information
    buyerName: v.string(),
    buyerEmail: v.string(),
    buyerPhone: v.optional(v.string()),
    buyerAddress: v.string(),
    
    // Shipment Information
    approximateShipmentDate: v.string(),
    shippingMethod: v.optional(v.string()),
    
    // Additional Information
    specialInstructions: v.optional(v.string()),
  },
  returns: v.id("middlemanRequests"),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Authentication required");
    }

    // Get user information
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .unique();

    if (!user) {
      throw new ConvexError("User not found");
    }

    const now = Date.now();
    const requestNumber = generateRequestNumber();

    // Create the middleman request
    const requestId = await ctx.db.insert("middlemanRequests", {
      requestNumber,
      userId: user._id,
      userEmail: user.email,
      userName: user.name,
      
      // Product Details
      productName: args.productName,
      productBrand: args.productBrand,
      productModel: args.productModel,
      productDescription: args.productDescription,
      productCondition: args.productCondition,
      productValue: args.productValue,
      currency: args.currency,
      productInclusions: args.productInclusions,
      productImages: args.productImages,
      
      // Seller Information
      sellerName: args.sellerName,
      sellerEmail: args.sellerEmail,
      sellerPhone: args.sellerPhone,
      sellerAddress: args.sellerAddress,
      
      // Buyer Information
      buyerName: args.buyerName,
      buyerEmail: args.buyerEmail,
      buyerPhone: args.buyerPhone,
      buyerAddress: args.buyerAddress,
      
      // Shipment Information
      approximateShipmentDate: args.approximateShipmentDate,
      shippingMethod: args.shippingMethod,
      
      // Additional Information
      specialInstructions: args.specialInstructions,
      
      // Default values
      status: "pending",
      priority: "medium",
      submittedAt: now,
      updatedAt: now,
    });

    return requestId;
  },
});

// Get user's middleman requests
export const getUserMiddlemanRequests = query({
  args: {},
  returns: v.any(),
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .unique();

    if (!user) {
      return [];
    }

    const requests = await ctx.db
      .query("middlemanRequests")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .order("desc")
      .collect();

    return requests.map(request => ({
      _id: request._id,
      _creationTime: request._creationTime,
      requestNumber: request.requestNumber,
      productName: request.productName,
      productBrand: request.productBrand,
      productValue: request.productValue,
      currency: request.currency,
      status: request.status,
      priority: request.priority,
      submittedAt: request.submittedAt,
      updatedAt: request.updatedAt,
      resolvedAt: request.resolvedAt,
      resolutionNotes: request.resolutionNotes,
    }));
  },
});

// Get all middleman requests (admin only)
export const getAllMiddlemanRequests = query({
  args: {
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("under_review"),
      v.literal("approved"),
      v.literal("in_progress"),
      v.literal("completed"),
      v.literal("rejected"),
      v.literal("cancelled")
    )),
    limit: v.optional(v.number()),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Authentication required");
    }

    // Check if user is admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .unique();

    if (!user || user.userType !== "admin") {
      throw new ConvexError("Admin access required");
    }

    let requests;

    if (args.status) {
      requests = await ctx.db
        .query("middlemanRequests")
        .withIndex("by_status", (q) => q.eq("status", args.status!))
        .order("desc")
        .collect();
    } else {
      requests = await ctx.db
        .query("middlemanRequests")
        .withIndex("by_submittedAt")
        .order("desc")
        .collect();
    }

    if (args.limit) {
      requests = requests.slice(0, args.limit);
    }

    return requests;
  },
});

// Update middleman request status (admin only)
export const updateMiddlemanRequestStatus = mutation({
  args: {
    requestId: v.id("middlemanRequests"),
    status: v.union(
      v.literal("pending"),
      v.literal("under_review"),
      v.literal("approved"),
      v.literal("in_progress"),
      v.literal("completed"),
      v.literal("rejected"),
      v.literal("cancelled")
    ),
    priority: v.optional(v.union(v.literal("low"), v.literal("medium"), v.literal("high"))),
    adminNotes: v.optional(v.string()),
    resolutionNotes: v.optional(v.string()),
    serviceFee: v.optional(v.number()),
    assignedStaffId: v.optional(v.id("users")),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Authentication required");
    }

    // Check if user is admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .unique();

    if (!user || user.userType !== "admin") {
      throw new ConvexError("Admin access required");
    }

    const request = await ctx.db.get(args.requestId);
    if (!request) {
      throw new ConvexError("Middleman request not found");
    }

    const now = Date.now();
    const updateData: any = {
      status: args.status,
      updatedAt: now,
    };

    if (args.priority !== undefined) {
      updateData.priority = args.priority;
    }

    if (args.adminNotes !== undefined) {
      updateData.adminNotes = args.adminNotes;
    }

    if (args.resolutionNotes !== undefined) {
      updateData.resolutionNotes = args.resolutionNotes;
    }

    if (args.serviceFee !== undefined) {
      updateData.serviceFee = args.serviceFee;
    }

    if (args.assignedStaffId !== undefined) {
      updateData.assignedStaffId = args.assignedStaffId;
    }

    // Set resolved timestamp if status is completed, rejected, or cancelled
    if (["completed", "rejected", "cancelled"].includes(args.status)) {
      updateData.resolvedAt = now;
    }

    await ctx.db.patch(args.requestId, updateData);

    return null;
  },
});

// Get middleman request statistics (admin only)
export const getMiddlemanStats = query({
  args: {},
  returns: v.any(),
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Authentication required");
    }

    // Check if user is admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .unique();

    if (!user || user.userType !== "admin") {
      throw new ConvexError("Admin access required");
    }

    const allRequests = await ctx.db
      .query("middlemanRequests")
      .collect();

    const stats = {
      total: allRequests.length,
      pending: allRequests.filter(r => r.status === "pending").length,
      underReview: allRequests.filter(r => r.status === "under_review").length,
      approved: allRequests.filter(r => r.status === "approved").length,
      inProgress: allRequests.filter(r => r.status === "in_progress").length,
      completed: allRequests.filter(r => r.status === "completed").length,
      rejected: allRequests.filter(r => r.status === "rejected").length,
      cancelled: allRequests.filter(r => r.status === "cancelled").length,
    };

    return stats;
  },
});
