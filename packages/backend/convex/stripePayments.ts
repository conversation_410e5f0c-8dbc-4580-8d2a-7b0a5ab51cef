import { v } from "convex/values";
import { mutation, action, query } from "./_generated/server";
import { ConvexError } from "convex/values";
import { requireAuth } from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";
import { createPaymentIntentWithTransfer, createRefund } from "./lib/stripe";
import getStripe from "./lib/stripe";

/**
 * Create a payment intent for a product purchase
 */
export const createPaymentIntent = action({
  args: {
    productId: v.id("products"),
    amount: v.number(),
    currency: v.optional(v.string()),
    metadata: v.optional(v.record(v.string(), v.string())),
  },
  handler: async (ctx, args) => {
    try {
      const paymentIntent = await getStripe().paymentIntents.create({
        amount: Math.round(args.amount * 100), // Convert to cents
        currency: args.currency || 'usd',
        metadata: {
          ...args.metadata,
          productId: args.productId,
        },
      });

      return {
        success: true,
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
      };
    } catch (error) {
      console.error("Error creating payment intent:", error);
      throw new ConvexError("Failed to create payment intent");
    }
  },
});

/**
 * Create a payment intent with Connect transfer for marketplace sales
 */
export const createMarketplacePayment = action({
  args: {
    productId: v.id("products"),
    sellerId: v.id("users"),
    amount: v.number(),
    currency: v.optional(v.string()),
    metadata: v.optional(v.record(v.string(), v.string())),
  },
  handler: async (ctx, args) => {
    try {
      // For now, we'll create a regular payment intent
      // The marketplace transfer logic can be implemented later
      const paymentIntent = await getStripe().paymentIntents.create({
        amount: Math.round(args.amount * 100), // Convert to cents
        currency: args.currency || 'usd',
        metadata: {
          ...args.metadata,
          productId: args.productId,
          sellerId: args.sellerId,
          type: "marketplace",
        },
      });

      return {
        success: true,
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        message: "Marketplace payment intent created (transfer logic to be implemented)",
      };
    } catch (error) {
      console.error("Error creating marketplace payment:", error);
      throw new ConvexError("Failed to create marketplace payment");
    }
  },
});

/**
 * Confirm a payment intent
 */
export const confirmPayment = mutation({
  args: {
    paymentIntentId: v.string(),
    orderId: v.id("orders"),
  },
  handler: async (ctx, args) => {
    try {
      // Get the payment intent from Stripe
      const paymentIntent = await getStripe().paymentIntents.retrieve(args.paymentIntentId);
      
      if (paymentIntent.status === 'succeeded') {
        // Update order status
        await ctx.db.patch(args.orderId, {
          orderStatus: "paid",
          paidDate: Date.now(),
          stripePaymentIntentId: args.paymentIntentId,
          updatedAt: Date.now(),
        });

        return {
          success: true,
          status: "paid",
          message: "Payment confirmed successfully",
        };
      } else if (paymentIntent.status === 'requires_payment_method') {
        return {
          success: false,
          status: "requires_payment_method",
          message: "Payment method required",
        };
      } else {
        return {
          success: false,
          status: paymentIntent.status,
          message: `Payment status: ${paymentIntent.status}`,
        };
      }
    } catch (error) {
      console.error("Error confirming payment:", error);
      throw new ConvexError("Failed to confirm payment");
    }
  },
});

/**
 * Process refund for an order
 */
export const processRefund = action({
  args: {
    paymentIntentId: v.string(),
    orderId: v.id("orders"),
    amount: v.optional(v.number()),
    reason: v.optional(v.union(
      v.literal("duplicate"),
      v.literal("fraudulent"),
      v.literal("requested_by_customer")
    )),
  },
  handler: async (ctx, args) => {
    try {
      const refund = await createRefund(
        args.paymentIntentId,
        args.amount,
        args.reason || 'requested_by_customer'
      );

      // Update order status directly since we can't call internal functions from actions
      // Note: This would need to be handled by a mutation or the order update logic
      console.log(`Refund created: ${refund.id} for order: ${args.orderId}`);

      return {
        success: true,
        refundId: refund.id,
        message: "Refund processed successfully",
      };
    } catch (error) {
      console.error("Error processing refund:", error);
      throw new ConvexError("Failed to process refund");
    }
  },
});

/**
 * Get payment intent status
 */
export const getPaymentStatus = query({
  args: {
    paymentIntentId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const paymentIntent = await getStripe().paymentIntents.retrieve(args.paymentIntentId);
      
      return {
        id: paymentIntent.id,
        status: paymentIntent.status,
        amount: paymentIntent.amount / 100,
        currency: paymentIntent.currency,
        created: paymentIntent.created,
        lastPaymentError: paymentIntent.last_payment_error,
      };
    } catch (error) {
      console.error("Error retrieving payment intent:", error);
      return null;
    }
  },
});

/**
 * Internal mutation to update order with payment details
 */
export const updateOrderPayment = mutation({
  args: {
    orderId: v.id("orders"),
    paymentIntentId: v.string(),
    stripePaymentIntentId: v.string(),
    stripeTransferId: v.union(v.string(), v.null()),
    stripeApplicationFee: v.union(v.number(), v.null()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    await ctx.db.patch(args.orderId, {
      orderStatus: "paid",
      paymentIntentId: args.paymentIntentId,
      stripePaymentIntentId: args.stripePaymentIntentId,
      stripeTransferId: args.stripeTransferId,
      stripeApplicationFee: args.stripeApplicationFee,
      paidDate: now,
      updatedAt: now,
    });

    return { success: true };
  },
});

/**
 * Internal mutation to update order with refund details
 */
export const updateOrderRefund = mutation({
  args: {
    orderId: v.id("orders"),
    refundId: v.string(),
    refundAmount: v.number(),
    refundReason: v.string(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    await ctx.db.patch(args.orderId, {
      orderStatus: "refunded",
      updatedAt: now,
      // Note: You might want to add refund-specific fields to the schema
    });

    return { success: true };
  },
});

/**
 * Internal mutation to log payment events
 */
export const logPaymentEvent = mutation({
  args: {
    eventType: v.string(),
    paymentIntentId: v.string(),
    orderId: v.id("orders"),
    amount: v.number(),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("analytics", {
      eventType: args.eventType,
      timestamp: Date.now(),
      metadata: {
        paymentIntentId: args.paymentIntentId,
        orderId: args.orderId,
        revenue: args.amount,
        source: "stripe_payment",
      },
    });

    return { success: true };
  },
});

/**
 * Internal query to get order details
 */
export const getOrder = query({
  args: {
    orderId: v.id("orders"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.orderId);
  },
});

/**
 * Internal query to get admin role
 */
export const getAdminRole = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("adminRoles")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();
  },
});

// Export internal functions
export const internal = {
  stripePayments: {
    updateOrderPayment,
    updateOrderRefund,
    logPaymentEvent,
    getOrder,
    getAdminRole,
  },
};
