import { v } from "convex/values";
import { query } from "./_generated/server";
import { getAuthUser } from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";

/**
 * Get user rating and transaction information for chat interface
 */
export const getUserRatingInfo = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Get the user
    const user = await ctx.db.get(args.userId);
    if (!user) {
      return {
        rating: 0,
        reviewCount: 0,
        transactionCount: 0,
        userType: null,
      };
    }

    // If user is a seller, get their seller profile rating
    if (user.userType === "seller") {
      const sellerProfile = await ctx.db
        .query("sellerProfiles")
        .withIndex("by_userId", (q) => q.eq("userId", args.userId))
        .first();

      if (sellerProfile) {
        return {
          rating: sellerProfile.rating || 0,
          reviewCount: sellerProfile.reviewCount || 0,
          transactionCount: sellerProfile.reviewCount || 0, // Use review count as transaction count for sellers
          userType: user.userType,
        };
      }
    }

    // For consumers, calculate rating from reviews they've received
    // Get reviews where this user is the seller (from their products)
    const userProducts = await ctx.db
      .query("products")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", args.userId))
      .collect();

    const productIds = userProducts.map(p => p._id);
    
    if (productIds.length > 0) {
      // Get reviews for all products by this user
      const reviews = await ctx.db
        .query("reviews")
        .withIndex("by_productId", (q) => q.eq("productId", productIds[0] as Id<"products">))
        .filter((q) => q.eq(q.field("isVerifiedPurchase"), true))
        .collect();

      if (reviews.length > 0) {
        const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
        const averageRating = Math.round((totalRating / reviews.length) * 10) / 10;

        return {
          rating: averageRating,
          reviewCount: reviews.length,
          transactionCount: reviews.length,
          userType: user.userType,
        };
      }
    }

    // Default return for users with no reviews
    return {
      rating: 0,
      reviewCount: 0,
      transactionCount: 0,
      userType: user.userType,
    };
  },
});

/**
 * Get user's transaction count (completed orders)
 */
export const getUserTransactionCount = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Get completed orders where user is the buyer
    const buyerOrders = await ctx.db
      .query("orders")
      .withIndex("by_buyerId", (q) => q.eq("buyerId", args.userId))
      .filter((q) => q.eq(q.field("orderStatus"), "delivered"))
      .collect();

    // Get completed orders where user is the seller
    const sellerOrders = await ctx.db
      .query("orders")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", args.userId))
      .filter((q) => q.eq(q.field("orderStatus"), "delivered"))
      .collect();

    return {
      buyerTransactions: buyerOrders.length,
      sellerTransactions: sellerOrders.length,
      totalTransactions: buyerOrders.length + sellerOrders.length,
    };
  },
});
