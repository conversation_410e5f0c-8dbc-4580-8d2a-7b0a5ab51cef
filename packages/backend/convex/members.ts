import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { ConvexError } from "convex/values";
import { requireAuth } from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";
import { paginationOptsValidator } from "convex/server";

// ============================================
// MEMBER PROFILE FUNCTIONS
// ============================================

/**
 * Get or create member profile for a user
 */
export const getOrCreateMemberProfile = mutation({
  args: {
    userId: v.optional(v.id("users")),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    const targetUserId = args.userId || currentUser._id;

    // Check if profile exists
    let profile = await ctx.db
      .query("memberProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", targetUserId))
      .first();

    if (!profile) {
      // Create new profile
      const user = await ctx.db.get(targetUserId);
      if (!user) throw new ConvexError("User not found");

      const profileId = await ctx.db.insert("memberProfiles", {
        userId: targetUserId,
        displayName: user.name,
        bio: "",
        company: "",
        website: "",
        location: "",
        specialty: [],
        yearsExperience: 0,
        badges: [],

        memberSince: Date.now(),

        // Initialize karma fields
        postKarma: 0,
        commentKarma: 0,
        awarderKarma: 0,
        awardeeKarma: 0,
        totalKarma: 0,

        isPublic: true,
        showEmail: false,
        showPhone: false,
        allowMessages: true,
        updatedAt: Date.now(),
      });
      
      profile = await ctx.db.get(profileId);
    }

    return profile;
  },
});

/**
 * Update member profile
 */
export const updateMemberProfile = mutation({
  args: {
    displayName: v.optional(v.string()),
    bio: v.optional(v.string()),
    company: v.optional(v.string()),
    website: v.optional(v.string()),
    location: v.optional(v.string()),
    specialty: v.optional(v.array(v.string())),
    yearsExperience: v.optional(v.number()),
    socialLinks: v.optional(v.object({
      instagram: v.optional(v.string()),
      twitter: v.optional(v.string()),
      linkedin: v.optional(v.string()),
      facebook: v.optional(v.string()),
    })),
    isPublic: v.optional(v.boolean()),
    showEmail: v.optional(v.boolean()),
    showPhone: v.optional(v.boolean()),
    allowMessages: v.optional(v.boolean()),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    const profile = await ctx.db
      .query("memberProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", currentUser._id))
      .first();

    if (!profile) {
      throw new ConvexError("Member profile not found");
    }

    await ctx.db.patch(profile._id, {
      ...args,
      updatedAt: Date.now(),

    });

    return await ctx.db.get(profile._id);
  },
});

/**
 * Get member profile with full details
 */
export const getMemberProfile = query({
  args: {
    userId: v.id("users"),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    const user = await ctx.db.get(args.userId);
    if (!user) return null;

    // Check if current user is blocked by the profile owner
    const isBlockedByProfileOwner = await ctx.db
      .query("userBlocks")
      .withIndex("by_blocker_blocked", (q) => 
        q.eq("blockerId", args.userId).eq("blockedUserId", currentUser._id)
      )
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    if (isBlockedByProfileOwner) {
      throw new ConvexError("You cannot view this profile");
    }

    // Check if current user has blocked the profile owner
    const hasBlockedProfileOwner = await ctx.db
      .query("userBlocks")
      .withIndex("by_blocker_blocked", (q) => 
        q.eq("blockerId", currentUser._id).eq("blockedUserId", args.userId)
      )
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    if (hasBlockedProfileOwner) {
      throw new ConvexError("You cannot view this profile");
    }

    const memberProfile = await ctx.db
      .query("memberProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();

    // Get seller profile if exists
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();

    // Get current listings
    const listings = await ctx.db
      .query("products")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", args.userId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .take(10);

    // Get past listings (sold products)
    const pastListings = await ctx.db
      .query("products")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", args.userId))
      .filter((q) => q.eq(q.field("status"), "sold"))
      .take(10);

    // Convert storage IDs to URLs for listings
    const listingsWithUrls = await Promise.all(
      listings.map(async (product) => {
        const imageUrls = await Promise.all(
          product.images.map(async (imageId) => {
            const url = await ctx.storage.getUrl(imageId);
            return url;
          })
        );
        return { ...product, imageUrls };
      })
    );

    const pastListingsWithUrls = await Promise.all(
      pastListings.map(async (product) => {
        const imageUrls = await Promise.all(
          product.images.map(async (imageId) => {
            const url = await ctx.storage.getUrl(imageId);
            return url;
          })
        );
        return { ...product, imageUrls };
      })
    );

    // Get references count
    const references = await ctx.db
      .query("memberReferences")
      .withIndex("by_toUserId", (q) => q.eq("toUserId", args.userId))
      .filter((q) => q.eq(q.field("isVisible"), true))
      .collect();

    return {
      user: {
        _id: user._id,
        name: user.name,
        email: memberProfile?.showEmail ? user.email : undefined,
        phone: memberProfile?.showPhone ? user.phone : undefined,
        profileImage: user.profileImage,
        userType: user.userType,
      },
      memberProfile: memberProfile || null,
      sellerProfile: sellerProfile || null,
      listings: listingsWithUrls,
      pastListings: pastListingsWithUrls,
      referencesCount: references.length,
    };
  },
});

// ============================================
// PRODUCT COMMENTS FUNCTIONS
// ============================================

/**
 * Add comment to a product
 */
export const addProductComment = mutation({
  args: {
    productId: v.id("products"),
    content: v.string(),
    parentCommentId: v.optional(v.id("productComments")),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);

    // Validate product exists
    const product = await ctx.db.get(args.productId);
    if (!product) throw new ConvexError("Product not found");

    // Validate parent comment if provided
    if (args.parentCommentId) {
      const parentComment = await ctx.db.get(args.parentCommentId);
      if (!parentComment) throw new ConvexError("Parent comment not found");
    }

    const commentId = await ctx.db.insert("productComments", {
      productId: args.productId,
      userId: currentUser._id,
      content: args.content,
      parentCommentId: args.parentCommentId,
      isEdited: false,
      likes: 0,
      isDeleted: false,
      updatedAt: Date.now(),
    });

    // Update member profile activity
    const memberProfile = await ctx.db
      .query("memberProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", currentUser._id))
      .first();
    
    if (memberProfile) {
      await ctx.db.patch(memberProfile._id, {
        updatedAt: Date.now(),
      });
    }

    return await ctx.db.get(commentId);
  },
});

/**
 * Get product comments with pagination
 */
export const getProductComments = query({
  args: {
    productId: v.id("products"),
    paginationOpts: paginationOptsValidator,
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const results = await ctx.db
      .query("productComments")
      .withIndex("by_productId", (q) => q.eq("productId", args.productId))
      .filter((q) => 
        q.and(
          q.eq(q.field("isDeleted"), false),
          q.eq(q.field("parentCommentId"), undefined)
        )
      )
      .order("desc")
      .paginate(args.paginationOpts);

    // Fetch user details and replies for each comment
    const commentsWithDetails = await Promise.all(
      results.page.map(async (comment) => {
        const user = await ctx.db.get(comment.userId);
        const memberProfile = await ctx.db
          .query("memberProfiles")
          .withIndex("by_userId", (q) => q.eq("userId", comment.userId))
          .first();
        
        // Get replies
        const replies = await ctx.db
          .query("productComments")
          .withIndex("by_parentCommentId", (q) => q.eq("parentCommentId", comment._id))
          .filter((q) => q.eq(q.field("isDeleted"), false))
          .order("asc")
          .take(5);

        const repliesWithDetails = await Promise.all(
          replies.map(async (reply) => {
            const replyUser = await ctx.db.get(reply.userId);
            const replyMemberProfile = await ctx.db
              .query("memberProfiles")
              .withIndex("by_userId", (q) => q.eq("userId", reply.userId))
              .first();
            
            return {
              ...reply,
              user: replyUser,
              memberProfile: replyMemberProfile,
            };
          })
        );

        return {
          ...comment,
          user,
          memberProfile,
          replies: repliesWithDetails,
        };
      })
    );

    return {
      ...results,
      page: commentsWithDetails,
    };
  },
});

/**
 * Like/unlike a product comment
 */
export const toggleCommentLike = mutation({
  args: {
    commentId: v.id("productComments"),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);

    const existingLike = await ctx.db
      .query("commentLikes")
      .withIndex("by_comment_user", (q) => 
        q.eq("commentId", args.commentId)
         .eq("userId", currentUser._id)
      )
      .first();

    const comment = await ctx.db.get(args.commentId);
    if (!comment) throw new ConvexError("Comment not found");

    if (existingLike) {
      // Unlike
      await ctx.db.delete(existingLike._id);
      await ctx.db.patch(args.commentId, {
        likes: Math.max(0, comment.likes - 1),
      });
      return { liked: false };
    } else {
      // Like
      await ctx.db.insert("commentLikes", {
        commentId: args.commentId,
        userId: currentUser._id,
      });
      await ctx.db.patch(args.commentId, {
        likes: comment.likes + 1,
      });
      return { liked: true };
    }
  },
});

/**
 * Fix member profile forum post and comment counts by recalculating from actual posts/comments
 */
export const fixMemberProfileCounts = mutation({
  args: {
    userId: v.optional(v.id("users")),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    const targetUserId = args.userId || currentUser._id;

    // Get or create member profile
    let profile = await ctx.db
      .query("memberProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", targetUserId))
      .first();

    if (!profile) {
      // Create new profile
      const user = await ctx.db.get(targetUserId);
      if (!user) throw new ConvexError("User not found");

      const profileId = await ctx.db.insert("memberProfiles", {
        userId: targetUserId,
        displayName: user.name,
        bio: "",
        company: "",
        website: "",
        location: "",
        specialty: [],
        yearsExperience: 0,
        badges: [],

        memberSince: Date.now(),

        // Initialize karma fields
        postKarma: 0,
        commentKarma: 0,
        awarderKarma: 0,
        awardeeKarma: 0,
        totalKarma: 0,

        isPublic: true,
        showEmail: false,
        showPhone: false,
        allowMessages: true,
        updatedAt: Date.now(),
      });
      
      profile = await ctx.db.get(profileId);
    }

    if (!profile) {
      throw new ConvexError("Failed to create or retrieve member profile");
    }

    // Count actual forum posts
    const forumPosts = await ctx.db
      .query("forumPosts")
      .withIndex("by_userId", (q) => q.eq("userId", targetUserId))
      .collect();

    // Count actual forum comments
    const forumComments = await ctx.db
      .query("forumComments")
      .withIndex("by_userId", (q) => q.eq("userId", targetUserId))
      .filter((q) => q.eq(q.field("isDeleted"), false))
      .collect();

    // Update the profile with correct counts
    await ctx.db.patch(profile._id, {


      updatedAt: Date.now(),
    });

    return {
      success: true,
      forumPosts: forumPosts.length,
      comments: forumComments.length,
      profileUpdated: true,
    };
  },
});

/**
 * Get top contributors by total karma/forum activity
 */
export const getTopContributors = query({
  args: {
    limit: v.optional(v.number()),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const limit = args.limit || 10;
    
    // Get all users to calculate their activity dynamically
    const allUsers = await ctx.db.query("users").collect();
    
    // Calculate activity for each user
    const usersWithActivity = await Promise.all(
      allUsers.map(async (user) => {
        // Get forum posts
        const forumPosts = await ctx.db
          .query("forumPosts")
          .withIndex("by_userId", (q) => q.eq("userId", user._id))
          .collect();
        
        // Get forum comments
        const forumComments = await ctx.db
          .query("forumComments")
          .withIndex("by_userId", (q) => q.eq("userId", user._id))
          .filter((q) => q.eq(q.field("isDeleted"), false))
          .collect();
        
        // Calculate karma from upvotes and likes
        const totalPostUpvotes = forumPosts.reduce((sum, post) => sum + (post.upvotes || 0), 0);
        
        let totalCommentLikes = 0;
        for (const comment of forumComments) {
          const votes = await ctx.db
            .query("forumCommentVotes")
            .withIndex("by_commentId", (q) => q.eq("commentId", comment._id))
            .filter((q) => q.eq(q.field("voteType"), "upvote"))
            .collect();
          totalCommentLikes += votes.length;
        }
        
        const totalKarma = totalPostUpvotes + totalCommentLikes;
        const totalActivity = forumPosts.length + forumComments.length;
        
        // Get member profile for display name
        const memberProfile = await ctx.db
          .query("memberProfiles")
          .withIndex("by_userId", (q) => q.eq("userId", user._id))
          .first();
        
        return {
          _id: user._id,
          userId: user._id,
          displayName: memberProfile?.displayName || user.name,
          name: user.name,
          profileImage: user.profileImage,
          totalKarma,
          totalForumPosts: forumPosts.length,
          totalComments: forumComments.length,
          totalActivity,
          lastActiveAt: Math.max(
            user.lastLoginAt || 0,
            user.updatedAt || 0,
            forumPosts.length > 0 ? Math.max(...forumPosts.map(p => p._creationTime)) : 0,
            forumComments.length > 0 ? Math.max(...forumComments.map(c => c._creationTime)) : 0
          ),
          isTopContributor: totalKarma > 100,
          isPremium: user.subscriptionStatus === "active" && user.subscriptionPlan === "premium",
        };
      })
    );
    
    // Filter out inactive users and sort by activity
    const activeContributors = usersWithActivity
      .filter(user => user.totalActivity > 0)
      .sort((a, b) => {
        // Sort by total karma first, then by total activity
        if (b.totalKarma !== a.totalKarma) {
          return b.totalKarma - a.totalKarma;
        }
        return b.totalActivity - a.totalActivity;
      })
      .slice(0, limit);
    
    return activeContributors;
  },
});

/**
 * Get community stats (member count, online count, etc.)
 */
export const getCommunityStats = query({
  args: {},
  returns: v.any(),
  handler: async (ctx, args) => {
    // Get total member count
    const totalUsers = await ctx.db
      .query("users")
      .collect();
    
    // Get members with profiles (active members)
    const activeMembers = await ctx.db
      .query("memberProfiles")
      .withIndex("by_isPublic", (q) => q.eq("isPublic", true))
      .collect();
    
    // Get recently active members (last 24 hours)
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    const recentlyActive = activeMembers.filter(
      profile => Date.now() > oneDayAgo
    );
    
    // Get total forum posts and comments
    const forumPosts = await ctx.db
      .query("forumPosts")
      .collect();
    
    const forumComments = await ctx.db
      .query("forumComments")
      .filter((q) => q.eq(q.field("isDeleted"), false))
      .collect();
    
    return {
      totalMembers: totalUsers.length,
      activeMembers: activeMembers.length,
      onlineMembers: recentlyActive.length,
      totalPosts: forumPosts.length,
      totalComments: forumComments.length,
    };
  },
});

/**
 * Create a clean member profile for a user (without calculated fields)
 */
export const createCleanMemberProfile = mutation({
  args: {
    userId: v.id("users"),
    displayName: v.optional(v.string()),
    bio: v.optional(v.string()),
    company: v.optional(v.string()),
    website: v.optional(v.string()),
    location: v.optional(v.string()),
    specialty: v.optional(v.array(v.string())),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) throw new ConvexError("User not found");

    // Check if profile already exists
    const existingProfile = await ctx.db
      .query("memberProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .first();
    
    if (existingProfile) {
      throw new ConvexError("Member profile already exists");
    }
    
    // Create clean member profile (without calculated fields)
    const profileId = await ctx.db.insert("memberProfiles", {
      userId: args.userId,
      displayName: args.displayName || user.name,
      bio: args.bio || "",
      company: args.company || "",
      website: args.website || "",
      location: args.location || "",
      specialty: args.specialty || [],
      yearsExperience: 0,
      badges: [],
      memberSince: Date.now(),
      // Initialize karma fields
      postKarma: 0,
      commentKarma: 0,
      awarderKarma: 0,
      awardeeKarma: 0,
      totalKarma: 0,
      isPublic: true,
      showEmail: false,
      showPhone: false,
      allowMessages: true,
      updatedAt: Date.now(),
    });
    
    return await ctx.db.get(profileId);
  },
});

/**
 * Get real members data for table display (no seed data)
 */
export const getRealMembers = query({
  args: {
    searchTerm: v.optional(v.string()),
    userType: v.optional(v.union(v.literal("consumer"), v.literal("seller"), v.literal("admin"))),
    subscriptionStatus: v.optional(v.union(v.literal("active"), v.literal("trial"), v.literal("inactive"))),
    sortBy: v.optional(v.union(
      v.literal("name"),
      v.literal("joinDate"),
      v.literal("lastActive"),
      v.literal("userType"),
      v.literal("subscriptionStatus")
    )),
    paginationOpts: paginationOptsValidator,
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    // Get all users
    let allUsers = await ctx.db.query("users").collect();
    
    // Filter out blocked users
    let filteredUsers = [];
    for (const user of allUsers) {
      // Skip if it's the current user
      if (user._id === currentUser._id) continue;
      
      // Check if current user is blocked by this user
      const isBlockedByUser = await ctx.db
        .query("userBlocks")
        .withIndex("by_blocker_blocked", (q) => 
          q.eq("blockerId", user._id).eq("blockedUserId", currentUser._id)
        )
        .filter((q) => q.eq(q.field("isActive"), true))
        .first();

      // Check if current user has blocked this user
      const hasBlockedUser = await ctx.db
        .query("userBlocks")
        .withIndex("by_blocker_blocked", (q) => 
          q.eq("blockerId", currentUser._id).eq("blockedUserId", user._id)
        )
        .filter((q) => q.eq(q.field("isActive"), true))
        .first();

      // Only include users where neither has blocked the other
      if (!isBlockedByUser && !hasBlockedUser) {
        filteredUsers.push(user);
      }
    }
    
    // Filter by search term
    if (args.searchTerm) {
      const searchLower = args.searchTerm.toLowerCase();
      filteredUsers = filteredUsers.filter(user => 
        user.name?.toLowerCase().includes(searchLower) ||
        user.email?.toLowerCase().includes(searchLower)
      );
    }
    
    // Filter by user type
    if (args.userType) {
      filteredUsers = filteredUsers.filter(user => user.userType === args.userType);
    }
    
    // Filter by subscription status
    if (args.subscriptionStatus) {
      filteredUsers = filteredUsers.filter(user => user.subscriptionStatus === args.subscriptionStatus);
    }
    
    // Sort the results
    const sortBy = args.sortBy || "joinDate";
    filteredUsers.sort((a, b) => {
      switch (sortBy) {
        case "name":
          return (a.name || "").localeCompare(b.name || "");
        case "joinDate":
          return b._creationTime - a._creationTime; // Newest first
        case "lastActive":
          return (b.lastLoginAt || b.updatedAt || 0) - (a.lastLoginAt || a.updatedAt || 0);
        case "userType":
          return (a.userType || "").localeCompare(b.userType || "");
        case "subscriptionStatus":
          return (a.subscriptionStatus || "").localeCompare(b.subscriptionStatus || "");
        default:
          return b._creationTime - a._creationTime;
      }
    });
    
    // Manual pagination
    const numItems = args.paginationOpts.numItems;
    const cursor = args.paginationOpts.cursor;
    let startIndex = 0;
    
    if (cursor) {
      startIndex = parseInt(cursor, 10);
    }
    
    const endIndex = startIndex + numItems;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
    const isDone = endIndex >= filteredUsers.length;
    const continueCursor = isDone ? null : endIndex.toString();
    
            // Get additional data for each user
        const membersWithDetails = await Promise.all(
          paginatedUsers.map(async (user) => {
            // Get member profile if it exists
            const memberProfile = await ctx.db
              .query("memberProfiles")
              .withIndex("by_userId", (q) => q.eq("userId", user._id))
              .first();
            
            // Get seller profile if it exists
            const sellerProfile = await ctx.db
              .query("sellerProfiles")
              .withIndex("by_userId", (q) => q.eq("userId", user._id))
              .first();
            
            // Count active products for sellers
            let activeProductsCount = 0;
            if (user.userType === "seller") {
              const activeProducts = await ctx.db
                .query("products")
                .withIndex("by_sellerId", (q) => q.eq("sellerId", user._id))
                .filter((q) => q.eq(q.field("status"), "active"))
                .collect();
              activeProductsCount = activeProducts.length;
            }
            
            // Count orders for spending calculation
            const orders = await ctx.db
              .query("orders")
              .withIndex("by_buyerId", (q) => q.eq("buyerId", user._id))
              .filter((q) => q.eq(q.field("orderStatus"), "delivered"))
              .collect();
            
            const totalSpent = orders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);
            
            // Calculate forum activity dynamically
            const forumPosts = await ctx.db
              .query("forumPosts")
              .withIndex("by_userId", (q) => q.eq("userId", user._id))
              .collect();
            
            const forumComments = await ctx.db
              .query("forumComments")
              .withIndex("by_userId", (q) => q.eq("userId", user._id))
              .filter((q) => q.eq(q.field("isDeleted"), false))
              .collect();
            
            // Calculate helpful votes from forum post upvotes and comment likes
            const totalPostUpvotes = forumPosts.reduce((sum, post) => sum + (post.upvotes || 0), 0);
            
            // Calculate comment likes (helpful votes) from upvotes
            let totalCommentLikes = 0;
            for (const comment of forumComments) {
              const votes = await ctx.db
                .query("forumCommentVotes")
                .withIndex("by_commentId", (q) => q.eq("commentId", comment._id))
                .filter((q) => q.eq(q.field("voteType"), "upvote"))
                .collect();
              totalCommentLikes += votes.length;
            }
            
            // Calculate karma (post karma + comment karma)
            const postKarma = totalPostUpvotes;
            const commentKarma = totalCommentLikes;
            const totalKarma = postKarma + commentKarma;
            
            // Determine last active time from various sources
            const lastActiveTimestamps = [
              user.lastLoginAt || 0,
              user.updatedAt || 0,
              forumPosts.length > 0 ? Math.max(...forumPosts.map(p => p._creationTime)) : 0,
              forumComments.length > 0 ? Math.max(...forumComments.map(c => c._creationTime)) : 0,
            ].filter(Boolean);
            
            const lastActiveAt = lastActiveTimestamps.length > 0 ? Math.max(...lastActiveTimestamps) : user._creationTime;
            
            return {
              _id: user._id,
              _creationTime: user._creationTime,
              name: user.name,
              email: user.email,
              userType: user.userType,
              subscriptionStatus: user.subscriptionStatus,
              subscriptionPlan: user.subscriptionPlan,
              subscriptionExpiresAt: user.subscriptionExpiresAt,
              profileImage: user.profileImage,
              isVerified: user.isVerified,
              lastLoginAt: user.lastLoginAt,
              updatedAt: user.updatedAt,
              // Additional computed fields from profile
              displayName: memberProfile?.displayName || user.name,
              location: memberProfile?.location,
              company: memberProfile?.company || (sellerProfile?.businessName),
              bio: memberProfile?.bio,
              // Dynamically calculated activity metrics
              totalForumPosts: forumPosts.length,
              totalComments: forumComments.length,
              helpfulVotes: totalCommentLikes,
              postKarma,
              commentKarma,
              totalKarma,
              // Other calculated fields
              activeProductsCount,
              totalSpent,
              lastActiveAt,
              memberSince: memberProfile?.memberSince || user._creationTime,
              isPublicProfile: memberProfile?.isPublic || false,
              // Derived status indicators
              isTopContributor: totalKarma > 100, // Dynamic calculation
              isPremium: user.subscriptionStatus === "active" && user.subscriptionPlan === "premium",
            };
          })
        );
    
    return {
      page: membersWithDetails,
      isDone,
      continueCursor,
      totalCount: allUsers.length,
    };
  },
});

// Note: Forum functions have been moved to forum.ts for better organization
// The functions below are re-exports for backward compatibility

/**
 * @deprecated Use forum.createForumPost instead
 */
export { createForumPost } from "./forum";

/**
 * @deprecated Use forum.getForumPosts instead
 */
export { getForumPosts } from "./forum";

/**
 * @deprecated Use forum.addForumComment instead
 */
export { addForumComment } from "./forum";

// ============================================
// MEMBER REFERENCES FUNCTIONS
// ============================================

/**
 * Add a reference for another member
 */
export const addMemberReference = mutation({
  args: {
    toUserId: v.id("users"),
    content: v.string(),
    relationshipType: v.union(
      v.literal("buyer"),
      v.literal("seller"),
      v.literal("business_partner"),
      v.literal("colleague"),
      v.literal("other")
    ),
    transactionCount: v.optional(v.number()),
    yearsKnown: v.optional(v.number()),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);

    if (currentUser._id === args.toUserId) {
      throw new ConvexError("You cannot add a reference for yourself");
    }

    // Check if reference already exists
    const existingReference = await ctx.db
      .query("memberReferences")
      .withIndex("by_fromUserId", (q) => q.eq("fromUserId", currentUser._id))
      .filter((q) => q.eq(q.field("toUserId"), args.toUserId))
      .first();

    if (existingReference) {
      throw new ConvexError("You have already added a reference for this member");
    }

    const referenceId = await ctx.db.insert("memberReferences", {
      fromUserId: currentUser._id,
      toUserId: args.toUserId,
      content: args.content,
      relationshipType: args.relationshipType,
      transactionCount: args.transactionCount,
      yearsKnown: args.yearsKnown,
      isVerified: false,
      isVisible: true,
      updatedAt: Date.now(),
    });

    return await ctx.db.get(referenceId);
  },
});

/**
 * Get references for a member
 */
export const getMemberReferences = query({
  args: {
    userId: v.id("users"),
    paginationOpts: paginationOptsValidator,
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const results = await ctx.db
      .query("memberReferences")
      .withIndex("by_toUserId", (q) => q.eq("toUserId", args.userId))
      .filter((q) => q.eq(q.field("isVisible"), true))
      .order("desc")
      .paginate(args.paginationOpts);

    // Fetch user details for each reference
    const referencesWithDetails = await Promise.all(
      results.page.map(async (reference) => {
        const fromUser = await ctx.db.get(reference.fromUserId);
        const fromMemberProfile = await ctx.db
          .query("memberProfiles")
          .withIndex("by_userId", (q) => q.eq("userId", reference.fromUserId))
          .first();
        
        return {
          ...reference,
          fromUser,
          fromMemberProfile,
        };
      })
    );

    return {
      ...results,
      page: referencesWithDetails,
    };
  },
});

// ============================================
// MEMBER DIRECTORY FUNCTIONS
// ============================================

/**
 * Search and list members in the directory
 */
export const searchMembers = query({
  args: {
    searchTerm: v.optional(v.string()),
    specialty: v.optional(v.string()),
    sortBy: v.optional(v.union(
      v.literal("name"),
      v.literal("joinDate"),
      v.literal("activity"),
      v.literal("helpfulVotes")
    )),
    paginationOpts: paginationOptsValidator,
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    // Fetch all public member profiles
    const allProfiles = await ctx.db
      .query("memberProfiles")
      .withIndex("by_isPublic", (q) => q.eq("isPublic", true))
      .collect();

    // Filter by specialty and search term
    let filteredResults = allProfiles;
    
    if (args.specialty) {
      filteredResults = filteredResults.filter(profile => 
        profile.specialty.includes(args.specialty!)
      );
    }
    
    if (args.searchTerm) {
      const searchLower = args.searchTerm.toLowerCase();
      filteredResults = filteredResults.filter(profile => 
        profile.displayName?.toLowerCase().includes(searchLower) ||
        profile.company?.toLowerCase().includes(searchLower) ||
        profile.bio?.toLowerCase().includes(searchLower)
      );
    }

    // Sort the results based on sortBy parameter
    if (args.sortBy === "activity") {
      filteredResults.sort((a, b) => b.updatedAt - a.updatedAt);
    } else if (args.sortBy === "helpfulVotes") {
      filteredResults.sort((a, b) => b.updatedAt - a.updatedAt); // Fallback to updated time
    } else if (args.sortBy === "joinDate") {
      filteredResults.sort((a, b) => b.memberSince - a.memberSince);
    } else if (args.sortBy === "name") {
      filteredResults.sort((a, b) => 
        (a.displayName || "").localeCompare(b.displayName || "")
      );
    } else {
      // Default sort by member since
      filteredResults.sort((a, b) => b.memberSince - a.memberSince);
    }

    // Manual pagination
    const numItems = args.paginationOpts.numItems;
    const cursor = args.paginationOpts.cursor;
    let startIndex = 0;
    
    if (cursor) {
      // Parse cursor to get the start index
      startIndex = parseInt(cursor, 10);
    }
    
    const endIndex = startIndex + numItems;
    const paginatedResults = filteredResults.slice(startIndex, endIndex);
    const isDone = endIndex >= filteredResults.length;
    const continueCursor = isDone ? null : endIndex.toString();

    // Fetch user details for each member
    const membersWithDetails = await Promise.all(
      paginatedResults.map(async (profile) => {
        const user = await ctx.db.get(profile.userId);
        
        // Get seller info if exists
        const sellerProfile = await ctx.db
          .query("sellerProfiles")
          .withIndex("by_userId", (q) => q.eq("userId", profile.userId))
          .first();

        // Get active listings count
        const activeListings = await ctx.db
          .query("products")
          .withIndex("by_sellerId", (q) => q.eq("sellerId", profile.userId))
          .filter((q) => q.eq(q.field("status"), "active"))
          .collect();

        return {
          ...profile,
          user,
          sellerProfile,
          activeListingsCount: activeListings.length,
        };
      })
    );

    return {
      page: membersWithDetails,
      isDone,
      continueCursor,
    };
  },
});

/**
 * Get member statistics for the directory
 */
export const getMemberStats = query({
  args: {},
  returns: v.any(),
  handler: async (ctx) => {
    const totalMembers = await ctx.db
      .query("memberProfiles")
      .collect();

    const publicMembers = await ctx.db
      .query("memberProfiles")
      .withIndex("by_isPublic", (q) => q.eq("isPublic", true))
      .collect();

    const totalForumPosts = await ctx.db
      .query("forumPosts")
      .collect();

    const totalReferences = await ctx.db
      .query("memberReferences")
      .filter((q) => q.eq(q.field("isVisible"), true))
      .collect();

    // Get member profiles for top contributors calculation
    const memberProfiles = await ctx.db
      .query("memberProfiles")
      .withIndex("by_isPublic", (q) => q.eq("isPublic", true))
      .take(20); // Get more to sort by activity

    const topContributorsWithDetails = await Promise.all(
      memberProfiles.slice(0, 5).map(async (profile) => {
        const user = await ctx.db.get(profile.userId);
        return { ...profile, user };
      })
    );

    return {
      totalMembers: totalMembers.length,
      publicMembers: publicMembers.length,
      totalForumPosts: totalForumPosts.length,
      totalReferences: totalReferences.length,
      topContributors: topContributorsWithDetails,
    };
  },
});

/**
 * Get calculated forum activity for a user (no longer updates stored fields)
 */
export const getCalculatedForumActivity = query({
  args: {
    userId: v.id("users"),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    // Count actual forum posts
    const forumPosts = await ctx.db
      .query("forumPosts")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .collect();

    // Count actual forum comments
    const forumComments = await ctx.db
      .query("forumComments")
      .withIndex("by_userId", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("isDeleted"), false))
      .collect();

    return {
      userId: args.userId,
      forumPosts: forumPosts.length,
      comments: forumComments.length,
    };
  },
});

/**
 * Get member profile (query version for reading) - with enhanced validation
 */
export const getMemberProfileForUser = query({
  args: {
    userId: v.optional(v.union(v.id("users"), v.string())), // Allow string to catch invalid IDs
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    let targetUserId = args.userId || currentUser._id;

    // Enhanced validation to catch invalid IDs
    if (typeof targetUserId === 'string') {
      // Check if it looks like a review ID or other invalid ID
      if (targetUserId.startsWith('m5') || targetUserId.length !== 32) {
        throw new ConvexError(`Invalid user ID format: ${targetUserId}. This appears to be a review ID or corrupted user ID.`);
      }
      // If it's a valid format string, try to use it as an ID
      try {
        targetUserId = targetUserId as any; // Cast to ID type
      } catch (e) {
        throw new ConvexError(`Invalid user ID: ${targetUserId}`);
      }
    }

    // Validate that we have a proper user ID
    if (!targetUserId) {
      throw new ConvexError("No user ID provided");
    }

    // Verify the user exists and is actually in the users table
    let user;
    try {
      user = await ctx.db.get(targetUserId as Id<"users">);
    } catch (e) {
      throw new ConvexError(`Failed to fetch user with ID: ${targetUserId}. This ID may be from the wrong table.`);
    }
    
    if (!user) {
      throw new ConvexError(`User not found with ID: ${targetUserId}`);
    }

    // Get existing profile
    const profile = await ctx.db
      .query("memberProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", targetUserId as Id<"users">))
      .first();

    return profile;
  },
});

/**
 * Debug function to check user authentication and ID
 */
export const debugUserAuth = query({
  args: {},
  returns: v.any(),
  handler: async (ctx) => {
    try {
      const currentUser = await requireAuth(ctx);
      return {
        success: true,
        user: {
          _id: currentUser._id,
          name: currentUser.name,
          email: currentUser.email,
          userType: currentUser.userType,
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});

/**
 * Migration: Clean up obsolete calculated fields from existing member profiles
 */
export const cleanupObsoleteProfileFields = mutation({
  args: {},
  returns: v.any(),
  handler: async (ctx) => {
    // Get all member profiles
    const profiles = await ctx.db.query("memberProfiles").collect();
    
    let updatedCount = 0;
    let skippedCount = 0;
    
    for (const profile of profiles) {
      // Check if profile has any obsolete fields
      const hasObsoleteFields = (profile as any).totalForumPosts !== undefined || 
                               (profile as any).totalComments !== undefined || 
                               (profile as any).helpfulVotes !== undefined || 
                               (profile as any).lastActiveAt !== undefined ||
                               (profile as any).postKarma !== undefined ||
                               (profile as any).commentKarma !== undefined ||
                               (profile as any).totalKarma !== undefined ||
                               (profile as any).isTopContributor !== undefined ||
                               (profile as any).isPremium !== undefined;
      
      if (hasObsoleteFields) {
        // Create clean profile data without obsolete fields
        const cleanProfileData: any = {
          userId: profile.userId,
          displayName: profile.displayName,
          bio: profile.bio,
          company: profile.company,
          website: profile.website,
          location: profile.location,
          specialty: profile.specialty,
          yearsExperience: profile.yearsExperience,
          socialLinks: profile.socialLinks,
          badges: profile.badges,
          memberSince: profile.memberSince,
          flair: profile.flair,
          isModerator: profile.isModerator,
          trophies: profile.trophies,
          isPublic: profile.isPublic,
          showEmail: profile.showEmail,
          showPhone: profile.showPhone,
          allowMessages: profile.allowMessages,
          updatedAt: Date.now(),
        };
        
        // Remove undefined fields
        Object.keys(cleanProfileData).forEach(key => {
          if (cleanProfileData[key] === undefined) {
            delete cleanProfileData[key];
          }
        });
        
        // Replace the entire document with clean data
        await ctx.db.replace(profile._id, cleanProfileData);
        updatedCount++;
      } else {
        skippedCount++;
      }
    }
    
    return {
      message: "Member profile cleanup completed",
      totalProfiles: profiles.length,
      updated: updatedCount,
      skipped: skippedCount,
    };
  },
});
