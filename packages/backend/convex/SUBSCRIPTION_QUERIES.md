# MODA Subscription Query System

This document describes the comprehensive subscription management query system for MODA, including performance optimizations, caching strategies, and analytics capabilities.

## Overview

The subscription query system provides real-time access to subscription data with intelligent caching, comprehensive analytics, and performance optimizations designed for a luxury marketplace platform.

## Core Features

### ✅ Real-Time Subscription Status
- **Individual user status** with detailed subscription information
- **Batch queries** for performance optimization
- **Feature access validation** with granular permissions
- **Expiration tracking** with grace period handling

### ✅ Advanced Analytics
- **Revenue metrics** with MRR/ARR calculations
- **Cohort analysis** for retention insights
- **Churn analysis** with reason tracking
- **Revenue forecasting** with scenario planning

### ✅ Performance Optimization
- **Intelligent caching** with configurable TTL
- **Batch operations** for bulk data access
- **Index optimization** for fast queries
- **Real-time health monitoring**

## Query Reference

### 1. getUserSubscriptionStatus

Get detailed subscription status for a user with comprehensive metadata.

```typescript
const status = await ctx.runQuery(api.subscriptionQueries.getUserSubscriptionStatus, {
  userId: "user_id", // Optional - defaults to current user
});
```

**Returns:**
- Subscription status and plan details
- Expiration dates and remaining time
- Feature access and limits
- Subscription history
- Upgrade/downgrade options
- Grace period information

**Caching:** 1-minute cache per user for performance

### 2. validateSubscriptionAccess

Validate user access to marketplace features with detailed permissions.

```typescript
const access = await ctx.runQuery(api.subscriptionQueries.validateSubscriptionAccess, {
  feature: "advancedAnalytics", // Optional specific feature
  userId: "user_id", // Optional for admin use
});
```

**Returns:**
- General marketplace access
- Feature-specific permissions
- Seller verification status
- Upgrade requirements
- Grace period handling

**Use Cases:**
- Feature gating in UI
- API access control
- Seller verification checks
- Premium feature validation

### 3. getSubscriptionMetrics (Admin Only)

Comprehensive subscription analytics for business intelligence.

```typescript
const metrics = await ctx.runQuery(api.subscriptionQueries.getSubscriptionMetrics, {
  timeRange: "30d", // "7d" | "30d" | "90d" | "1y"
  includeChurnAnalysis: true,
});
```

**Returns:**
- User and subscription counts
- Revenue metrics (MRR/ARR)
- Plan distribution
- Growth and churn analysis
- Conversion rates
- Expiration forecasts

**Caching:** 5-minute cache for dashboard performance

### 4. getExpiringSubscriptions (Admin Only)

Identify users with expiring subscriptions for renewal campaigns.

```typescript
const expiring = await ctx.runQuery(api.subscriptionQueries.getExpiringSubscriptions, {
  daysAhead: 30, // Days to look ahead
  includeGracePeriod: true,
  planFilter: "premium", // Optional plan filter
  limit: 100,
});
```

**Returns:**
- Users with expiring subscriptions
- Urgency levels (critical/high/medium/low)
- Seller information and activity
- Renewal reminder flags
- Revenue impact analysis

**Use Cases:**
- Automated renewal campaigns
- Customer success outreach
- Revenue retention planning
- Support prioritization

## Advanced Analytics

### Cohort Analysis

Track user retention across signup cohorts:

```typescript
const cohorts = await ctx.runQuery(api.subscriptionAnalytics.getSubscriptionCohortAnalysis, {
  cohortType: "monthly", // "monthly" | "weekly"
  periodsBack: 12,
});
```

### Revenue Forecasting

Project future revenue with scenario planning:

```typescript
const forecast = await ctx.runQuery(api.subscriptionAnalytics.getSubscriptionRevenueForecast, {
  forecastMonths: 12,
  includeScenarios: true, // Conservative/optimistic scenarios
});
```

### Churn Analysis

Analyze subscription cancellations with reasons:

```typescript
const churn = await ctx.runQuery(api.subscriptionAnalytics.getSubscriptionChurnAnalysis, {
  timeRange: "90d",
});
```

## Performance Optimizations

### Caching Strategy

| Query Type | Cache Duration | Use Case |
|------------|----------------|----------|
| User Status | 1 minute | Feature access checks |
| Dashboard Metrics | 5 minutes | Admin dashboards |
| Analytics Reports | 1 hour | Business intelligence |
| Expiring Subscriptions | 15 minutes | Renewal campaigns |

### Batch Operations

For performance-critical operations:

```typescript
// Batch subscription status for multiple users
const batchStatus = await ctx.runQuery(api.subscriptionQueries.getBatchSubscriptionStatus, {
  userIds: ["user1", "user2", "user3"], // Max 100 users
});
```

### Health Monitoring

Real-time subscription health scoring:

```typescript
const health = await ctx.runQuery(api.subscriptionQueries.getSubscriptionHealthScore);
// Returns health score (0-100) with recommendations
```

## Subscription Features & Limits

### Plan Comparison

| Feature | Basic | Premium | Enterprise |
|---------|-------|---------|------------|
| Max Products | 50 | 200 | Unlimited |
| Max Images | 5 | 10 | 20 |
| File Size Limit | 5MB | 10MB | 50MB |
| Premium Support | ❌ | ✅ | ✅ |
| Advanced Analytics | ❌ | ✅ | ✅ |
| API Access | ❌ | ❌ | ✅ |
| Custom Branding | ❌ | ❌ | ✅ |

### Feature Access Validation

```typescript
// Check specific feature access
const features = getSubscriptionFeatures(user.subscriptionPlan);
const limits = getSubscriptionLimits(user.subscriptionPlan);

if (features.advancedAnalytics) {
  // Show advanced analytics
}

if (userProductCount >= limits.maxProducts) {
  // Show upgrade prompt
}
```

## Error Handling

### Common Error Scenarios

```typescript
try {
  const metrics = await ctx.runQuery(api.subscriptionQueries.getSubscriptionMetrics);
} catch (error) {
  switch (error.message) {
    case "Unauthorized: Only admins can view subscription metrics":
      // Handle authorization error
      break;
    case "User not found":
      // Handle missing user
      break;
    case "Maximum 100 users per batch query":
      // Handle batch limit
      break;
  }
}
```

### Graceful Degradation

```typescript
// Fallback for cached data
const metrics = await ctx.runQuery(api.subscriptionQueries.getCachedSubscriptionMetrics);

if (metrics.cached && metrics.cacheAge > (10 * 60 * 1000)) {
  // Data is older than 10 minutes, show refresh option
  showRefreshButton();
}
```

## Integration Examples

### React Component Usage

```typescript
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";

function SubscriptionStatus() {
  const status = useQuery(api.subscriptionQueries.getUserSubscriptionStatus);
  
  if (!status) return <Loading />;
  
  return (
    <div>
      <h3>Subscription: {status.subscriptionPlan}</h3>
      <p>Status: {status.isActive ? "Active" : "Inactive"}</p>
      {status.isExpiringSoon && (
        <Alert>Your subscription expires in {status.daysRemaining} days</Alert>
      )}
    </div>
  );
}
```

### Feature Gating

```typescript
function PremiumFeature() {
  const access = useQuery(api.subscriptionQueries.validateSubscriptionAccess, {
    feature: "advancedAnalytics"
  });
  
  if (!access?.featureAccess?.hasAccess) {
    return <UpgradePrompt feature="Advanced Analytics" />;
  }
  
  return <AdvancedAnalyticsDashboard />;
}
```

### Admin Dashboard

```typescript
function AdminDashboard() {
  const metrics = useQuery(api.subscriptionQueries.getCachedSubscriptionMetrics);
  const health = useQuery(api.subscriptionQueries.getSubscriptionHealthScore);
  
  return (
    <div>
      <HealthScore score={health?.healthScore} />
      <RevenueMetrics mrr={metrics?.revenue?.monthlyRecurringRevenue} />
      <ChurnAnalysis rate={metrics?.churnAnalysis?.churnRate} />
    </div>
  );
}
```

## Best Practices

### 1. Use Appropriate Caching

```typescript
// For real-time features, use direct queries
const userAccess = await ctx.runQuery(api.subscriptionQueries.validateSubscriptionAccess);

// For dashboards, use cached queries
const dashboardMetrics = await ctx.runQuery(api.subscriptionQueries.getCachedSubscriptionMetrics);
```

### 2. Batch Operations for Performance

```typescript
// Instead of multiple individual queries
const userIds = ["user1", "user2", "user3"];
const statuses = await ctx.runQuery(api.subscriptionQueries.getBatchSubscriptionStatus, {
  userIds
});
```

### 3. Handle Grace Periods

```typescript
if (status.isInGracePeriod) {
  // Show grace period warning
  showGracePeriodNotice(status.gracePeriodEnds);
} else if (status.isExpiringSoon) {
  // Show renewal prompt
  showRenewalPrompt(status.daysRemaining);
}
```

### 4. Monitor Subscription Health

```typescript
// Regular health checks for proactive management
const health = await ctx.runQuery(api.subscriptionQueries.getSubscriptionHealthScore);

if (health.healthScore < 60) {
  // Alert admin team
  sendHealthAlert(health.recommendations);
}
```

## File Structure

```
packages/backend/convex/
├── subscriptionQueries.ts         # Core subscription queries
├── subscriptionAnalytics.ts       # Advanced analytics queries
├── subscriptionQueries.test.ts    # Usage examples and tests
└── SUBSCRIPTION_QUERIES.md        # This documentation
```

## Monitoring and Alerts

### Key Metrics to Monitor

1. **Health Score**: Overall subscription system health (target: >70)
2. **Churn Rate**: Monthly subscription cancellation rate (target: <5%)
3. **Conversion Rate**: Trial to paid conversion (target: >60%)
4. **MRR Growth**: Monthly recurring revenue growth (target: >5%)

### Automated Alerts

- Health score drops below 60
- Churn rate exceeds 8%
- Large number of expiring subscriptions
- Significant revenue impact from cancellations

The subscription query system provides comprehensive insights into subscription health while maintaining high performance through intelligent caching and optimization strategies.
