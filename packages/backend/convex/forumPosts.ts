import { v } from "convex/values";
import { query } from "./_generated/server";
import type { Id } from "./_generated/dataModel";

/**
 * Get posts by group with sorting options
 */
export const getPostsByGroup = query({
  args: {
    groupId: v.union(v.id("forumGroups"), v.null()),
    sortBy: v.optional(v.union(v.literal("hot"), v.literal("new"), v.literal("top"))),
    limit: v.optional(v.number()),
  },
  returns: v.array(v.any()),
  handler: async (ctx, args) => {
    if (!args.groupId) {
      return [];
    }

    const sortBy = args.sortBy || "hot";
    const limit = args.limit || 20;
    
    let posts;
    
    if (sortBy === "new") {
      posts = await ctx.db
        .query("forumPosts")
        .withIndex("by_groupId", (q) => q.eq("groupId", args.groupId as Id<"forumGroups">))
        .order("desc")
        .take(limit);
    } else if (sortBy === "top") {
      posts = await ctx.db
        .query("forumPosts")
        .withIndex("by_score")
        .filter((q) => q.eq(q.field("groupId"), args.groupId))
        .order("desc")
        .take(limit);
    } else {
      // "hot" - sort by lastActivityAt (most recently active)
      posts = await ctx.db
        .query("forumPosts")
        .withIndex("by_lastActivityAt")
        .filter((q) => q.eq(q.field("groupId"), args.groupId))
        .order("desc")
        .take(limit);
    }

    // Enrich posts with author information
    const enrichedPosts = await Promise.all(
      posts.map(async (post) => {
        const author = await ctx.db.get(post.userId);
        return {
          ...post,
          authorName: author?.name || "Unknown User",
          authorImage: author?.profileImage,
        };
      })
    );

    return enrichedPosts;
  },
});
