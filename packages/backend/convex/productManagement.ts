import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { ConvexError } from "convex/values";
import { 
  getAuthUser, 
  requireAuth, 
  requireRole 
} from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";
import { 
  productBrandAggregate, 
  productCategoryAggregate, 
  productConditionAggregate, 
  productPriceAggregate 
} from "./productAggregates";
import { internal } from "./_generated/api";

/**
 * Create a new product (seller only)
 */
export const createProduct = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    price: v.number(),
    marketplaceType: v.optional(v.union(
      v.literal("moda-watch"), // Moda Watch Club
      v.literal("real-watch-buyers"), // Real Watch Buyers
      v.literal("moda-car"), // Moda Car Club
      v.literal("moda-lifestyle"), // Moda Lifestyle Club
      v.literal("moda-misc") // Moda Misc Club
    )),
    categoryId: v.id("categories"),
    condition: v.union(
      v.literal("new"),
      v.literal("like_new"),
      v.literal("good"),
      v.literal("fair")
    ),
    brand: v.optional(v.string()),
    sku: v.optional(v.string()),
    size: v.optional(v.string()),
    color: v.optional(v.string()),
    material: v.optional(v.string()),
    yearPurchased: v.optional(v.number()),
    originalPrice: v.optional(v.number()),
    tags: v.optional(v.array(v.string())),
    images: v.array(v.id("_storage")),
    shippingWeight: v.optional(v.number()),
    dimensions: v.optional(v.object({
      length: v.number(),
      width: v.number(),
      height: v.number(),
    })),
    isAuthentic: v.boolean(),
    authenticityProof: v.optional(v.string()),
    privateNotes: v.optional(v.string()),
    ownershipType: v.optional(v.union(
      v.literal("owned"),
      v.literal("consigned")
    )),
    consignmentInfo: v.optional(v.object({
      consignorId: v.optional(v.id("users")),
      consignorName: v.optional(v.string()),
      consignorEmail: v.optional(v.string()),
      consignorPhone: v.optional(v.string()),
      commissionRate: v.optional(v.number()),
      agreementDate: v.optional(v.number()),
      agreementDuration: v.optional(v.number()),
      minimumPrice: v.optional(v.number()),
      specialTerms: v.optional(v.string()),
    })),
    sourceInfo: v.optional(v.object({
      source: v.string(),
      costPaid: v.optional(v.number()),
      paymentMethod: v.optional(v.string()),
      purchaseDate: v.optional(v.number()),
      receipt: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Verify user is a seller
    if (user.userType !== "seller") {
      throw new ConvexError("Only sellers can create products");
    }

    // Get seller profile
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .first();

    if (!sellerProfile) {
      throw new ConvexError("Seller profile not found");
    }

    // Check if seller is approved and active
    if (sellerProfile.verificationStatus !== "approved") {
      throw new ConvexError("Seller must be approved to create products");
    }

    if (!sellerProfile.isActive) {
      throw new ConvexError("Seller account is not active");
    }

    // Check subscription status - allow both active and trial users
    const hasValidSubscription = (user.subscriptionStatus === "active" || user.subscriptionStatus === "trial") && 
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

    if (!hasValidSubscription) {
      throw new ConvexError("Active or trial subscription required to create products");
    }

    // Validate required fields
    if (args.title.trim().length < 3) {
      throw new ConvexError("Title must be at least 3 characters long");
    }

    if (args.description.trim().length < 10) {
      throw new ConvexError("Description must be at least 10 characters long");
    }

    if (args.price <= 0) {
      throw new ConvexError("Price must be greater than 0");
    }

    if (args.images.length === 0) {
      throw new ConvexError("At least one image is required");
    }

    if (args.images.length > 10) {
      throw new ConvexError("Maximum 10 images allowed");
    }

    // Verify category exists and get the category slug
    const categoryDoc = await ctx.db.get(args.categoryId);
    if (!categoryDoc || !categoryDoc.isActive) {
      throw new ConvexError("Invalid or inactive category");
    }
    
    // Map category slug to schema literal
    const categorySlug = categoryDoc.slug || categoryDoc.name.toLowerCase();
    
    // Validate that the category slug matches the schema
    const validCategories = ["clothing", "sneakers", "collectibles", "accessories", "handbags", "jewelry", "watches", "sunglasses"];
    if (!validCategories.includes(categorySlug)) {
      throw new ConvexError(`Invalid category: ${categorySlug}. Must be one of: ${validCategories.join(", ")}`);
    }

    // Validate images exist in storage
    for (const imageId of args.images) {
      const imageUrl = await ctx.storage.getUrl(imageId);
      if (!imageUrl) {
        throw new ConvexError(`Image ${imageId} not found in storage`);
      }
      // Note: Size validation would need to be done client-side or during upload
    }

    // Check seller's product limits based on subscription
    const existingProducts = await ctx.db
      .query("products")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", user._id))
      .filter((q) => q.neq(q.field("status"), "archived"))
      .collect();

    const productLimits = {
      basic: 50,
      premium: 200,
      enterprise: -1, // Unlimited
    };

    // Get full user document for subscription plan
    const fullUser = await ctx.db.get(user._id);
    const limit = productLimits[fullUser?.subscriptionPlan as keyof typeof productLimits] || 50;
    if (limit !== -1 && existingProducts.length >= limit) {
      throw new ConvexError(`Product limit reached. Upgrade subscription to add more products.`);
    }

    const now = Date.now();

    // Create product
    const productId = await ctx.db.insert("products", {
      sellerId: user._id,
      title: args.title.trim(),
      description: args.description.trim(),
      price: args.price,
      marketplaceType: args.marketplaceType || "moda-lifestyle", // Default to moda-lifestyle if not specified
      category: categorySlug as any,
      condition: args.condition,
      brand: args.brand?.trim() || "",
      sku: args.sku?.trim(),
      size: args.size?.trim(),
      color: args.color?.trim(),
      material: args.material?.trim(),
      originalPrice: args.originalPrice,
      tags: args.tags?.map(tag => tag.trim().toLowerCase()) || [],
      images: args.images,
      dimensions: args.dimensions,
      ownershipType: args.ownershipType || "owned", // Default to owned
      consignmentInfo: args.consignmentInfo,
      status: "draft",
      quantity: 1,
      isDraft: true,
      views: 0,
      favorites: 0,
      updatedAt: now,
      lastSavedAt: now,
      
    });

    // TODO: Update aggregates for the new product
    // This will be implemented once we have the correct aggregate setup

    // Log product creation
    await ctx.db.insert("analytics", {
      eventType: "product_created",
      userId: user._id,
      timestamp: now,
      metadata: {
        source: productId,
        category: "product_management",
        revenue: args.price,
      },
    });

    return {
      success: true,
      productId,
      message: "Product created successfully as draft",
      status: "draft",
    };
  },
});

/**
 * Update product information (seller only)
 */
export const updateProduct = mutation({
  args: {
    productId: v.id("products"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    price: v.optional(v.number()),
    categoryId: v.optional(v.id("categories")),
    condition: v.optional(v.union(
      v.literal("new"),
      v.literal("like_new"),
      v.literal("good"),
      v.literal("fair")
    )),
    brand: v.optional(v.string()),
    sku: v.optional(v.string()),
    size: v.optional(v.string()),
    color: v.optional(v.string()),
    material: v.optional(v.string()),
    yearPurchased: v.optional(v.number()),
    originalPrice: v.optional(v.number()),
    tags: v.optional(v.array(v.string())),
    newImages: v.optional(v.array(v.id("_storage"))),
    removeImageIds: v.optional(v.array(v.id("_storage"))),
    shippingWeight: v.optional(v.number()),
    dimensions: v.optional(v.object({
      length: v.number(),
      width: v.number(),
      height: v.number(),
    })),
    isAuthentic: v.optional(v.boolean()),
    authenticityProof: v.optional(v.string()),
    privateNotes: v.optional(v.string()),
    ownershipType: v.optional(v.union(
      v.literal("owned"),
      v.literal("consigned")
    )),
    consignmentInfo: v.optional(v.object({
      consignorId: v.optional(v.id("users")),
      consignorName: v.optional(v.string()),
      consignorEmail: v.optional(v.string()),
      consignorPhone: v.optional(v.string()),
      commissionRate: v.optional(v.number()),
      agreementDate: v.optional(v.number()),
      agreementDuration: v.optional(v.number()),
      minimumPrice: v.optional(v.number()),
      specialTerms: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Get product
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new ConvexError("Product not found");
    }

    // Verify ownership
    if (product.sellerId !== user._id && user.userType !== "admin") {
      throw new ConvexError("Only the seller or admin can update this product");
    }

    // Check if product can be updated
    if (product.status === "sold") {
      throw new ConvexError("Cannot update sold products");
    }

    if (product.status === "archived") {
      throw new ConvexError("Cannot update archived products");
    }

    // Validate updates
    if (args.title && args.title.trim().length < 3) {
      throw new ConvexError("Title must be at least 3 characters long");
    }

    if (args.description && args.description.trim().length < 10) {
      throw new ConvexError("Description must be at least 10 characters long");
    }

    if (args.price && args.price <= 0) {
      throw new ConvexError("Price must be greater than 0");
    }

    // Validate category if provided
    if (args.categoryId) {
      const category = await ctx.db.get(args.categoryId);
      if (!category || !category.isActive) {
        throw new ConvexError("Invalid or inactive category");
      }
    }

    // Handle image updates
    let updatedImages = [...product.images];

    // Remove specified images
    if (args.removeImageIds) {
      updatedImages = updatedImages.filter(imageId => 
        !args.removeImageIds!.includes(imageId as Id<"_storage">)
      );
    }

    // Add new images
    if (args.newImages) {
      // Validate new images
      for (const imageId of args.newImages) {
        const imageUrl = await ctx.storage.getUrl(imageId);
        if (!imageUrl) {
          throw new ConvexError(`Image ${imageId} not found in storage`);
        }
      }

      updatedImages = [...updatedImages, ...args.newImages];
    }

    // Validate final image count
    if (updatedImages.length === 0) {
      throw new ConvexError("At least one image is required");
    }

    if (updatedImages.length > 10) {
      throw new ConvexError("Maximum 10 images allowed");
    }

    const now = Date.now();

    // Prepare update data
    const updateData: any = {
      updatedAt: now,
      images: updatedImages,
    };

    // Add provided fields
    if (args.title !== undefined) updateData.title = args.title.trim();
    if (args.description !== undefined) updateData.description = args.description.trim();
    if (args.price !== undefined) updateData.price = args.price;
    if (args.categoryId !== undefined) updateData.categoryId = args.categoryId;
    if (args.condition !== undefined) updateData.condition = args.condition;
    if (args.brand !== undefined) updateData.brand = args.brand?.trim();
    if (args.sku !== undefined) updateData.sku = args.sku?.trim();
    if (args.size !== undefined) updateData.size = args.size?.trim();
    if (args.color !== undefined) updateData.color = args.color?.trim();
    if (args.material !== undefined) updateData.material = args.material?.trim();
    if (args.yearPurchased !== undefined) updateData.year = args.yearPurchased;
    if (args.originalPrice !== undefined) updateData.originalPrice = args.originalPrice;
    if (args.tags !== undefined) updateData.tags = args.tags.map(tag => tag.trim().toLowerCase());
    if (args.shippingWeight !== undefined) updateData.weight = args.shippingWeight;
    if (args.dimensions !== undefined) updateData.dimensions = args.dimensions;
    if (args.isAuthentic !== undefined) updateData.isAuthentic = args.isAuthentic;
    if (args.authenticityProof !== undefined) updateData.authenticityProof = args.authenticityProof?.trim();
    if (args.privateNotes !== undefined) updateData.privateNotes = args.privateNotes?.trim();

    // Update product
    await ctx.db.patch(args.productId, updateData);

    // Log product update
    await ctx.db.insert("analytics", {
      eventType: "product_updated",
      userId: user._id,
      timestamp: now,
      metadata: {
        source: args.productId,
        category: "product_management",
      },
    });

    return {
      success: true,
      message: "Product updated successfully",
      updatedFields: Object.keys(updateData).filter(key => key !== 'updatedAt'),
    };
  },
});

/**
 * Publish product to marketplace (seller only)
 */
export const publishProduct = mutation({
  args: {
    productId: v.id("products"),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Get product
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new ConvexError("Product not found");
    }

    // Verify ownership
    if (product.sellerId !== user._id && user.userType !== "admin") {
      throw new ConvexError("Only the seller or admin can publish this product");
    }

    // Check current status
    if (product.status === "active") {
      throw new ConvexError("Product is already published");
    }

    if (product.status === "sold") {
      throw new ConvexError("Cannot publish sold products");
    }

    if (product.status === "archived") {
      throw new ConvexError("Cannot publish archived products");
    }

    // Validate product completeness
    const validationErrors = [];

    if (!product.title || product.title.trim().length < 3) {
      validationErrors.push("Title must be at least 3 characters long");
    }

    if (!product.description || product.description.trim().length < 10) {
      validationErrors.push("Description must be at least 10 characters long");
    }

    if (!product.price || product.price <= 0) {
      validationErrors.push("Valid price is required");
    }

    if (!product.images || product.images.length === 0) {
      validationErrors.push("At least one image is required");
    }

    if (!product.condition) {
      validationErrors.push("Product condition is required");
    }

    if (validationErrors.length > 0) {
      throw new ConvexError(`Cannot publish incomplete product: ${validationErrors.join(", ")}`);
    }

    // Check seller status
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", product.sellerId))
      .first();

    if (!sellerProfile || sellerProfile.verificationStatus !== "approved" || !sellerProfile.isActive) {
      throw new ConvexError("Seller must be approved and active to publish products");
    }

    // Check subscription status
    const seller = await ctx.db.get(product.sellerId);
    if (!seller) {
      throw new ConvexError("Seller not found");
    }

    const hasValidSubscription = (seller.subscriptionStatus === "active" || seller.subscriptionStatus === "trial") &&
      (!seller.subscriptionExpiresAt || seller.subscriptionExpiresAt > Date.now());

    if (!hasValidSubscription) {
      throw new ConvexError("Active or trial subscription required to publish products");
    }

    const now = Date.now();

    // Update product status to active
    await ctx.db.patch(args.productId, {
      status: "active",
      publishedAt: now,
      updatedAt: now,
    });

    // Log product publication
    await ctx.db.insert("analytics", {
      eventType: "product_published",
      userId: user._id,
      timestamp: now,
      metadata: {
        source: args.productId,
        category: "product_management",
        revenue: product.price,
      },
    });

    // Check for SKU subscriptions if product has a SKU
    if (product.sku) {
      await ctx.scheduler.runAfter(0, internal.skuNotifications.checkSkuSubscriptions, {
        productId: args.productId,
        sku: product.sku,
        brand: product.brand,
        category: product.category,
        price: product.price,
        condition: product.condition,
      });
    }

    // TODO: Add to search index
    // TODO: Send notification to followers

    return {
      success: true,
      message: "Product published successfully",
      status: "active",
      publishedAt: now,
    };
  },
});

/**
 * Remove product from marketplace (seller only)
 */
export const removeProduct = mutation({
  args: {
    productId: v.id("products"),
    reason: v.optional(v.string()),
    handleActiveOrders: v.optional(v.boolean()), // Admin can force removal
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Get product
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new ConvexError("Product not found");
    }

    // Verify ownership
    if (product.sellerId !== user._id && user.userType !== "admin") {
      throw new ConvexError("Only the seller or admin can remove this product");
    }

    // Check if product is already removed
    if (product.status === "archived") {
      throw new ConvexError("Product is already archived");
    }

    if (product.status === "sold") {
      throw new ConvexError("Cannot remove sold products");
    }

    // Check for active orders
    const activeOrders = await ctx.db
      .query("orders")
      .withIndex("by_productId", (q) => q.eq("productId", args.productId))
      .filter((q) => q.or(
        q.eq(q.field("orderStatus"), "pending"),
        q.eq(q.field("orderStatus"), "confirmed"),
        q.eq(q.field("orderStatus"), "shipped")
      ))
      .collect();

    if (activeOrders.length > 0 && !args.handleActiveOrders && user.userType !== "admin") {
      throw new ConvexError(`Cannot remove product with ${activeOrders.length} active orders. Contact support for assistance.`);
    }

    const now = Date.now();

    // Update product status
    await ctx.db.patch(args.productId, {
      status: "archived",
      updatedAt: now,
    });

    // Handle active orders if admin forced removal
    if (activeOrders.length > 0 && args.handleActiveOrders && user.userType === "admin") {
      for (const order of activeOrders) {
        await ctx.db.patch(order._id, {
          orderStatus: "cancelled",
          cancellationReason: "Product removed by admin",
          cancelledDate: now,
          updatedAt: now,
        });

        // Log order cancellation
        await ctx.db.insert("analytics", {
          eventType: "order_cancelled",
          userId: order.buyerId,
          timestamp: now,
          metadata: {
            source: order._id,
            category: "order_management",
          },
        });
      }
    }

    // Log product removal
    await ctx.db.insert("analytics", {
      eventType: "product_removed",
      userId: user._id,
      timestamp: now,
      metadata: {
        source: args.productId,
        category: "product_management",
        revenue: product.price,
      },
    });

    return {
      success: true,
      message: "Product removed successfully",
      status: "removed",
      activeOrdersHandled: activeOrders.length,
      removedAt: now,
    };
  },
});

/**
 * Sell product offline and remove from marketplace
 */
export const sellProductOffline = mutation({
  args: {
    productId: v.id("products"),
    buyerName: v.string(),
    buyerEmail: v.optional(v.string()),
    buyerPhone: v.optional(v.string()),
    salePrice: v.number(),
    saleDate: v.optional(v.number()),
    paymentMethod: v.union(
      v.literal("cash"),
      v.literal("check"),
      v.literal("bank_transfer"),
      v.literal("other")
    ),
    notes: v.optional(v.string()),
    generateInvoice: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Get product
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new ConvexError("Product not found");
    }

    // Verify ownership
    if (product.sellerId !== user._id) {
      throw new ConvexError("Only the seller can mark their product as sold offline");
    }

    // Check product status
    if (product.status === "sold") {
      throw new ConvexError("Product is already sold");
    }

    if (product.status === "archived") {
      throw new ConvexError("Cannot sell archived products");
    }

    // Validate sale data
    if (args.buyerName.trim().length < 2) {
      throw new ConvexError("Buyer name must be at least 2 characters long");
    }

    if (args.salePrice <= 0) {
      throw new ConvexError("Sale price must be greater than 0");
    }

    if (args.buyerEmail) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(args.buyerEmail)) {
        throw new ConvexError("Invalid email format");
      }
    }

    // Check for active orders
    const activeOrders = await ctx.db
      .query("orders")
      .withIndex("by_productId", (q) => q.eq("productId", args.productId))
      .filter((q) => q.or(
        q.eq(q.field("orderStatus"), "pending"),
        q.eq(q.field("orderStatus"), "confirmed"),
        q.eq(q.field("orderStatus"), "shipped")
      ))
      .collect();

    if (activeOrders.length > 0) {
      throw new ConvexError(`Cannot sell offline with ${activeOrders.length} active online orders. Cancel online orders first.`);
    }

    const now = Date.now();
    const saleDate = args.saleDate || now;

    // Get seller profile for commission calculation
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .first();

    const commissionRate = sellerProfile?.commissionRate || 0.1; // Default 10%
    const commissionAmount = args.salePrice * commissionRate;
    const sellerEarnings = args.salePrice - commissionAmount;

    // Create offline sale record
    const offlineSaleId = await ctx.db.insert("offlineSales", {
      sellerId: user._id,
      productId: args.productId,
      clientName: args.buyerName.trim(),
      clientEmail: args.buyerEmail?.trim() || "",
      clientPhone: args.buyerPhone?.trim(),
      clientAddress: {
        street: "",
        city: "",
        state: "",
        zipCode: "",
        country: "",
      },
      status: "paid",
      salePrice: args.salePrice,
      saleDate,
      paymentMethod: args.paymentMethod,
      notes: args.notes?.trim(),
    });

    // Update product status
    await ctx.db.patch(args.productId, {
      status: "sold",
      updatedAt: now,
    });

    // Generate invoice if requested
    let invoiceId = null;
    if (args.generateInvoice) {
      invoiceId = await ctx.db.insert("invoices", {
        sellerId: user._id,
        offlineSaleId,
        productId: args.productId,
        invoiceNumber: `INV-${Date.now()}-${offlineSaleId.slice(-6)}`,
        clientName: args.buyerName.trim(),
        clientEmail: args.buyerEmail?.trim() || "",
        clientAddress: {
          street: "",
          city: "",
          state: "",
          zipCode: "",
          country: "",
        },
        itemDescription: `Product sale - ${args.productId}`,
        salePrice: args.salePrice,
        tax: 0,
        totalAmount: args.salePrice,
        paymentMethod: args.paymentMethod,
        paymentTerms: "Paid in full",
        status: "paid",
        sentAt: saleDate,
        emailsSent: [],
        updatedAt: now,
      });
    }

    // Log offline sale
    await ctx.db.insert("analytics", {
      eventType: "product_sold_offline",
      userId: user._id,
      timestamp: now,
      metadata: {
        source: args.productId,
        category: "sales",
        revenue: args.salePrice,
      },
    });

    // Update seller profile stats
    if (sellerProfile) {
      await ctx.db.patch(sellerProfile._id, {
        totalSales: (sellerProfile.totalSales || 0) + 1,
        totalEarnings: (sellerProfile.totalEarnings || 0) + sellerEarnings,
        updatedAt: now,
      });
    }

    return {
      success: true,
      message: "Product marked as sold offline",
      offlineSaleId,
      invoiceId,
      sellerEarnings: Math.round(sellerEarnings * 100) / 100,
      commissionAmount: Math.round(commissionAmount * 100) / 100,
      soldAt: saleDate,
    };
  },
});

/**
 * Generate upload URL for product images
 */
export const generateImageUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await requireAuth(ctx);

    // Verify user is a seller
    if (user.userType !== "seller") {
      throw new ConvexError("Only sellers can upload product images");
    }

    // Generate upload URL
    return await ctx.storage.generateUploadUrl();
  },
});

/**
 * Validate uploaded image and get metadata
 */
export const validateProductImage = query({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);

    if (!user || user.userType !== "seller") {
      throw new ConvexError("Only sellers can validate product images");
    }

    // Get image URL for client-side processing
    const imageUrl = await ctx.storage.getUrl(args.storageId);

    if (!imageUrl) {
      throw new ConvexError("Image not found in storage");
    }

    return {
      storageId: args.storageId,
      url: imageUrl,
      isValid: true,
    };
  },
});

/**
 * Bulk update product status (admin only)
 */
export const bulkUpdateProductStatus = mutation({
  args: {
    productIds: v.array(v.id("products")),
    status: v.union(
      v.literal("active"),
      v.literal("draft"),
      v.literal("removed")
    ),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Only admins can bulk update
    await requireRole(ctx, ["admin"]);

    if (args.productIds.length === 0) {
      throw new ConvexError("No products specified");
    }

    if (args.productIds.length > 100) {
      throw new ConvexError("Maximum 100 products can be updated at once");
    }

    const now = Date.now();
    const results = [];

    for (const productId of args.productIds) {
      try {
        const product = await ctx.db.get(productId);
        if (!product) {
          results.push({ productId, success: false, error: "Product not found" });
          continue;
        }

        // Skip if already in target status
        if (product.status === args.status) {
          results.push({ productId, success: true, message: "Already in target status" });
          continue;
        }

        // Validate status change
        if (product.status === "sold" && args.status !== "removed") {
          results.push({ productId, success: false, error: "Cannot change status of sold products" });
          continue;
        }

        const updateData: any = {
          status: args.status,
          updatedAt: now,
        };

        if (args.status === "removed") {
          updateData.removedAt = now;
          updateData.removalReason = args.reason || "Bulk admin action";
        } else if (args.status === "active") {
          updateData.publishedAt = now;
        }

        await ctx.db.patch(productId, updateData);

        // Log the change
        await ctx.db.insert("analytics", {
          eventType: `product_bulk_${args.status}`,
          userId: product.sellerId,
          timestamp: now,
          metadata: {
            source: productId,
            category: "admin_action",
          },
        });

        results.push({ productId, success: true, message: `Status updated to ${args.status}` });
      } catch (error) {
        results.push({
          productId,
          success: false,
          error: error instanceof Error ? error.message : "Unknown error"
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return {
      success: true,
      message: `Bulk update completed: ${successCount} successful, ${failureCount} failed`,
      results,
      summary: {
        total: args.productIds.length,
        successful: successCount,
        failed: failureCount,
      },
    };
  },
});

/**
 * Duplicate product (seller only)
 */
export const duplicateProduct = mutation({
  args: {
    productId: v.id("products"),
    newTitle: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Get original product
    const originalProduct = await ctx.db.get(args.productId);
    if (!originalProduct) {
      throw new ConvexError("Product not found");
    }

    // Verify ownership
    if (originalProduct.sellerId !== user._id) {
      throw new ConvexError("Only the seller can duplicate their own products");
    }

    // Check subscription limits
    const existingProducts = await ctx.db
      .query("products")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", user._id))
      .filter((q) => q.neq(q.field("status"), "removed"))
      .collect();

    const productLimits = {
      basic: 50,
      premium: 200,
      enterprise: -1,
    };

    // Get full user document for subscription plan
    const fullUser = await ctx.db.get(user._id);
    const limit = productLimits[fullUser?.subscriptionPlan as keyof typeof productLimits] || 50;
    if (limit !== -1 && existingProducts.length >= limit) {
      throw new ConvexError("Product limit reached. Upgrade subscription to add more products.");
    }

    const now = Date.now();
    const newTitle = args.newTitle || `${originalProduct.title} (Copy)`;

    // Create duplicate product
    const duplicateId = await ctx.db.insert("products", {
      sellerId: originalProduct.sellerId,
      title: newTitle,
      description: originalProduct.description,
      price: originalProduct.price,
      category: originalProduct.category,
      condition: originalProduct.condition,
      brand: originalProduct.brand,
      size: originalProduct.size,
      color: originalProduct.color,
      material: originalProduct.material,
      originalPrice: originalProduct.originalPrice,
      tags: originalProduct.tags,
      images: originalProduct.images,
      dimensions: originalProduct.dimensions,
      status: "draft", // Always start as draft
      quantity: 1,
      isDraft: true,
      views: 0,
      favorites: 0,
      updatedAt: now,
      lastSavedAt: now,
      ownershipType: originalProduct.ownershipType,
      consignmentInfo: originalProduct.consignmentInfo,
      marketplaceType: originalProduct.marketplaceType,
    });

    // Log product duplication
    await ctx.db.insert("analytics", {
      eventType: "product_duplicated",
      userId: user._id,
      timestamp: now,
      metadata: {
        source: duplicateId,
        category: "product_management",
      },
    });

    return {
      success: true,
      productId: duplicateId,
      message: "Product duplicated successfully",
      newTitle,
    };
  },
});

/**
 * Get product management statistics for seller
 */
export const getProductStats = query({
  args: {
    sellerId: v.optional(v.id("users")),
    timeRange: v.optional(v.union(
      v.literal("7d"),
      v.literal("30d"),
      v.literal("90d"),
      v.literal("1y")
    )),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Determine target seller
    const targetSellerId = args.sellerId || user._id;

    // Authorization check
    if (args.sellerId && args.sellerId !== user._id && user.userType !== "admin") {
      throw new ConvexError("Unauthorized: Can only view own stats or admin access required");
    }

    const timeRange = args.timeRange || "30d";
    const now = Date.now();
    const timeRanges = {
      "7d": 7 * 24 * 60 * 60 * 1000,
      "30d": 30 * 24 * 60 * 60 * 1000,
      "90d": 90 * 24 * 60 * 60 * 1000,
      "1y": 365 * 24 * 60 * 60 * 1000,
    };

    const startTime = now - timeRanges[timeRange];

    // Get all products
    const allProducts = await ctx.db
      .query("products")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", targetSellerId))
      .collect();

    // Get products in time range
    const recentProducts = allProducts.filter(p => p._creationTime >= startTime);

    // Calculate statistics
    const stats = {
      total: allProducts.length,
      active: allProducts.filter(p => p.status === "active").length,
      draft: allProducts.filter(p => p.status === "draft").length,
      sold: allProducts.filter(p => p.status === "sold").length,
      archived: allProducts.filter(p => p.status === "archived").length,
      newInPeriod: recentProducts.length,
      totalViews: allProducts.reduce((sum, p) => sum + (p.views || 0), 0),
      totalFavorites: allProducts.reduce((sum, p) => sum + (p.favorites || 0), 0),
      averagePrice: allProducts.length > 0
        ? allProducts.reduce((sum, p) => sum + p.price, 0) / allProducts.length
        : 0,
      topCategories: getTopCategories(allProducts),
      recentActivity: recentProducts.slice(0, 10).map(p => ({
        productId: p._id,
        title: p.title,
        status: p.status,
        _creationTime: p._creationTime,
      })),
    };

    return {
      sellerId: targetSellerId,
      timeRange,
      generatedAt: now,
      stats: {
        ...stats,
        averagePrice: Math.round(stats.averagePrice * 100) / 100,
      },
    };
  },
});

/**
 * Helper function to get top categories
 */
function getTopCategories(products: any[]): Array<{ categoryId: string; count: number }> {
  const categoryCount = new Map<string, number>();

  products.forEach(product => {
    const categoryId = product.categoryId;
    categoryCount.set(categoryId, (categoryCount.get(categoryId) || 0) + 1);
  });

  return Array.from(categoryCount.entries())
    .map(([categoryId, count]) => ({ categoryId, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
}

/**
 * Bulk create products from CSV/Excel data (seller only)
 */
export const bulkCreateProducts = mutation({
  args: {
    products: v.array(v.object({
      title: v.string(),
      description: v.optional(v.string()),
      price: v.number(),
      marketplaceType: v.optional(v.union(
        v.literal("moda-watch"), // Moda Watch Club
        v.literal("real-watch-buyers"), // Real Watch Buyers
        v.literal("moda-car"), // Moda Car Club
        v.literal("moda-lifestyle"), // Moda Lifestyle Club
        v.literal("moda-misc") // Moda Misc Club
      )),
      category: v.union(
        v.literal("clothing"),
        v.literal("sneakers"),
        v.literal("collectibles"),
        v.literal("accessories"),
        v.literal("handbags"),
        v.literal("jewelry"),
        v.literal("watches"),
        v.literal("sunglasses"),
        v.literal("cars"),
        v.literal("art")
      ),
      brand: v.string(),
      condition: v.union(
        v.literal("new"),
        v.literal("like_new"),
        v.literal("excellent"),
        v.literal("very_good"),
        v.literal("good"),
        v.literal("fair")
      ),
      size: v.optional(v.string()),
      color: v.optional(v.string()),
      material: v.optional(v.string()),
      year: v.optional(v.number()),
      originalPrice: v.optional(v.number()),
      sku: v.optional(v.string()),
      ownershipType: v.optional(v.union(
        v.literal("owned"),
        v.literal("consigned")
      )),
      sourceInfo: v.optional(v.object({
        source: v.string(),
        costPaid: v.optional(v.number()),
        paymentMethod: v.optional(v.string()),
        purchaseDate: v.optional(v.number()),
        receipt: v.optional(v.string()),
      })),
      weight: v.optional(v.number()),
      dimensions: v.optional(v.object({
        length: v.number(),
        width: v.number(),
        height: v.number(),
      })),
      shippingCost: v.optional(v.number()),
      tags: v.optional(v.array(v.string())),
    })),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Verify user is a seller
    if (user.userType !== "seller") {
      throw new ConvexError("Only sellers can create products");
    }

    // Get seller profile
    const sellerProfile = await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .first();

    if (!sellerProfile) {
      throw new ConvexError("Seller profile not found");
    }

    // Check if seller is approved and active
    if (sellerProfile.verificationStatus !== "approved") {
      throw new ConvexError("Seller must be approved to create products");
    }

    if (!sellerProfile.isActive) {
      throw new ConvexError("Seller account is not active");
    }

    // Check subscription status and limits
    const hasValidSubscription = (user.subscriptionStatus === "active" || user.subscriptionStatus === "trial") && 
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

    if (!hasValidSubscription) {
      throw new ConvexError("Active or trial subscription required to create products");
    }

    // Get current product count
    const existingProducts = await ctx.db
      .query("products")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", user._id))
      .filter((q) => q.neq(q.field("status"), "removed"))
      .collect();

    // For now, use a default limit since subscriptionPlan is not available in AuthUser
    const defaultLimit = 50;
    if (existingProducts.length + args.products.length > defaultLimit) {
      throw new ConvexError(`Bulk upload would exceed your product limit of ${defaultLimit}. You currently have ${existingProducts.length} products.`);
    }

    const now = Date.now();
    const createdProducts: any[] = [];
    const errors: string[] = [];

    // Process each product
    for (let i = 0; i < args.products.length; i++) {
      const productData = args.products[i];
      
      // Skip if productData is undefined
      if (!productData) {
        errors.push(`Row ${i + 1}: Invalid product data`);
        continue;
      }
      
      try {
        // Validate required fields
        if (productData.title?.trim().length < 3) {
          errors.push(`Row ${i + 1}: Title must be at least 3 characters long`);
          continue;
        }

        if (productData.price <= 0) {
          errors.push(`Row ${i + 1}: Price must be greater than 0`);
          continue;
        }

        // Create product with empty images array (will be added manually later)
        const productId = await ctx.db.insert("products", {
          sellerId: user._id,
          title: productData.title.trim(),
          description: productData.description?.trim() || `${productData.brand} ${productData.title}`,
          price: productData.price,
          marketplaceType: productData.marketplaceType || "moda-lifestyle", // Default to moda-lifestyle if not specified
          category: productData.category,
          brand: productData.brand.trim(),
          condition: productData.condition,
          size: productData.size?.trim(),
          color: productData.color?.trim(),
          material: productData.material?.trim(),
          year: productData.year,
          originalPrice: productData.originalPrice,
          sku: productData.sku?.trim(),
          ownershipType: productData.ownershipType || "owned",
          sourceInfo: productData.sourceInfo,
          weight: productData.weight,
          dimensions: productData.dimensions,
          shippingCost: productData.shippingCost,
          tags: productData.tags?.map(tag => tag.trim().toLowerCase()) || [],
          images: [], // Empty array - images will be added manually
          status: "draft",
          quantity: 1,
          isDraft: true,
          views: 0,
          favorites: 0,
          updatedAt: now,
          lastSavedAt: now,
        });

        createdProducts.push({
          productId,
          title: productData.title,
          status: "draft"
        });

      } catch (error) {
        errors.push(`Row ${i + 1}: ${error}`);
      }
    }

    // Log bulk creation
    if (createdProducts.length > 0) {
      await ctx.db.insert("analytics", {
        eventType: "bulk_products_created",
        userId: user._id,
        timestamp: now,
        metadata: {
          category: "product_management",
          source: `bulk_upload_${createdProducts.length}_products`,
        },
      });
    }

    return {
      success: true,
      createdCount: createdProducts.length,
      totalCount: args.products.length,
      createdProducts,
      errors,
      message: `Successfully created ${createdProducts.length} out of ${args.products.length} products as drafts`,
    };
  },
});
