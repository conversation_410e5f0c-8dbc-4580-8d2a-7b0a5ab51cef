import { v } from "convex/values";
import { action, mutation, query, internalQuery, internalMutation } from "./_generated/server";
import { internal } from "./_generated/api";
import { ConvexError } from "convex/values";
import type { Id } from "./_generated/dataModel";
import getStripe from "./lib/stripe";
import { betterAuthComponent } from "./auth";
import { AuthUser } from "./lib/auth_utils";

// Helper function to handle Stripe rate limits with exponential backoff
async function handleStripeRateLimit<T>(
  operation: () => Promise<T>,
  userContext: string,
  operationName: string
): Promise<T> {
  const maxRetries = 3;
  let attempt = 0;

  console.log(`Starting ${operationName} for user ${userContext}, attempt ${attempt + 1}`);

  while (attempt < maxRetries) {
    try {
      const result = await operation();
      console.log(`Successfully completed ${operationName} for user ${userContext}`);
      return result;
    } catch (error: any) {
      attempt++;
      
      // Check if it's a rate limit error
      if (error.type === 'StripeRateLimitError' || error.statusCode === 429) {
        console.log(`Rate limit hit for ${operationName} - user ${userContext}, attempt ${attempt}/${maxRetries}`);
        
        if (attempt < maxRetries) {
          // Exponential backoff: wait 2^attempt seconds
          const delay = Math.pow(2, attempt) * 1000;
          console.log(`Waiting ${delay}ms before retry for ${operationName} - user ${userContext}`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        } else {
          console.error(`Max retries exceeded for rate limit for ${operationName} - user ${userContext}`);
          throw new ConvexError(`Stripe rate limit exceeded for ${operationName}. Please try again later.`);
        }
      }
      
      // For other errors, log and throw immediately
      console.error(`Error in ${operationName} for user ${userContext}:`, error);
      throw error;
    }
  }
  
  // This should never be reached, but just in case
  throw new ConvexError(`Failed to complete ${operationName} after retries`);
}

async function getCurrentUserHelper(ctx: any) {
  const userMetadata = await betterAuthComponent.getAuthUser(ctx);
  if (!userMetadata) throw new ConvexError("User not found");

  // Get user from database using the userId from auth metadata
  const user: AuthUser = await ctx.runQuery(internal.users.get, { id: userMetadata.userId as Id<"users"> });
  console.log("user", user);
  if (!user) throw new ConvexError("User not found");
  
  return user;
}

/**
 * Internal mutation to update user's Stripe customer ID
 */
export const updateUserStripeCustomer = internalMutation({
  args: {
    userId: v.id("users"),
    stripeCustomerId: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, {
      stripeCustomerId: args.stripeCustomerId,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

/**
 * Get or create Stripe customer for the authenticated user
 */
export const getOrCreateCustomer = action({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUserHelper(ctx);
    if (!user) throw new ConvexError("User not found");

    // Check if user already has a Stripe customer ID
    if (user.stripeCustomerId) {
      return {
        success: true,
        customerId: user.stripeCustomerId,
        isNew: false,
      };
    }

    // Create new Stripe customer
    try {
      const result = await handleStripeRateLimit(
        async () => {
          const customer = await getStripe().customers.create({
            email: user.email,
            name: user.name,
            metadata: {
              userId: user._id,
            },
          });

          // Store customer ID in user record using internal mutation
          await ctx.runMutation(internal.stripeCustomers.updateUserStripeCustomer, {
            userId: user._id,
            stripeCustomerId: customer.id,
          });

          return {
            success: true,
            customerId: customer.id,
            isNew: true,
          };
        },
        user._id,
        "getOrCreateCustomer"
      );

      return result;
    } catch (error) {
      console.error("Error creating Stripe customer:", error);
      throw new ConvexError("Failed to create customer account");
    }
  },
});

/**
 * Update customer information in Stripe
 */
export const updateCustomer = action({
  args: {
    name: v.optional(v.string()),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    address: v.optional(v.object({
      line1: v.string(),
      line2: v.optional(v.string()),
      city: v.string(),
      state: v.string(),
      postal_code: v.string(),
      country: v.string(),
    })),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUserHelper(ctx);
    if (!user) throw new ConvexError("User not found");
    
    if (!user.stripeCustomerId) {
      throw new ConvexError("No Stripe customer found");
    }

    try {
      const result = await handleStripeRateLimit(
        async () => {
          const updateData: any = {};
          
          if (args.name) updateData.name = args.name;
          if (args.email) updateData.email = args.email;
          if (args.phone) updateData.phone = args.phone;
          if (args.address) updateData.address = args.address;

          await getStripe().customers.update(user.stripeCustomerId!, updateData);

          return {
            success: true,
            message: "Customer information updated successfully",
          };
        },
        user._id as string,
        "updateCustomer"
      );

      return result;
    } catch (error) {
      console.error("Error updating Stripe customer:", error);
      throw new ConvexError("Failed to update customer information");
    }
  },
});

/**
 * Get customer's payment methods
 */
export const getPaymentMethods = action({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUserHelper(ctx);
    if (!user) throw new ConvexError("User not found");
    
    if (!user.stripeCustomerId) {
      return {
        success: true,
        paymentMethods: [],
      };
    }

    try {
      const result = await handleStripeRateLimit(
        async () => {
          const paymentMethods = await getStripe().paymentMethods.list({
            customer: user.stripeCustomerId,
            type: 'card',
          });

          // Get default payment method
          const customer = await getStripe().customers.retrieve(user.stripeCustomerId!);
          const defaultPaymentMethodId = typeof customer !== 'string' && 
            'invoice_settings' in customer && customer.invoice_settings?.default_payment_method;

          const formattedMethods = paymentMethods.data.map((pm: any) => ({
            id: pm.id,
            type: pm.type,
            card: pm.card,
            isDefault: pm.id === defaultPaymentMethodId,
          }));

          return {
            success: true,
            paymentMethods: formattedMethods,
          };
        },
        user._id as string,
        "getPaymentMethods"
      );

      return result;
    } catch (error) {
      console.error("Error fetching payment methods:", error);
      throw new ConvexError("Failed to fetch payment methods");
    }
  },
});

/**
 * Add a new payment method
 */
export const addPaymentMethod = action({
  args: {
    paymentMethodId: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUserHelper(ctx);
    
    if (!user) {
      throw new ConvexError("User not found");
    }
    
    if (!user.stripeCustomerId) {
      throw new ConvexError("No Stripe customer found");
    }

    try {
      const result = await handleStripeRateLimit(
        async () => {
          // Attach payment method to customer
          await getStripe().paymentMethods.attach(args.paymentMethodId, {
            customer: user.stripeCustomerId!,
          });

          return {
            success: true,
            message: "Payment method added successfully",
          };
        },
        user._id as string,
        "addPaymentMethod"
      );

      return result;
    } catch (error) {
      console.error("Error adding payment method:", error);
      throw new ConvexError("Failed to add payment method");
    }
  },
});

/**
 * Delete a payment method
 */
export const deletePaymentMethod = action({
  args: {
    paymentMethodId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const result = await handleStripeRateLimit(
        async () => {
          // Detach payment method from customer
          await getStripe().paymentMethods.detach(args.paymentMethodId);

          return {
            success: true,
            message: "Payment method removed successfully",
          };
        },
        "unknown", // We don't have user context here, but we can still retry
        "deletePaymentMethod"
      );

      return result;
    } catch (error) {
      console.error("Error deleting payment method:", error);
      throw new ConvexError("Failed to remove payment method");
    }
  },
});

/**
 * Set default payment method
 */
export const setDefaultPaymentMethod = action({
  args: {
    paymentMethodId: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUserHelper(ctx);
    
    if (!user) {
      throw new ConvexError("User not found");
    }
    
    if (!user.stripeCustomerId) {
      throw new ConvexError("No Stripe customer found");
    }

    try {
      const result = await handleStripeRateLimit(
        async () => {
          await getStripe().customers.update(user.stripeCustomerId!, {
            invoice_settings: {
              default_payment_method: args.paymentMethodId,
            },
          });

          return {
            success: true,
            message: "Default payment method updated successfully",
          };
        },
        user._id as string,
        "setDefaultPaymentMethod"
      );

      return result;
    } catch (error) {
      console.error("Error setting default payment method:", error);
      throw new ConvexError("Failed to update default payment method");
    }
  },
});

/**
 * Get customer's billing history
 */
export const getBillingHistory = action({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUserHelper(ctx);
    
    if (!user) {
      throw new ConvexError("User not found");
    }
    
    if (!user.stripeCustomerId) {
      return {
        success: true,
        invoices: [],
        subscriptions: [],
      };
    }

    try {
      const result = await handleStripeRateLimit(
        async () => {
          // Get invoices
          const invoices = await getStripe().invoices.list({
            customer: user.stripeCustomerId,
            limit: args.limit || 50,
          });

          // Get subscriptions
          const subscriptions = await getStripe().subscriptions.list({
            customer: user.stripeCustomerId,
            limit: args.limit || 50,
          });

          return {
            success: true,
            invoices: invoices.data.map((invoice: any) => ({
              id: invoice.id,
              number: invoice.number,
              amount: invoice.amount_paid / 100,
              currency: invoice.currency,
              status: invoice.status,
              created: invoice.created,
              dueDate: invoice.due_date,
              description: invoice.description,
              pdf: invoice.invoice_pdf,
            })),
            subscriptions: subscriptions.data.map((sub: any) => ({
              id: sub.id,
              status: sub.status,
              currentPeriodStart: sub.current_period_start,
              currentPeriodEnd: sub.current_period_end,
              amount: sub.items.data[0]?.price.unit_amount ? 
                sub.items.data[0].price.unit_amount / 100 : 0,
              currency: sub.currency,
              interval: sub.items.data[0]?.price.recurring?.interval,
            })),
          };
        },
        user._id as string,
        "getBillingHistory"
      );

      return result;
    } catch (error) {
      console.error("Error fetching billing history:", error);
      throw new ConvexError("Failed to fetch billing history");
    }
  },
});

/**
 * Create a payment intent for adding a payment method
 */
export const createSetupIntent = action({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUserHelper(ctx);
    
    if (!user) {
      throw new ConvexError("User not found");
    }
    
    if (!user.stripeCustomerId) {
      throw new ConvexError("No Stripe customer found");
    }

    try {
      const result = await handleStripeRateLimit(
        async () => {
          const setupIntent = await getStripe().setupIntents.create({
            customer: user.stripeCustomerId,
            payment_method_types: ['card'],
            usage: 'off_session',
          });

          return {
            success: true,
            clientSecret: setupIntent.client_secret,
            setupIntentId: setupIntent.id,
          };
        },
        user._id as string,
        "createSetupIntent"
      );

      return result;
    } catch (error) {
      console.error("Error creating setup intent:", error);
      throw new ConvexError("Failed to create setup intent");
    }
  },
});

