import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";
import { ConvexError } from "convex/values";
import { requireAuth } from "./lib/auth_utils";

/**
 * Get comments for an auction with nested replies
 */
export const getAuctionComments = query({
  args: {
    auctionId: v.id("auctions"),
    limit: v.optional(v.number()),
  },
  returns: v.array(v.object({
    _id: v.id("auctionComments"),
    _creationTime: v.number(),
    content: v.string(),
    isEdited: v.boolean(),
    editedAt: v.optional(v.number()),
    likes: v.number(),
    user: v.object({
      _id: v.id("users"),
      name: v.string(),
      avatar: v.optional(v.string()),
      isVerified: v.boolean(),
    }),
    replies: v.array(v.object({
      _id: v.id("auctionComments"),
      _creationTime: v.number(),
      content: v.string(),
      isEdited: v.boolean(),
      editedAt: v.optional(v.number()),
      likes: v.number(),
      user: v.object({
        _id: v.id("users"),
        name: v.string(),
        avatar: v.optional(v.string()),
        isVerified: v.boolean(),
      }),
    })),
  })),
  handler: async (ctx, args) => {
    const limit = args.limit || 50;

    // Get top-level comments (no parent)
    const topLevelComments = await ctx.db
      .query("auctionComments")
      .withIndex("by_auctionId", (q) => 
        q.eq("auctionId", args.auctionId)
      )
      .filter((q) => 
        q.and(
          q.eq(q.field("parentId"), undefined),
          q.neq(q.field("isDeleted"), true),
          q.neq(q.field("isHidden"), true)
        )
      )
      .order("desc")
      .take(limit);

    // Get all replies for these comments
    const commentsWithReplies = await Promise.all(
      topLevelComments.map(async (comment) => {
        // Get user info
        const user = await ctx.db.get(comment.userId);
        if (!user) throw new ConvexError("User not found");

        // Get replies
        const replies = await ctx.db
          .query("auctionComments")
          .withIndex("by_parentId", (q) => q.eq("parentId", comment._id))
          .filter((q) => 
            q.and(
              q.neq(q.field("isDeleted"), true),
              q.neq(q.field("isHidden"), true)
            )
          )
          .order("asc")
          .collect();

        // Get reply user info
        const repliesWithUsers = await Promise.all(
          replies.map(async (reply) => {
            const replyUser = await ctx.db.get(reply.userId);
            if (!replyUser) throw new ConvexError("Reply user not found");

            return {
              _id: reply._id,
              _creationTime: reply._creationTime,
              content: reply.content,
              isEdited: reply.isEdited || false,
              editedAt: reply.editedAt,
              likes: reply.likes || 0,
              user: {
                _id: replyUser._id,
                name: replyUser.name,
                avatar: replyUser.profileImage,
                isVerified: replyUser.isVerified || false,
              },
            };
          })
        );

        return {
          _id: comment._id,
          _creationTime: comment._creationTime,
          content: comment.content,
          isEdited: comment.isEdited || false,
          editedAt: comment.editedAt,
          likes: comment.likes || 0,
          user: {
            _id: user._id,
            name: user.name,
            avatar: user.profileImage,
            isVerified: user.isVerified || false,
          },
          replies: repliesWithUsers,
        };
      })
    );

    return commentsWithReplies;
  },
});

/**
 * Add a comment to an auction
 */
export const addAuctionComment = mutation({
  args: {
    auctionId: v.id("auctions"),
    content: v.string(),
    parentId: v.optional(v.id("auctionComments")),
  },
  returns: v.id("auctionComments"),
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Check subscription status
    if (user.subscriptionStatus !== "active" && user.subscriptionStatus !== "trial") {
      throw new ConvexError("Active subscription required to comment on auctions");
    }

    // Validate auction exists
    const auction = await ctx.db.get(args.auctionId);
    if (!auction) {
      throw new ConvexError("Auction not found");
    }

    // Validate content
    if (!args.content.trim() || args.content.length > 1000) {
      throw new ConvexError("Comment must be between 1 and 1000 characters");
    }

    // If replying to a comment, validate parent exists
    if (args.parentId) {
      const parentComment = await ctx.db.get(args.parentId);
      if (!parentComment || parentComment.auctionId !== args.auctionId) {
        throw new ConvexError("Parent comment not found or invalid");
      }
    }

    // Create comment
    const commentId = await ctx.db.insert("auctionComments", {
      auctionId: args.auctionId,
      userId: user._id,
      content: args.content.trim(),
      parentId: args.parentId,
      likes: 0,
      isEdited: false,
      isDeleted: false,
      isReported: false,
      reportCount: 0,
      isHidden: false,
    });

    return commentId;
  },
});

/**
 * Edit a comment
 */
export const editAuctionComment = mutation({
  args: {
    commentId: v.id("auctionComments"),
    content: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new ConvexError("Comment not found");
    }

    // Only comment owner can edit
    if (comment.userId !== user._id) {
      throw new ConvexError("You can only edit your own comments");
    }

    // Can't edit deleted comments
    if (comment.isDeleted) {
      throw new ConvexError("Cannot edit deleted comments");
    }

    // Validate content
    if (!args.content.trim() || args.content.length > 1000) {
      throw new ConvexError("Comment must be between 1 and 1000 characters");
    }

    await ctx.db.patch(args.commentId, {
      content: args.content.trim(),
      isEdited: true,
      editedAt: Date.now(),
    });

    return null;
  },
});

/**
 * Delete a comment
 */
export const deleteAuctionComment = mutation({
  args: {
    commentId: v.id("auctionComments"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new ConvexError("Comment not found");
    }

    // Only comment owner or admin can delete
    if (comment.userId !== user._id && user.userType !== "admin") {
      throw new ConvexError("You can only delete your own comments");
    }

    await ctx.db.patch(args.commentId, {
      isDeleted: true,
      deletedAt: Date.now(),
      content: "[deleted]",
    });

    return null;
  },
});

/**
 * Like/unlike a comment
 */
export const toggleCommentLike = mutation({
  args: {
    commentId: v.id("auctionComments"),
  },
  returns: v.object({
    liked: v.boolean(),
    newLikeCount: v.number(),
  }),
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new ConvexError("Comment not found");
    }

    // Check if user already liked this comment
    const existingLike = await ctx.db
      .query("auctionCommentLikes")
      .withIndex("by_commentId_userId", (q) => 
        q.eq("commentId", args.commentId).eq("userId", user._id)
      )
      .first();

    if (existingLike) {
      // Unlike
      await ctx.db.delete(existingLike._id);
      const newLikeCount = Math.max(0, (comment.likes || 0) - 1);
      await ctx.db.patch(args.commentId, { likes: newLikeCount });
      
      return {
        liked: false,
        newLikeCount,
      };
    } else {
      // Like
      await ctx.db.insert("auctionCommentLikes", {
        commentId: args.commentId,
        userId: user._id,
      });
      const newLikeCount = (comment.likes || 0) + 1;
      await ctx.db.patch(args.commentId, { likes: newLikeCount });
      
      return {
        liked: true,
        newLikeCount,
      };
    }
  },
});

/**
 * Report a comment
 */
export const reportAuctionComment = mutation({
  args: {
    commentId: v.id("auctionComments"),
    reason: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new ConvexError("Comment not found");
    }

    // Check if user already reported this comment
    const existingReport = await ctx.db
      .query("commentReports")
      .withIndex("by_commentId_userId", (q) => 
        q.eq("commentId", args.commentId).eq("userId", user._id)
      )
      .first();

    if (existingReport) {
      throw new ConvexError("You have already reported this comment");
    }

    // Create report
    await ctx.db.insert("commentReports", {
      commentId: args.commentId,
      userId: user._id,
      reason: args.reason,
    });

    // Update comment report count
    const newReportCount = (comment.reportCount || 0) + 1;
    await ctx.db.patch(args.commentId, {
      isReported: true,
      reportCount: newReportCount,
    });

    return null;
  },
});
