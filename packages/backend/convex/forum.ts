import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { ConvexError } from "convex/values";
import { requireAuth, getAuthUser, requireAdmin } from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";
import { paginationOptsValidator } from "convex/server";

// ============================================
// FORUM GROUPS FUNCTIONS
// ============================================

/**
 * Seed test communities (for development)
 */
export const seedTestCommunities = mutation({
  args: {},
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    // Check if communities already exist
    const existingGroups = await ctx.db
      .query("forumGroups")
      .withIndex("by_isActive", (q) => q.eq("isActive", true))
      .collect();
    
    if (existingGroups.length > 0) {
      return { message: "Communities already exist", count: existingGroups.length };
    }

    const testCommunities = [
      {
        name: "Moda Watch Club",
        slug: "moda-watch",
        description: "Premium timepieces and luxury watches community",
        icon: "⌚",
        color: "#3B82F6",
        isPrivate: false,
        rules: "1. Authentic watches only\n2. Include proper verification\n3. Respect all price ranges\n4. Be respectful and constructive"
      },
      {
        name: "Real Watch Buyers",
        slug: "real-watch-buyers",
        description: "Community for serious watch collectors and buyers",
        icon: "🕰️",
        color: "#6B7280",
        isPrivate: false,
        rules: "1. Serious collectors only\n2. Authentic pieces required\n3. Share knowledge and expertise\n4. No spam or fake listings"
      },
      {
        name: "Moda Car Club",
        slug: "moda-car",
        description: "Luxury cars and collectible vehicles community",
        icon: "🚗",
        color: "#DC2626",
        isPrivate: false,
        rules: "1. Luxury and collectible vehicles only\n2. Authentic documentation required\n3. Share experiences and knowledge\n4. Be respectful to all members"
      },
      {
        name: "Moda Lifestyle Club",
        slug: "moda-lifestyle",
        description: "Fashion, jewelry, and luxury lifestyle community",
        icon: "👑",
        color: "#EC4899",
        isPrivate: false,
        rules: "1. Focus on luxury lifestyle\n2. Share authentic pieces only\n3. Be constructive in feedback\n4. Respect diverse tastes"
      },
      {
        name: "Moda Misc Club",
        slug: "moda-misc",
        description: "Art, collectibles, and miscellaneous luxury items community",
        icon: "🎨",
        color: "#7C3AED",
        isPrivate: false,
        rules: "1. Luxury items and collectibles\n2. Authentic pieces only\n3. Share knowledge and stories\n4. Keep discussions relevant"
      }
    ];

    const createdGroups = [];
    for (const community of testCommunities) {
      const groupId = await ctx.db.insert("forumGroups", {
        name: community.name,
        slug: community.slug,
        description: community.description,
        icon: community.icon,
        color: community.color,
        memberCount: 1,
        postCount: 0,
        isActive: true,
        isPrivate: community.isPrivate,
        createdBy: currentUser._id,
        moderators: [currentUser._id],
        rules: community.rules,
        updatedAt: Date.now(),
      });

      // Add creator as admin member
      await ctx.db.insert("forumGroupMembers", {
        groupId,
        userId: currentUser._id,
        role: "admin",
        joinedAt: Date.now(),
      });

      createdGroups.push(await ctx.db.get(groupId));
    }

    return { message: "Test communities created", communities: createdGroups };
  },
});

/**
 * Create a new forum group (Admin only)
 */
export const createForumGroup = mutation({
  args: {
    name: v.string(),
    slug: v.string(),
    description: v.string(),
    icon: v.optional(v.string()),
    color: v.optional(v.string()),
    isPrivate: v.optional(v.boolean()),
    rules: v.optional(v.string()),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAdmin(ctx);
    
    // Check if slug already exists
    const existing = await ctx.db
      .query("forumGroups")
      .withIndex("by_slug", (q) => q.eq("slug", args.slug))
      .first();
    
    if (existing) {
      throw new ConvexError("A group with this slug already exists");
    }

    const groupId = await ctx.db.insert("forumGroups", {
      name: args.name,
      slug: args.slug,
      description: args.description,
      icon: args.icon,
      color: args.color,
      memberCount: 1,
      postCount: 0,
      isActive: true,
      isPrivate: args.isPrivate || false,
      createdBy: currentUser._id,
      moderators: [currentUser._id],
      rules: args.rules,
      updatedAt: Date.now(),
    });

    // Add creator as admin member
    await ctx.db.insert("forumGroupMembers", {
      groupId,
      userId: currentUser._id,
      role: "admin",
      joinedAt: Date.now(),
    });

    return await ctx.db.get(groupId);
  },
});

/**
 * Get forum group by slug
 */
export const getForumGroupBySlug = query({
  args: {
    slug: v.string(),
  },
  returns: v.union(v.any(), v.null()),
  handler: async (ctx, args) => {
    const group = await ctx.db
      .query("forumGroups")
      .withIndex("by_slug", (q) => q.eq("slug", args.slug))
      .first();
    
    if (!group || !group.isActive) {
      return null;
    }

    // Check if user has access to private groups
    const currentUser = await getAuthUser(ctx);
    if (group.isPrivate && currentUser) {
      const membership = await ctx.db
        .query("forumGroupMembers")
        .withIndex("by_group_user", (q) => q.eq("groupId", group._id).eq("userId", currentUser._id))
        .first();
      
      if (!membership && group.createdBy !== currentUser._id && !group.moderators.includes(currentUser._id)) {
        return null;
      }
    } else if (group.isPrivate) {
      return null;
    }

    return group;
  },
});

/**
 * Get forum group stats
 */
export const getForumGroupStats = query({
  args: {
    slug: v.string(),
  },
  returns: v.union(v.object({
    memberCount: v.number(),
    postCount: v.number(),
    activeToday: v.number(),
  }), v.null()),
  handler: async (ctx, args) => {
    const group = await ctx.db
      .query("forumGroups")
      .withIndex("by_slug", (q) => q.eq("slug", args.slug))
      .first();
    
    if (!group) {
      return null;
    }

    // Get active users today (users who posted or commented today)
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    
    const todayPosts = await ctx.db
      .query("forumPosts")
      .withIndex("by_groupId", (q) => q.eq("groupId", group._id))
      .filter((q) => q.gte(q.field("_creationTime"), todayStart.getTime()))
      .collect();

    const activeUserIds = new Set(todayPosts.map(p => p.userId));

    return {
      memberCount: group.memberCount,
      postCount: group.postCount,
      activeToday: activeUserIds.size,
    };
  },
});

/**
 * Get all active forum groups
 */
export const getForumGroups = query({
  args: {
    includePrivate: v.optional(v.boolean()),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("forumGroups")
      .withIndex("by_isActive", (q) => q.eq("isActive", true));
    
    const groups = await query.collect();
    const currentUser = await getAuthUser(ctx);
    
    // Filter groups based on privacy and membership
    let filteredGroups;
    if (args.includePrivate && currentUser) {
      // Get user's memberships for private groups
      const userMemberships = await ctx.db
        .query("forumGroupMembers")
        .withIndex("by_userId", (q) => q.eq("userId", currentUser._id))
        .collect();
      
      const memberGroupIds = new Set(userMemberships.map(m => m.groupId));
      
      filteredGroups = groups.filter(g => {
        // Include public groups
        if (!g.isPrivate) return true;
        
        // For private groups, only include if user is a member, creator, or moderator
        return (
          memberGroupIds.has(g._id) || 
          g.createdBy === currentUser._id || 
          g.moderators.includes(currentUser._id)
        );
      });
    } else {
      // Only show public groups if not including private or user not authenticated
      filteredGroups = groups.filter(g => !g.isPrivate);
    }
    
    // Sort by member count (most popular first)
    filteredGroups.sort((a, b) => b.memberCount - a.memberCount);
    
    // Add creator info, membership status, and convert storage IDs to URLs
    const groupsWithDetails = await Promise.all(
      filteredGroups.map(async (group) => {
        const creator = await ctx.db.get(group.createdBy);
        const bannerUrl = group.bannerImage 
          ? await ctx.storage.getUrl(group.bannerImage)
          : null;
        
        // Check if current user is a member
        let isMember = false;
        let isCreator = false;
        let isModerator = false;
        
        if (currentUser) {
          // Check if user is creator
          isCreator = group.createdBy === currentUser._id;
          
          // Check if user is moderator
          isModerator = group.moderators.includes(currentUser._id);
          
          // Check membership
          const membership = await ctx.db
            .query("forumGroupMembers")
            .withIndex("by_group_user", (q) => 
              q.eq("groupId", group._id).eq("userId", currentUser._id)
            )
            .first();
          
          isMember = !!membership || isCreator || isModerator;
        }
        
        return {
          ...group,
          creator,
          bannerUrl,
          isMember,
          isCreator,
          isModerator,
        };
      })
    );
    
    return groupsWithDetails;
  },
});

/**
 * Get a single forum group by slug
 */
export const getForumGroup = query({
  args: {
    slug: v.string(),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const group = await ctx.db
      .query("forumGroups")
      .withIndex("by_slug", (q) => q.eq("slug", args.slug))
      .first();
    
    if (!group) return null;
    
    // Get member count
    const members = await ctx.db
      .query("forumGroupMembers")
      .withIndex("by_groupId", (q) => q.eq("groupId", group._id))
      .collect();
    
    // Get moderator details
    const moderatorDetails = await Promise.all(
      group.moderators.map(async (modId) => {
        const user = await ctx.db.get(modId);
        return user;
      })
    );
    
    const bannerUrl = group.bannerImage 
      ? await ctx.storage.getUrl(group.bannerImage)
      : null;
    
    // Check if current user is a member
    const currentUser = await getAuthUser(ctx);
    let isMember = false;
    let isCreator = false;
    let isModerator = false;
    
    if (currentUser) {
      // Check if user is creator
      isCreator = group.createdBy === currentUser._id;
      
      // Check if user is moderator
      isModerator = group.moderators.includes(currentUser._id);
      
      // Check membership
      const membership = await ctx.db
        .query("forumGroupMembers")
        .withIndex("by_group_user", (q) => 
          q.eq("groupId", group._id).eq("userId", currentUser._id)
        )
        .first();
      
      isMember = !!membership || isCreator || isModerator;
    }
    
    return {
      ...group,
      memberCount: members.length,
      moderators: moderatorDetails,
      bannerUrl,
      isMember,
      isCreator,
      isModerator,
    };
  },
});

/**
 * Join a forum group
 */
export const joinForumGroup = mutation({
  args: {
    groupId: v.id("forumGroups"),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    // Check if already a member
    const existingMembership = await ctx.db
      .query("forumGroupMembers")
      .withIndex("by_group_user", (q) => 
        q.eq("groupId", args.groupId).eq("userId", currentUser._id)
      )
      .first();
    
    if (existingMembership) {
      throw new ConvexError("You are already a member of this group");
    }
    
    const group = await ctx.db.get(args.groupId);
    if (!group) {
      throw new ConvexError("Group not found");
    }
    
    // Add membership
    await ctx.db.insert("forumGroupMembers", {
      groupId: args.groupId,
      userId: currentUser._id,
      role: "member",
      joinedAt: Date.now(),
    });
    
    // Update member count
    await ctx.db.patch(args.groupId, {
      memberCount: group.memberCount + 1,
    });
    
    return { success: true };
  },
});

/**
 * Leave a forum group
 */
export const leaveForumGroup = mutation({
  args: {
    groupId: v.id("forumGroups"),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    const membership = await ctx.db
      .query("forumGroupMembers")
      .withIndex("by_group_user", (q) => 
        q.eq("groupId", args.groupId).eq("userId", currentUser._id)
      )
      .first();
    
    if (!membership) {
      throw new ConvexError("You are not a member of this group");
    }
    
    const group = await ctx.db.get(args.groupId);
    if (!group) {
      throw new ConvexError("Group not found");
    }
    
    // Don't allow creator to leave
    if (group.createdBy === currentUser._id) {
      throw new ConvexError("Group creator cannot leave the group");
    }
    
    // Remove membership
    await ctx.db.delete(membership._id);
    
    // Update member count
    await ctx.db.patch(args.groupId, {
      memberCount: Math.max(0, group.memberCount - 1),
    });
    
    return { success: true };
  },
});

// ============================================
// ENHANCED FORUM POST FUNCTIONS
// ============================================

/**
 * Create a new forum post with images
 */
export const createForumPost = mutation({
  args: {
    groupId: v.optional(v.id("forumGroups")),
    title: v.string(),
    content: v.string(),
    contentPlainText: v.optional(v.string()),
    images: v.optional(v.array(v.id("_storage"))),
    category: v.union(
      v.literal("general"),
      v.literal("marketplace"),
      v.literal("authentication"),
      v.literal("deals"),
      v.literal("questions"),
      v.literal("announcements"),
      v.literal("discussion"),
      v.literal("showcase"),
      v.literal("help")
    ),
    tags: v.array(v.string()),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    // If posting to a group, check membership
    if (args.groupId) {
      const membership = await ctx.db
        .query("forumGroupMembers")
        .withIndex("by_group_user", (q) => 
          q.eq("groupId", args.groupId as Id<"forumGroups">).eq("userId", currentUser._id)
        )
        .first();
      
      if (!membership) {
        throw new ConvexError("You must be a member of this group to post");
      }
      
      const group = await ctx.db.get(args.groupId);
      if (group) {
        // Update post count
        await ctx.db.patch(args.groupId, {
          postCount: group.postCount + 1,
        });
      }
    }

    const postId = await ctx.db.insert("forumPosts", {
      userId: currentUser._id,
      groupId: args.groupId,
      title: args.title,
      content: args.content,
      contentPlainText: args.contentPlainText,
      images: args.images && args.images.length > 0 ? args.images : undefined,
      category: args.category,
      tags: args.tags,
      isPinned: false,
      isLocked: false,
      isStickied: false,
      views: 0,
      score: 0,
      upvotes: 0,
      downvotes: 0,
      commentCount: 0,
      lastActivityAt: Date.now(),
      isEdited: false,
      updatedAt: Date.now(),
    });

    // Update or create member profile
    let memberProfile = await ctx.db
      .query("memberProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", currentUser._id))
      .first();
    
    if (memberProfile) {
      await ctx.db.patch(memberProfile._id, {
        updatedAt: Date.now(),
      });
    } else {
      // Create new member profile if it doesn't exist
      await ctx.db.insert("memberProfiles", {
        userId: currentUser._id,
        displayName: currentUser.name,
        bio: "",
        company: "",
        website: "",
        location: "",
        specialty: [],
        yearsExperience: 0,
        badges: [],
        memberSince: Date.now(),
        isPublic: true,
        showEmail: false,
        showPhone: false,
        allowMessages: true,
        updatedAt: Date.now(),
      });
    }

    return await ctx.db.get(postId);
  },
});

/**
 * Get forum posts with pagination and filtering
 */
export const getForumPosts = query({
  args: {
    groupId: v.optional(v.id("forumGroups")),
    category: v.optional(v.union(
      v.literal("general"),
      v.literal("marketplace"),
      v.literal("authentication"),
      v.literal("deals"),
      v.literal("questions"),
      v.literal("announcements"),
      v.literal("discussion"),
      v.literal("showcase"),
      v.literal("help")
    )),
    sortBy: v.optional(v.union(
      v.literal("recent"),
      v.literal("popular"),
      v.literal("mostViewed"),
      v.literal("mostCommented")
    )),
    paginationOpts: paginationOptsValidator,
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    // Get all posts based on filters
    let allPosts;
    
    if (args.groupId && args.category) {
      // If both filters, use group_category index
      allPosts = await ctx.db
        .query("forumPosts")
        .withIndex("by_group_category", (q) => 
          q.eq("groupId", args.groupId as Id<"forumGroups">)
           .eq("category", args.category as any)
        )
        .collect();
    } else if (args.groupId) {
      // Filter by group only
      allPosts = await ctx.db
        .query("forumPosts")
        .withIndex("by_groupId", (q) => q.eq("groupId", args.groupId as Id<"forumGroups">))
        .collect();
    } else if (args.category) {
      // Filter by category only
      allPosts = await ctx.db
        .query("forumPosts")
        .withIndex("by_category", (q) => q.eq("category", args.category as any))
        .collect();
    } else {
      // No filters - get all posts
      allPosts = await ctx.db
        .query("forumPosts")
        .collect();
    }
    
    // Sort based on sortBy parameter
    let sortedPosts = [...allPosts];
    if (args.sortBy === "popular") {
      sortedPosts.sort((a, b) => (b.score ?? 0) - (a.score ?? 0));
    } else if (args.sortBy === "mostViewed") {
      sortedPosts.sort((a, b) => b.views - a.views);
    } else if (args.sortBy === "mostCommented") {
      sortedPosts.sort((a, b) => b.commentCount - a.commentCount);
    } else {
      // Default to recent (by lastActivityAt)
      sortedPosts.sort((a, b) => b.lastActivityAt - a.lastActivityAt);
    }
    
    // Put stickied posts at the top
    const stickiedPosts = sortedPosts.filter(p => p.isStickied === true);
    const nonStickiedPosts = sortedPosts.filter(p => p.isStickied !== true);
    sortedPosts = [...stickiedPosts, ...nonStickiedPosts];
    
    // Manual pagination
    const numItems = args.paginationOpts.numItems;
    const cursor = args.paginationOpts.cursor;
    let startIndex = 0;
    
    if (cursor) {
      startIndex = parseInt(cursor, 10);
    }
    
    const endIndex = startIndex + numItems;
    const paginatedPosts = sortedPosts.slice(startIndex, endIndex);
    const isDone = endIndex >= sortedPosts.length;
    const continueCursor = isDone ? null : endIndex.toString();

    // Fetch additional details for each post
    const postsWithDetails = await Promise.all(
      paginatedPosts.map(async (post) => {
        const user = await ctx.db.get(post.userId);
        const memberProfile = await ctx.db
          .query("memberProfiles")
          .withIndex("by_userId", (q) => q.eq("userId", post.userId))
          .first();
        
        // Convert image storage IDs to URLs
        const imageUrls = post.images ? await Promise.all(
          post.images.map(async (imageId) => {
            const url = await ctx.storage.getUrl(imageId);
            return url;
          })
        ) : [];
        
        // Get group info if post belongs to a group
        let group = null;
        if (post.groupId) {
          group = await ctx.db.get(post.groupId);
        }
        
        return {
          ...post,
          user,
          memberProfile,
          imageUrls,
          group,
        };
      })
    );

    return {
      page: postsWithDetails,
      isDone,
      continueCursor,
    };
  },
});

/**
 * Get a single forum post with details
 */
export const getForumPost = query({
  args: {
    postId: v.id("forumPosts"),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const post = await ctx.db.get(args.postId);
    if (!post) return null;

    // Note: View increment is handled separately via incrementPostViews mutation

    const user = await ctx.db.get(post.userId);
    const memberProfile = await ctx.db
      .query("memberProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", post.userId))
      .first();
    
    // Convert image storage IDs to URLs
    const imageUrls = post.images ? await Promise.all(
      post.images.map(async (imageId) => {
        const url = await ctx.storage.getUrl(imageId);
        return url;
      })
    ) : [];
    
    // Get group info if post belongs to a group
    let group = null;
    if (post.groupId) {
      group = await ctx.db.get(post.groupId);
    }
    
    return {
      ...post,
      user,
      memberProfile,
      imageUrls,
      group,
    };
  },
});

/**
 * Increment view count for a forum post
 */
export const incrementPostViews = mutation({
  args: {
    postId: v.id("forumPosts"),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const post = await ctx.db.get(args.postId);
    if (!post) return { success: false };

    await ctx.db.patch(args.postId, {
      views: (post.views ?? 0) + 1,
    });

    return { success: true };
  },
});

/**
 * Upload image for forum post
 */
export const generateUploadUrl = mutation({
  args: {},
  returns: v.string(),
  handler: async (ctx) => {
    await requireAuth(ctx);
    return await ctx.storage.generateUploadUrl();
  },
});

/**
 * Update a forum post
 */
export const updateForumPost = mutation({
  args: {
    postId: v.id("forumPosts"),
    title: v.optional(v.string()),
    content: v.optional(v.string()),
    contentPlainText: v.optional(v.string()),
    images: v.optional(v.array(v.id("_storage"))),
    tags: v.optional(v.array(v.string())),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    const post = await ctx.db.get(args.postId);
    if (!post) {
      throw new ConvexError("Post not found");
    }
    
    // Check if user is the author
    if (post.userId !== currentUser._id) {
      throw new ConvexError("You can only edit your own posts");
    }
    
    if (post.isLocked) {
      throw new ConvexError("This post is locked and cannot be edited");
    }
    
    await ctx.db.patch(args.postId, {
      ...(args.title !== undefined && { title: args.title }),
      ...(args.content !== undefined && { content: args.content }),
      ...(args.contentPlainText !== undefined && { contentPlainText: args.contentPlainText }),
      ...(args.images !== undefined && { images: args.images }),
      ...(args.tags !== undefined && { tags: args.tags }),
      isEdited: true,
      editedAt: Date.now(),
      updatedAt: Date.now(),
    });
    
    return await ctx.db.get(args.postId);
  },
});

/**
 * User function to delete their own forum post
 */
export const deleteForumPost = mutation({
  args: {
    postId: v.id("forumPosts"),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    const post = await ctx.db.get(args.postId);
    if (!post) {
      throw new ConvexError("Post not found");
    }
    
    // Check if user is the author
    if (post.userId !== currentUser._id) {
      throw new ConvexError("You can only delete your own posts");
    }
    
    if (post.isLocked) {
      throw new ConvexError("This post is locked and cannot be deleted");
    }
    
    // Get all comments for this post
    const comments = await ctx.db
      .query("forumComments")
      .withIndex("by_postId", (q: any) => q.eq("postId", args.postId))
      .collect();
    
    // Delete all comment votes first
    for (const comment of comments) {
      const commentVotes = await ctx.db
        .query("forumCommentVotes")
        .withIndex("by_commentId", (q: any) => q.eq("commentId", comment._id))
        .collect();
      
      for (const vote of commentVotes) {
        await ctx.db.delete(vote._id);
      }
    }
    
    // Delete all comments
    for (const comment of comments) {
      await ctx.db.delete(comment._id);
    }
    
    // Delete all post votes
    const postVotes = await ctx.db
      .query("forumPostVotes")
      .withIndex("by_postId", (q: any) => q.eq("postId", args.postId))
      .collect();
    
    for (const vote of postVotes) {
      await ctx.db.delete(vote._id);
    }
    
    // Update group post count if post belongs to a group
    if (post.groupId) {
      const group = await ctx.db.get(post.groupId);
      if (group && group.postCount > 0) {
        await ctx.db.patch(post.groupId, {
          postCount: group.postCount - 1,
        });
      }
    }
    
    // Finally delete the post
    await ctx.db.delete(args.postId);
    
    return { success: true, message: "Post deleted successfully" };
  },
});

// NOTE: toggleForumPostLike has been replaced with Reddit-style voting system
// See redditForum.ts for upvote/downvote functions

// ============================================
// FORUM COMMENTS WITH THREADING
// ============================================

/**
 * Get comments for a forum post with nested replies
 */
export const getForumComments = query({
  args: {
    postId: v.id("forumPosts"),
    paginationOpts: paginationOptsValidator,
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    // Get top-level comments (no parent)
    const topLevelComments = await ctx.db
      .query("forumComments")
      .withIndex("by_postId", (q) => q.eq("postId", args.postId))
      .filter((q) => 
        q.and(
          q.eq(q.field("isDeleted"), false),
          q.eq(q.field("parentCommentId"), undefined)
        )
      )
      .collect();
    
    // Sort by score and creation time
    topLevelComments.sort((a, b) => {
      // Best answers first
      if (a.isBestAnswer && !b.isBestAnswer) return -1;
      if (!a.isBestAnswer && b.isBestAnswer) return 1;
      // Then by score (with fallback to likes for backward compatibility)
      const aScore = a.score ?? a.likes ?? 0;
      const bScore = b.score ?? b.likes ?? 0;
      if (bScore !== aScore) return bScore - aScore;
      // Then by time
      return b._creationTime - a._creationTime;
    });
    
    // Manual pagination for top-level comments
    const numItems = args.paginationOpts.numItems;
    const cursor = args.paginationOpts.cursor;
    let startIndex = cursor ? parseInt(cursor, 10) : 0;
    const endIndex = startIndex + numItems;
    const paginatedComments = topLevelComments.slice(startIndex, endIndex);
    const isDone = endIndex >= topLevelComments.length;
    const continueCursor = isDone ? null : endIndex.toString();
    
    // Fetch nested replies and user details
    const commentsWithDetails = await Promise.all(
      paginatedComments.map(async (comment) => {
        const user = await ctx.db.get(comment.userId);
        const memberProfile = await ctx.db
          .query("memberProfiles")
          .withIndex("by_userId", (q) => q.eq("userId", comment.userId))
          .first();
        
        // Get all nested replies recursively
        const replies = await getNestedReplies(ctx, comment._id);
        
        return {
          ...comment,
          user,
          memberProfile,
          replies,
        };
      })
    );
    
    return {
      page: commentsWithDetails,
      isDone,
      continueCursor,
    };
  },
});

// Helper function to get nested replies
async function getNestedReplies(ctx: any, parentCommentId: Id<"forumComments">): Promise<any[]> {
  const replies = await ctx.db
    .query("forumComments")
    .withIndex("by_parentCommentId", (q: any) => q.eq("parentCommentId", parentCommentId))
    .filter((q: any) => q.eq(q.field("isDeleted"), false))
    .collect();
  
  const repliesWithDetails = await Promise.all(
    replies.map(async (reply: any) => {
      const user = await ctx.db.get(reply.userId);
      const memberProfile = await ctx.db
        .query("memberProfiles")
        .withIndex("by_userId", (q: any) => q.eq("userId", reply.userId))
        .first();
      
      // Recursively get nested replies
      const nestedReplies = await getNestedReplies(ctx, reply._id);
      
      return {
        ...reply,
        user,
        memberProfile,
        replies: nestedReplies,
      };
    })
  );
  
  return repliesWithDetails;
}

/**
 * Add a comment to a forum post
 */
export const addForumComment = mutation({
  args: {
    postId: v.id("forumPosts"),
    content: v.string(),
    parentCommentId: v.optional(v.id("forumComments")),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    const post = await ctx.db.get(args.postId);
    if (!post) {
      throw new ConvexError("Post not found");
    }
    
    if (post.isLocked) {
      throw new ConvexError("This post is locked");
    }
    
    // Validate parent comment if provided
    if (args.parentCommentId) {
      const parentComment = await ctx.db.get(args.parentCommentId);
      if (!parentComment) {
        throw new ConvexError("Parent comment not found");
      }
      if (parentComment.postId !== args.postId) {
        throw new ConvexError("Parent comment belongs to a different post");
      }
    }
    
    const commentId = await ctx.db.insert("forumComments", {
      postId: args.postId,
      userId: currentUser._id,
      content: args.content,
      parentCommentId: args.parentCommentId,
      score: 0,
      upvotes: 0,
      downvotes: 0,
      isEdited: false,
      isDeleted: false,
      deletedAt: undefined,
      isBestAnswer: false,
      isCollapsed: false,
      updatedAt: Date.now(),
    });
    
    // Update post comment count and last activity
    await ctx.db.patch(args.postId, {
      commentCount: post.commentCount + 1,
      lastActivityAt: Date.now(),
    });
    
    // Update or create member profile
    let memberProfile = await ctx.db
      .query("memberProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", currentUser._id))
      .first();
    
    if (memberProfile) {
      await ctx.db.patch(memberProfile._id, {
        updatedAt: Date.now(),
      });
    } else {
      // Create new member profile if it doesn't exist
      await ctx.db.insert("memberProfiles", {
        userId: currentUser._id,
        displayName: currentUser.name,
        bio: "",
        company: "",
        website: "",
        location: "",
        specialty: [],
        yearsExperience: 0,
        badges: [],
        memberSince: Date.now(),
        isPublic: true,
        showEmail: false,
        showPhone: false,
        allowMessages: true,
        updatedAt: Date.now(),
      });
    }
    
    return await ctx.db.get(commentId);
  },
});

/**
 * Update a forum comment
 */
export const updateForumComment = mutation({
  args: {
    commentId: v.id("forumComments"),
    content: v.string(),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new ConvexError("Comment not found");
    }
    
    // Check if user is the author
    if (comment.userId !== currentUser._id) {
      throw new ConvexError("You can only edit your own comments");
    }
    
    if (comment.isDeleted) {
      throw new ConvexError("This comment has been deleted and cannot be edited");
    }
    
    // Check if the post is locked
    const post = await ctx.db.get(comment.postId);
    if (post?.isLocked) {
      throw new ConvexError("Cannot edit comments on a locked post");
    }
    
    await ctx.db.patch(args.commentId, {
      content: args.content,
      isEdited: true,
      editedAt: Date.now(),
      updatedAt: Date.now(),
    });
    
    return await ctx.db.get(args.commentId);
  },
});

/**
 * User function to delete their own comment
 */
export const deleteForumComment = mutation({
  args: {
    commentId: v.id("forumComments"),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new ConvexError("Comment not found");
    }
    
    // Check if user is the author
    if (comment.userId !== currentUser._id) {
      throw new ConvexError("You can only delete your own comments");
    }
    
    if (comment.isDeleted) {
      throw new ConvexError("This comment has already been deleted");
    }
    
    // Check if the post is locked
    const post = await ctx.db.get(comment.postId);
    if (post?.isLocked) {
      throw new ConvexError("Cannot delete comments on a locked post");
    }
    
    // Check if comment has replies
    const replies = await ctx.db
      .query("forumComments")
      .withIndex("by_parentCommentId", (q: any) => q.eq("parentCommentId", args.commentId))
      .collect();
    
    if (replies.length > 0) {
      // If comment has replies, mark as deleted but keep the structure
      await ctx.db.patch(args.commentId, {
        content: "[deleted]",
        isDeleted: true,
        deletedAt: Date.now(),
        updatedAt: Date.now(),
      });
      
      return { success: true, message: "Comment marked as deleted", hasReplies: true };
    } else {
      // If no replies, completely delete the comment and its votes
      const commentVotes = await ctx.db
        .query("forumCommentVotes")
        .withIndex("by_commentId", (q: any) => q.eq("commentId", args.commentId))
        .collect();
      
      for (const vote of commentVotes) {
        await ctx.db.delete(vote._id);
      }
      
      await ctx.db.delete(args.commentId);
      
      // Update post comment count
      if (post) {
        await ctx.db.patch(comment.postId, {
          commentCount: Math.max(0, post.commentCount - 1),
        });
      }
      
      return { success: true, message: "Comment deleted successfully", hasReplies: false };
    }
  },
});

// ============================================
// ADMIN FORUM MANAGEMENT FUNCTIONS
// ============================================

/**
 * Admin function to delete a forum post and all its comments
 */
export const adminDeleteForumPost = mutation({
  args: {
    postId: v.id("forumPosts"),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    // Check if user is admin
    if (currentUser.userType !== "admin") {
      throw new ConvexError("Only admins can delete forum posts");
    }
    
    const post = await ctx.db.get(args.postId);
    if (!post) {
      throw new ConvexError("Post not found");
    }
    
    // Get all comments for this post
    const comments = await ctx.db
      .query("forumComments")
      .withIndex("by_postId", (q) => q.eq("postId", args.postId))
      .collect();
    
    // Delete all comment votes first
    for (const comment of comments) {
      const commentVotes = await ctx.db
        .query("forumCommentVotes")
        .withIndex("by_commentId", (q) => q.eq("commentId", comment._id))
        .collect();
      
      for (const vote of commentVotes) {
        await ctx.db.delete(vote._id);
      }
    }
    
    // Delete all comments
    for (const comment of comments) {
      await ctx.db.delete(comment._id);
    }
    
    // Delete all post votes
    const postVotes = await ctx.db
      .query("forumPostVotes")
      .withIndex("by_postId", (q) => q.eq("postId", args.postId))
      .collect();
    
    for (const vote of postVotes) {
      await ctx.db.delete(vote._id);
    }
    
    // Update group post count if post belongs to a group
    if (post.groupId) {
      const group = await ctx.db.get(post.groupId);
      if (group && group.postCount > 0) {
        await ctx.db.patch(post.groupId, {
          postCount: group.postCount - 1,
        });
      }
    }
    
    // Finally delete the post
    await ctx.db.delete(args.postId);
    
    return { success: true, message: "Post and all related data deleted successfully" };
  },
});

/**
 * Admin function to delete a forum comment and all its replies
 */
export const adminDeleteForumComment = mutation({
  args: {
    commentId: v.id("forumComments"),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    // Check if user is admin
    if (currentUser.userType !== "admin") {
      throw new ConvexError("Only admins can delete forum comments");
    }
    
    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new ConvexError("Comment not found");
    }
    
    // Recursively delete all replies
    await deleteCommentAndReplies(ctx, args.commentId);
    
    // Update post comment count
    const post = await ctx.db.get(comment.postId);
    if (post) {
      // Count how many comments were actually deleted
      const deletedCount = await countDeletedComments(ctx, args.commentId);
      const newCommentCount = Math.max(0, post.commentCount - deletedCount);
      
      await ctx.db.patch(comment.postId, {
        commentCount: newCommentCount,
      });
    }
    
    return { success: true, message: "Comment and all replies deleted successfully" };
  },
});

/**
 * Helper function to recursively delete a comment and all its replies
 */
async function deleteCommentAndReplies(ctx: any, commentId: Id<"forumComments">) {
  // Find all direct replies to this comment
  const replies = await ctx.db
    .query("forumComments")
    .withIndex("by_parentCommentId", (q: any) => q.eq("parentCommentId", commentId))
    .collect();
  
  // Recursively delete each reply
  for (const reply of replies) {
    await deleteCommentAndReplies(ctx, reply._id);
  }
  
  // Delete all votes for this comment
  const commentVotes = await ctx.db
    .query("forumCommentVotes")
    .withIndex("by_commentId", (q: any) => q.eq("commentId", commentId))
    .collect();
  
  for (const vote of commentVotes) {
    await ctx.db.delete(vote._id);
  }
  
  // Delete the comment itself
  await ctx.db.delete(commentId);
}

/**
 * Helper function to count how many comments will be deleted (including replies)
 */
async function countDeletedComments(ctx: any, commentId: Id<"forumComments">): Promise<number> {
  let count = 1; // Count the comment itself
  
  // Find all direct replies to this comment
  const replies = await ctx.db
    .query("forumComments")
    .withIndex("by_parentCommentId", (q: any) => q.eq("parentCommentId", commentId))
    .collect();
  
  // Recursively count replies
  for (const reply of replies) {
    count += await countDeletedComments(ctx, reply._id);
  }
  
  return count;
}

/**
 * Admin function to get all forum posts with pagination
 */
export const adminGetAllForumPosts = query({
  args: {
    paginationOpts: paginationOptsValidator,
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await getAuthUser(ctx);
    
    // Check if user is admin
    if (!currentUser || currentUser.userType !== "admin") {
      throw new ConvexError("Only admins can access this function");
    }
    
    const posts = await ctx.db
      .query("forumPosts")
      .order("desc")
      .paginate(args.paginationOpts);
    
    // Enhance posts with user and group information
    const enhancedPosts = await Promise.all(
      posts.page.map(async (post) => {
        const author = await ctx.db.get(post.userId);
        const group = post.groupId ? await ctx.db.get(post.groupId) : null;
        
        return {
          ...post,
          author: author ? {
            _id: author._id,
            firstName: (author as any).firstName || author.name?.split(' ')[0] || '',
            lastName: (author as any).lastName || author.name?.split(' ')[1] || '',
            email: author.email,
          } : null,
          group: group ? {
            _id: group._id,
            name: group.name,
            slug: group.slug,
          } : null,
        };
      })
    );
    
    return {
      ...posts,
      page: enhancedPosts,
    };
  },
});

/**
 * Admin function to get all forum comments with pagination
 */
export const adminGetAllForumComments = query({
  args: {
    paginationOpts: paginationOptsValidator,
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await getAuthUser(ctx);
    
    // Check if user is admin
    if (!currentUser || currentUser.userType !== "admin") {
      throw new ConvexError("Only admins can access this function");
    }
    
    const comments = await ctx.db
      .query("forumComments")
      .order("desc")
      .paginate(args.paginationOpts);
    
    // Enhance comments with user and post information
    const enhancedComments = await Promise.all(
      comments.page.map(async (comment) => {
        const author = await ctx.db.get(comment.userId);
        const post = await ctx.db.get(comment.postId);
        
        return {
          ...comment,
          author: author ? {
            _id: author._id,
            firstName: (author as any).firstName || author.name?.split(' ')[0] || '',
            lastName: (author as any).lastName || author.name?.split(' ')[1] || '',
            email: author.email,
          } : null,
          post: post ? {
            _id: post._id,
            title: post.title,
          } : null,
        };
      })
    );
    
    return {
      ...comments,
      page: enhancedComments,
    };
  },
});

/**
 * Mark a comment as best answer
 */
export const markBestAnswer = mutation({
  args: {
    commentId: v.id("forumComments"),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    const comment = await ctx.db.get(args.commentId);
    if (!comment) {
      throw new ConvexError("Comment not found");
    }
    
    const post = await ctx.db.get(comment.postId);
    if (!post) {
      throw new ConvexError("Post not found");
    }
    
    // Only post author can mark best answer
    if (post.userId !== currentUser._id) {
      throw new ConvexError("Only the post author can mark best answers");
    }
    
    // Remove previous best answer if any
    const previousBest = await ctx.db
      .query("forumComments")
      .withIndex("by_postId", (q) => q.eq("postId", comment.postId))
      .filter((q) => q.eq(q.field("isBestAnswer"), true))
      .first();
    
    if (previousBest) {
      await ctx.db.patch(previousBest._id, {
        isBestAnswer: false,
      });
    }
    
    // Mark this comment as best answer
    await ctx.db.patch(args.commentId, {
      isBestAnswer: true,
    });
    
    return { success: true };
  },
});
