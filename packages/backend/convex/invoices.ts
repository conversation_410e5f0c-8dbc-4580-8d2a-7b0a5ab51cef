import { v } from "convex/values";
import { action, internalAction, internalMutation, internalQuery, mutation, query } from "./_generated/server";
import { internal } from "./_generated/api";
import { Id } from "./_generated/dataModel";
import { requireAuth } from "./lib/auth_utils";

export const generateInvoiceNumber = (sellerId: string): string => {
  const timestamp = Date.now().toString().slice(-6);
  const sellerPrefix = sellerId.slice(-4).toUpperCase();
  return `HV-${sellerPrefix}-${timestamp}`;
};

export const createInvoice = mutation({
  args: {
    offlineSaleId: v.id("offlineSales"),
    paymentTerms: v.optional(v.string()),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const offlineSale = await ctx.db.get(args.offlineSaleId);
    if (!offlineSale) {
      throw new Error("Offline sale not found");
    }

    const now = Date.now();
    const dueDate = now + (30 * 24 * 60 * 60 * 1000); // 30 days from now
    const invoiceNumber = generateInvoiceNumber(offlineSale.sellerId);

    // Get product for item description if productId exists
    let itemDescription = "Product";
    if (offlineSale.productId) {
      const product = await ctx.db.get(offlineSale.productId);
      if (product) {
        itemDescription = `${product.title} - ${product.brand}`;
      }
    }
    
    const tax = 0; // Default tax
    const totalAmount = offlineSale.salePrice + tax;

    const invoiceId = await ctx.db.insert("invoices", {
      sellerId: offlineSale.sellerId,
      offlineSaleId: args.offlineSaleId,
      productId: offlineSale.productId, // This can now be undefined
      invoiceNumber,
      clientName: offlineSale.clientName,
      clientEmail: offlineSale.clientEmail,
      clientPhone: offlineSale.clientPhone,
      clientAddress: offlineSale.clientAddress,
      itemDescription,
      salePrice: offlineSale.salePrice,
      tax,
      totalAmount,
      paymentMethod: offlineSale.paymentMethod,
      status: "draft",
      dueDate,
      paymentTerms: args.paymentTerms || "Payment due within 30 days",
      notes: args.notes,
      emailsSent: [],
      updatedAt: now,
    });

    return invoiceId;
  },
});

export const createCustomInvoice = mutation({
  args: {
    clientName: v.string(),
    clientEmail: v.string(),
    clientPhone: v.optional(v.string()),
    clientAddress: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
    }),
    items: v.array(v.object({
      description: v.string(),
      quantity: v.number(),
      unitPrice: v.number(),
      amount: v.number(),
    })),
    subtotal: v.number(),
    tax: v.optional(v.number()),
    totalAmount: v.number(),
    paymentTerms: v.optional(v.string()),
    notes: v.optional(v.string()),
    dueDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    const now = Date.now();
    
    // Generate invoice number
    const invoiceNumber = generateInvoiceNumber(user._id);
    
    // Create a placeholder offline sale for the invoice
    // For custom invoices, we don't need a specific product
    const offlineSaleId = await ctx.db.insert("offlineSales", {
      sellerId: user._id,
      productId: undefined, // No specific product for custom invoices
      clientName: args.clientName,
      clientEmail: args.clientEmail,
      clientPhone: args.clientPhone,
      clientAddress: args.clientAddress,
      salePrice: args.totalAmount,
      paymentMethod: "other",
      saleDate: now,
      status: "pending_payment",
      notes: args.notes,
    });

    // Create the invoice
    const invoiceId = await ctx.db.insert("invoices", {
      sellerId: user._id,
      offlineSaleId,
      productId: undefined, // No specific product for custom invoices
      invoiceNumber,
      clientName: args.clientName,
      clientEmail: args.clientEmail,
      clientPhone: args.clientPhone,
      clientAddress: args.clientAddress,
      itemDescription: args.items.map(item => `${item.description} (${item.quantity}x)`).join(", "),
      salePrice: args.totalAmount,
      tax: args.tax || 0,
      totalAmount: args.totalAmount,
      paymentMethod: "other",
      status: "draft",
      dueDate: args.dueDate || (now + (30 * 24 * 60 * 60 * 1000)), // 30 days from now
      paymentTerms: args.paymentTerms || "Payment due within 30 days",
      notes: args.notes,
      emailsSent: [],
      updatedAt: now,
    });

    return { invoiceId, offlineSaleId };
  },
});

export const saveInvoiceDraft = mutation({
  args: {
    clientName: v.string(),
    clientEmail: v.string(),
    clientPhone: v.optional(v.string()),
    clientAddress: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
    }),
    items: v.array(v.object({
      description: v.string(),
      quantity: v.number(),
      unitPrice: v.number(),
      amount: v.number(),
    })),
    subtotal: v.number(),
    tax: v.optional(v.number()),
    totalAmount: v.number(),
    paymentTerms: v.optional(v.string()),
    notes: v.optional(v.string()),
    dueDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    const now = Date.now();
    
    // Generate invoice number
    const invoiceNumber = generateInvoiceNumber(user._id);
    
    // Create a placeholder offline sale for the draft invoice
    // For custom invoices, we don't need a specific product
    const offlineSaleId = await ctx.db.insert("offlineSales", {
      sellerId: user._id,
      productId: undefined, // No specific product for custom invoices
      clientName: args.clientName,
      clientEmail: args.clientEmail,
      clientPhone: args.clientPhone,
      clientAddress: args.clientAddress,
      salePrice: args.totalAmount,
      paymentMethod: "other",
      saleDate: now,
      status: "pending_payment",
      notes: args.notes,
    });

    // Create the draft invoice
    const invoiceId = await ctx.db.insert("invoices", {
      sellerId: user._id,
      offlineSaleId,
      productId: undefined, // No specific product for custom invoices
      invoiceNumber,
      clientName: args.clientName,
      clientEmail: args.clientEmail,
      clientPhone: args.clientPhone,
      clientAddress: args.clientAddress,
      itemDescription: args.items.map(item => `${item.description} (${item.quantity}x)`).join(", "),
      salePrice: args.totalAmount,
      tax: args.tax || 0,
      totalAmount: args.totalAmount,
      paymentMethod: "other",
      status: "draft", // Explicitly set as draft
      dueDate: args.dueDate || (now + (30 * 24 * 60 * 60 * 1000)), // 30 days from now
      paymentTerms: args.paymentTerms || "Payment due within 30 days",
      notes: args.notes,
      emailsSent: [],
      updatedAt: now,
    });

    return { invoiceId, offlineSaleId };
  },
});

export const updateInvoiceDraft = mutation({
  args: {
    invoiceId: v.id("invoices"),
    clientName: v.string(),
    clientEmail: v.string(),
    clientPhone: v.optional(v.string()),
    clientAddress: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      zipCode: v.string(),
      country: v.string(),
    }),
    items: v.array(v.object({
      description: v.string(),
      quantity: v.number(),
      unitPrice: v.number(),
      amount: v.number(),
    })),
    subtotal: v.number(),
    tax: v.optional(v.number()),
    totalAmount: v.number(),
    paymentTerms: v.optional(v.string()),
    notes: v.optional(v.string()),
    dueDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    const now = Date.now();
    
    // Verify the invoice belongs to the user and is a draft
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice || invoice.sellerId !== user._id || invoice.status !== "draft") {
      throw new Error("Invoice not found or not editable");
    }

    // Update the invoice
    await ctx.db.patch(args.invoiceId, {
      clientName: args.clientName,
      clientEmail: args.clientEmail,
      clientPhone: args.clientPhone,
      clientAddress: args.clientAddress,
      itemDescription: args.items.map(item => `${item.description} (${item.quantity}x)`).join(", "),
      salePrice: args.totalAmount,
      tax: args.tax || 0,
      totalAmount: args.totalAmount,
      paymentTerms: args.paymentTerms || "Payment due within 30 days",
      notes: args.notes,
      dueDate: args.dueDate || (now + (30 * 24 * 60 * 60 * 1000)),
      updatedAt: now,
    });

    // Also update the offline sale
    const offlineSale = await ctx.db.get(invoice.offlineSaleId);
    if (offlineSale) {
      await ctx.db.patch(offlineSale._id, {
        clientName: args.clientName,
        clientEmail: args.clientEmail,
        clientPhone: args.clientPhone,
        clientAddress: args.clientAddress,
        salePrice: args.totalAmount,
        notes: args.notes,
      });
    }

    return args.invoiceId;
  },
});

export const getInvoices = query({
  args: {},
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    const invoices = await ctx.db
      .query("invoices")
      .withIndex("by_sellerId", (q: any) => q.eq("sellerId", user._id))
      .order("desc")
      .collect();

    // Get related data for each invoice
    const invoicesWithDetails = await Promise.all(
      invoices.map(async (invoice) => {
        const offlineSale = await ctx.db.get(invoice.offlineSaleId);
        const product = offlineSale && offlineSale.productId ? await ctx.db.get(offlineSale.productId) : null;
        
        return {
          ...invoice,
          offlineSale,
          product,
        };
      })
    );

    return invoicesWithDetails;
  },
});

export const getInvoice = query({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      return null;
    }

    const offlineSale = await ctx.db.get(invoice.offlineSaleId);
    const product = offlineSale && offlineSale.productId ? await ctx.db.get(offlineSale.productId) : null;
    const seller = await ctx.db.get(invoice.sellerId);
    
    // Get seller's company information for branding
    const sellerProfile = seller ? await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", seller._id))
      .first() : null;

    return {
      ...invoice,
      offlineSale,
      product,
      seller,
      sellerProfile,
    };
  },
});

export const getInvoiceInternal = internalQuery({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      return null;
    }

    const offlineSale = await ctx.db.get(invoice.offlineSaleId);
    const product = offlineSale && offlineSale.productId ? await ctx.db.get(offlineSale.productId) : null;
    const seller = await ctx.db.get(invoice.sellerId);
    
    // Get seller's company information for branding
    const sellerProfile = seller ? await ctx.db
      .query("sellerProfiles")
      .withIndex("by_userId", (q) => q.eq("userId", seller._id))
      .first() : null;

    return {
      ...invoice,
      offlineSale,
      product,
      seller,
      sellerProfile,
    };
  },
});

export const updateInvoiceStatus = mutation({
  args: {
    invoiceId: v.id("invoices"),
    status: v.union(
      v.literal("draft"),
      v.literal("sent"),
      v.literal("paid"),
      v.literal("overdue"),
      v.literal("cancelled")
    ),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Update invoice status
    await ctx.db.patch(args.invoiceId, {
      status: args.status,
      updatedAt: now,
    });

    // If marking as paid, also update related entities
    if (args.status === "paid") {
      // Get the invoice to find related entities
      const invoice = await ctx.db.get(args.invoiceId);
      if (!invoice) {
        throw new Error("Invoice not found");
      }

      // Update offline sale status to paid
      if (invoice.offlineSaleId) {
        try {
          await ctx.db.patch(invoice.offlineSaleId, {
            status: "paid",
          });
        } catch (error) {
          console.error("Failed to update offline sale status:", error);
          // Continue with other updates even if this fails
        }
      }

      // Update product status to sold if it exists
      if (invoice.productId) {
        try {
          await ctx.db.patch(invoice.productId, {
            status: "sold",
            updatedAt: now,
          });
        } catch (error) {
          console.error("Failed to update product status:", error);
          // Continue with other updates even if this fails
        }
      }

      // Update invoice with paid date
      await ctx.db.patch(args.invoiceId, {
        paidDate: now,
        updatedAt: now,
      });

      // Log the payment event
      try {
        await ctx.db.insert("analytics", {
          eventType: "invoice_paid",
          userId: invoice.sellerId,
          sellerId: invoice.sellerId,
          productId: invoice.productId,
          timestamp: now,
          metadata: {
            source: args.invoiceId,
            category: "sales",
            revenue: invoice.totalAmount,
            paymentMethod: invoice.paymentMethod,
          },
        });
      } catch (error) {
        console.error("Failed to log payment event:", error);
        // Don't fail the main operation if logging fails
      }
    }
  },
});

export const bulkUpdateInvoiceStatus = mutation({
  args: {
    invoiceIds: v.array(v.id("invoices")),
    status: v.union(
      v.literal("draft"),
      v.literal("sent"),
      v.literal("paid"),
      v.literal("overdue"),
      v.literal("cancelled")
    ),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const results: { invoiceId: Id<"invoices">; success: boolean; error?: string }[] = [];

    for (const invoiceId of args.invoiceIds) {
      try {
        // Update invoice status
        await ctx.db.patch(invoiceId, {
          status: args.status,
          updatedAt: now,
        });

        // If marking as paid, also update related entities
        if (args.status === "paid") {
          const invoice = await ctx.db.get(invoiceId);
          if (invoice) {
            // Update offline sale status to paid
            if (invoice.offlineSaleId) {
              try {
                await ctx.db.patch(invoice.offlineSaleId, {
                  status: "paid",
                });
              } catch (error) {
                console.error(`Failed to update offline sale ${invoice.offlineSaleId}:`, error);
              }
            }

            // Update product status to sold if it exists
            if (invoice.productId) {
              try {
                await ctx.db.patch(invoice.productId, {
                  status: "sold",
                  updatedAt: now,
                });
              } catch (error) {
                console.error(`Failed to update product ${invoice.productId}:`, error);
              }
            }

            // Update invoice with paid date
            await ctx.db.patch(invoiceId, {
              paidDate: now,
              updatedAt: now,
            });

            // Log the payment event
            try {
              await ctx.db.insert("analytics", {
                eventType: "invoice_paid",
                userId: invoice.sellerId,
                sellerId: invoice.sellerId,
                productId: invoice.productId,
                timestamp: now,
                metadata: {
                  source: invoiceId,
                  category: "sales",
                  revenue: invoice.totalAmount,
                  paymentMethod: invoice.paymentMethod,
                },
              });
            } catch (error) {
              console.error("Failed to log payment event:", error);
            }
          }
        }

        results.push({ invoiceId, success: true });
      } catch (error) {
        console.error(`Failed to update invoice ${invoiceId}:`, error);
        results.push({ invoiceId, success: false, error: error instanceof Error ? error.message : String(error) });
      }
    }

    return results;
  },
});

export const markInvoiceAsPaid = mutation({
  args: {
    invoiceId: v.id("invoices"),
    paymentMethod: v.optional(v.string()),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Get the invoice
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    // Update invoice status to paid
    await ctx.db.patch(args.invoiceId, {
      status: "paid",
      paidDate: now,
      updatedAt: now,
      notes: args.notes ? `${invoice.notes || ""}\n\nPayment received: ${args.notes}`.trim() : invoice.notes,
    });

    // Update offline sale status to paid
    if (invoice.offlineSaleId) {
      try {
        await ctx.db.patch(invoice.offlineSaleId, {
          status: "paid",
        });
      } catch (error) {
        console.error("Failed to update offline sale status:", error);
      }
    }

    // Update product status to sold if it exists
    if (invoice.productId) {
      try {
        await ctx.db.patch(invoice.productId, {
          status: "sold",
          updatedAt: now,
        });
      } catch (error) {
        console.error("Failed to update product status:", error);
      }
    }

    // Log the payment event
    try {
      await ctx.db.insert("analytics", {
        eventType: "invoice_paid",
        userId: invoice.sellerId,
        sellerId: invoice.sellerId,
        productId: invoice.productId,
        timestamp: now,
        metadata: {
          source: args.invoiceId,
          category: "sales",
          revenue: invoice.totalAmount,
          paymentMethod: args.paymentMethod || invoice.paymentMethod,
        },
      });
    } catch (error) {
      console.error("Failed to log payment event:", error);
    }

    return { success: true, invoiceId: args.invoiceId };
  },
});

export const convertDraftToInvoice = mutation({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    const now = Date.now();
    
    // Verify the invoice belongs to the user and is a draft
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice || invoice.sellerId !== user._id || invoice.status !== "draft") {
      throw new Error("Invoice not found or not a draft");
    }

    // Update the invoice status from draft to sent
    await ctx.db.patch(args.invoiceId, {
      status: "sent",
      sentAt: now,
      updatedAt: now,
    });

    return args.invoiceId;
  },
});









export const sendInvoiceEmail = action({
  args: {
    invoiceId: v.id("invoices"),
    customMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.runQuery(internal.invoices.getInvoiceInternal, {
      invoiceId: args.invoiceId,
    });

    if (!invoice || !invoice.offlineSale) {
      throw new Error("Invoice or sale not found");
    }

    // Send email with invoice data (no PDF attachment)
    await ctx.runAction(internal.invoices.sendInvoiceEmailInternal, {
      invoiceId: args.invoiceId,
      recipientEmail: invoice.offlineSale.clientEmail,
      recipientName: invoice.offlineSale.clientName,
      customMessage: args.customMessage,
    });

    // Update invoice status and email log
    await ctx.runMutation(internal.invoices.logEmailSent, {
      invoiceId: args.invoiceId,
      recipientEmail: invoice.offlineSale.clientEmail,
    });
  },
});

export const sendInvoiceEmailInternal = internalAction({
  args: {
    invoiceId: v.id("invoices"),
    recipientEmail: v.string(),
    recipientName: v.string(),
    customMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // This would integrate with your email service
    // For now, we'll use the existing sendEmail function
    const { sendEmail } = await import("./lib/email");
    
    await sendEmail(ctx, {
      to: args.recipientEmail,
      subject: `Invoice from MODA - ${args.invoiceId}`,
      react: {
        // We'll create an invoice email template
        type: "invoice",
        data: {
          recipientName: args.recipientName,
          invoiceId: args.invoiceId,
          customMessage: args.customMessage,
        },
      },
    });
  },
});

export const logEmailSent = internalMutation({
  args: {
    invoiceId: v.id("invoices"),
    recipientEmail: v.string(),
  },
  handler: async (ctx, args) => {
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice) {
      throw new Error("Invoice not found");
    }

    const emailLogString = `${new Date().toISOString()} - Sent to ${args.recipientEmail}`;

    await ctx.db.patch(args.invoiceId, {
      emailsSent: [...invoice.emailsSent, emailLogString],
      status: invoice.status === "draft" ? "sent" : invoice.status,
      updatedAt: Date.now(),
    });
  },
});

export const getStorageUrl = action({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args): Promise<string | null> => {
    try {
      const url = await ctx.storage.getUrl(args.storageId);
      return url;
    } catch (error) {
      console.error("Error getting storage URL:", error);
      return null;
    }
  },
});

export const deleteInvoiceDraft = mutation({
  args: {
    invoiceId: v.id("invoices"),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Verify the invoice belongs to the user and is a draft
    const invoice = await ctx.db.get(args.invoiceId);
    if (!invoice || invoice.sellerId !== user._id || invoice.status !== "draft") {
      throw new Error("Invoice not found or not a draft");
    }

    // Delete the draft invoice
    await ctx.db.delete(args.invoiceId);
    
    // Also delete the associated offline sale if it exists
    if (invoice.offlineSaleId) {
      await ctx.db.delete(invoice.offlineSaleId);
    }

    return args.invoiceId;
  },
});



