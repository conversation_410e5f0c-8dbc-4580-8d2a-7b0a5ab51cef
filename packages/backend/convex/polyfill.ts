import { v } from "convex/values";
import { internalAction } from "./_generated/server";
import { sendEmail } from "./lib/email";
import { render } from "@repo/email";
import SignInOTP from "@repo/email/templates/signInOTP";

// polyfill MessageChannel without using node:events
if (typeof MessageChannel === "undefined") {
  class MockMessagePort {
    onmessage: ((ev: MessageEvent) => void) | undefined;
    onmessageerror: ((ev: MessageEvent) => void) | undefined;

    close() {}
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    postMessage(_message: unknown, _transfer: Transferable[] = []) {}
    start() {}
    addEventListener() {}
    removeEventListener() {}
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    dispatchEvent(_event: Event): boolean {
      return false;
    }
  }

  class MockMessageChannel {
    port1: MockMessagePort;
    port2: MockMessagePort;

    constructor() {
      this.port1 = new MockMessagePort();
      this.port2 = new MockMessagePort();
    }
  }

  globalThis.MessageChannel =
    MockMessageChannel as unknown as typeof MessageChannel;
}

export const sendSignInOTP = internalAction({
   args: { to: v.string(), code: v.string() },
   handler: async (ctx, { to, code }) => {
      await sendEmail(ctx, {
         to,
         subject: "Sign in to your account - Elite Moda Club",
         react: await render(SignInOTP({ code })),
      });
   },
});
