import { mutation } from "./_generated/server";
import { v } from "convex/values";

/**
 * Seed default Reddit-style awards
 */
export const seedDefaultAwards = mutation({
  args: {},
  returns: v.any(),
  handler: async (ctx) => {
    // Check if awards already exist
    const existingAwards = await ctx.db
      .query("forumAwards")
      .collect();
    
    if (existingAwards.length > 0) {
      return { message: "Awards already seeded" };
    }
    
    const awards = [
      {
        name: "Gold",
        icon: "🏆",
        cost: 500,
        description: "Shows exceptional quality or contribution",
        color: "#FFD700",
        isActive: true,
      },
      {
        name: "Silver",
        icon: "🥈",
        cost: 100,
        description: "Shows appreciation for good content",
        color: "#C0C0C0",
        isActive: true,
      },
      {
        name: "Helpful",
        icon: "🤝",
        cost: 50,
        description: "Thank you for being helpful",
        color: "#00A6ED",
        isActive: true,
      },
      {
        name: "Wholesome",
        icon: "🤗",
        cost: 50,
        description: "Makes everyone smile",
        color: "#FF69B4",
        isActive: true,
      },
      {
        name: "Rocket Like",
        icon: "🚀",
        cost: 75,
        description: "To the moon!",
        color: "#FF4500",
        isActive: true,
      },
      {
        name: "Mind Blown",
        icon: "🤯",
        cost: 100,
        description: "Incredible insight",
        color: "#9932CC",
        isActive: true,
      },
      {
        name: "Fire",
        icon: "🔥",
        cost: 50,
        description: "Hot take!",
        color: "#FF6347",
        isActive: true,
      },
      {
        name: "100",
        icon: "💯",
        cost: 75,
        description: "Perfect response",
        color: "#FFD700",
        isActive: true,
      },
      {
        name: "Platinum",
        icon: "💎",
        cost: 1000,
        description: "Extraordinary contribution to the community",
        color: "#E5E4E2",
        isActive: true,
      },
      {
        name: "Take My Energy",
        icon: "⚡",
        cost: 25,
        description: "Support and encouragement",
        color: "#00CED1",
        isActive: true,
      },
    ];
    
    for (const award of awards) {
      await ctx.db.insert("forumAwards", award);
    }
    
    return { message: `Seeded ${awards.length} awards` };
  },
});
