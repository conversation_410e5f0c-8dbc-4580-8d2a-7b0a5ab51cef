import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUser, requireAuth } from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";

/**
 * Get user's notifications
 */
export const getUserNotifications = query({
  args: {
    limit: v.optional(v.number()),
    unreadOnly: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      return {
        notifications: [],
        unreadCount: 0,
        total: 0,
      };
    }

    const limit = args.limit || 20;
    let query = ctx.db
      .query("notifications")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .order("desc");

    // Filter for unread only if requested
    if (args.unreadOnly) {
      query = query.filter((q) => q.eq(q.field("read"), false));
    }

    const notifications = await query.collect();

    // Get unread count
    const unreadNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_userId_read", (q) => 
        q.eq("userId", user._id).eq("read", false)
      )
      .collect();

    return {
      notifications: notifications.slice(0, limit),
      unreadCount: unreadNotifications.length,
      total: notifications.length,
    };
  },
});

/**
 * Mark notification as read
 */
export const markNotificationAsRead = mutation({
  args: {
    notificationId: v.id("notifications"),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    const notification = await ctx.db.get(args.notificationId);
    if (!notification) {
      throw new Error("Notification not found");
    }

    if (notification.userId !== user._id) {
      throw new Error("You can only mark your own notifications as read");
    }

    await ctx.db.patch(args.notificationId, {
      read: true,
    });

    return true;
  },
});

/**
 * Mark all notifications as read
 */
export const markAllNotificationsAsRead = mutation({
  args: {},
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    const unreadNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_userId_read", (q) => 
        q.eq("userId", user._id).eq("read", false)
      )
      .collect();

    // Mark all unread notifications as read
    for (const notification of unreadNotifications) {
      await ctx.db.patch(notification._id, {
        read: true,
      });
    }

    return unreadNotifications.length;
  },
});

/**
 * Delete notification
 */
export const deleteNotification = mutation({
  args: {
    notificationId: v.id("notifications"),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    const notification = await ctx.db.get(args.notificationId);
    if (!notification) {
      throw new Error("Notification not found");
    }

    if (notification.userId !== user._id) {
      throw new Error("You can only delete your own notifications");
    }

    await ctx.db.delete(args.notificationId);

    return true;
  },
});

/**
 * Delete all read notifications
 */
export const deleteAllReadNotifications = mutation({
  args: {},
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    const readNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_userId_read", (q) => 
        q.eq("userId", user._id).eq("read", true)
      )
      .collect();

    // Delete all read notifications
    for (const notification of readNotifications) {
      await ctx.db.delete(notification._id);
    }

    return readNotifications.length;
  },
});

/**
 * Get unread notification count
 */
export const getUnreadNotificationCount = query({
  args: {},
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      return 0;
    }

    const unreadNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_userId_read", (q) => 
        q.eq("userId", user._id).eq("read", false)
      )
      .collect();

    return unreadNotifications.length;
  },
});
