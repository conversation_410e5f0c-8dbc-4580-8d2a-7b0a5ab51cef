# MODA Order Management System

This document describes the comprehensive order management system for MODA, including order creation, status updates, cancellations, delivery confirmation, and dispute handling for the luxury marketplace platform.

## Overview

The order management system provides secure, validated workflows for processing luxury item purchases with proper authorization, payment integration, notification triggers, and buyer protection mechanisms. It supports the complete order lifecycle from creation to delivery confirmation.

## Core Features

### ✅ Order Creation & Processing
- **Subscription-gated purchasing** with validation
- **Product availability checking** and reservation
- **Automatic pricing calculation** (tax, shipping, commission)
- **Payment method integration** with Stripe
- **Order number generation** and tracking

### ✅ Status Management
- **Validated status transitions** with proper authorization
- **Tracking integration** for shipping updates
- **Automatic notifications** for status changes
- **Delivery confirmation** with payment release
- **Admin override** capabilities for special cases

### ✅ Cancellation & Refunds
- **Time-based cancellation rules** (24-hour buyer window)
- **Partial refund support** for special circumstances
- **Automatic product re-listing** after cancellation
- **Refund processing** integration points
- **Cancellation reason tracking** for analytics

### ✅ Buyer Protection
- **Dispute system** with evidence upload
- **Multiple dispute reasons** (not received, damaged, wrong item)
- **Review and rating system** for seller feedback
- **Auto-delivery confirmation** via tracking
- **Admin dispute resolution** tools

## Mutations Reference

### 1. createOrder

Process marketplace purchases with comprehensive validation and payment integration.

```typescript
await ctx.runMutation(api.orderManagement.createOrder, {
  productId: "product_id",
  shippingAddress: {
    street: "123 Fifth Avenue, Apt 4B",
    city: "New York",
    state: "NY",
    zipCode: "10001",
    country: "USA",
  },
  paymentMethodId: "pm_1234567890abcdef", // Stripe payment method
  notes: "Please handle with care - gift for anniversary",
});
```

**Features:**
- **Subscription validation**: Active subscription required for purchases
- **Product availability**: Prevents double-purchasing and validates status
- **Self-purchase prevention**: Users cannot buy their own products
- **Seller verification**: Ensures seller is approved and active
- **Pricing calculation**: Automatic tax, shipping, and commission calculation
- **Order reservation**: Temporarily reserves product during payment processing

**Pricing Breakdown:**
- **Product price**: Base item price
- **Shipping cost**: From product or default rates
- **Tax calculation**: State-based tax rates (integrates with tax services)
- **Commission**: Platform fee (default 10%, configurable per seller)
- **Total amount**: Sum of all components

**Returns:**
- **Order ID**: Unique order identifier
- **Order number**: Human-readable order reference
- **Total amount**: Final price including all fees
- **Payment client secret**: For frontend payment confirmation

### 2. updateOrderStatus

Update order status with tracking information and notifications.

```typescript
await ctx.runMutation(api.orderManagement.updateOrderStatus, {
  orderId: "order_id",
  status: "shipped",
  trackingNumber: "1Z999AA1234567890",
  carrier: "UPS",
  estimatedDelivery: Date.now() + (3 * 24 * 60 * 60 * 1000),
  notes: "Shipped via UPS Next Day Air with signature required",
  sendNotification: true,
});
```

**Status Transitions:**
- **pending → confirmed**: Seller accepts order
- **confirmed → shipped**: Seller ships with tracking
- **shipped → delivered**: Buyer or system confirms delivery
- **Any status → cancelled**: Cancellation with refund

**Authorization Rules:**
- **Sellers**: Can confirm and ship orders
- **Buyers**: Can confirm delivery
- **Admins**: Can perform any status update
- **System**: Can auto-confirm delivery via tracking

**Features:**
- **Tracking integration**: Carrier and tracking number storage
- **Estimated delivery**: Delivery date predictions
- **Status notes**: Additional context for status changes
- **Automatic notifications**: Configurable notification sending
- **Payment processing**: Automatic payment release on delivery

### 3. cancelOrder

Handle order cancellations with refund processing and product re-listing.

```typescript
await ctx.runMutation(api.orderManagement.cancelOrder, {
  orderId: "order_id",
  reason: "Item damaged during preparation for shipping",
  refundAmount: 4500.00, // Optional partial refund
  sendNotification: true,
});
```

**Cancellation Rules:**
- **Buyers**: 24-hour cancellation window for pending orders
- **Sellers**: Can cancel before shipping
- **Admins**: Can cancel any order at any time
- **System**: Cannot cancel delivered orders

**Features:**
- **Partial refunds**: Support for partial refund amounts
- **Reason tracking**: Cancellation reason for analytics
- **Product re-listing**: Automatic return to active status
- **Refund processing**: Integration with payment processor
- **Notification system**: Automatic buyer/seller notifications

### 4. confirmDelivery

Buyer confirmation of delivery with rating and payment release.

```typescript
await ctx.runMutation(api.orderManagement.confirmDelivery, {
  orderId: "order_id",
  rating: 5, // 1-5 star rating
  review: "Excellent seller! Item exactly as described.",
  requestFeedback: true,
});
```

**Features:**
- **Buyer-only access**: Only buyers can confirm delivery
- **Rating system**: 1-5 star seller ratings
- **Review collection**: Optional written reviews
- **Payment release**: Automatic payment to seller
- **Seller metrics**: Updates seller profile statistics
- **Feedback requests**: Optional mutual feedback requests

**Payment Processing:**
- **Seller earnings**: Product price minus commission
- **Commission collection**: Platform fee collection
- **Payment timing**: Immediate release on confirmation
- **Seller statistics**: Updates total sales and earnings

### 5. autoConfirmDelivery

System-triggered delivery confirmation via shipping carrier webhooks.

```typescript
await ctx.runMutation(api.orderManagement.autoConfirmDelivery, {
  orderId: "order_id",
  trackingConfirmation: {
    carrier: "UPS",
    trackingNumber: "1Z999AA1234567890",
    deliveredAt: 1234567890000,
    signedBy: "J. SMITH",
  },
});
```

**Features:**
- **Admin/system only**: Restricted to system processes
- **Carrier integration**: Webhook-triggered confirmations
- **Tracking verification**: Validates delivery via carrier data
- **Automatic processing**: No buyer action required
- **Signature capture**: Delivery signature information

### 6. disputeOrder

Buyer protection system for order disputes with evidence collection.

```typescript
await ctx.runMutation(api.orderManagement.disputeOrder, {
  orderId: "order_id",
  reason: "item_not_as_described",
  description: "Item has significant wear not mentioned in listing",
  evidence: ["photo1_storage_id", "photo2_storage_id"],
});
```

**Dispute Reasons:**
- **item_not_received**: Package never arrived
- **item_not_as_described**: Item differs from listing
- **item_damaged**: Item damaged in shipping
- **wrong_item**: Incorrect item received
- **other**: Custom dispute reason

**Features:**
- **Evidence upload**: Photo evidence support
- **Dispute tracking**: Complete dispute lifecycle management
- **Order status update**: Marks order as disputed
- **Admin notification**: Alerts for dispute resolution
- **Buyer protection**: Comprehensive buyer safeguards

## Payment Integration

### Stripe Integration Points

```typescript
// Payment processing integration points (to be implemented)

// 1. Create payment intent on order creation
const paymentIntent = await stripe.paymentIntents.create({
  amount: Math.round(totalAmount * 100), // Convert to cents
  currency: 'usd',
  payment_method: paymentMethodId,
  confirmation_method: 'manual',
  confirm: true,
  metadata: { orderId },
});

// 2. Release payment to seller on delivery
const transfer = await stripe.transfers.create({
  amount: Math.round(sellerEarnings * 100),
  currency: 'usd',
  destination: sellerStripeAccountId,
  metadata: { orderId },
});

// 3. Process refund on cancellation
const refund = await stripe.refunds.create({
  payment_intent: paymentIntentId,
  amount: Math.round(refundAmount * 100),
  reason: 'requested_by_customer',
  metadata: { orderId },
});
```

### Payment Flow

1. **Order Creation**: Create payment intent with Stripe
2. **Payment Confirmation**: Frontend confirms payment
3. **Order Processing**: Payment held in escrow
4. **Delivery Confirmation**: Payment released to seller
5. **Commission Collection**: Platform fee automatically deducted

## Notification System

### Notification Triggers

```typescript
// Notification integration points (to be implemented)

// Order status notifications
await sendNotification({
  type: 'order_status_update',
  recipients: [buyerId, sellerId],
  data: {
    orderId,
    status,
    trackingNumber,
    estimatedDelivery,
  },
});

// Cancellation notifications
await sendNotification({
  type: 'order_cancelled',
  recipients: [buyerId, sellerId],
  data: {
    orderId,
    reason,
    refundAmount,
  },
});

// Dispute notifications
await sendNotification({
  type: 'order_disputed',
  recipients: [sellerId, 'admin'],
  data: {
    orderId,
    disputeId,
    reason,
  },
});
```

### Notification Types

- **Order created**: Buyer and seller confirmation
- **Order confirmed**: Seller acceptance notification
- **Order shipped**: Tracking information to buyer
- **Order delivered**: Delivery confirmation
- **Order cancelled**: Cancellation and refund details
- **Order disputed**: Dispute creation alerts
- **Payment released**: Seller payment notification

## Security & Authorization

### Order Access Control

```typescript
// Authorization validation for order operations
const canUpdateOrder = (user, order, action) => {
  // Sellers can confirm and ship
  if (['confirmed', 'shipped'].includes(action) && order.sellerId === user._id) {
    return true;
  }
  
  // Buyers can confirm delivery and cancel (within limits)
  if (['delivered', 'cancelled'].includes(action) && order.buyerId === user._id) {
    return true;
  }
  
  // Admins can perform any action
  if (user.userType === 'admin') {
    return true;
  }
  
  return false;
};
```

### Data Protection

- **Order privacy**: Users can only access their own orders
- **Payment security**: Sensitive payment data handled by Stripe
- **Dispute confidentiality**: Disputes visible to involved parties and admins
- **Admin oversight**: Complete order visibility for support and compliance

## Error Handling

### Common Error Scenarios

```typescript
try {
  await ctx.runMutation(api.orderManagement.createOrder, orderData);
} catch (error) {
  switch (error.message) {
    case "Active subscription required to make purchases":
      // Redirect to subscription page
      break;
    case "Product is not available for purchase":
      // Show product unavailable message
      break;
    case "Cannot purchase your own product":
      // Show self-purchase error
      break;
    case "Seller is not available for transactions":
      // Show seller unavailable message
      break;
  }
}
```

### Validation Errors

- **Subscription status**: Active subscription required for purchases
- **Product availability**: Product must be active and available
- **Seller verification**: Seller must be approved and active
- **Payment validation**: Valid payment method required
- **Address validation**: Complete shipping address required

## Integration Examples

### React Order Flow

```typescript
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";

function OrderFlow({ productId, paymentMethodId }) {
  const createOrder = useMutation(api.orderManagement.createOrder);
  const [shippingAddress, setShippingAddress] = useState({});
  
  const handleCreateOrder = async () => {
    try {
      const result = await createOrder({
        productId,
        shippingAddress,
        paymentMethodId,
      });
      
      // Handle successful order creation
      showSuccess(`Order ${result.orderNumber} created successfully!`);
      redirectToOrderTracking(result.orderId);
    } catch (error) {
      showError(error.message);
    }
  };
  
  return (
    <OrderForm 
      onSubmit={handleCreateOrder}
      shippingAddress={shippingAddress}
      onAddressChange={setShippingAddress}
    />
  );
}
```

### Seller Order Management

```typescript
function SellerOrderDashboard() {
  const updateOrderStatus = useMutation(api.orderManagement.updateOrderStatus);
  
  const handleShipOrder = async (orderId, trackingInfo) => {
    try {
      await updateOrderStatus({
        orderId,
        status: "shipped",
        trackingNumber: trackingInfo.trackingNumber,
        carrier: trackingInfo.carrier,
        estimatedDelivery: trackingInfo.estimatedDelivery,
        sendNotification: true,
      });
      
      showSuccess("Order shipped successfully!");
    } catch (error) {
      showError(error.message);
    }
  };
  
  return <OrderManagementTable onShip={handleShipOrder} />;
}
```

### Buyer Order Tracking

```typescript
function OrderTracking({ orderId }) {
  const confirmDelivery = useMutation(api.orderManagement.confirmDelivery);
  const disputeOrder = useMutation(api.orderManagement.disputeOrder);
  
  const handleConfirmDelivery = async (rating, review) => {
    try {
      await confirmDelivery({
        orderId,
        rating,
        review,
        requestFeedback: true,
      });
      
      showSuccess("Delivery confirmed! Payment released to seller.");
    } catch (error) {
      showError(error.message);
    }
  };
  
  return (
    <OrderTrackingInterface 
      orderId={orderId}
      onConfirmDelivery={handleConfirmDelivery}
      onDispute={disputeOrder}
    />
  );
}
```

## Best Practices

### 1. Order State Management

```typescript
// Always validate state transitions
const validateStatusTransition = (currentStatus, newStatus) => {
  const validTransitions = {
    pending: ["confirmed", "cancelled"],
    confirmed: ["shipped", "cancelled"],
    shipped: ["delivered", "cancelled"],
    delivered: [], // Final state
    cancelled: [], // Final state
  };
  
  return validTransitions[currentStatus]?.includes(newStatus);
};
```

### 2. Payment Security

```typescript
// Never store sensitive payment data
const orderData = {
  paymentMethodId: "pm_stripe_id", // Stripe token only
  // Never store: card numbers, CVV, etc.
};

// Use Stripe webhooks for payment confirmations
const handleStripeWebhook = (event) => {
  if (event.type === 'payment_intent.succeeded') {
    // Confirm order payment
  }
};
```

### 3. Error Recovery

```typescript
// Implement retry logic for failed operations
const retryOperation = async (operation, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await delay(1000 * Math.pow(2, i)); // Exponential backoff
    }
  }
};
```

## File Structure

```
packages/backend/convex/
├── orderManagement.ts              # Core order mutations
├── orderManagement.test.ts         # Usage examples and tests
└── ORDER_MANAGEMENT.md             # This documentation
```

The order management system provides comprehensive, secure order processing with payment integration, buyer protection, and complete lifecycle management for the MODA luxury marketplace platform.
