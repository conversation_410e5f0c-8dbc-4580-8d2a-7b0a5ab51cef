import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { ConvexError } from "convex/values";

// Generate a unique request number for warranty checks
function generateRequestNumber(): string {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `HV-WC-${timestamp}-${random}`;
}

// Submit a new warranty check request
export const submitWarrantyCheck = mutation({
  args: {
    // Product Information
    productName: v.string(),
    productBrand: v.string(),
    productModel: v.optional(v.string()),
    serialNumber: v.optional(v.string()),
    purchaseDate: v.optional(v.string()),
    purchasePrice: v.optional(v.string()),
    
    // Verification Type
    verificationType: v.union(
      v.literal("authenticity"),
      v.literal("warranty_status"),
      v.literal("both")
    ),
    
    // Documentation Available
    hasOriginalReceipt: v.boolean(),
    hasWarrantyCard: v.boolean(),
    hasOriginalBox: v.boolean(),
    hasCertificates: v.boolean(),
    
    // Contact Information
    contactName: v.string(),
    contactEmail: v.string(),
    contactPhone: v.optional(v.string()),
    
    // Additional Information
    concernsDescription: v.optional(v.string()),
    urgencyLevel: v.union(v.literal("low"), v.literal("medium"), v.literal("high")),
    productImages: v.optional(v.array(v.string())),
  },
  returns: v.id("warrantyChecks"),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Authentication required");
    }

    // Get user information
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .unique();

    if (!user) {
      throw new ConvexError("User not found");
    }

    const now = Date.now();
    const requestNumber = generateRequestNumber();

    // Create the warranty check request
    const requestId = await ctx.db.insert("warrantyChecks", {
      requestNumber,
      userId: user._id,
      userEmail: user.email,
      userName: user.name,
      
      // Product Information
      productName: args.productName,
      productBrand: args.productBrand,
      productModel: args.productModel,
      serialNumber: args.serialNumber,
      purchaseDate: args.purchaseDate,
      purchasePrice: args.purchasePrice,
      
      // Verification Type
      verificationType: args.verificationType,
      
      // Documentation Available
      hasOriginalReceipt: args.hasOriginalReceipt,
      hasWarrantyCard: args.hasWarrantyCard,
      hasOriginalBox: args.hasOriginalBox,
      hasCertificates: args.hasCertificates,
      
      // Contact Information
      contactName: args.contactName,
      contactEmail: args.contactEmail,
      contactPhone: args.contactPhone,
      
      // Additional Information
      concernsDescription: args.concernsDescription,
      urgencyLevel: args.urgencyLevel,
      productImages: args.productImages,
      
      // Default values
      status: "pending",
      submittedAt: now,
      updatedAt: now,
    });

    return requestId;
  },
});

// Get user's warranty check requests
export const getUserWarrantyChecks = query({
  args: {},
  returns: v.any(),
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .unique();

    if (!user) {
      return [];
    }

    const checks = await ctx.db
      .query("warrantyChecks")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .order("desc")
      .collect();

    return checks.map(check => ({
      _id: check._id,
      _creationTime: check._creationTime,
      requestNumber: check.requestNumber,
      productName: check.productName,
      productBrand: check.productBrand,
      verificationType: check.verificationType,
      status: check.status,
      urgencyLevel: check.urgencyLevel,
      authenticationResult: check.authenticationResult,
      warrantyStatus: check.warrantyStatus,
      submittedAt: check.submittedAt,
      updatedAt: check.updatedAt,
      completedAt: check.completedAt,
      certificateUrl: check.certificateUrl,
    }));
  },
});

// Get all warranty check requests (admin only)
export const getAllWarrantyChecks = query({
  args: {
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("under_review"),
      v.literal("needs_payment"),
      v.literal("in_process"),
      v.literal("completed_approved"),
      v.literal("completed_rejected")
    )),
    verificationType: v.optional(v.union(
      v.literal("authenticity"),
      v.literal("warranty_status"),
      v.literal("both")
    )),
    limit: v.optional(v.number()),
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Authentication required");
    }

    // Check if user is admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .unique();

    if (!user || user.userType !== "admin") {
      throw new ConvexError("Admin access required");
    }

    let checks;

    if (args.status) {
      checks = await ctx.db
        .query("warrantyChecks")
        .withIndex("by_status", (q) => q.eq("status", args.status!))
        .order("desc")
        .collect();
    } else if (args.verificationType) {
      checks = await ctx.db
        .query("warrantyChecks")
        .withIndex("by_verificationType", (q) => q.eq("verificationType", args.verificationType!))
        .order("desc")
        .collect();
    } else {
      checks = await ctx.db
        .query("warrantyChecks")
        .withIndex("by_submittedAt")
        .order("desc")
        .collect();
    }

    if (args.limit) {
      checks = checks.slice(0, args.limit);
    }

    return checks;
  },
});

// Update warranty check status and results (admin only)
export const updateWarrantyCheckStatus = mutation({
  args: {
    checkId: v.id("warrantyChecks"),
    status: v.union(
      v.literal("pending"),
      v.literal("under_review"),
      v.literal("needs_payment"),
      v.literal("in_process"),
      v.literal("completed_approved"),
      v.literal("completed_rejected")
    ),
    authenticationResult: v.optional(v.union(
      v.literal("authentic"),
      v.literal("not_authentic"),
      v.literal("inconclusive")
    )),
    warrantyStatus: v.optional(v.union(
      v.literal("valid"),
      v.literal("expired"),
      v.literal("void"),
      v.literal("unknown")
    )),
    adminNotes: v.optional(v.string()),
    authenticationReport: v.optional(v.string()),
    certificateUrl: v.optional(v.string()),
    serviceFee: v.optional(v.number()),
    assignedExpertId: v.optional(v.id("users")),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Authentication required");
    }

    // Check if user is admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .unique();

    if (!user || user.userType !== "admin") {
      throw new ConvexError("Admin access required");
    }

    const check = await ctx.db.get(args.checkId);
    if (!check) {
      throw new ConvexError("Warranty check not found");
    }

    const now = Date.now();
    const updateData: any = {
      status: args.status,
      updatedAt: now,
    };

    if (args.authenticationResult !== undefined) {
      updateData.authenticationResult = args.authenticationResult;
    }

    if (args.warrantyStatus !== undefined) {
      updateData.warrantyStatus = args.warrantyStatus;
    }

    if (args.adminNotes !== undefined) {
      updateData.adminNotes = args.adminNotes;
    }

    if (args.authenticationReport !== undefined) {
      updateData.authenticationReport = args.authenticationReport;
    }

    if (args.certificateUrl !== undefined) {
      updateData.certificateUrl = args.certificateUrl;
    }

    if (args.serviceFee !== undefined) {
      updateData.serviceFee = args.serviceFee;
    }

    if (args.assignedExpertId !== undefined) {
      updateData.assignedExpertId = args.assignedExpertId;
    }

    // Set completed timestamp if status is completed
    if (["completed_approved", "completed_rejected"].includes(args.status)) {
      updateData.completedAt = now;
    }

    await ctx.db.patch(args.checkId, updateData);

    return null;
  },
});

// Get warranty check statistics (admin only)
export const getWarrantyCheckStats = query({
  args: {},
  returns: v.any(),
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Authentication required");
    }

    // Check if user is admin
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .unique();

    if (!user || user.userType !== "admin") {
      throw new ConvexError("Admin access required");
    }

    const allChecks = await ctx.db
      .query("warrantyChecks")
      .collect();

    const completedChecks = allChecks.filter(c => 
      c.status === "completed_approved" || c.status === "completed_rejected"
    );

    const approvedChecks = allChecks.filter(c => c.status === "completed_approved");
    const rejectedChecks = allChecks.filter(c => c.status === "completed_rejected");

    const approvalRate = completedChecks.length > 0 
      ? Math.round((approvedChecks.length / completedChecks.length) * 100)
      : 0;

    const rejectionRate = completedChecks.length > 0 
      ? Math.round((rejectedChecks.length / completedChecks.length) * 100)
      : 0;

    const stats = {
      total: allChecks.length,
      pending: allChecks.filter(c => c.status === "pending").length,
      underReview: allChecks.filter(c => c.status === "under_review").length,
      needsPayment: allChecks.filter(c => c.status === "needs_payment").length,
      inProcess: allChecks.filter(c => c.status === "in_process").length,
      completedApproved: approvedChecks.length,
      completedRejected: rejectedChecks.length,
      totalCompleted: completedChecks.length,
      approvalRate,
      rejectionRate,
      
      // Verification type breakdown
      authenticityOnly: allChecks.filter(c => c.verificationType === "authenticity").length,
      warrantyOnly: allChecks.filter(c => c.verificationType === "warranty_status").length,
      both: allChecks.filter(c => c.verificationType === "both").length,
    };

    return stats;
  },
});
