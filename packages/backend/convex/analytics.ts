import { v } from "convex/values";
import { query } from "./_generated/server";
import { requireAuth } from "./lib/auth_utils";
import { Id } from "./_generated/dataModel";

// Time-based sales analytics
export const getSalesAnalytics = query({
  args: {
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    period: v.optional(v.union(
      v.literal("daily"),
      v.literal("weekly"), 
      v.literal("monthly"),
      v.literal("yearly")
    )),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    const now = Date.now();
    const startDate = args.startDate || (now - (30 * 24 * 60 * 60 * 1000)); // Default 30 days
    const endDate = args.endDate || now;

    // Get offline sales in date range
    const offlineSales = await ctx.db
      .query("offlineSales")
      .withIndex("sellerId", (q) => q.eq("sellerId", user._id))
      .filter((q) => 
        q.and(
          q.gte(q.field("saleDate"), startDate),
          q.lte(q.field("saleDate"), endDate)
        )
      )
      .collect();

    // Get product details for each sale
    const salesWithProducts = await Promise.all(
      offlineSales.map(async (sale) => {
        const product = await ctx.db.get(sale.productId as Id<"products">);
        return {
          ...sale,
          product,
          category: product?.category || null,
        };
      })
    );

    // Calculate metrics
    const totalRevenue = salesWithProducts.reduce((sum, sale) => sum + sale.salePrice, 0);
    const totalItems = salesWithProducts.length;
    const averageSalePrice = totalItems > 0 ? totalRevenue / totalItems : 0;

    // Group by time period if specified
    let timeSeriesData: Array<{ date: string; revenue: number; sales: number }> = [];
    
    if (args.period) {
      const groupedData = new Map<string, { revenue: number; sales: number }>();
      
      salesWithProducts.forEach((sale) => {
        const date = new Date(sale.saleDate);
        let key: string;
        
        switch (args.period) {
          case "daily":
            key = date.toISOString().split('T')[0] || "";
            break;
          case "weekly":
            const weekStart = new Date(date);
            weekStart.setDate(date.getDate() - date.getDay());
            key = weekStart.toISOString().split('T')[0] || "";
            break;
          case "monthly":
            key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            break;
          case "yearly":
            key = String(date.getFullYear());
            break;
          default:
            key = date.toISOString().split('T')[0] || "";
        }
        
        const existing = groupedData.get(key) || { revenue: 0, sales: 0 };
        groupedData.set(key, {
          revenue: existing.revenue + sale.salePrice,
          sales: existing.sales + 1,
        });
      });
      
      timeSeriesData = Array.from(groupedData.entries())
        .map(([date, data]) => ({ date, ...data }))
        .sort((a, b) => a.date.localeCompare(b.date));
    }

    return {
      totalRevenue,
      totalItems,
      averageSalePrice,
      timeSeriesData,
      salesWithProducts,
    };
  },
});

// Top performers analytics
export const getTopPerformers = query({
  args: {
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    const now = Date.now();
    const startDate = args.startDate || (now - (90 * 24 * 60 * 60 * 1000)); // Default 90 days
    const endDate = args.endDate || now;
    const limit = args.limit || 10;

    // Get sales in date range
    const sales = await ctx.db
      .query("offlineSales")
      .withIndex("sellerId", (q) => q.eq("sellerId", user._id))
      .filter((q) => 
        q.and(
          q.gte(q.field("saleDate"), startDate),
          q.lte(q.field("saleDate"), endDate)
        )
      )
      .collect();

    // Get product and category details
    const salesWithDetails = await Promise.all(
      sales.map(async (sale) => {
        const product = await ctx.db.get(sale.productId as Id<"products">);
        return {
          ...sale,
          product,
          category: product?.category || null,
        };
      })
    );

    // Top customers by purchase amount
    const customerMap = new Map<string, { name: string; email: string; totalSpent: number; purchases: number }>();
    salesWithDetails.forEach((sale) => {
      const key = sale.clientEmail;
      const existing = customerMap.get(key) || { 
        name: sale.clientName, 
        email: sale.clientEmail, 
        totalSpent: 0, 
        purchases: 0 
      };
      customerMap.set(key, {
        ...existing,
        totalSpent: existing.totalSpent + sale.salePrice,
        purchases: existing.purchases + 1,
      });
    });

    const topCustomers = Array.from(customerMap.values())
      .sort((a, b) => b.totalSpent - a.totalSpent)
      .slice(0, limit);

    // Best-selling categories
    const categoryMap = new Map<string, { name: string; sales: number; revenue: number }>();
    salesWithDetails.forEach((sale) => {
      if (sale.category) {
        const key = sale.category;
        const existing = categoryMap.get(key) || { 
          name: sale.category, 
          sales: 0, 
          revenue: 0 
        };
        categoryMap.set(key, {
          ...existing,
          sales: existing.sales + 1,
          revenue: existing.revenue + sale.salePrice,
        });
      }
    });

    const topCategories = Array.from(categoryMap.values())
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, limit);

    // Most profitable items (by profit margin if cost data available)
    const productMap = new Map<string, { 
      title: string; 
      brand: string; 
      sales: number; 
      revenue: number; 
      averagePrice: number;
      profit?: number;
    }>();
    
    salesWithDetails.forEach((sale) => {
      if (sale.product) {
        const key = sale.product._id;
        const existing = productMap.get(key) || { 
          title: sale.product.title,
          brand: sale.product.brand,
          sales: 0, 
          revenue: 0,
          averagePrice: 0,
        };
        
        const newSales = existing.sales + 1;
        const newRevenue = existing.revenue + sale.salePrice;
        
        // Calculate profit if cost data is available
        let profit = existing.profit;
        if (sale.product.originalPrice) {
          const itemProfit = sale.salePrice - sale.product.originalPrice;
          profit = (existing.profit || 0) + itemProfit;
        }
        
        productMap.set(key, {
          ...existing,
          sales: newSales,
          revenue: newRevenue,
          averagePrice: newRevenue / newSales,
          profit,
        });
      }
    });

    const topProducts = Array.from(productMap.values())
      .sort((a, b) => (b.profit || b.revenue) - (a.profit || a.revenue))
      .slice(0, limit);

    return {
      topCustomers,
      topCategories,
      topProducts,
    };
  },
});

// Key metrics dashboard
export const getKeyMetrics = query({
  args: {
    compareToLastPeriod: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    const now = Date.now();
    const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);
    const sixtyDaysAgo = now - (60 * 24 * 60 * 60 * 1000);

    // Current period (last 30 days)
    const currentSales = await ctx.db
      .query("offlineSales")
      .withIndex("sellerId", (q) => q.eq("sellerId", user._id))
      .filter((q) => q.gte(q.field("saleDate"), thirtyDaysAgo))
      .collect();

    // Previous period (30-60 days ago) for comparison
    let previousSales: any[] = [];
    if (args.compareToLastPeriod) {
      previousSales = await ctx.db
        .query("offlineSales")
        .withIndex("sellerId", (q) => q.eq("sellerId", user._id))
        .filter((q) => 
          q.and(
            q.gte(q.field("saleDate"), sixtyDaysAgo),
            q.lt(q.field("saleDate"), thirtyDaysAgo)
          )
        )
        .collect();
    }

    // Get active listings
    const activeListings = await ctx.db
      .query("products")
      .withIndex("by_seller_status", (q) => q.eq("sellerId", user._id).eq("status", "active"))
      .collect();

    // Calculate current metrics
    const currentRevenue = currentSales.reduce((sum, sale) => sum + sale.salePrice, 0);
    const currentItemsSold = currentSales.length;
    const currentAvgSalePrice = currentItemsSold > 0 ? currentRevenue / currentItemsSold : 0;
    const activeListingValue = activeListings.reduce((sum, product) => sum + product.price, 0);

    // Calculate previous metrics for comparison
    const previousRevenue = previousSales.reduce((sum, sale) => sum + sale.salePrice, 0);
    const previousItemsSold = previousSales.length;

    // Calculate percentage changes
    const revenueChange = previousRevenue > 0 ? 
      ((currentRevenue - previousRevenue) / previousRevenue) * 100 : 0;
    const itemsSoldChange = previousItemsSold > 0 ? 
      ((currentItemsSold - previousItemsSold) / previousItemsSold) * 100 : 0;

    // Calculate conversion rate (sales vs total views)
    const totalViews = activeListings.reduce((sum, product) => sum + product.views, 0);
    const conversionRate = totalViews > 0 ? (currentItemsSold / totalViews) * 100 : 0;

    return {
      totalRevenue: currentRevenue,
      itemsSold: currentItemsSold,
      averageSalePrice: currentAvgSalePrice,
      activeListingValue,
      conversionRate,
      revenueChange: args.compareToLastPeriod ? revenueChange : undefined,
      itemsSoldChange: args.compareToLastPeriod ? itemsSoldChange : undefined,
      activeListingsCount: activeListings.length,
    };
  },
});
