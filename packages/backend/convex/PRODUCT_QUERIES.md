# MODA Product Query System

This document describes the comprehensive product query system for MODA, including marketplace browsing, search functionality, seller inventory management, and detailed product views for the luxury marketplace platform.

## Overview

The product query system provides secure, subscription-gated access to product data with advanced filtering, search capabilities, and performance optimizations. It supports both public marketplace browsing and private seller inventory management with proper authorization controls.

## Core Features

### ✅ Marketplace Browsing
- **Subscription-gated access** for premium marketplace experience
- **Advanced filtering** by category, price, brand, condition
- **Search integration** within marketplace browsing
- **Sorting options** (newest, price, popularity)
- **Pagination support** for large product catalogs

### ✅ Search Functionality
- **Full-text search** across titles, descriptions, brands, tags
- **Relevance scoring** with intelligent ranking
- **Autocomplete suggestions** for enhanced UX
- **Category-specific search** with filtering
- **Search analytics** for business intelligence

### ✅ Seller Inventory Management
- **Complete inventory overview** with all product statuses
- **Performance metrics** per product (orders, revenue)
- **Status filtering** (draft, active, sold, archived)
- **Search within inventory** for easy management
- **Admin oversight** capabilities

### ✅ Product Details
- **Comprehensive product information** with authorization controls
- **Related products** discovery
- **Seller information** and contact details
- **View tracking** for analytics (separate mutation required)
- **User interaction context** (ownership, permissions)

## Query Reference

### 1. getMarketplaceProducts

Browse active products in the marketplace with filtering and search.

```typescript
const products = await ctx.runQuery(api.productQueries.getMarketplaceProducts, {
  category: "handbags",
  minPrice: 1000,
  maxPrice: 5000,
  brands: ["chanel", "hermes"],
  conditions: ["new", "like_new"],
  searchQuery: "vintage",
  sortBy: "price_low",
  limit: 24,
  offset: 0,
});
```

**Features:**
- **Subscription validation**: Active subscription required for access
- **Category filtering**: Filter by specific product categories
- **Price range filtering**: Min/max price boundaries
- **Brand filtering**: Multiple brand selection
- **Condition filtering**: Product condition states
- **Search integration**: Text search within filtered results
- **Sorting options**: newest, price_low, price_high, popular
- **Pagination**: Configurable limits with offset support

**Returns:**
- **Product list**: Enriched product data with seller information
- **Pagination info**: Total count, hasMore indicator
- **Applied filters**: Current filter state for UI

### 2. getSellerInventory

Comprehensive seller inventory management with metrics and filtering.

```typescript
const inventory = await ctx.runQuery(api.productQueries.getSellerInventory, {
  sellerId: "seller_id", // Optional for self-lookup
  status: "active", // "draft" | "active" | "sold" | "archived" | "all"
  searchQuery: "chanel bag",
  sortBy: "newest",
  includeMetrics: true,
  limit: 50,
});
```

**Features:**
- **Authorization controls**: Self-access or admin override
- **Status filtering**: All product lifecycle states
- **Performance metrics**: Orders, revenue, conversion data
- **Search functionality**: Search within seller's inventory
- **Sorting options**: newest, oldest, price, views, status
- **Summary statistics**: Inventory overview and totals

**Returns:**
- **Product inventory**: Complete product list with private data
- **Performance metrics**: Revenue, orders, averages (if requested)
- **Summary statistics**: Status breakdown, total value
- **Pagination**: Standard pagination support

### 3. getProductDetails

Detailed product view with authorization-based data filtering.

```typescript
const product = await ctx.runQuery(api.productQueries.getProductDetails, {
  productId: "product_id",
  trackView: true, // Note: Actual tracking requires separate mutation
});
```

**Features:**
- **Authorization-based access**: Public, owner, admin data levels
- **Status-based visibility**: Draft (owner/admin), archived (admin only)
- **Subscription validation**: Required for non-owners
- **Related products**: Same category recommendations
- **Seller information**: Contact details for active products
- **User interaction context**: Ownership and permission flags

**Returns:**
- **Complete product data**: All fields based on authorization level
- **Seller information**: Business details and ratings
- **Related products**: Category-based recommendations
- **User interaction**: Ownership, editing, purchasing permissions

### 4. searchProducts

Advanced full-text search with relevance scoring and suggestions.

```typescript
const results = await ctx.runQuery(api.productQueries.searchProducts, {
  query: "vintage rolex submariner",
  category: "watches",
  minPrice: 5000,
  brands: ["rolex"],
  conditions: ["excellent", "very_good"],
  sortBy: "relevance",
  limit: 24,
});
```

**Features:**
- **Intelligent relevance scoring**: Title, brand, tag, description weights
- **Multi-term search**: Space-separated search terms
- **Category filtering**: Search within specific categories
- **Advanced filters**: Price, brand, condition combinations
- **Autocomplete suggestions**: Brand and tag suggestions
- **Search analytics**: Query logging for business intelligence

**Relevance Scoring:**
- **Title matches**: 10 points
- **Brand matches**: 8 points
- **Tag matches**: 5 points
- **Description matches**: 2 points
- **Exact phrase matches**: +15 bonus points

**Returns:**
- **Ranked results**: Products sorted by relevance or other criteria
- **Search suggestions**: Autocomplete recommendations
- **Applied filters**: Current search and filter state

### 5. getProductsByCategory

Category-specific browsing with trending analysis and statistics.

```typescript
const categoryData = await ctx.runQuery(api.productQueries.getProductsByCategory, {
  category: "watches",
  brands: ["rolex", "omega"],
  minPrice: 2000,
  sortBy: "trending",
  includeTrending: true,
  limit: 24,
});
```

**Features:**
- **Category validation**: Ensures category exists and is active
- **Trending analysis**: Recent activity-based scoring
- **Brand filtering**: Multiple brand selection within category
- **Price filtering**: Min/max price boundaries
- **Category statistics**: Price ranges, brand breakdown, condition analysis
- **Trending products**: Separate trending product list

**Trending Score Calculation:**
- **Recent views**: 2x multiplier
- **Recent likes**: 5x multiplier
- **Recency factor**: Time-based boost for new products
- **Price factor**: Luxury market boost for high-value items

**Returns:**
- **Category products**: Filtered and sorted product list
- **Trending products**: Top trending items (if requested)
- **Category statistics**: Analytics and breakdowns
- **Filter state**: Applied filters for UI state management

## Authorization & Security

### Subscription Requirements

```typescript
// All product queries require active subscription
const hasActiveSubscription = user.subscriptionStatus === "active" && 
  (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

if (!hasActiveSubscription) {
  throw new ConvexError("Active subscription required to browse marketplace");
}
```

### Data Access Levels

#### **Public Data** (All authenticated users)
- Basic product information (title, price, condition, images)
- Public seller information (name, business name, ratings)
- Category and basic metadata

#### **Owner Data** (Product owner or admin)
- Private notes and source information
- Complete seller contact details
- Draft product access
- Performance metrics and analytics

#### **Admin Data** (Admin only)
- Archived product access
- All seller inventory access
- System-level analytics and controls

### Product Visibility Rules

```typescript
// Draft products: Owner or admin only
if (product.status === "draft") {
  if (!user || (product.sellerId !== user._id && user.userType !== "admin")) {
    throw new ConvexError("Product not available");
  }
}

// Archived products: Admin only
if (product.status === "archived" && (!user || user.userType !== "admin")) {
  throw new ConvexError("Product not available");
}
```

## Performance Optimizations

### Database Indexing

```typescript
// Optimized queries using appropriate indexes
const products = await ctx.db
  .query("products")
  .withIndex("by_category_status", (q) => 
    q.eq("category", category).eq("status", "active")
  )
  .collect();
```

### Pagination Strategy

```typescript
// Efficient pagination with configurable limits
const limit = Math.min(args.limit || 24, 100); // Max 100 per query
const offset = args.offset || 0;

// Apply pagination after filtering and sorting
const paginatedProducts = products.slice(offset, offset + limit);
```

### Search Optimization

```typescript
// Pre-filter by category before text search
if (args.category) {
  products = products.filter(p => p.category === args.category);
}

// Efficient relevance scoring with early termination
const searchResults = products
  .map(product => ({ product, relevanceScore: calculateScore(product) }))
  .filter(result => result.relevanceScore > 0); // Remove non-matches early
```

## Integration Examples

### React Marketplace Component

```typescript
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";

function MarketplaceBrowser() {
  const [filters, setFilters] = useState({
    category: null,
    minPrice: null,
    maxPrice: null,
    sortBy: "newest",
  });

  const products = useQuery(api.productQueries.getMarketplaceProducts, {
    ...filters,
    limit: 24,
    offset: 0,
  });

  return (
    <div>
      <FilterPanel filters={filters} onChange={setFilters} />
      <ProductGrid products={products?.products} />
      <Pagination pagination={products?.pagination} />
    </div>
  );
}
```

### Search Interface

```typescript
function ProductSearch() {
  const [query, setQuery] = useState("");
  const [filters, setFilters] = useState({});

  const results = useQuery(api.productQueries.searchProducts, {
    query,
    ...filters,
    limit: 24,
  });

  return (
    <div>
      <SearchInput 
        value={query} 
        onChange={setQuery}
        suggestions={results?.suggestions}
      />
      <SearchFilters filters={filters} onChange={setFilters} />
      <SearchResults products={results?.products} />
    </div>
  );
}
```

### Seller Inventory Dashboard

```typescript
function SellerInventory() {
  const [status, setStatus] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");

  const inventory = useQuery(api.productQueries.getSellerInventory, {
    status,
    searchQuery,
    includeMetrics: true,
    sortBy: "newest",
  });

  return (
    <div>
      <InventoryStats summary={inventory?.summary} />
      <InventoryFilters 
        status={status} 
        onStatusChange={setStatus}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
      />
      <ProductTable 
        products={inventory?.products}
        showMetrics={true}
      />
    </div>
  );
}
```

## Error Handling

### Common Error Scenarios

```typescript
try {
  const products = await ctx.runQuery(api.productQueries.getMarketplaceProducts);
} catch (error) {
  switch (error.message) {
    case "Active subscription required to browse marketplace":
      // Redirect to subscription page
      break;
    case "Product not found":
      // Show 404 page
      break;
    case "Product not available":
      // Show access denied message
      break;
  }
}
```

### Validation Errors

- **Search query length**: Minimum 2 characters required
- **Subscription status**: Active subscription required for all queries
- **Authorization**: Proper role/ownership validation
- **Product status**: Visibility rules based on product state

## Best Practices

### 1. Efficient Filtering

```typescript
// Apply most selective filters first
const products = await ctx.runQuery(api.productQueries.getMarketplaceProducts, {
  category: "handbags", // Most selective
  brands: ["chanel"], // Then brand
  minPrice: 1000, // Then price range
  searchQuery: "vintage", // Finally text search
});
```

### 2. Pagination Management

```typescript
// Use reasonable page sizes
const PRODUCTS_PER_PAGE = 24; // Good for grid layouts
const MAX_PRODUCTS_PER_PAGE = 100; // System limit

// Implement infinite scroll or traditional pagination
const loadMore = () => {
  setOffset(offset + PRODUCTS_PER_PAGE);
};
```

### 3. Search UX

```typescript
// Debounce search queries
const debouncedSearch = useDebounce(searchQuery, 300);

// Show suggestions for better UX
const suggestions = results?.suggestions || [];

// Handle empty results gracefully
if (results?.products.length === 0) {
  return <NoResultsMessage query={searchQuery} />;
}
```

### 4. Performance Monitoring

```typescript
// Track search performance
const searchStart = Date.now();
const results = await ctx.runQuery(api.productQueries.searchProducts, { query });
const searchTime = Date.now() - searchStart;

// Log slow queries for optimization
if (searchTime > 1000) {
  console.warn(`Slow search query: ${query} took ${searchTime}ms`);
}
```

## File Structure

```
packages/backend/convex/
├── productQueries.ts              # Core product query functions
├── productQueries.test.ts         # Usage examples and tests
└── PRODUCT_QUERIES.md             # This documentation
```

The product query system provides comprehensive, secure access to product data with advanced search, filtering, and authorization controls for the MODA luxury marketplace platform.
