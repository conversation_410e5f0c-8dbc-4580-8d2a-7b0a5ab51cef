import { mutation } from "./_generated/server";
import { v } from "convex/values";

export const seedSampleStaff = mutation({
  args: {
    createdById: v.id("users"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const now = Date.now();

    const sampleStaff = [
      {
        name: "<PERSON>",
        role: "Chief Executive Officer",
        department: "Executive",
        bio: "<PERSON> leads MODA's vision to revolutionize the luxury marketplace. With 15+ years in luxury retail and e-commerce, she brings deep expertise in scaling premium platforms.",
        imageUrl: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face",
        email: "<EMAIL>",
        linkedinUrl: "https://linkedin.com/in/sarahchen",
        startDate: new Date("2020-01-15").getTime(),
        isActive: true,
        isPublic: true,
        sortOrder: 1,
      },
      {
        name: "<PERSON>",
        role: "Chief Technology Officer",
        department: "Engineering",
        bio: "<PERSON> oversees all technical aspects of the platform, from architecture to security. Former tech lead at luxury e-commerce companies, he ensures our platform meets the highest standards.",
        imageUrl: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face",
        email: "<EMAIL>",
        linkedinUrl: "https://linkedin.com/in/marcusrodriguez",
        startDate: new Date("2020-03-01").getTime(),
        isActive: true,
        isPublic: true,
        sortOrder: 2,
      },
      {
        name: "Isabella Thompson",
        role: "Head of Marketing",
        department: "Marketing",
        bio: "Isabella drives our brand strategy and user acquisition. With expertise in luxury brand marketing, she crafts campaigns that resonate with our sophisticated clientele.",
        imageUrl: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face",
        email: "<EMAIL>",
        linkedinUrl: "https://linkedin.com/in/isabellathompson",
        startDate: new Date("2020-06-15").getTime(),
        isActive: true,
        isPublic: true,
        sortOrder: 3,
      },
      {
        name: "James Wilson",
        role: "Senior Software Engineer",
        department: "Engineering",
        bio: "James specializes in full-stack development and platform optimization. He's instrumental in building features that enhance both seller and buyer experiences.",
        imageUrl: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
        email: "<EMAIL>",
        linkedinUrl: "https://linkedin.com/in/jameswilson",
        startDate: new Date("2021-02-01").getTime(),
        isActive: true,
        isPublic: true,
        sortOrder: 4,
      },
      {
        name: "Olivia Martinez",
        role: "Customer Success Manager",
        department: "Customer Success",
        bio: "Olivia ensures our users have exceptional experiences on the platform. She leads customer support initiatives and builds relationships with our premium sellers.",
        imageUrl: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=400&fit=crop&crop=face",
        email: "<EMAIL>",
        linkedinUrl: "https://linkedin.com/in/oliviamartinez",
        startDate: new Date("2021-04-12").getTime(),
        isActive: true,
        isPublic: true,
        sortOrder: 5,
      },
      {
        name: "David Kim",
        role: "Head of Operations",
        department: "Operations",
        bio: "David oversees day-to-day operations and process optimization. His focus on operational excellence ensures smooth transactions and seller onboarding.",
        imageUrl: "https://images.unsplash.com/photo-1556157382-97eda2d62296?w=400&h=400&fit=crop&crop=face",
        email: "<EMAIL>",
        linkedinUrl: "https://linkedin.com/in/davidkim",
        startDate: new Date("2021-07-20").getTime(),
        isActive: true,
        isPublic: true,
        sortOrder: 6,
      },
      {
        name: "Emma Foster",
        role: "UX/UI Designer",
        department: "Engineering",
        bio: "Emma crafts beautiful and intuitive user experiences. Her design philosophy centers on luxury aesthetics combined with seamless functionality.",
        imageUrl: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=400&fit=crop&crop=face",
        email: "<EMAIL>",
        linkedinUrl: "https://linkedin.com/in/emmafoster",
        startDate: new Date("2022-01-10").getTime(),
        isActive: true,
        isPublic: true,
        sortOrder: 7,
      },
      {
        name: "Alexander Brown",
        role: "Business Development Manager",
        department: "Sales",
        bio: "Alexander builds strategic partnerships with luxury brands and high-end sellers. His relationships help expand our marketplace with premium inventory.",
        imageUrl: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=400&fit=crop&crop=face",
        email: "<EMAIL>",
        linkedinUrl: "https://linkedin.com/in/alexanderbrown",
        startDate: new Date("2022-03-15").getTime(),
        isActive: true,
        isPublic: true,
        sortOrder: 8,
      },
    ];

    // Insert all staff members
    for (const staff of sampleStaff) {
      await ctx.db.insert("staff", {
        ...staff,
        createdById: args.createdById,
        updatedAt: now,
      });
    }

    return null;
  },
});
