import { cronJobs } from "convex/server";
import { internal } from "./_generated/api";
import { internalMutation } from "./_generated/server";
import { v } from "convex/values";

/**
 * End expired auctions
 */
export const endExpiredAuctions = internalMutation({
  args: {},
  returns: v.null(),
  handler: async (ctx) => {
    const now = Date.now();
    
    // Find all active auctions that have expired
    const expiredAuctions = await ctx.db
      .query("auctions")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .filter((q) => q.lt(q.field("endTime"), now))
      .collect();

    console.log(`Found ${expiredAuctions.length} expired auctions to end`);

    // End each expired auction
    for (const auction of expiredAuctions) {
      // Check if auction has bids - if so, mark as "ended", otherwise "cancelled"
      const hasBids = auction.totalBids > 0;
      const newStatus = hasBids ? "ended" : "cancelled";
      
      await ctx.db.patch(auction._id, {
        status: newStatus,
      });
      
      console.log(`${newStatus === "ended" ? "Ended" : "Cancelled"} auction: ${auction.title}`);
      
      // If ended with bids, create notification for winner
      if (newStatus === "ended") {
        const winningBid = await ctx.db
          .query("bids")
          .withIndex("by_auctionId", (q) => q.eq("auctionId", auction._id))
          .filter((q) => q.eq(q.field("isWinning"), true))
          .first();
        
        if (winningBid) {
          await ctx.db.insert("notifications", {
            userId: winningBid.bidderId,
            type: "auction_ended",
            title: "Auction ended - You're the highest bidder!",
            message: `The auction for "${auction.title}" has ended. You were the highest bidder at $${winningBid.amount}. We'll review and contact you soon.`,
            data: {
              auctionId: auction._id,
              finalPrice: winningBid.amount,
            },
            read: false,
          });
        }
      }
    }

    return null;
  },
});

/**
 * Update auction statuses from scheduled to active
 */
export const activateScheduledAuctions = internalMutation({
  args: {},
  returns: v.null(),
  handler: async (ctx) => {
    const now = Date.now();
    
    // Find all scheduled auctions that should start now
    const scheduledAuctions = await ctx.db
      .query("auctions")
      .withIndex("by_status", (q) => q.eq("status", "scheduled"))
      .filter((q) => q.lte(q.field("startTime"), now))
      .collect();

    console.log(`Found ${scheduledAuctions.length} auctions to activate`);

    // Activate each scheduled auction
    for (const auction of scheduledAuctions) {
      await ctx.db.patch(auction._id, {
        status: "active",
      });
      console.log(`Activated auction: ${auction.title}`);
    }

    return null;
  },
});

const crons = cronJobs();

// Check for expired auctions every 30 seconds
crons.interval("end expired auctions", { seconds: 30 }, internal.auctionCrons.endExpiredAuctions, {});

// Check for scheduled auctions to activate every 30 seconds  
crons.interval("activate scheduled auctions", { seconds: 30 }, internal.auctionCrons.activateScheduledAuctions, {});

export default crons;
