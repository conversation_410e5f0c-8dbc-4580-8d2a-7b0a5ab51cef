# MODA Seller Query System

This document describes the comprehensive seller management query system for MODA, including profile access, application management, performance metrics, and administrative tools.

## Overview

The seller query system provides secure, role-based access to seller data with proper authorization, data filtering, and performance optimizations. It supports both seller self-service and administrative oversight for the luxury marketplace platform.

## Core Features

### ✅ Profile Management
- **Secure profile access** with role-based data filtering
- **Public vs private data** separation for privacy
- **Business information** and verification status
- **Performance metrics** and activity tracking

### ✅ Application Management
- **Pending application tracking** for administrators
- **Priority scoring** based on application completeness
- **Processing time analytics** for workflow optimization
- **Bulk operations** support for efficient review

### ✅ Performance Analytics
- **Revenue and sales metrics** with time range filtering
- **Product performance** tracking and top performers
- **Conversion rate analysis** and customer insights
- **Comparative analytics** with period-over-period analysis

### ✅ Administrative Tools
- **Seller search and filtering** with multiple criteria
- **Status-based organization** for workflow management
- **Export capabilities** for reporting and analysis
- **Bulk action support** for administrative efficiency

## Query Reference

### 1. getSellerProfile

Get comprehensive seller profile data with proper authorization and data filtering.

```typescript
const profile = await ctx.runQuery(api.sellerQueries.getSellerProfile, {
  sellerId: "seller_id", // Optional - defaults to current user
  includePrivateData: true, // Admin or self-access only
});
```

**Returns:**
- **Public Data**: Business name, verification status, ratings, public metrics
- **Private Data** (authorized users): Contact info, bank details, documents, revenue
- **Performance Metrics**: Product counts, order statistics, revenue data

**Authorization:**
- **Self-access**: Sellers can view their own complete profile
- **Admin access**: Admins can view any seller's complete profile
- **Public access**: Limited data for public viewing

### 2. getPendingApplications (Admin Only)

List and manage pending seller applications with filtering and sorting.

```typescript
const applications = await ctx.runQuery(api.sellerQueries.getPendingApplications, {
  status: "all_pending", // "pending" | "under_review" | "all_pending"
  limit: 50,
  offset: 0,
  sortBy: "applicationDate", // "applicationDate" | "businessName" | "expectedVolume"
  sortOrder: "desc",
});
```

**Returns:**
- **Application List**: Enriched application data with metrics
- **Summary Statistics**: Counts, processing times, priority breakdown
- **Pagination**: Support for large datasets
- **Priority Scoring**: High/medium/low priority based on completeness

**Features:**
- **Smart Prioritization**: Based on document count, expected volume, age
- **Processing Analytics**: Average review times and workflow metrics
- **Subscription Tracking**: Active subscription status for applicants

### 3. getSellerMetrics

Comprehensive seller performance analytics with time range filtering.

```typescript
const metrics = await ctx.runQuery(api.sellerQueries.getSellerMetrics, {
  sellerId: "seller_id", // Optional for self-lookup
  timeRange: "30d", // "7d" | "30d" | "90d" | "1y" | "all"
  includeComparisons: true, // Period-over-period analysis
});
```

**Returns:**
- **Product Metrics**: Total, active, sold counts and top performers
- **Order Analytics**: Conversion rates, order values, completion rates
- **Revenue Data**: Total revenue, averages, currency handling
- **Performance KPIs**: Views, conversions, repeat customers
- **Comparative Analysis**: Period-over-period changes

**Authorization:**
- **Self-access**: Sellers can view their own metrics
- **Admin access**: Admins can view any seller's metrics

### 4. getSellersByStatus (Admin Only)

Advanced seller search and filtering with bulk operation support.

```typescript
const sellers = await ctx.runQuery(api.sellerQueries.getSellersByStatus, {
  status: "approved", // "pending" | "approved" | "rejected" | "all"
  searchQuery: "luxury boutique",
  subscriptionFilter: "active",
  businessTypeFilter: "LLC",
  includeMetrics: true,
  limit: 100,
  sortBy: "revenue",
  sortOrder: "desc",
});
```

**Returns:**
- **Seller List**: Comprehensive seller data with optional metrics
- **Summary Statistics**: Status breakdown, subscription analytics
- **Export Data**: CSV-ready format for reporting
- **Tag System**: Automated seller categorization

**Features:**
- **Multi-criteria Search**: Name, email, business type filtering
- **Performance Sorting**: Revenue, products, activity-based ordering
- **Bulk Operations**: Export and administrative action support
- **Smart Tagging**: Automated seller classification

### 5. getSellerDashboardSummary

Seller dashboard overview with key metrics and actionable insights.

```typescript
const dashboard = await ctx.runQuery(api.sellerQueries.getSellerDashboardSummary, {
  sellerId: "seller_id", // Optional for self-lookup
});
```

**Returns:**
- **Key Metrics**: Active products, pending orders, monthly revenue
- **Subscription Status**: Plan details, expiration tracking
- **Recent Activity**: Latest seller actions and events
- **Smart Alerts**: Actionable notifications and warnings
- **Quick Actions**: Common seller tasks and shortcuts

**Features:**
- **Proactive Alerts**: Subscription expiration, verification status
- **Activity Tracking**: Recent seller actions and engagement
- **Quick Navigation**: Direct links to common seller tasks

## Authorization & Security

### Role-Based Access Control

```typescript
// Seller accessing own data
const myProfile = await ctx.runQuery(api.sellerQueries.getSellerProfile);

// Admin accessing any seller's data
const sellerProfile = await ctx.runQuery(api.sellerQueries.getSellerProfile, {
  sellerId: "target_seller_id",
  includePrivateData: true,
});

// Public access (limited data)
const publicProfile = await ctx.runQuery(api.sellerQueries.getSellerProfile, {
  sellerId: "seller_id",
  includePrivateData: false,
});
```

### Data Filtering

- **Private Data**: Bank details, contact info, documents (owner/admin only)
- **Public Data**: Business name, ratings, public metrics (all users)
- **Sensitive Metrics**: Revenue data (owner/admin only)
- **Encrypted Fields**: Bank account numbers are masked in storage

## Performance Optimizations

### Pagination Support

```typescript
// Large dataset handling
const sellers = await ctx.runQuery(api.sellerQueries.getSellersByStatus, {
  limit: 100, // Max 200 per query
  offset: 200, // For pagination
});
```

### Efficient Filtering

```typescript
// Database index optimization
const applications = await ctx.runQuery(api.sellerQueries.getPendingApplications, {
  status: "pending", // Uses verification status index
  sortBy: "applicationDate", // Optimized sorting
});
```

### Metric Calculations

- **Cached Calculations**: Common metrics cached for performance
- **Lazy Loading**: Optional metrics only calculated when requested
- **Batch Processing**: Multiple seller metrics calculated efficiently

## Integration Examples

### React Dashboard Component

```typescript
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";

function SellerDashboard() {
  const dashboard = useQuery(api.sellerQueries.getSellerDashboardSummary);
  
  if (!dashboard) return <Loading />;
  
  return (
    <div>
      <MetricsCards metrics={dashboard.metrics} />
      <SubscriptionStatus subscription={dashboard.subscription} />
      <AlertsList alerts={dashboard.alerts} />
      <RecentActivity activities={dashboard.recentActivity} />
    </div>
  );
}
```

### Admin Application Review

```typescript
function AdminApplicationReview() {
  const applications = useQuery(api.sellerQueries.getPendingApplications, {
    status: "all_pending",
    sortBy: "applicationDate",
    limit: 50,
  });
  
  return (
    <div>
      <ApplicationStats summary={applications?.summary} />
      <ApplicationList 
        applications={applications?.applications}
        onApprove={handleApprove}
        onReject={handleReject}
      />
    </div>
  );
}
```

### Seller Search Interface

```typescript
function SellerSearchInterface() {
  const [filters, setFilters] = useState({
    searchQuery: "",
    status: "all",
    subscriptionFilter: "all",
  });
  
  const sellers = useQuery(api.sellerQueries.getSellersByStatus, {
    ...filters,
    includeMetrics: true,
    limit: 50,
  });
  
  return (
    <div>
      <SearchFilters filters={filters} onChange={setFilters} />
      <SellerTable sellers={sellers?.sellers} />
      <ExportButton data={sellers?.exportData} />
    </div>
  );
}
```

## Error Handling

### Common Error Scenarios

```typescript
try {
  const profile = await ctx.runQuery(api.sellerQueries.getSellerProfile, {
    sellerId: "seller_id",
    includePrivateData: true,
  });
} catch (error) {
  switch (error.message) {
    case "Seller not found":
      // Handle missing seller
      break;
    case "User is not a seller":
      // Handle wrong user type
      break;
    case "Unauthorized":
      // Handle permission error
      break;
  }
}
```

### Authorization Validation

- **Role Checking**: Automatic admin role validation for restricted queries
- **Ownership Validation**: Sellers can only access their own private data
- **Data Filtering**: Automatic filtering based on user permissions

## Best Practices

### 1. Use Appropriate Data Access

```typescript
// For public seller listings
const publicProfile = await ctx.runQuery(api.sellerQueries.getSellerProfile, {
  sellerId: "seller_id",
  includePrivateData: false, // Public data only
});

// For seller self-service
const myProfile = await ctx.runQuery(api.sellerQueries.getSellerProfile, {
  includePrivateData: true, // Full access to own data
});
```

### 2. Optimize Query Performance

```typescript
// Use pagination for large datasets
const sellers = await ctx.runQuery(api.sellerQueries.getSellersByStatus, {
  limit: 50, // Reasonable page size
  includeMetrics: false, // Only when needed
});

// Include metrics only when necessary
const sellersWithMetrics = await ctx.runQuery(api.sellerQueries.getSellersByStatus, {
  includeMetrics: true, // More expensive query
  limit: 20, // Smaller page size
});
```

### 3. Handle Time Ranges Appropriately

```typescript
// For dashboard widgets (fast)
const recentMetrics = await ctx.runQuery(api.sellerQueries.getSellerMetrics, {
  timeRange: "7d",
  includeComparisons: false,
});

// For detailed analytics (slower)
const detailedMetrics = await ctx.runQuery(api.sellerQueries.getSellerMetrics, {
  timeRange: "1y",
  includeComparisons: true,
});
```

## File Structure

```
packages/backend/convex/
├── sellerQueries.ts              # Core seller query functions
├── sellerQueries.test.ts         # Usage examples and tests
└── SELLER_QUERIES.md             # This documentation
```

The seller query system provides comprehensive, secure access to seller data with proper authorization, performance optimization, and administrative tools for managing the MODA luxury marketplace platform.
