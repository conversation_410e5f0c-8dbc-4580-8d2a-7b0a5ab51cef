# MODA Order Tracking & Management Queries

This document describes the comprehensive order tracking and management query system for MODA, including buyer purchase history, seller sales management, detailed order views, and analytics for the luxury marketplace platform.

## Overview

The order query system provides secure, role-based access to order data with advanced filtering, search capabilities, and comprehensive analytics. It supports both buyer and seller perspectives with proper authorization controls and detailed metrics for business intelligence.

## Core Features

### ✅ Buyer Order Management
- **Purchase history tracking** with complete order details
- **Status and tracking information** for all orders
- **Search and filtering** by status, date, product details
- **Order summaries** with spending analytics
- **Review and rating access** for completed orders

### ✅ Seller Order Management
- **Sales history** with buyer information
- **Revenue calculations** and commission tracking
- **Order fulfillment status** management
- **Customer insights** and repeat buyer analysis
- **Performance metrics** for business optimization

### ✅ Detailed Order Views
- **Authorization-based data filtering** (buyer, seller, admin levels)
- **Complete order information** including shipping and payment
- **Dispute and review integration** for comprehensive context
- **Order timeline** with event tracking
- **User permissions** for available actions

### ✅ Analytics & Metrics
- **Sales analytics** with trend analysis
- **Order volume trends** and growth metrics
- **Average order values** and conversion rates
- **Customer insights** and retention analysis
- **Category breakdowns** and performance comparisons

## Query Reference

### 1. getBuyerOrders

Get buyer's purchase history with filtering and search capabilities.

```typescript
const orders = await ctx.runQuery(api.orderQueries.getBuyerOrders, {
  buyerId: "buyer_id", // Optional for self-lookup
  status: "delivered", // "pending" | "confirmed" | "shipped" | "delivered" | "cancelled" | "disputed" | "all"
  searchQuery: "chanel bag",
  dateRange: {
    startDate: Date.now() - (90 * 24 * 60 * 60 * 1000), // Last 90 days
    endDate: Date.now(),
  },
  sortBy: "newest", // "newest" | "oldest" | "amount_high" | "amount_low" | "status"
  limit: 50,
  offset: 0,
});
```

**Features:**
- **Authorization controls**: Self-access or admin override
- **Status filtering**: All order lifecycle states
- **Date range filtering**: Custom time periods
- **Search functionality**: Product titles, brands, order numbers
- **Sorting options**: Multiple sort criteria
- **Pagination support**: Configurable limits with offset

**Returns:**
- **Order list**: Enriched order data with product and seller information
- **Summary statistics**: Total orders, spending, status breakdown
- **Pagination info**: Total count, hasMore indicator
- **Applied filters**: Current filter state for UI

### 2. getSellerOrders

Get seller's sales history with buyer information and revenue calculations.

```typescript
const orders = await ctx.runQuery(api.orderQueries.getSellerOrders, {
  sellerId: "seller_id", // Optional for self-lookup
  status: "shipped",
  searchQuery: "john smith", // Search buyer names, emails, products
  dateRange: {
    startDate: Date.now() - (30 * 24 * 60 * 60 * 1000),
    endDate: Date.now(),
  },
  sortBy: "earnings", // "newest" | "oldest" | "amount_high" | "amount_low" | "status" | "earnings"
  includeRevenue: true,
  limit: 50,
});
```

**Features:**
- **Revenue calculations**: Earnings, commissions, gross sales
- **Buyer information**: Contact details for sellers
- **Order fulfillment**: Status tracking and management
- **Search capabilities**: Buyer names, emails, product details
- **Performance metrics**: Sales analytics and trends

**Returns:**
- **Sales history**: Complete order list with buyer details
- **Revenue summary**: Earnings, commissions, averages
- **Status breakdown**: Order state distribution
- **Pagination**: Standard pagination support

### 3. getOrderDetails

Get detailed order information with authorization-based data filtering.

```typescript
const order = await ctx.runQuery(api.orderQueries.getOrderDetails, {
  orderId: "order_id",
  includePaymentInfo: true, // Only for buyers and admins
});
```

**Features:**
- **Authorization-based access**: Different data levels for buyers, sellers, admins
- **Complete order information**: All order fields based on permissions
- **Dispute integration**: Dispute details and evidence for involved parties
- **Review access**: Ratings and reviews for completed orders
- **Order timeline**: Event tracking and status history
- **User permissions**: Available actions based on role and order status

**Data Access Levels:**
- **Buyers**: Full order details, payment info, seller contact, dispute access
- **Sellers**: Order details, buyer contact, earnings info, dispute access
- **Admins**: Complete access to all order data and sensitive information

**Returns:**
- **Complete order data**: All fields based on authorization level
- **Product information**: Detailed product data
- **Party information**: Buyer and seller details based on permissions
- **Dispute details**: Dispute information and evidence (if applicable)
- **Review data**: Ratings and reviews for the order
- **Timeline**: Order event history
- **User permissions**: Available actions for current user

### 4. getOrderMetrics

Get comprehensive order analytics and metrics with trend analysis.

```typescript
const metrics = await ctx.runQuery(api.orderQueries.getOrderMetrics, {
  userId: "user_id", // Optional for self-lookup
  userType: "seller", // "buyer" | "seller"
  timeRange: "30d", // "7d" | "30d" | "90d" | "1y" | "all"
  includeComparisons: true,
  includeBreakdowns: true,
});
```

**Features:**
- **Comprehensive metrics**: Orders, revenue, rates, averages
- **Trend analysis**: Growth comparisons with previous periods
- **Breakdown analytics**: Status, monthly, category distributions
- **Customer insights**: Repeat customers, retention rates (sellers)
- **Performance tracking**: Delivery rates, cancellation rates, dispute rates

**Metrics Included:**
- **Basic metrics**: Total orders, delivered orders, cancelled orders, disputed orders
- **Financial metrics**: Total value, delivered value, average order value
- **Performance rates**: Delivery rate, cancellation rate, dispute rate
- **Seller-specific**: Total earnings, commission amounts, conversion rates
- **Growth metrics**: Period-over-period comparisons

**Returns:**
- **Core metrics**: Comprehensive performance data
- **Comparisons**: Growth metrics vs. previous periods (if requested)
- **Breakdowns**: Status, monthly, and category distributions (if requested)
- **Customer insights**: Repeat customer analysis for sellers (if requested)

## Authorization & Security

### Role-Based Access Control

```typescript
// Authorization validation for order queries
const canAccessOrders = (user, targetUserId, userType) => {
  // Users can access their own orders
  if (targetUserId === user._id) {
    return true;
  }
  
  // Admins can access any user's orders
  if (user.userType === "admin") {
    return true;
  }
  
  return false;
};
```

### Data Filtering by Role

#### **Buyer Access**
- **Own orders**: Complete order details including payment information
- **Seller contact**: Email and business information for active orders
- **Dispute access**: Can view and create disputes for their orders
- **Review access**: Can view and create reviews for delivered orders

#### **Seller Access**
- **Own sales**: Complete sales history with buyer contact information
- **Earnings data**: Commission rates, earnings, and revenue calculations
- **Buyer information**: Contact details and subscription status
- **Order management**: Can update order status and shipping information

#### **Admin Access**
- **Complete visibility**: All order data across the platform
- **Sensitive information**: Payment details, dispute evidence, private notes
- **System metrics**: Platform-wide analytics and performance data
- **Override capabilities**: Can access any user's order information

### Data Privacy Controls

```typescript
// Example of authorization-based data filtering
const response = {
  // Always visible
  orderNumber: order.orderNumber,
  orderStatus: order.orderStatus,
  totalAmount: order.totalAmount,
  
  // Seller/admin only
  commissionAmount: (isSeller || isAdmin) ? order.commissionAmount : undefined,
  sellerEarnings: (isSeller || isAdmin) ? order.sellerEarnings : undefined,
  
  // Buyer/admin only
  paymentMethodId: (isBuyer || isAdmin) ? order.paymentMethodId : undefined,
  
  // Contact information based on role
  buyer: {
    name: buyer.name,
    email: (isSeller || isAdmin) ? buyer.email : undefined,
  },
  seller: {
    name: seller.name,
    email: (isBuyer || isAdmin) ? seller.email : undefined,
  },
};
```

## Performance Optimizations

### Database Indexing

```typescript
// Optimized queries using appropriate indexes
const orders = await ctx.db
  .query("orders")
  .withIndex("by_buyerId", (q) => q.eq("buyerId", targetBuyerId))
  .filter((q) => q.eq(q.field("orderStatus"), status))
  .collect();
```

### Efficient Filtering

```typescript
// Apply filters in order of selectivity
// 1. Database-level filtering (most efficient)
let orders = await ctx.db.query("orders").withIndex("by_sellerId", ...).collect();

// 2. Date range filtering (high selectivity)
if (dateRange) {
  orders = orders.filter(order => 
    order.orderDate >= dateRange.startDate && 
    order.orderDate <= dateRange.endDate
  );
}

// 3. Text search (lower selectivity, applied last)
if (searchQuery) {
  orders = orders.filter(order => /* search logic */);
}
```

### Pagination Strategy

```typescript
// Efficient pagination with reasonable limits
const limit = Math.min(args.limit || 50, 200); // Max 200 per query
const offset = args.offset || 0;

// Apply pagination after all filtering and sorting
const paginatedOrders = orders.slice(offset, offset + limit);
```

## Integration Examples

### React Buyer Order History

```typescript
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";

function BuyerOrderHistory() {
  const [filters, setFilters] = useState({
    status: "all",
    sortBy: "newest",
  });

  const orders = useQuery(api.orderQueries.getBuyerOrders, {
    ...filters,
    limit: 20,
  });

  return (
    <div>
      <OrderFilters filters={filters} onChange={setFilters} />
      <OrderSummary summary={orders?.summary} />
      <OrderList orders={orders?.orders} />
      <Pagination pagination={orders?.pagination} />
    </div>
  );
}
```

### Seller Sales Dashboard

```typescript
function SellerSalesDashboard() {
  const [timeRange, setTimeRange] = useState("30d");
  
  const orders = useQuery(api.orderQueries.getSellerOrders, {
    includeRevenue: true,
    sortBy: "newest",
    limit: 50,
  });

  const metrics = useQuery(api.orderQueries.getOrderMetrics, {
    userType: "seller",
    timeRange,
    includeComparisons: true,
    includeBreakdowns: true,
  });

  return (
    <div>
      <MetricsDashboard metrics={metrics} />
      <RevenueChart data={metrics?.breakdowns?.byMonth} />
      <OrderManagementTable orders={orders?.orders} />
      <CustomerInsights insights={metrics?.customerInsights} />
    </div>
  );
}
```

### Order Detail View

```typescript
function OrderDetailView({ orderId }) {
  const order = useQuery(api.orderQueries.getOrderDetails, {
    orderId,
    includePaymentInfo: true,
  });

  if (!order) return <LoadingSpinner />;

  return (
    <div>
      <OrderHeader order={order} />
      <OrderTimeline timeline={order.timeline} />
      <ProductDetails product={order.product} />
      <ShippingInfo 
        address={order.shippingAddress}
        tracking={order.trackingNumber}
        carrier={order.carrier}
      />
      {order.dispute && <DisputeDetails dispute={order.dispute} />}
      {order.review && <ReviewDisplay review={order.review} />}
      <OrderActions 
        order={order}
        permissions={order.userPermissions}
      />
    </div>
  );
}
```

### Analytics Dashboard

```typescript
function AnalyticsDashboard() {
  const [timeRange, setTimeRange] = useState("30d");
  const [userType, setUserType] = useState("seller");

  const metrics = useQuery(api.orderQueries.getOrderMetrics, {
    userType,
    timeRange,
    includeComparisons: true,
    includeBreakdowns: true,
  });

  return (
    <div>
      <MetricsOverview metrics={metrics?.metrics} />
      <GrowthComparison comparisons={metrics?.comparisons} />
      <CategoryBreakdown categories={metrics?.breakdowns?.byCategory} />
      <TrendChart data={metrics?.breakdowns?.byMonth} />
      {userType === "seller" && (
        <CustomerInsights insights={metrics?.customerInsights} />
      )}
    </div>
  );
}
```

## Best Practices

### 1. Efficient Data Loading

```typescript
// Use appropriate pagination sizes
const ORDERS_PER_PAGE = 20; // Good for list views
const MAX_ORDERS_PER_PAGE = 200; // System limit

// Load metrics separately for better performance
const orders = useQuery(api.orderQueries.getBuyerOrders, { limit: 20 });
const metrics = useQuery(api.orderQueries.getOrderMetrics, { timeRange: "30d" });
```

### 2. Smart Filtering

```typescript
// Apply most selective filters first
const filters = {
  status: "delivered", // Most selective
  dateRange: { startDate, endDate }, // Then date range
  searchQuery: "luxury bag", // Finally text search
};
```

### 3. Caching Strategy

```typescript
// Cache metrics for better performance
const cachedMetrics = useMemo(() => {
  return metrics ? processMetricsData(metrics) : null;
}, [metrics]);

// Debounce search queries
const debouncedSearch = useDebounce(searchQuery, 300);
```

### 4. Error Handling

```typescript
// Handle authorization errors gracefully
try {
  const orders = await ctx.runQuery(api.orderQueries.getBuyerOrders, args);
} catch (error) {
  if (error.message.includes("Unauthorized")) {
    // Redirect to login or show access denied
  } else if (error.message.includes("not found")) {
    // Show not found message
  }
}
```

## File Structure

```
packages/backend/convex/
├── orderQueries.ts              # Core order query functions
├── orderQueries.test.ts         # Usage examples and tests
└── ORDER_QUERIES.md             # This documentation
```

The order query system provides comprehensive, secure access to order data with advanced filtering, analytics, and role-based authorization controls for the MODA luxury marketplace platform.
