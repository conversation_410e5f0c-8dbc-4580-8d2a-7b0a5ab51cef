import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";
import { ConvexError } from "convex/values";
import { requireAuth, requireAdmin } from "./lib/auth_utils";

/**
 * Submit an auction request
 */
export const submitAuctionRequest = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    brand: v.string(),
    category: v.union(
      v.literal("clothing"),
      v.literal("sneakers"),
      v.literal("collectibles"),
      v.literal("accessories"),
      v.literal("handbags"),
      v.literal("jewelry"),
      v.literal("watches"),
      v.literal("sunglasses"),
      v.literal("cars"),
      v.literal("art")
    ),
    condition: v.optional(v.union(
      v.literal("new"),
      v.literal("like_new"),
      v.literal("excellent"),
      v.literal("very_good"),
      v.literal("good"),
      v.literal("fair")
    )),
    estimatedValue: v.optional(v.number()),
    maxBudget: v.optional(v.number()),
    images: v.optional(v.array(v.id("_storage"))),
    notes: v.optional(v.string()),
  },
  returns: v.id("auctionRequests"),
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    // Check subscription status
    if (user.subscriptionStatus !== "active" && user.subscriptionStatus !== "trial") {
      throw new ConvexError("Active subscription required to request auctions");
    }

    // Validate input
    if (!args.title.trim() || args.title.length > 200) {
      throw new ConvexError("Title must be between 1 and 200 characters");
    }

    if (!args.description.trim() || args.description.length > 2000) {
      throw new ConvexError("Description must be between 1 and 2000 characters");
    }

    if (!args.brand.trim() || args.brand.length > 100) {
      throw new ConvexError("Brand must be between 1 and 100 characters");
    }

    if (args.notes && args.notes.length > 1000) {
      throw new ConvexError("Notes must be less than 1000 characters");
    }

    if (args.estimatedValue && args.estimatedValue < 0) {
      throw new ConvexError("Estimated value must be positive");
    }

    if (args.maxBudget && args.maxBudget < 0) {
      throw new ConvexError("Max budget must be positive");
    }

    // Check for duplicate requests (same user, title, brand within 7 days)
    const recentDuplicate = await ctx.db
      .query("auctionRequests")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .filter((q) => 
        q.and(
          q.eq(q.field("title"), args.title.trim()),
          q.eq(q.field("brand"), args.brand.trim()),
          q.gt(q.field("_creationTime"), Date.now() - 7 * 24 * 60 * 60 * 1000)
        )
      )
      .first();

    if (recentDuplicate) {
      throw new ConvexError("You have already requested this item recently. Please wait before submitting again.");
    }

    // Create auction request
    const requestId = await ctx.db.insert("auctionRequests", {
      userId: user._id,
      title: args.title.trim(),
      description: args.description.trim(),
      brand: args.brand.trim(),
      category: args.category,
      condition: args.condition,
      estimatedValue: args.estimatedValue,
      maxBudget: args.maxBudget,
      images: args.images,
      notes: args.notes?.trim(),
      status: "pending",
      upvotes: 0,
      priority: "low",
    });

    return requestId;
  },
});

/**
 * Get auction requests (public, for users to browse and upvote)
 */
export const getAuctionRequests = query({
  args: {
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("under_review"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("completed")
    )),
    category: v.optional(v.string()),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  returns: v.object({
    requests: v.array(v.object({
      _id: v.id("auctionRequests"),
      _creationTime: v.number(),
      title: v.string(),
      description: v.string(),
      brand: v.string(),
      category: v.string(),
      condition: v.optional(v.string()),
      estimatedValue: v.optional(v.number()),
      images: v.optional(v.array(v.string())),
      status: v.string(),
      upvotes: v.number(),
      priority: v.optional(v.string()),
      user: v.object({
        name: v.string(),
        isVerified: v.boolean(),
      }),
      hasUpvoted: v.boolean(),
    })),
    hasMore: v.boolean(),
    total: v.number(),
  }),
  handler: async (ctx, args) => {
    const limit = args.limit || 20;
    const offset = args.offset || 0;
    const currentUser = await ctx.auth.getUserIdentity();

    // Build query based on filters
    let allRequests;
    if (args.status) {
      allRequests = await ctx.db
        .query("auctionRequests")
        .withIndex("by_status", (q) => q.eq("status", args.status!))
        .collect();
    } else {
      allRequests = await ctx.db
        .query("auctionRequests")
        .collect();
    }
    
    // Apply category filter if specified
    let filteredRequests = allRequests;
    if (args.category) {
      filteredRequests = allRequests.filter(request => request.category === args.category);
    }

    // Sort by upvotes (descending), then by creation time (descending)
    filteredRequests.sort((a, b) => {
      const upvotesDiff = (b.upvotes || 0) - (a.upvotes || 0);
      if (upvotesDiff !== 0) return upvotesDiff;
      return b._creationTime - a._creationTime;
    });

    // Apply pagination
    const paginatedRequests = filteredRequests.slice(offset, offset + limit);

    // Enrich with user data and upvote status
    const enrichedRequests = await Promise.all(
      paginatedRequests.map(async (request) => {
        const user = await ctx.db.get(request.userId);
        if (!user) throw new ConvexError("Request user not found");

        // Check if current user has upvoted this request
        let hasUpvoted = false;
        if (currentUser) {
          const upvote = await ctx.db
            .query("auctionRequestUpvotes")
            .withIndex("by_requestId_userId", (q) => 
              q.eq("requestId", request._id).eq("userId", currentUser.subject as Id<"users">)
            )
            .first();
          hasUpvoted = !!upvote;
        }

        // Get image URLs if images exist
        let imageUrls: string[] = [];
        if (request.images) {
          imageUrls = await Promise.all(
            request.images.map(async (imageId) => {
              const url = await ctx.storage.getUrl(imageId);
              return url || "";
            })
          );
          imageUrls = imageUrls.filter(url => url); // Remove empty URLs
        }

        return {
          _id: request._id,
          _creationTime: request._creationTime,
          title: request.title,
          description: request.description,
          brand: request.brand,
          category: request.category,
          condition: request.condition,
          estimatedValue: request.estimatedValue,
          images: imageUrls,
          status: request.status,
          upvotes: request.upvotes || 0,
          priority: request.priority,
          user: {
            name: user.name,
            isVerified: user.isVerified || false,
          },
          hasUpvoted,
        };
      })
    );

    return {
      requests: enrichedRequests,
      hasMore: offset + limit < filteredRequests.length,
      total: filteredRequests.length,
    };
  },
});

/**
 * Get user's own auction requests
 */
export const getUserAuctionRequests = query({
  args: {},
  returns: v.array(v.object({
    _id: v.id("auctionRequests"),
    _creationTime: v.number(),
    title: v.string(),
    description: v.string(),
    brand: v.string(),
    category: v.string(),
    condition: v.optional(v.string()),
    estimatedValue: v.optional(v.number()),
    maxBudget: v.optional(v.number()),
    images: v.optional(v.array(v.string())),
    notes: v.optional(v.string()),
    status: v.string(),
    upvotes: v.number(),
    priority: v.optional(v.string()),
    adminResponse: v.optional(v.string()),
    reviewedAt: v.optional(v.number()),
    auctionId: v.optional(v.id("auctions")),
  })),
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    const requests = await ctx.db
      .query("auctionRequests")
      .withIndex("by_userId", (q) => q.eq("userId", user._id))
      .order("desc")
      .collect();

    // Enrich with image URLs
    const enrichedRequests = await Promise.all(
      requests.map(async (request) => {
        let imageUrls: string[] = [];
        if (request.images) {
          imageUrls = await Promise.all(
            request.images.map(async (imageId) => {
              const url = await ctx.storage.getUrl(imageId);
              return url || "";
            })
          );
          imageUrls = imageUrls.filter(url => url);
        }

        return {
          _id: request._id,
          _creationTime: request._creationTime,
          title: request.title,
          description: request.description,
          brand: request.brand,
          category: request.category,
          condition: request.condition,
          estimatedValue: request.estimatedValue,
          maxBudget: request.maxBudget,
          images: imageUrls,
          notes: request.notes,
          status: request.status,
          upvotes: request.upvotes || 0,
          priority: request.priority,
          adminResponse: request.adminResponse,
          reviewedAt: request.reviewedAt,
          auctionId: request.auctionId,
        };
      })
    );

    return enrichedRequests;
  },
});

/**
 * Upvote an auction request
 */
export const upvoteAuctionRequest = mutation({
  args: {
    requestId: v.id("auctionRequests"),
  },
  returns: v.object({
    upvoted: v.boolean(),
    newUpvoteCount: v.number(),
  }),
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);

    const request = await ctx.db.get(args.requestId);
    if (!request) {
      throw new ConvexError("Auction request not found");
    }

    // Can't upvote your own request
    if (request.userId === user._id) {
      throw new ConvexError("You cannot upvote your own request");
    }

    // Check if user already upvoted
    const existingUpvote = await ctx.db
      .query("auctionRequestUpvotes")
      .withIndex("by_requestId_userId", (q) => 
        q.eq("requestId", args.requestId).eq("userId", user._id)
      )
      .first();

    if (existingUpvote) {
      // Remove upvote
      await ctx.db.delete(existingUpvote._id);
      const newUpvoteCount = Math.max(0, (request.upvotes || 0) - 1);
      await ctx.db.patch(args.requestId, { upvotes: newUpvoteCount });
      
      return {
        upvoted: false,
        newUpvoteCount,
      };
    } else {
      // Add upvote
      await ctx.db.insert("auctionRequestUpvotes", {
        requestId: args.requestId,
        userId: user._id,
      });
      const newUpvoteCount = (request.upvotes || 0) + 1;
      await ctx.db.patch(args.requestId, { upvotes: newUpvoteCount });
      
      return {
        upvoted: true,
        newUpvoteCount,
      };
    }
  },
});

/**
 * Admin: Update auction request status
 */
export const updateAuctionRequestStatus = mutation({
  args: {
    requestId: v.id("auctionRequests"),
    status: v.union(
      v.literal("pending"),
      v.literal("under_review"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("completed")
    ),
    adminResponse: v.optional(v.string()),
    priority: v.optional(v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("urgent")
    )),
    auctionId: v.optional(v.id("auctions")),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const admin = await requireAdmin(ctx);

    const request = await ctx.db.get(args.requestId);
    if (!request) {
      throw new ConvexError("Auction request not found");
    }

    await ctx.db.patch(args.requestId, {
      status: args.status,
      adminResponse: args.adminResponse,
      priority: args.priority,
      reviewedBy: admin._id,
      reviewedAt: Date.now(),
      auctionId: args.auctionId,
    });

    // Send notification to user
    await ctx.db.insert("notifications", {
      userId: request.userId,
      type: "system",
      title: `Auction Request ${args.status.charAt(0).toUpperCase() + args.status.slice(1)}`,
      message: `Your auction request for "${request.title}" has been ${args.status}.${args.adminResponse ? ` Admin note: ${args.adminResponse}` : ""}`,
      data: {
        requestId: args.requestId,
        status: args.status,
        adminResponse: args.adminResponse,
      },
      read: false,
    });

    return null;
  },
});
