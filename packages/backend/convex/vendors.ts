import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

// Public query to get all approved vendors for the directory
export const getApprovedVendors = query({
  args: {
    category: v.optional(v.union(
      v.literal("legal"),
      v.literal("marketing"),
      v.literal("design"),
      v.literal("automotive"),
      v.literal("cleaning"),
      v.literal("consulting"),
      v.literal("technology"),
      v.literal("other")
    )),
    featured: v.optional(v.boolean()),
  },
  returns: v.array(v.object({
    _id: v.id("vendors"),
    _creationTime: v.number(),
    companyName: v.string(),
    website: v.optional(v.string()),
    description: v.string(),
    services: v.array(v.string()),
    contactEmail: v.optional(v.string()),
    contactPhone: v.optional(v.string()),
    logoUrl: v.optional(v.string()),
    bannerUrl: v.optional(v.string()),
    category: v.union(
      v.literal("legal"),
      v.literal("marketing"),
      v.literal("design"),
      v.literal("automotive"),
      v.literal("cleaning"),
      v.literal("consulting"),
      v.literal("technology"),
      v.literal("other")
    ),
    isApproved: v.boolean(),
    isFeatured: v.optional(v.boolean()),
    rating: v.optional(v.number()),
    reviewCount: v.optional(v.number()),
    location: v.optional(v.string()),
    establishedYear: v.optional(v.number()),
    employeeCount: v.optional(v.string()),
    specialties: v.optional(v.array(v.string())),
    certifications: v.optional(v.array(v.string())),
    updatedAt: v.number(),
  })),
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("vendors")
      .withIndex("by_approved", (q) => q.eq("isApproved", true));

    if (args.category) {
      query = ctx.db
        .query("vendors")
        .withIndex("by_category", (q) => q.eq("category", args.category as "legal" | "marketing" | "design" | "automotive" | "cleaning" | "consulting" | "technology" | "other"))
        .filter((q) => q.eq(q.field("isApproved"), true));
    }

    if (args.featured !== undefined) {
      query = query.filter((q) => q.eq(q.field("isFeatured"), args.featured));
    }

    const vendors = await query.order("desc").collect();

    return vendors.map((vendor) => ({
      _id: vendor._id,
      _creationTime: vendor._creationTime,
      companyName: vendor.companyName,
      website: vendor.website,
      description: vendor.description,
      services: vendor.services,
      contactEmail: vendor.contactEmail,
      contactPhone: vendor.contactPhone,
      logoUrl: vendor.logoUrl,
      bannerUrl: vendor.bannerUrl,
      category: vendor.category,
      isApproved: vendor.isApproved,
      isFeatured: vendor.isFeatured,
      rating: vendor.rating,
      reviewCount: vendor.reviewCount,
      location: vendor.location,
      establishedYear: vendor.establishedYear,
      employeeCount: vendor.employeeCount,
      specialties: vendor.specialties,
      certifications: vendor.certifications,
      updatedAt: vendor.updatedAt,
    }));
  },
});

// Admin query to get all vendors (approved and unapproved)
export const getAllVendors = query({
  args: {
    category: v.optional(v.union(
      v.literal("legal"),
      v.literal("marketing"),
      v.literal("design"),
      v.literal("automotive"),
      v.literal("cleaning"),
      v.literal("consulting"),
      v.literal("technology"),
      v.literal("other")
    )),
    approved: v.optional(v.boolean()),
  },
  returns: v.array(v.object({
    _id: v.id("vendors"),
    _creationTime: v.number(),
    companyName: v.string(),
    website: v.optional(v.string()),
    description: v.string(),
    services: v.array(v.string()),
    contactEmail: v.optional(v.string()),
    contactPhone: v.optional(v.string()),
    logoUrl: v.optional(v.string()),
    bannerUrl: v.optional(v.string()),
    category: v.union(
      v.literal("legal"),
      v.literal("marketing"),
      v.literal("design"),
      v.literal("automotive"),
      v.literal("cleaning"),
      v.literal("consulting"),
      v.literal("technology"),
      v.literal("other")
    ),
    isApproved: v.boolean(),
    isFeatured: v.optional(v.boolean()),
    rating: v.optional(v.number()),
    reviewCount: v.optional(v.number()),
    location: v.optional(v.string()),
    establishedYear: v.optional(v.number()),
    employeeCount: v.optional(v.string()),
    specialties: v.optional(v.array(v.string())),
    certifications: v.optional(v.array(v.string())),
    createdById: v.id("users"),
    updatedAt: v.number(),
  })),
  handler: async (ctx, args) => {
    let vendors;

    if (args.category && args.approved !== undefined) {
      // Filter by both category and approved status
      vendors = await ctx.db
        .query("vendors")
        .withIndex("by_category", (q) => q.eq("category", args.category!))
        .filter((q) => q.eq(q.field("isApproved"), args.approved!))
        .order("desc")
        .collect();
    } else if (args.category) {
      vendors = await ctx.db
        .query("vendors")
        .withIndex("by_category", (q) => q.eq("category", args.category!))
        .order("desc")
        .collect();
    } else if (args.approved !== undefined) {
      vendors = await ctx.db
        .query("vendors")
        .withIndex("by_approved", (q) => q.eq("isApproved", args.approved!))
        .order("desc")
        .collect();
    } else {
      vendors = await ctx.db
        .query("vendors")
        .order("desc")
        .collect();
    }

    return vendors.map((vendor) => ({
      _id: vendor._id,
      _creationTime: vendor._creationTime,
      companyName: vendor.companyName,
      website: vendor.website,
      description: vendor.description,
      services: vendor.services,
      contactEmail: vendor.contactEmail,
      contactPhone: vendor.contactPhone,
      logoUrl: vendor.logoUrl,
      bannerUrl: vendor.bannerUrl,
      category: vendor.category,
      isApproved: vendor.isApproved,
      isFeatured: vendor.isFeatured,
      rating: vendor.rating,
      reviewCount: vendor.reviewCount,
      location: vendor.location,
      establishedYear: vendor.establishedYear,
      employeeCount: vendor.employeeCount,
      specialties: vendor.specialties,
      certifications: vendor.certifications,
      createdById: vendor.createdById,
      updatedAt: vendor.updatedAt,
    }));
  },
});

// Get a single vendor by ID
export const getVendorById = query({
  args: { vendorId: v.id("vendors") },
  returns: v.union(
    v.null(),
    v.object({
      _id: v.id("vendors"),
      _creationTime: v.number(),
      companyName: v.string(),
      website: v.optional(v.string()),
      description: v.string(),
      services: v.array(v.string()),
      contactEmail: v.optional(v.string()),
      contactPhone: v.optional(v.string()),
      logoUrl: v.optional(v.string()),
      bannerUrl: v.optional(v.string()),
      category: v.union(
        v.literal("legal"),
        v.literal("marketing"),
        v.literal("design"),
        v.literal("automotive"),
        v.literal("cleaning"),
        v.literal("consulting"),
        v.literal("technology"),
        v.literal("other")
      ),
      isApproved: v.boolean(),
      isFeatured: v.optional(v.boolean()),
      rating: v.optional(v.number()),
      reviewCount: v.optional(v.number()),
      location: v.optional(v.string()),
      establishedYear: v.optional(v.number()),
      employeeCount: v.optional(v.string()),
      specialties: v.optional(v.array(v.string())),
      certifications: v.optional(v.array(v.string())),
      createdById: v.id("users"),
      updatedAt: v.number(),
    })
  ),
  handler: async (ctx, args) => {
    const vendor = await ctx.db.get(args.vendorId);
    if (!vendor) return null;

    return {
      _id: vendor._id,
      _creationTime: vendor._creationTime,
      companyName: vendor.companyName,
      website: vendor.website,
      description: vendor.description,
      services: vendor.services,
      contactEmail: vendor.contactEmail,
      contactPhone: vendor.contactPhone,
      logoUrl: vendor.logoUrl,
      bannerUrl: vendor.bannerUrl,
      category: vendor.category,
      isApproved: vendor.isApproved,
      isFeatured: vendor.isFeatured,
      rating: vendor.rating,
      reviewCount: vendor.reviewCount,
      location: vendor.location,
      establishedYear: vendor.establishedYear,
      employeeCount: vendor.employeeCount,
      specialties: vendor.specialties,
      certifications: vendor.certifications,
      createdById: vendor.createdById,
      updatedAt: vendor.updatedAt,
    };
  },
});

// Admin mutation to create a new vendor
export const createVendor = mutation({
  args: {
    companyName: v.string(),
    website: v.optional(v.string()),
    description: v.string(),
    services: v.array(v.string()),
    contactEmail: v.optional(v.string()),
    contactPhone: v.optional(v.string()),
    logoUrl: v.optional(v.string()),
    bannerUrl: v.optional(v.string()),
    category: v.union(
      v.literal("legal"),
      v.literal("marketing"),
      v.literal("design"),
      v.literal("automotive"),
      v.literal("cleaning"),
      v.literal("consulting"),
      v.literal("technology"),
      v.literal("other")
    ),
    location: v.optional(v.string()),
    establishedYear: v.optional(v.number()),
    employeeCount: v.optional(v.string()),
    specialties: v.optional(v.array(v.string())),
    certifications: v.optional(v.array(v.string())),
    createdById: v.id("users"),
  },
  returns: v.id("vendors"),
  handler: async (ctx, args) => {
    const now = Date.now();

    return await ctx.db.insert("vendors", {
      companyName: args.companyName,
      website: args.website,
      description: args.description,
      services: args.services,
      contactEmail: args.contactEmail,
      contactPhone: args.contactPhone,
      logoUrl: args.logoUrl,
      bannerUrl: args.bannerUrl,
      category: args.category,
      isApproved: true, // Admin-created vendors are automatically approved
      isFeatured: false,
      location: args.location,
      establishedYear: args.establishedYear,
      employeeCount: args.employeeCount,
      specialties: args.specialties,
      certifications: args.certifications,
      createdById: args.createdById,
      updatedAt: now,
    });
  },
});

// Admin mutation to update a vendor
export const updateVendor = mutation({
  args: {
    vendorId: v.id("vendors"),
    companyName: v.optional(v.string()),
    website: v.optional(v.string()),
    description: v.optional(v.string()),
    services: v.optional(v.array(v.string())),
    contactEmail: v.optional(v.string()),
    contactPhone: v.optional(v.string()),
    logoUrl: v.optional(v.string()),
    bannerUrl: v.optional(v.string()),
    category: v.optional(v.union(
      v.literal("legal"),
      v.literal("marketing"),
      v.literal("design"),
      v.literal("automotive"),
      v.literal("cleaning"),
      v.literal("consulting"),
      v.literal("technology"),
      v.literal("other")
    )),
    isApproved: v.optional(v.boolean()),
    isFeatured: v.optional(v.boolean()),
    location: v.optional(v.string()),
    establishedYear: v.optional(v.number()),
    employeeCount: v.optional(v.string()),
    specialties: v.optional(v.array(v.string())),
    certifications: v.optional(v.array(v.string())),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const { vendorId, ...updates } = args;
    
    const vendor = await ctx.db.get(vendorId);
    if (!vendor) {
      throw new Error("Vendor not found");
    }

    const updateData: any = {
      updatedAt: Date.now(),
    };

    // Only include defined fields in the update
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        updateData[key] = value;
      }
    });

    await ctx.db.patch(vendorId, updateData);
    return null;
  },
});

// Admin mutation to delete a vendor
export const deleteVendor = mutation({
  args: { vendorId: v.id("vendors") },
  returns: v.null(),
  handler: async (ctx, args) => {
    const vendor = await ctx.db.get(args.vendorId);
    if (!vendor) {
      throw new Error("Vendor not found");
    }

    await ctx.db.delete(args.vendorId);
    return null;
  },
});

// Search vendors by name or description
export const searchVendors = query({
  args: {
    searchTerm: v.string(),
    category: v.optional(v.union(
      v.literal("legal"),
      v.literal("marketing"),
      v.literal("design"),
      v.literal("automotive"),
      v.literal("cleaning"),
      v.literal("consulting"),
      v.literal("technology"),
      v.literal("other")
    )),
    approvedOnly: v.optional(v.boolean()),
  },
  returns: v.array(v.object({
    _id: v.id("vendors"),
    _creationTime: v.number(),
    companyName: v.string(),
    website: v.optional(v.string()),
    description: v.string(),
    services: v.array(v.string()),
    contactEmail: v.optional(v.string()),
    contactPhone: v.optional(v.string()),
    logoUrl: v.optional(v.string()),
    bannerUrl: v.optional(v.string()),
    category: v.union(
      v.literal("legal"),
      v.literal("marketing"),
      v.literal("design"),
      v.literal("automotive"),
      v.literal("cleaning"),
      v.literal("consulting"),
      v.literal("technology"),
      v.literal("other")
    ),
    isApproved: v.boolean(),
    isFeatured: v.optional(v.boolean()),
    rating: v.optional(v.number()),
    reviewCount: v.optional(v.number()),
    location: v.optional(v.string()),
    establishedYear: v.optional(v.number()),
    employeeCount: v.optional(v.string()),
    specialties: v.optional(v.array(v.string())),
    certifications: v.optional(v.array(v.string())),
    updatedAt: v.number(),
  })),
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("vendors")
      .withSearchIndex("search_vendors", (q) => q.search("companyName", args.searchTerm));

    if (args.category) {
      query = query.filter((q) => q.eq(q.field("category"), args.category));
    }

    if (args.approvedOnly !== false) {
      query = query.filter((q) => q.eq(q.field("isApproved"), true));
    }

    const vendors = await query.collect();

    return vendors.map((vendor) => ({
      _id: vendor._id,
      _creationTime: vendor._creationTime,
      companyName: vendor.companyName,
      website: vendor.website,
      description: vendor.description,
      services: vendor.services,
      contactEmail: vendor.contactEmail,
      contactPhone: vendor.contactPhone,
      logoUrl: vendor.logoUrl,
      bannerUrl: vendor.bannerUrl,
      category: vendor.category,
      isApproved: vendor.isApproved,
      isFeatured: vendor.isFeatured,
      rating: vendor.rating,
      reviewCount: vendor.reviewCount,
      location: vendor.location,
      establishedYear: vendor.establishedYear,
      employeeCount: vendor.employeeCount,
      specialties: vendor.specialties,
      certifications: vendor.certifications,
      updatedAt: vendor.updatedAt,
    }));
  },
});

// Get vendor statistics for admin dashboard
export const getVendorStats = query({
  args: {},
  returns: v.object({
    totalVendors: v.number(),
    approvedVendors: v.number(),
    pendingVendors: v.number(),
    featuredVendors: v.number(),
    vendorsByCategory: v.object({
      legal: v.number(),
      marketing: v.number(),
      design: v.number(),
      automotive: v.number(),
      cleaning: v.number(),
      consulting: v.number(),
      technology: v.number(),
      other: v.number(),
    }),
  }),
  handler: async (ctx) => {
    const allVendors = await ctx.db.query("vendors").collect();
    
    const totalVendors = allVendors.length;
    const approvedVendors = allVendors.filter(v => v.isApproved).length;
    const pendingVendors = allVendors.filter(v => !v.isApproved).length;
    const featuredVendors = allVendors.filter(v => v.isFeatured).length;

    const vendorsByCategory = {
      legal: allVendors.filter(v => v.category === "legal").length,
      marketing: allVendors.filter(v => v.category === "marketing").length,
      design: allVendors.filter(v => v.category === "design").length,
      automotive: allVendors.filter(v => v.category === "automotive").length,
      cleaning: allVendors.filter(v => v.category === "cleaning").length,
      consulting: allVendors.filter(v => v.category === "consulting").length,
      technology: allVendors.filter(v => v.category === "technology").length,
      other: allVendors.filter(v => v.category === "other").length,
    };

    return {
      totalVendors,
      approvedVendors,
      pendingVendors,
      featuredVendors,
      vendorsByCategory,
    };
  },
});
