import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { ConvexError } from "convex/values";
import { 
  getAuthUser, 
  requireAuth, 
} from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";

/**
 * Create a new offer on a product
 */
export const createOffer = mutation({
  args: {
    productId: v.id("products"),
    offerAmount: v.number(),
    message: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Verify user has active subscription
    const hasActiveSubscription = user.subscriptionStatus === "active" && 
      (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());

    // if (!hasActiveSubscription) {
    //   throw new ConvexError("Active subscription required to make offers");
    // }

    // Get product
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new ConvexError("Product not found");
    }

    // Validate product availability
    if (product.status !== "active") {
      throw new ConvexError("Product is not available for offers");
    }

    // Prevent self-offers
    if (product.sellerId === user._id) {
      throw new ConvexError("Cannot make offers on your own product");
    }

    // Validate offer amount (must be positive and reasonable)
    if (args.offerAmount <= 0) {
      throw new ConvexError("Offer amount must be greater than 0");
    }

    if (args.offerAmount > product.price * 1.5) {
      throw new ConvexError("Offer amount cannot exceed 150% of listing price");
    }

    // Check for existing pending offers from this buyer
    const existingOffer = await ctx.db
      .query("offers")
      .withIndex("by_buyer_status", (q) => 
        q.eq("buyerId", user._id).eq("status", "pending")
      )
      .filter((q) => q.eq(q.field("productId"), args.productId))
      .first();

    if (existingOffer) {
      throw new ConvexError("You already have a pending offer on this product");
    }

    const now = Date.now();
    const expiresAt = now + (7 * 24 * 60 * 60 * 1000); // 7 days from now

    // Create offer
    const offerId = await ctx.db.insert("offers", {
      productId: args.productId,
      buyerId: user._id,
      sellerId: product.sellerId,
      offerAmount: args.offerAmount,
      message: args.message?.trim() || "",
      status: "pending",
      expiresAt,
      updatedAt: now,
    });

    return offerId;
  },
});

/**
 * Accept an offer
 */
export const acceptOffer = mutation({
  args: {
    offerId: v.id("offers"),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Get offer
    const offer = await ctx.db.get(args.offerId);
    if (!offer) {
      throw new ConvexError("Offer not found");
    }

    // Verify seller ownership
    if (offer.sellerId !== user._id) {
      throw new ConvexError("Only the seller can accept offers");
    }

    // Check offer status
    if (offer.status !== "pending") {
      throw new ConvexError("Offer is not in pending status");
    }

    // Check if offer has expired
    if (offer.expiresAt < Date.now()) {
      throw new ConvexError("Offer has expired");
    }

    // Get product to verify it's still available
    const product = await ctx.db.get(offer.productId);
    if (!product || product.status !== "active") {
      throw new ConvexError("Product is no longer available");
    }

    const now = Date.now();

    // Update offer status
    await ctx.db.patch(args.offerId, {
      status: "accepted",
      updatedAt: now,
    });

    // Update product status to reserved
    await ctx.db.patch(offer.productId, {
      status: "reserved",
      updatedAt: now,
    });

    // Decline all other pending offers on this product
    const otherOffers = await ctx.db
      .query("offers")
      .withIndex("by_productId", (q) => q.eq("productId", offer.productId))
      .filter((q) => 
        q.and(
          q.neq(q.field("_id"), args.offerId),
          q.eq(q.field("status"), "pending")
        )
      )
      .collect();

    for (const otherOffer of otherOffers) {
      await ctx.db.patch(otherOffer._id, {
        status: "declined",
        updatedAt: now,
      });
    }

    return { success: true };
  },
});

/**
 * Decline an offer
 */
export const declineOffer = mutation({
  args: {
    offerId: v.id("offers"),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Get offer
    const offer = await ctx.db.get(args.offerId);
    if (!offer) {
      throw new ConvexError("Offer not found");
    }

    // Verify seller ownership
    if (offer.sellerId !== user._id) {
      throw new ConvexError("Only the seller can decline offers");
    }

    // Check offer status
    if (offer.status !== "pending") {
      throw new ConvexError("Offer is not in pending status");
    }

    const now = Date.now();

    // Update offer status
    await ctx.db.patch(args.offerId, {
      status: "declined",
      updatedAt: now,
    });

    return { success: true };
  },
});

/**
 * Counter an offer
 */
export const counterOffer = mutation({
  args: {
    offerId: v.id("offers"),
    counterAmount: v.number(),
    message: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Get offer
    const offer = await ctx.db.get(args.offerId);
    if (!offer) {
      throw new ConvexError("Offer not found");
    }

    // Verify seller ownership
    if (offer.sellerId !== user._id) {
      throw new ConvexError("Only the seller can counter offers");
    }

    // Check offer status
    if (offer.status !== "pending") {
      throw new ConvexError("Offer is not in pending status");
    }

    // Check if offer has expired
    if (offer.expiresAt < Date.now()) {
      throw new ConvexError("Offer has expired");
    }

    // Validate counter amount
    if (args.counterAmount <= 0) {
      throw new ConvexError("Counter amount must be greater than 0");
    }

    // Get product to check original price
    const product = await ctx.db.get(offer.productId);
    if (!product) {
      throw new ConvexError("Product not found");
    }

    if (args.counterAmount > product.price * 1.5) {
      throw new ConvexError("Counter amount cannot exceed 150% of listing price");
    }

    const now = Date.now();

    // Update offer status
    await ctx.db.patch(args.offerId, {
      status: "countered",
      counterOffer: args.counterAmount,
      counterMessage: args.message?.trim() || "",
      updatedAt: now,
    });

    return { success: true };
  },
});

/**
 * Accept a counter offer
 */
export const acceptCounterOffer = mutation({
  args: {
    offerId: v.id("offers"),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Get offer
    const offer = await ctx.db.get(args.offerId);
    if (!offer) {
      throw new ConvexError("Offer not found");
    }

    // Verify buyer ownership
    if (offer.buyerId !== user._id) {
      throw new ConvexError("Only the buyer can accept counter offers");
    }

    // Check offer status
    if (offer.status !== "countered") {
      throw new ConvexError("Offer is not in countered status");
    }

    // Check if offer has expired
    if (offer.expiresAt < Date.now()) {
      throw new ConvexError("Offer has expired");
    }

    const now = Date.now();

    // Update offer status to accepted
    await ctx.db.patch(args.offerId, {
      status: "accepted",
      updatedAt: now,
    });

    // Update product status to reserved
    await ctx.db.patch(offer.productId, {
      status: "reserved",
      updatedAt: now,
    });

    // Decline all other pending offers on this product
    const otherOffers = await ctx.db
      .query("offers")
      .withIndex("by_productId", (q) => q.eq("productId", offer.productId))
      .filter((q) => 
        q.and(
          q.neq(q.field("_id"), args.offerId),
          q.eq(q.field("status"), "pending")
        )
      )
      .collect();

    for (const otherOffer of otherOffers) {
      await ctx.db.patch(otherOffer._id, {
        status: "declined",
        updatedAt: now,
      });
    }

    return { success: true };
  },
});

/**
 * Respond to an offer with a message (seller only)
 */
export const respondToOffer = mutation({
  args: {
    offerId: v.id("offers"),
    message: v.string(),
    responseType: v.optional(v.union(
      v.literal("info"),
      v.literal("negotiate"),
      v.literal("other")
    )),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Get offer
    const offer = await ctx.db.get(args.offerId);
    if (!offer) {
      throw new ConvexError("Offer not found");
    }

    // Verify seller ownership
    if (offer.sellerId !== user._id) {
      throw new ConvexError("Only the seller can respond to offers");
    }

    // Check offer status
    if (offer.status !== "pending") {
      throw new ConvexError("Can only respond to pending offers");
    }

    const now = Date.now();

    // Update offer with response
    await ctx.db.patch(args.offerId, {
      sellerResponse: {
        message: args.message,
        responseType: args.responseType || "other",
        timestamp: now,
      },
      updatedAt: now,
    });

    return { success: true };
  },
});

/**
 * Withdraw an offer (buyer only)
 */
export const withdrawOffer = mutation({
  args: {
    offerId: v.id("offers"),
  },
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Get offer
    const offer = await ctx.db.get(args.offerId);
    if (!offer) {
      throw new ConvexError("Offer not found");
    }

    // Verify buyer ownership
    if (offer.buyerId !== user._id) {
      throw new ConvexError("Only the buyer can withdraw offers");
    }

    // Check offer status
    if (offer.status !== "pending") {
      throw new ConvexError("Offer cannot be withdrawn in current status");
    }

    const now = Date.now();

    // Update offer status
    await ctx.db.patch(args.offerId, {
      status: "withdrawn",
      updatedAt: now,
    });

    return { success: true };
  },
});

/**
 * Get offers for a seller (pending, countered, accepted, declined)
 */
export const getSellerOffers = query({
  args: {
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("countered"),
      v.literal("accepted"),
      v.literal("declined"),
      v.literal("expired"),
      v.literal("withdrawn")
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user || user.userType !== "seller") {
      return [];
    }

    let baseQuery = ctx.db
      .query("offers")
      .withIndex("by_sellerId", (q) => q.eq("sellerId", user._id));

    if (args.status) {
      baseQuery = baseQuery.filter((q) => q.eq(q.field("status"), args.status));
    } else {
      // Default to pending and countered offers
      baseQuery = baseQuery.filter((q) => 
        q.or(
          q.eq(q.field("status"), "pending"),
          q.eq(q.field("status"), "countered")
        )
      );
    }

    const offers = await baseQuery.order("desc").take(args.limit || 50);
    const enrichedOffers = await Promise.all(
      offers.map(async (offer) => {
        const [product, buyer] = await Promise.all([
          ctx.db.get(offer.productId),
          ctx.db.get(offer.buyerId),
        ]);

        // Convert storage IDs to URLs for product images
        let imageUrls: string[] = [];
        if (product && product.images && product.images.length > 0) {
          const imageUrlPromises = product.images
            .filter(imageId => imageId !== "")
            .map(async (imageId) => {
              const url = await ctx.storage.getUrl(imageId);
              return url;
            });
          
          const imageUrlResults = await Promise.all(imageUrlPromises);
          imageUrls = imageUrlResults.filter((url): url is string => url !== null);
        }

        return {
          ...offer,
          product: product ? {
            _id: product._id,
            title: product.title,
            brand: product.brand,
            price: product.price,
            images: imageUrls,
            condition: product.condition,
          } : null,
          buyer: buyer ? {
            _id: buyer._id,
            name: buyer.name,
            email: buyer.email,
          } : null,
        };
      })
    );

    return enrichedOffers;
  },
});

/**
 * Get offers for a buyer
 */
export const getBuyerOffers = query({
  args: {
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("countered"),
      v.literal("accepted"),
      v.literal("declined"),
      v.literal("expired"),
      v.literal("withdrawn")
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      return [];
    }

    let baseQuery = ctx.db
      .query("offers")
      .withIndex("by_buyerId", (q) => q.eq("buyerId", user._id));

    if (args.status) {
      baseQuery = baseQuery.filter((q) => q.eq(q.field("status"), args.status));
    }

    const offers = await baseQuery.order("desc").take(args.limit || 50);

    // Enrich offers with product and seller information
    const enrichedOffers = await Promise.all(
      offers.map(async (offer) => {
        const [product, seller] = await Promise.all([
          ctx.db.get(offer.productId),
          ctx.db.get(offer.sellerId),
        ]);

        // Convert storage IDs to URLs for product images
        let imageUrls: string[] = [];
        if (product && product.images && product.images.length > 0) {
          const imageUrlPromises = product.images
            .filter(imageId => imageId !== "")
            .map(async (imageId) => {
              const url = await ctx.storage.getUrl(imageId);
              return url;
            });
          
          const imageUrlResults = await Promise.all(imageUrlPromises);
          imageUrls = imageUrlResults.filter((url): url is string => url !== null);
        }

        return {
          ...offer,
          product: product ? {
            _id: product._id,
            title: product.title,
            brand: product.brand,
            price: product.price,
            images: imageUrls,
            condition: product.condition,
          } : null,
          seller: seller ? {
            _id: seller._id,
            name: seller.name,
            email: seller.email,
          } : null,
        };
      })
    );

    return enrichedOffers;
  },
});

/**
 * Get offers for a specific product
 */
export const getProductOffers = query({
  args: {
    productId: v.id("products"),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      return [];
    }

    // Get product to verify access
    const product = await ctx.db.get(args.productId);
    if (!product) {
      return [];
    }

    // Only show offers if user is the seller or has made an offer
    if (product.sellerId !== user._id) {
      // Check if user has made an offer on this product
      const userOffer = await ctx.db
        .query("offers")
        .withIndex("by_buyer_status", (q) => 
          q.eq("buyerId", user._id).eq("status", "pending")
        )
        .filter((q) => q.eq(q.field("productId"), args.productId))
        .first();

      if (!userOffer) {
        return [];
      }
    }

    const offers = await ctx.db
      .query("offers")
      .withIndex("by_productId", (q) => q.eq("productId", args.productId))
      .order("desc")
      .collect();

    // Enrich offers with user information
    const enrichedOffers = await Promise.all(
      offers.map(async (offer) => {
        const [buyer, seller] = await Promise.all([
          ctx.db.get(offer.buyerId),
          ctx.db.get(offer.sellerId),
        ]);

        return {
          ...offer,
          buyer: buyer ? {
            _id: buyer._id,
            name: buyer.name,
            email: buyer.email,
          } : null,
          seller: seller ? {
            _id: seller._id,
            name: seller.name,
            email: seller.email,
          } : null,
        };
      })
    );

    return enrichedOffers;
  },
});

/**
 * Get offer count for a seller (for dashboard)
 */
export const getSellerOfferCounts = query({
  args: {},
  handler: async (ctx) => {
    const user = await getAuthUser(ctx);
    if (!user || user.userType !== "seller") {
      return { pending: 0, countered: 0, total: 0 };
    }

    const [pendingOffers, counteredOffers, totalOffers] = await Promise.all([
      ctx.db
        .query("offers")
        .withIndex("by_seller_status", (q) => 
          q.eq("sellerId", user._id).eq("status", "pending")
        )
        .collect(),
      ctx.db
        .query("offers")
        .withIndex("by_seller_status", (q) => 
          q.eq("sellerId", user._id).eq("status", "countered")
        )
        .collect(),
      ctx.db
        .query("offers")
        .withIndex("by_sellerId", (q) => q.eq("sellerId", user._id))
        .collect(),
    ]);

    return {
      pending: pendingOffers.length,
      countered: counteredOffers.length,
      total: totalOffers.length,
    };
  },
});

/**
 * Send a message in an offer conversation
 */
export const sendOfferMessage = mutation({
  args: {
    offerId: v.id("offers"),
    message: v.string(),
    messageType: v.union(
      v.literal("offer"),
      v.literal("counter"),
      v.literal("negotiation"),
      v.literal("question"),
      v.literal("info"),
      v.literal("other")
    ),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      throw new ConvexError("Authentication required");
    }

    // Get the offer to verify access
    const offer = await ctx.db.get(args.offerId);
    if (!offer) {
      throw new ConvexError("Offer not found");
    }

    // Verify user is part of this offer (buyer or seller)
    if (offer.buyerId !== user._id && offer.sellerId !== user._id) {
      throw new ConvexError("Unauthorized to send message in this offer");
    }

    // Determine receiver ID
    const receiverId = offer.buyerId === user._id ? offer.sellerId : offer.buyerId;

    // Create the message
    const messageId = await ctx.db.insert("offerMessages", {
      offerId: args.offerId,
      senderId: user._id,
      receiverId: receiverId,
      message: args.message,
      messageType: args.messageType,
      isRead: false,
      updatedAt: Date.now(),
    });

    // Update the offer's updatedAt timestamp
    await ctx.db.patch(args.offerId, {
      updatedAt: Date.now(),
    });

    return { messageId, success: true };
  },
});

/**
 * Get messages for a specific offer
 */
export const getOfferMessages = query({
  args: {
    offerId: v.id("offers"),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      return [];
    }

    // Get the offer to verify access
    const offer = await ctx.db.get(args.offerId);
    if (!offer) {
      return [];
    }

    // Verify user is part of this offer (buyer or seller)
    if (offer.buyerId !== user._id && offer.sellerId !== user._id) {
      return [];
    }

    // Get messages for this offer, ordered by creation time
    const messages = await ctx.db
      .query("offerMessages")
      .withIndex("by_offerId", (q) => q.eq("offerId", args.offerId))
      .order("asc")
      .collect();

    // Enrich messages with sender information
    const enrichedMessages = await Promise.all(
      messages.map(async (message) => {
        const sender = await ctx.db.get(message.senderId);
        return {
          ...message,
          sender: sender ? {
            _id: sender._id,
            name: sender.name,
            email: sender.email,
          } : null,
        };
      })
    );

    return enrichedMessages;
  },
});

/**
 * Mark offer messages as read
 */
export const markOfferMessagesAsRead = mutation({
  args: {
    offerId: v.id("offers"),
  },
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      throw new ConvexError("Authentication required");
    }

    // Get the offer to verify access
    const offer = await ctx.db.get(args.offerId);
    if (!offer) {
      throw new ConvexError("Offer not found");
    }

    // Verify user is part of this offer (buyer or seller)
    if (offer.buyerId !== user._id && offer.sellerId !== user._id) {
      throw new ConvexError("Unauthorized to mark messages as read");
    }

    // Mark all unread messages for this user in this offer as read
    const unreadMessages = await ctx.db
      .query("offerMessages")
      .withIndex("by_unread", (q) => 
        q.eq("receiverId", user._id).eq("isRead", false)
      )
      .filter((q) => q.eq(q.field("offerId"), args.offerId))
      .collect();

    // Update each message
    for (const message of unreadMessages) {
      await ctx.db.patch(message._id, {
        isRead: true,
        readAt: Date.now(),
        updatedAt: Date.now(),
      });
    }

    return { success: true, updatedCount: unreadMessages.length };
  },
});

/**
 * Get unread message count for a user across all offers
 */
export const getUnreadOfferMessageCount = query({
  args: {},
  handler: async (ctx) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      return 0;
    }

    const unreadMessages = await ctx.db
      .query("offerMessages")
      .withIndex("by_unread", (q) => 
        q.eq("receiverId", user._id).eq("isRead", false)
      )
      .collect();

    return unreadMessages.length;
  },
});
