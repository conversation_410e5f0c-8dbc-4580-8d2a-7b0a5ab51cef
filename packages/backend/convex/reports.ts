import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUser } from "./lib/auth_utils";

/**
 * Create a user report
 */
export const createUserReport = mutation({
  args: {
    reportedUserId: v.id("users"),
    reason: v.string(),
    description: v.optional(v.string()),
    context: v.optional(v.string()),
  },
  returns: v.id("reports"),
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      throw new Error("Not authenticated");
    }

    // Don't allow users to report themselves
    if (user._id === args.reportedUserId) {
      throw new Error("Cannot report yourself");
    }

    // Check if user has already reported this user recently (within 24 hours)
    const existingReport = await ctx.db
      .query("reports")
      .withIndex("by_reporter_and_reported", (q) => 
        q.eq("reporterId", user._id).eq("reportedUserId", args.reportedUserId)
      )
      .filter((q) => 
        q.gte(q.field("_creationTime"), Date.now() - 24 * 60 * 60 * 1000)
      )
      .first();

    if (existingReport) {
      throw new Error("You have already reported this user recently");
    }

    // Create the report
    const now = Date.now();
    const reportId = await ctx.db.insert("reports", {
      reporterId: user._id,
      reportedUserId: args.reportedUserId,
      reason: args.reason,
      description: args.description || "",
      context: args.context || "general",
      status: "pending",
      updatedAt: now,
    });

    return reportId;
  },
});

/**
 * Get reports for admin review
 */
export const getReportsForAdmin = query({
  args: {
    status: v.optional(v.union(v.literal("pending"), v.literal("reviewed"), v.literal("resolved"))),
    limit: v.optional(v.number()),
  },
  returns: v.array(v.object({
    _id: v.id("reports"),
    _creationTime: v.number(),
    reporterId: v.id("users"),
    reportedUserId: v.id("users"),
    reason: v.string(),
    description: v.string(),
    context: v.string(),
    status: v.string(),
    reporter: v.object({
      _id: v.id("users"),
      name: v.string(),
      email: v.string(),
      userType: v.string(),
    }),
    reportedUser: v.object({
      _id: v.id("users"),
      name: v.string(),
      email: v.string(),
      userType: v.string(),
    }),
  })),
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user || user.userType !== "admin") {
      throw new Error("Not authorized");
    }

    let reportsQuery = ctx.db.query("reports");
    
    if (args.status) {
      reportsQuery = reportsQuery.filter((q) => q.eq(q.field("status"), args.status));
    }

    const reports = await (reportsQuery
      .order("desc")
      .take(args.limit || 50) as any)
      .collect();

    // Get user details for each report
    const reportsWithUsers = [];
    for (const report of reports) {
      const reporter = await ctx.db.get(report.reporterId);
      const reportedUser = await ctx.db.get(report.reportedUserId);

      reportsWithUsers.push({
        ...report,
        reporter: {
          _id: (reporter as any)?._id || report.reporterId,
          name: (reporter as any)?.name || "Unknown User",
          email: (reporter as any)?.email || "<EMAIL>",
          userType: (reporter as any)?.userType || "consumer",
        },
        reportedUser: {
          _id: (reportedUser as any)?._id || report.reportedUserId,
          name: (reportedUser as any)?.name || "Unknown User",
          email: (reportedUser as any)?.email || "<EMAIL>",
          userType: (reportedUser as any)?.userType || "consumer",
        },
      });
    }

    return reportsWithUsers;
  },
});

/**
 * Update report status (admin only)
 */
export const updateReportStatus = mutation({
  args: {
    reportId: v.id("reports"),
    status: v.union(v.literal("pending"), v.literal("reviewed"), v.literal("resolved")),
    adminNotes: v.optional(v.string()),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user || user.userType !== "admin") {
      throw new Error("Not authorized");
    }

    await ctx.db.patch(args.reportId, {
      status: args.status,
      adminNotes: args.adminNotes,
      reviewedBy: user._id,
      reviewedAt: Date.now(),
    });

    return null;
  },
});

/**
 * Block a user
 */
export const blockUser = mutation({
  args: {
    blockedUserId: v.id("users"),
    reason: v.optional(v.string()),
  },
  returns: v.id("userBlocks"),
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      throw new Error("Not authenticated");
    }

    // Don't allow users to block themselves
    if (user._id === args.blockedUserId) {
      throw new Error("Cannot block yourself");
    }

    // Check if user is already blocked
    const existingBlock = await ctx.db
      .query("userBlocks")
      .withIndex("by_blocker_blocked", (q) => 
        q.eq("blockerId", user._id).eq("blockedUserId", args.blockedUserId)
      )
      .first();

    if (existingBlock) {
      throw new Error("User is already blocked");
    }

    // Create the block
    const now = Date.now();
    const blockId = await ctx.db.insert("userBlocks", {
      blockerId: user._id,
      blockedUserId: args.blockedUserId,
      reason: args.reason || "",
      isActive: true,
      blockedAt: now,
      updatedAt: now,
    });

    return blockId;
  },
});

/**
 * Unblock a user
 */
export const unblockUser = mutation({
  args: {
    blockedUserId: v.id("users"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      throw new Error("Not authenticated");
    }

    // Find and deactivate the block
    const existingBlock = await ctx.db
      .query("userBlocks")
      .withIndex("by_blocker_blocked", (q) => 
        q.eq("blockerId", user._id).eq("blockedUserId", args.blockedUserId)
      )
      .first();

    if (!existingBlock) {
      throw new Error("User is not blocked");
    }

    await ctx.db.patch(existingBlock._id, {
      isActive: false,
      updatedAt: Date.now(),
    });

    return null;
  },
});

/**
 * Check if a user is blocked by the current user
 */
export const isUserBlocked = query({
  args: {
    userId: v.id("users"),
  },
  returns: v.boolean(),
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      return false;
    }

    const block = await ctx.db
      .query("userBlocks")
      .withIndex("by_blocker_blocked", (q) => 
        q.eq("blockerId", user._id).eq("blockedUserId", args.userId)
      )
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    return !!block;
  },
});

/**
 * Get list of users blocked by the current user
 */
export const getBlockedUsers = query({
  args: {},
  returns: v.array(v.object({
    _id: v.id("userBlocks"),
    blockedUserId: v.id("users"),
    reason: v.optional(v.string()),
    blockedAt: v.number(),
    blockedUser: v.object({
      _id: v.id("users"),
      name: v.string(),
      email: v.string(),
      userType: v.string(),
    }),
  })),
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    if (!user) {
      return [];
    }

    const blocks = await ctx.db
      .query("userBlocks")
      .withIndex("by_blockerId", (q) => q.eq("blockerId", user._id))
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .collect();

    const blocksWithUsers = [];
    for (const block of blocks) {
      const blockedUser = await ctx.db.get(block.blockedUserId);
      if (blockedUser) {
        blocksWithUsers.push({
          _id: block._id,
          blockedUserId: block.blockedUserId,
          reason: block.reason,
          blockedAt: block.blockedAt,
          blockedUser: {
            _id: (blockedUser as any)._id,
            name: (blockedUser as any).name,
            email: (blockedUser as any).email,
            userType: (blockedUser as any).userType,
          },
        });
      }
    }

    return blocksWithUsers;
  },
});
