import { v } from "convex/values";
import { query } from "./_generated/server";
import { ConvexError } from "convex/values";
import { 
  getAuthUser, 
  requireAuth 
} from "./lib/auth_utils";
import type { Id } from "./_generated/dataModel";

/**
 * Get products by marketplace type with filtering and pagination
 */
export const getProductsByMarketplace = query({
  args: {
    marketplaceType: v.union(
      v.literal("moda-watch"),
      v.literal("real-watch-buyers"),
      v.literal("moda-car"),
      v.literal("moda-lifestyle"),
      v.literal("moda-misc")
    ),
    category: v.optional(v.string()),
    minPrice: v.optional(v.number()),
    maxPrice: v.optional(v.number()),
    brand: v.optional(v.string()),
    condition: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  returns: v.array(v.object({
    _id: v.id("products"),
    _creationTime: v.number(),
    sellerId: v.id("users"),
    title: v.string(),
    description: v.string(),
    price: v.number(),
    marketplaceType: v.optional(v.string()),
    category: v.string(),
    brand: v.string(),
    condition: v.string(),
    images: v.array(v.string()),
    status: v.string(),
    views: v.number(),
    favorites: v.number(),
    // Additional optional fields that might exist in products
    color: v.optional(v.string()),
    size: v.optional(v.string()),
    material: v.optional(v.string()),
    model: v.optional(v.string()),
    year: v.optional(v.number()),
    originalPrice: v.optional(v.number()),
    sku: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    sellerInfo: v.optional(v.object({
      name: v.string(),
      profileImage: v.optional(v.string()),
      isVerified: v.optional(v.boolean()),
    })),
  })),
  handler: async (ctx, args) => {
    const user = await getAuthUser(ctx);
    
    // Check if user is authenticated
    if (!user) {
      throw new ConvexError("Authentication required to view marketplace");
    }
    
    // Check if user has valid subscription (active or trial)
    const hasValidSubscription = user.subscriptionStatus === "active" || 
                                 user.subscriptionStatus === "trial" ||
                                 (!user.subscriptionExpiresAt || user.subscriptionExpiresAt > Date.now());
    
    if (!hasValidSubscription) {
      throw new ConvexError("Active subscription required to view marketplace");
    }

    // Build query with marketplace type filter
    // Only get products that belong to the specific marketplace type
    let products = await ctx.db
      .query("products")
      .withIndex("by_marketplaceType_and_status", (q) => 
        q.eq("marketplaceType", args.marketplaceType).eq("status", "active")
      )
      .collect();

    // If no products found with marketplace type, return empty array
    // This ensures each marketplace only shows its own products
    if (products.length === 0) {
      return [];
    }

    // Filter by category if provided
    if (args.category) {
      products = products.filter(p => p.category === args.category);
    }

    // Filter by price range
    if (args.minPrice !== undefined) {
      products = products.filter(p => p.price >= args.minPrice!);
    }
    if (args.maxPrice !== undefined) {
      products = products.filter(p => p.price <= args.maxPrice!);
    }

    // Filter by brand
    if (args.brand) {
      products = products.filter(p => 
        p.brand.toLowerCase().includes(args.brand!.toLowerCase())
      );
    }

    // Filter by condition
    if (args.condition) {
      products = products.filter(p => p.condition === args.condition);
    }

    // Sort by creation time (newest first)
    products.sort((a, b) => b._creationTime - a._creationTime);

    // Apply limit
    const limit = args.limit || 50;
    products = products.slice(0, limit);

    // Enrich with seller information and image URLs
    const enrichedProducts = await Promise.all(
      products.map(async (product) => {
        // Get seller info
        const seller = await ctx.db.get(product.sellerId);
        
        // Get image URLs
        const imageUrls = await Promise.all(
          product.images.map(async (imageId) => {
            const url = await ctx.storage.getUrl(imageId as Id<"_storage">);
            return url || "";
          })
        );

        return {
          _id: product._id,
          _creationTime: product._creationTime,
          sellerId: product.sellerId,
          title: product.title,
          description: product.description,
          price: product.price,
          marketplaceType: product.marketplaceType || "luxury", // Provide default for legacy products
          category: product.category as string,
          brand: product.brand as string,
          condition: product.condition as string,
          status: product.status as string,
          views: product.views,
          favorites: product.favorites,
          images: imageUrls.filter(url => url !== ""),
          // Include optional fields if they exist
          color: product.color,
          size: product.size,
          material: product.material,
          model: product.model,
          year: product.year,
          originalPrice: product.originalPrice,
          sku: product.sku,
          tags: product.tags,
          sellerInfo: seller ? {
            name: seller.name,
            profileImage: seller.profileImage,
            isVerified: seller.isVerified,
          } : undefined,
        };
      })
    );

    return enrichedProducts;
  },
});

/**
 * Get available marketplace types
 */
export const getMarketplaceTypes = query({
  args: {},
  returns: v.array(v.object({
    type: v.string(),
    name: v.string(),
    description: v.string(),
    icon: v.string(),
  })),
  handler: async (ctx, args) => {
    return [
      {
        type: "general",
        name: "General",
        description: "General marketplace",
        icon: "Globe",
      },
      {
        type: "moda-watch",
        name: "Moda Watch Club",
        description: "Premium timepieces and luxury watches",
        icon: "Clock",
      },
      {
        type: "real-watch-buyers",
        name: "Real Watch Buyers",
        description: "Authentic watch trading and sales",
        icon: "Clock",
      },
      {
        type: "moda-car",
        name: "Moda Car Club",
        description: "Luxury cars and collectible vehicles",
        icon: "Car",
      },
      {
        type: "moda-lifestyle",
        name: "Moda Lifestyle Club",
        description: "Fashion, jewelry, and luxury lifestyle items",
        icon: "Crown",
      },
      {
        type: "moda-misc",
        name: "Moda Misc Club",
        description: "Art, collectibles, and miscellaneous luxury items",
        icon: "Palette",
      },
    ];
  },
});

/**
 * Get marketplace statistics for overview page
 */
export const getMarketplaceStats = query({
  args: {},
  returns: v.array(v.object({
    marketplaceType: v.string(),
    productCount: v.number(),
    sellerCount: v.number(),
  })),
  handler: async (ctx, args) => {
    const marketplaceTypes = ["moda-watch", "real-watch-buyers", "moda-car", "moda-lifestyle", "moda-misc"];
    
    const stats = await Promise.all(
      marketplaceTypes.map(async (type) => {
        // Get product count for this marketplace
        const products = await ctx.db
          .query("products")
          .filter((q) => 
            q.and(
              q.eq(q.field("marketplaceType"), type),
              q.eq(q.field("status"), "active")
            )
          )
          .collect();
        
        // Get unique seller count for this marketplace
        const sellerIds = new Set(products.map(p => p.sellerId));
        
        return {
          marketplaceType: type,
          productCount: products.length,
          sellerCount: sellerIds.size,
        };
      })
    );
    
    return stats;
  },
});
