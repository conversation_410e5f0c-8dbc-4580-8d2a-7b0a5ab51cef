import { mutation } from "./_generated/server";
import { v } from "convex/values";

// Mutation to seed the database with sample vendors
export const seedSampleVendors = mutation({
  args: {
    createdById: v.id("users"), // Admin user ID
  },
  returns: v.array(v.id("vendors")),
  handler: async (ctx, args) => {
    const now = Date.now();

    // Check if vendors already exist to avoid duplicates
    const existingVendors = await ctx.db.query("vendors").collect();
    if (existingVendors.length > 0) {
      throw new Error("Vendors already exist. Use this function only for initial seeding.");
    }

    const sampleVendors = [
      {
        companyName: "Elite Legal Partners",
        website: "https://elitelegalpartners.com",
        description: "Premier luxury legal services specializing in high-net-worth individuals and luxury businesses. We provide comprehensive legal counsel for luxury goods transactions, intellectual property protection, and business formation.",
        services: [
          "Luxury Business Legal Counsel",
          "Intellectual Property Protection",
          "Contract Negotiation",
          "Estate Planning",
          "Tax Optimization",
          "Litigation Support"
        ],
        contactEmail: "<EMAIL>",
        contactPhone: "+****************",
        logoUrl: "/logos/elite-legal.png",
        category: "legal" as const,
        location: "New York, NY",
        establishedYear: 2010,
        employeeCount: "11-50",
        specialties: [
          "Luxury Goods Law",
          "High-Net-Worth Estate Planning",
          "International Business Law",
          "Intellectual Property"
        ],
        certifications: [
          "American Bar Association",
          "New York State Bar",
          "International Bar Association"
        ],
        isApproved: true,
        isFeatured: true,
        rating: 4.9,
        reviewCount: 47,
        createdById: args.createdById,
        updatedAt: now,
      },
      {
        companyName: "Luxe Web Design Studio",
        website: "https://luxewebdesign.studio",
        description: "Award-winning web design and digital marketing agency specializing in luxury brands. We create stunning, high-converting websites and digital experiences that reflect the sophistication of luxury businesses.",
        services: [
          "Luxury Website Design",
          "E-commerce Development",
          "Brand Identity Design",
          "Digital Marketing Strategy",
          "SEO Optimization",
          "Social Media Management"
        ],
        contactEmail: "<EMAIL>",
        contactPhone: "+****************",
        logoUrl: "/logos/luxe-web-design.png",
        category: "design" as const,
        location: "Los Angeles, CA",
        establishedYear: 2015,
        employeeCount: "6-10",
        specialties: [
          "Luxury Brand Web Design",
          "E-commerce Platforms",
          "Mobile-First Design",
          "User Experience Design"
        ],
        certifications: [
          "Google Partner",
          "Shopify Partner",
          "Adobe Certified Expert"
        ],
        isApproved: true,
        isFeatured: true,
        rating: 4.8,
        reviewCount: 62,
        createdById: args.createdById,
        updatedAt: now,
      },
      {
        companyName: "Prestige Auto Group",
        website: "https://prestigeautogroup.com",
        description: "Exclusive luxury and exotic car dealership offering the finest selection of premium vehicles. From rare collectibles to the latest supercars, we provide white-glove service for discerning automotive enthusiasts.",
        services: [
          "Luxury Car Sales",
          "Exotic Car Sourcing",
          "Vehicle Consignment",
          "Automotive Appraisal",
          "Concierge Delivery",
          "Maintenance & Service"
        ],
        contactEmail: "<EMAIL>",
        contactPhone: "+****************",
        logoUrl: "/logos/prestige-auto.png",
        category: "automotive" as const,
        location: "Miami, FL",
        establishedYear: 2008,
        employeeCount: "21-50",
        specialties: [
          "Ferrari & Lamborghini",
          "Classic Car Collection",
          "Limited Edition Vehicles",
          "Custom Modifications"
        ],
        certifications: [
          "Ferrari Authorized Dealer",
          "McLaren Certified",
          "Porsche Premier Dealer"
        ],
        isApproved: true,
        isFeatured: false,
        rating: 4.7,
        reviewCount: 89,
        createdById: args.createdById,
        updatedAt: now,
      },
      {
        companyName: "Platinum Marketing Solutions",
        website: "https://platinummarketing.co",
        description: "Full-service luxury marketing agency specializing in high-end brands and exclusive products. We create sophisticated marketing campaigns that resonate with affluent audiences and drive premium sales.",
        services: [
          "Luxury Brand Marketing",
          "Influencer Partnerships",
          "Event Marketing",
          "Public Relations",
          "Content Creation",
          "Digital Advertising"
        ],
        contactEmail: "<EMAIL>",
        contactPhone: "+****************",
        logoUrl: "/logos/platinum-marketing.png",
        category: "marketing" as const,
        location: "Chicago, IL",
        establishedYear: 2012,
        employeeCount: "16-25",
        specialties: [
          "High-Net-Worth Targeting",
          "Luxury Event Management",
          "Celebrity Endorsements",
          "Brand Positioning"
        ],
        certifications: [
          "Google Ads Certified",
          "Facebook Marketing Partner",
          "HubSpot Certified Agency"
        ],
        isApproved: true,
        isFeatured: true,
        rating: 4.6,
        reviewCount: 34,
        createdById: args.createdById,
        updatedAt: now,
      },
      {
        companyName: "Royal Carpet Care",
        website: "https://royalcarpetcare.com",
        description: "Premium carpet and upholstery cleaning service for luxury homes and high-end businesses. We use eco-friendly methods and state-of-the-art equipment to maintain the finest carpets, rugs, and fabrics.",
        services: [
          "Luxury Carpet Cleaning",
          "Oriental Rug Restoration",
          "Upholstery Cleaning",
          "Leather Care",
          "Stain Protection",
          "Emergency Cleaning"
        ],
        contactEmail: "<EMAIL>",
        contactPhone: "+****************",
        logoUrl: "/logos/royal-carpet.png",
        category: "cleaning" as const,
        location: "San Francisco, CA",
        establishedYear: 2005,
        employeeCount: "11-20",
        specialties: [
          "Antique Rug Restoration",
          "Eco-Friendly Cleaning",
          "Pet Stain Removal",
          "Commercial Services"
        ],
        certifications: [
          "IICRC Certified",
          "Green Seal Approved",
          "Better Business Bureau A+"
        ],
        isApproved: true,
        isFeatured: false,
        rating: 4.5,
        reviewCount: 156,
        createdById: args.createdById,
        updatedAt: now,
      },
    ];

    const vendorIds = [];
    
    for (const vendorData of sampleVendors) {
      const vendorId = await ctx.db.insert("vendors", vendorData);
      vendorIds.push(vendorId);
    }

    return vendorIds;
  },
});
