{"name": "@repo/backend", "private": true, "version": "0.0.0", "scripts": {"dev": "convex dev", "setup": "convex dev --until-success", "build": "convex deploy", "clean": "git clean -xdf .cache .turbo dist node_modules"}, "dependencies": {"@convex-dev/aggregate": "^0.1.22", "@convex-dev/better-auth": "^0.8.0-alpha.0", "@convex-dev/resend": "^0.1.9", "@repo/email": "workspace:*", "better-auth": "^1.3.4", "convex": "^1.25.4", "jspdf": "^3.0.1", "next": "^15.3.0", "stripe": "^18.4.0", "zod": "^3.25.76"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^20.19.8"}}