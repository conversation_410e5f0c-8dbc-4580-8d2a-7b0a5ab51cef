# MODA Authentication Troubleshooting Guide

This guide helps resolve common authentication issues in the MODA application.

## Common Issues and Solutions

### 1. "You are using the default secret" Error

**Error Message:**
```
ERROR [Better Auth]: You are using the default secret. Please set `BETTER_AUTH_SECRET` in your environment variables
```

**Solution:**
1. Generate a secure secret:
   ```bash
   openssl rand -base64 32
   ```

2. Add to `packages/backend/.env`:
   ```env
   BETTER_AUTH_SECRET=your_generated_secret_here
   ```

3. Or run the setup script:
   ```bash
   chmod +x scripts/setup-auth.sh
   ./scripts/setup-auth.sh
   ```

### 2. "API key is not set" Error

**Error Message:**
```
SERVER_ERROR: API key is not set
```

**Cause:** Missing Resend API key for email functionality.

**Solutions:**

#### Option A: Set up Resend (Recommended for production)
1. Sign up at [resend.com](https://resend.com)
2. Get your API key
3. Add to `packages/backend/.env`:
   ```env
   RESEND_API_KEY=re_your_api_key_here
   RESEND_FROM_EMAIL=<EMAIL>
   ```

#### Option B: Development Mode (Testing only)
The app will automatically use test mode if no API key is provided. Email verification URLs will be logged to the console for testing.

### 3. Social Provider Warnings

**Warning Messages:**
```
WARN [Better Auth]: Social provider google is missing clientId or clientSecret
WARN [Better Auth]: Social provider apple is missing clientId or clientSecret
```

**Cause:** Social OAuth providers not configured (this is optional).

**Solution (Optional):**
1. Set up Google OAuth:
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Create OAuth 2.0 credentials
   - Add to `.env`:
     ```env
     GOOGLE_CLIENT_ID=your_google_client_id
     GOOGLE_CLIENT_SECRET=your_google_client_secret
     ```

2. Set up Apple OAuth:
   - Go to [Apple Developer](https://developer.apple.com)
   - Create Sign in with Apple service
   - Add to `.env`:
     ```env
     APPLE_CLIENT_ID=your_apple_client_id
     APPLE_CLIENT_SECRET=your_apple_client_secret
     ```

### 4. Email Verification Not Working

**Symptoms:**
- Users not receiving verification emails
- Email verification failing

**Solutions:**

#### Check Email Configuration:
1. Verify Resend API key is set correctly
2. Check email logs in console (development mode)
3. Verify domain configuration in Resend dashboard

#### Development Testing:
If emails aren't working, verification URLs are logged to console:
```
🔗 Verification URL for testing: https://your-app.com/verify?token=...
```

### 5. CORS Issues

**Error Message:**
```
CORS error when accessing auth endpoints
```

**Solution:**
1. Check `trustedOrigins` in `packages/backend/better-auth/server.ts`
2. Add your domain:
   ```typescript
   trustedOrigins: [
     "https://your-production-domain.com",
     "http://localhost:3000",
   ],
   ```

### 6. Session Issues

**Symptoms:**
- Users getting logged out frequently
- Session not persisting

**Solutions:**

#### Check Cookie Configuration:
1. Verify `COOKIE_DOMAIN` in `.env`
2. For localhost: `COOKIE_DOMAIN=localhost`
3. For production: `COOKIE_DOMAIN=.yourdomain.com`

#### Check Session Settings:
Session configuration in `server.ts`:
```typescript
session: {
  expiresIn: 60 * 60 * 24 * 7, // 7 days
  updateAge: 60 * 60 * 24, // 1 day
  cookieCache: {
    enabled: true,
    maxAge: 60 * 5, // 5 minutes
  },
},
```

## Environment Variables Reference

### Required for Basic Auth:
```env
# App Configuration
APP_URL=http://localhost:3000
NODE_ENV=development

# Authentication
BETTER_AUTH_SECRET=your_secure_secret_here

# Convex
CONVEX_DEPLOYMENT=your_deployment_name
NEXT_PUBLIC_CONVEX_URL=https://your-deployment.convex.cloud
```

### Optional for Enhanced Features:
```env
# Email (Resend)
RESEND_API_KEY=re_your_api_key
RESEND_FROM_EMAIL=<EMAIL>

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Apple OAuth
APPLE_CLIENT_ID=your_apple_client_id
APPLE_CLIENT_SECRET=your_apple_client_secret

# Cookies
COOKIE_DOMAIN=localhost
```

## Development Setup

### Quick Setup:
```bash
# 1. Run setup script
chmod +x scripts/setup-auth.sh
./scripts/setup-auth.sh

# 2. Set Convex URL
# Add your Convex deployment URL to .env

# 3. Start development
pnpm dev
```

### Manual Setup:
```bash
# 1. Copy environment file
cp packages/backend/.env.example packages/backend/.env

# 2. Generate auth secret
openssl rand -base64 32

# 3. Edit .env file with your values
# 4. Start development
pnpm dev
```

## Testing Authentication

### Test Email Verification:
1. Sign up with a new account
2. Check console for verification URL (development mode)
3. Visit the URL to verify email

### Test Social Login:
1. Configure OAuth providers
2. Test login flow
3. Verify account linking works

### Test Session Persistence:
1. Log in
2. Refresh page
3. Verify user stays logged in
4. Test across browser tabs

## Production Checklist

- [ ] Generate secure `BETTER_AUTH_SECRET`
- [ ] Set production `APP_URL`
- [ ] Configure Resend with verified domain
- [ ] Set up OAuth providers (if using)
- [ ] Configure proper `COOKIE_DOMAIN`
- [ ] Test email delivery
- [ ] Test all auth flows
- [ ] Monitor error logs

## Getting Help

If you're still experiencing issues:

1. Check the console logs for detailed error messages
2. Verify all environment variables are set correctly
3. Test with a fresh browser session (clear cookies)
4. Check the [Better Auth documentation](https://better-auth.com)
5. Review the [Convex Better Auth integration](https://docs.convex.dev/auth/better-auth)

## Common Development Commands

```bash
# Check environment variables
cat packages/backend/.env

# Generate new auth secret
openssl rand -base64 32

# Clear browser data for testing
# Chrome: DevTools > Application > Storage > Clear storage

# View auth logs
# Check browser console and server logs
```
