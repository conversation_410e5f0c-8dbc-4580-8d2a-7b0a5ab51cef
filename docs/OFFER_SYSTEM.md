# MODA Offer System

This document describes the comprehensive offer system for MODA, where users can make offers on products and sellers can accept, decline, or counter them.

## Overview

The offer system allows buyers to negotiate prices with sellers through a structured bidding process. Buyers can submit offers on products, and sellers can respond by accepting, declining, or countering the offer. This creates a flexible marketplace where both parties can reach mutually agreeable terms.

## Key Features

### ✅ Buyer Capabilities
- **Submit Offers**: Make offers on products with custom amounts and messages
- **Track Status**: Monitor offer status (pending, countered, accepted, declined)
- **Respond to Counters**: Accept or let counter offers expire
- **Withdraw Offers**: Cancel pending offers before seller response
- **Offer History**: View all past offers and their outcomes

### ✅ Seller Capabilities
- **Review Offers**: See all incoming offers on their products
- **Accept Offers**: Accept offers to reserve products
- **Decline Offers**: Reject offers with optional reasons
- **Counter Offers**: Propose alternative prices with messages
- **Offer Management**: Organize offers by status and priority

### ✅ System Features
- **Automatic Expiration**: Offers expire after 7 days
- **Validation**: Prevents invalid offer amounts and duplicate offers
- **Notifications**: Real-time updates on offer status changes
- **Product Reservation**: Accepted offers automatically reserve products
- **Conflict Resolution**: Handles multiple offers on the same product

## User Workflows

### Making an Offer (Buyer)

1. **Browse Products**: Navigate to any active product listing
2. **Click "Make an Offer"**: Opens the offer modal
3. **Enter Offer Amount**: Must be between 50% and 150% of listing price
4. **Add Message**: Optional message to the seller (max 500 characters)
5. **Submit Offer**: Offer is sent and expires in 7 days

### Managing Offers (Seller)

1. **View Dashboard**: Check seller dashboard for pending offers
2. **Review Details**: See buyer information, offer amount, and message
3. **Take Action**: Accept, decline, or counter the offer
4. **Counter Offer**: Propose new price with optional message
5. **Monitor Status**: Track offer through the complete lifecycle

### Responding to Counters (Buyer)

1. **Receive Notification**: Get notified of seller's counter offer
2. **Review Counter**: See new price and seller's message
3. **Make Decision**: Accept counter or let it expire
4. **Complete Transaction**: Accepted counters reserve the product

## Offer Statuses

### Pending
- **Description**: Offer submitted, waiting for seller response
- **Actions Available**: Buyer can withdraw, seller can accept/decline/counter
- **Duration**: 7 days from creation

### Countered
- **Description**: Seller has proposed a counter offer
- **Actions Available**: Buyer can accept or let expire
- **Duration**: 7 days from original offer creation

### Accepted
- **Description**: Offer has been accepted by seller
- **Actions Available**: None (product is reserved)
- **Next Steps**: Product status changes to "reserved"

### Declined
- **Description**: Offer has been declined by seller
- **Actions Available**: None
- **Next Steps**: Buyer can make a new offer

### Expired
- **Description**: Offer expired without response
- **Actions Available**: None
- **Next Steps**: Buyer can make a new offer

### Withdrawn
- **Description**: Buyer withdrew the offer
- **Actions Available**: None
- **Next Steps**: Buyer can make a new offer

## Business Rules

### Offer Validation
- **Minimum Amount**: $1 or 50% of listing price (whichever is higher)
- **Maximum Amount**: 150% of listing price
- **Duplicate Prevention**: One active offer per buyer per product
- **Subscription Required**: Active subscription needed to make offers

### Counter Offer Rules
- **Must Be Higher**: Counter must exceed buyer's original offer
- **Within Limits**: Cannot exceed 150% of listing price
- **Message Optional**: Seller can include explanation with counter
- **Same Expiration**: Uses original offer's expiration date

### Product Status Changes
- **Active**: Available for offers and purchases
- **Reserved**: Product reserved due to accepted offer
- **Sold**: Product sold through offer or direct purchase
- **Archived**: Product no longer available

## Technical Implementation

### Database Schema
```typescript
offers: defineTable({
  productId: v.id("products"),
  buyerId: v.id("users"),
  sellerId: v.id("users"),
  offerAmount: v.number(),
  message: v.optional(v.string()),
  status: v.union("pending" | "accepted" | "declined" | "countered" | "expired" | "withdrawn"),
  counterOffer: v.optional(v.number()),
  counterMessage: v.optional(v.string()),
  expiresAt: v.number(),
  createdAt: v.number(),
  updatedAt: v.number(),
})
```

### Key Functions
- **createOffer**: Submit new offer with validation
- **acceptOffer**: Accept offer and reserve product
- **declineOffer**: Decline offer
- **counterOffer**: Propose counter offer
- **acceptCounterOffer**: Accept seller's counter
- **withdrawOffer**: Buyer withdraws pending offer

### Queries
- **getSellerOffers**: Get offers for seller dashboard
- **getBuyerOffers**: Get offers for buyer management
- **getProductOffers**: Get offers for specific product
- **getSellerOfferCounts**: Get offer statistics for dashboard

## User Experience

### Buyer Experience
- **Intuitive Interface**: Clear offer submission process
- **Real-time Updates**: Immediate feedback on offer status
- **Easy Management**: Simple interface to track all offers
- **Quick Actions**: One-click responses to counter offers

### Seller Experience
- **Dashboard Overview**: Quick view of all pending offers
- **Efficient Management**: Bulk actions and filtering options
- **Clear Actions**: Simple accept/decline/counter buttons
- **Offer Insights**: See offer history and buyer information

## Security & Validation

### Authorization
- **Buyer Verification**: Only authenticated users can make offers
- **Seller Verification**: Only product owners can respond to offers
- **Subscription Check**: Active subscription required for offers
- **Ownership Validation**: Prevents self-offers on own products

### Data Validation
- **Amount Validation**: Ensures reasonable offer amounts
- **Status Transitions**: Validates offer state changes
- **Expiration Handling**: Automatic status updates
- **Conflict Resolution**: Handles multiple offers gracefully

## Future Enhancements

### Planned Features
- **Bulk Operations**: Process multiple offers simultaneously
- **Advanced Analytics**: Offer performance metrics and insights
- **Automated Responses**: AI-powered offer suggestions
- **Escrow Integration**: Secure payment handling for accepted offers
- **Dispute Resolution**: Mediation for offer-related conflicts

### Integration Opportunities
- **Notification System**: Email and push notifications for offer updates
- **Messaging System**: Direct communication between buyers and sellers
- **Analytics Dashboard**: Comprehensive offer performance metrics
- **Mobile App**: Native mobile experience for offer management

## Support & Troubleshooting

### Common Issues
- **Offer Not Showing**: Check subscription status and product availability
- **Cannot Submit Offer**: Verify amount is within valid range
- **Status Not Updating**: Refresh page or check network connection
- **Product Reserved**: Product may have been reserved by another offer

### Getting Help
- **Documentation**: Review this guide for detailed information
- **Support Team**: Contact MODA support for technical issues
- **Community**: Join seller forums for best practices and tips
- **Feedback**: Submit feature requests and bug reports

---

The offer system provides a flexible and user-friendly way for buyers and sellers to negotiate prices while maintaining the integrity and security of the MODA marketplace.
