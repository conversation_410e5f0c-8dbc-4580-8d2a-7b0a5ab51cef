# Reddit-Style Forum System

A comprehensive Reddit-style forum system for the MODA luxury marketplace community.

## 🎯 Overview

The Reddit-style forum transforms the existing forum into a familiar Reddit-like experience with:
- **Upvote/Downvote system** instead of simple likes
- **Karma points** for users
- **Awards system** with Gold, Silver, and more
- **User badges** (MOD, Top 1%, OP)
- **Collapsible comment threads**
- **Advanced sorting** (Hot, New, Top, Controversial)

## ✨ Key Features

### 1. **Voting System**
- **Upvotes/Downvotes** - Reddit-style voting on posts and comments
- **Score calculation** - Score = upvotes - downvotes
- **Vote tracking** - Users can only vote once per item
- **Karma impact** - Votes affect user karma

### 2. **Karma System**
- **Post Karma** - Earned from post upvotes
- **Comment Karma** - Earned from comment upvotes
- **Awardee Karma** - Earned from receiving awards
- **Awarder Karma** - Earned from giving awards (10% back)
- **Total Karma** - Sum of all karma types

### 3. **Awards System**
Available awards with costs:
- 🏆 **Gold** (500 karma) - Exceptional quality
- 🥈 **Silver** (100 karma) - Good content
- 🤝 **Helpful** (50 karma) - Helpful contribution
- 🤗 **Wholesome** (50 karma) - Heartwarming content
- 🚀 **Rocket Like** (75 karma) - Amazing post
- 🤯 **Mind Blown** (100 karma) - Incredible insight
- 🔥 **Fire** (50 karma) - Hot take
- 💯 **100** (75 karma) - Perfect response
- 💎 **Platinum** (1000 karma) - Extraordinary contribution
- ⚡ **Take My Energy** (25 karma) - Support

### 4. **User Badges & Flair**
- **OP Badge** - Original poster in a thread
- **MOD Badge** - Moderator status
- **Top 1%** - Top contributors by karma
- **Custom Flair** - User-set text with colors
- **Trophies** - Achievements like "1 Year Club", "10k Karma"

### 5. **UI Components**

#### Reddit-Style Post Component
```tsx
<RedditStylePost
  post={postData}
  showContent={true}
  onAwardClick={() => openAwardModal()}
/>
```
Features:
- Vertical voting arrows with score
- Post metadata (community, author, time)
- Award display
- Action buttons (Comments, Award, Share, Save)
- Dropdown menu (Hide, Report)

#### Reddit-Style Comment Component
```tsx
<RedditStyleComment
  comment={commentData}
  postAuthorId={postAuthor}
  level={0}
  onReply={handleReply}
  onAwardClick={() => openAwardModal()}
/>
```
Features:
- Nested threading with visual indent
- Collapsible threads
- Inline reply forms
- Vote buttons
- Best answer highlighting

### 6. **Sorting Algorithms**

#### Hot Sort (Reddit's Algorithm)
```typescript
function calculateHotScore(post) {
  const score = post.score || 0;
  const order = Math.log10(Math.max(Math.abs(score), 1));
  const sign = score > 0 ? 1 : score < 0 ? -1 : 0;
  const seconds = post._creationTime / 1000 - 1134028003;
  return Math.round(sign * order + seconds / 45000);
}
```

#### Best Sort (Wilson Score)
```typescript
function calculateBestScore(comment) {
  const upvotes = comment.upvotes || 0;
  const downvotes = comment.downvotes || 0;
  const n = upvotes + downvotes;
  if (n === 0) return 0;
  const z = 1.96; // 95% confidence
  const phat = upvotes / n;
  return (phat + z*z/(2*n) - z*Math.sqrt((phat*(1-phat)+z*z/(4*n))/n))/(1+z*z/n);
}
```

#### Controversial Sort
```typescript
function calculateControversyScore(item) {
  const upvotes = item.upvotes || 0;
  const downvotes = item.downvotes || 0;
  if (upvotes <= 0 || downvotes <= 0) return 0;
  const magnitude = upvotes + downvotes;
  const balance = upvotes > downvotes ? downvotes/upvotes : upvotes/downvotes;
  return magnitude ** balance;
}
```

## 📁 File Structure

```
packages/backend/convex/
├── redditForum.ts       # Reddit-specific backend functions
├── seedAwards.ts        # Award seeding script
├── forum.ts            # Updated with score fields
└── schema.ts           # Updated with Reddit fields

apps/web/components/forum/
├── RedditStylePost.tsx      # Reddit-style post component
├── RedditStyleComment.tsx   # Reddit-style comment component
├── RedditForumHome.tsx     # Reddit-style forum home
└── app/forum/
    └── submit/page.tsx     # Reddit-style post creation
```

## 🗄️ Database Schema Updates

### Forum Posts
```typescript
{
  // Previous fields...
  score: number,        // upvotes - downvotes
  upvotes: number,      // Total upvotes
  downvotes: number,    // Total downvotes
  likes?: number,       // Deprecated, for backward compatibility
}
```

### Forum Comments
```typescript
{
  // Previous fields...
  score: number,        // upvotes - downvotes
  upvotes: number,      // Total upvotes
  downvotes: number,    // Total downvotes
  isCollapsed?: boolean, // For collapsed threads
  likes?: number,       // Deprecated
}
```

### Member Profiles
```typescript
{
  // Previous fields...
  postKarma?: number,
  commentKarma?: number,
  awardeeKarma?: number,
  awarderKarma?: number,
  totalKarma?: number,
  flair?: {
    text: string,
    backgroundColor?: string,
    textColor?: string,
  },
  isModerator?: boolean,
  isTopContributor?: boolean,
  isPremium?: boolean,
  trophies?: string[],
}
```

### New Tables
- `forumPostVotes` - Track user votes on posts
- `forumCommentVotes` - Track user votes on comments
- `forumAwards` - Award definitions
- `forumPostAwards` - Awards given to posts
- `forumCommentAwards` - Awards given to comments

## 🔧 API Functions

### Voting
- `voteOnPost` - Vote on a post (upvote/downvote/none)
- `voteOnComment` - Vote on a comment

### Awards
- `getAwards` - Get available awards
- `givePostAward` - Give award to a post
- `giveCommentAward` - Give award to a comment

### User Features
- `updateUserFlair` - Update user flair text/colors
- `updateUserTrophies` - Check and update achievements
- `toggleCommentCollapse` - Collapse/expand comment threads

## 🎨 UI Features

### Forum Home
- **Sort Tabs** - Hot, New, Top, Controversial
- **Time Range** - For Top sort (hour, day, week, month, year, all)
- **Sidebar** - About, Communities, Top Contributors, Rules
- **Compact/Card View** - View options

### Post Display
- **Score Display** - Formatted (1.2k, 24.5k, 1.1M)
- **User Badges** - MOD, Top 1%, OP
- **Awards Row** - Inline award display
- **Action Bar** - Comments, Award, Share, Save
- **View Count** - Track post views

### Comment Features
- **Nested Threading** - Visual hierarchy with indents
- **Collapse Button** - Hide/show thread
- **Inline Reply** - Reply without leaving page
- **Edit/Delete** - For comment authors
- **Best Answer** - Highlighted by OP

## 🚀 Usage Examples

### Toggle Between Styles
The forum page includes tabs to switch between Reddit and Classic styles:
```tsx
<Tabs defaultValue="reddit">
  <TabsTrigger value="reddit">Reddit Style</TabsTrigger>
  <TabsTrigger value="classic">Classic Forum</TabsTrigger>
</Tabs>
```

### Create a Post
Navigate to `/forum/submit` for the Reddit-style post creation:
- Choose post type (Text, Images, Link)
- Select community (optional)
- Add title and content
- Tag your post
- Submit to community

### Give an Award
```typescript
await givePostAward({
  postId: "post123",
  awardId: "gold_award",
  message: "Great post!",
  isAnonymous: false
});
```

## 🎯 Best Practices

1. **Karma Management**
   - Vote thoughtfully - karma affects user reputation
   - Award meaningful content
   - Build karma through quality contributions

2. **Content Organization**
   - Use appropriate post types
   - Tag posts for discoverability
   - Choose the right community

3. **Community Engagement**
   - Reply to comments
   - Mark best answers
   - Use awards to recognize quality

## 📈 Future Enhancements

- [ ] Subreddit-style community customization
- [ ] Moderator tools and automod
- [ ] User blocking and filtering
- [ ] Crossposting between communities
- [ ] Reddit-style notifications
- [ ] Mobile app optimization
- [ ] Karma leaderboards
- [ ] Community-specific awards

## 🔐 Security & Moderation

- **Vote Manipulation** - One vote per user enforced
- **Karma Farming** - Limited by vote tracking
- **Award Abuse** - Karma cost prevents spam
- **Content Moderation** - Report system for inappropriate content

The Reddit-style forum provides a familiar, engaging experience for community members while maintaining the luxury marketplace focus of MODA.
