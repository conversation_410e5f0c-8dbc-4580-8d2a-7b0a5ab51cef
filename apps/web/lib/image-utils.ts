/**
 * Utility functions for handling images with transparent backgrounds
 */

/**
 * Detects if an image URL is likely to have a transparent background
 * @param url - The image URL
 * @returns boolean indicating if the image likely has transparency
 */
export function hasTransparentBackground(url: string): boolean {
  if (!url) return false;
  
  // Check for PNG format (most common for transparent backgrounds)
  if (url.toLowerCase().includes('.png')) return true;
  
  // Check for WebP with transparency
  if (url.toLowerCase().includes('.webp')) return true;
  
  // Check for SVG (always supports transparency)
  if (url.toLowerCase().includes('.svg')) return true;
  
  // Check for specific patterns in URLs that might indicate transparency
  const transparencyIndicators = [
    'transparent',
    'no-bg',
    'nobg',
    'cutout',
    'isolated',
    'png',
  ];
  
  return transparencyIndicators.some(indicator => 
    url.toLowerCase().includes(indicator)
  );
}

/**
 * Gets appropriate CSS classes for image containers based on transparency
 * @param url - The image URL
 * @param defaultBg - Default background class if not transparent
 * @returns CSS classes for the image container
 */
export function getImageContainerClasses(
  url: string, 
  defaultBg: string = 'bg-muted'
): string {
  const isTransparent = hasTransparentBackground(url);
  
  if (isTransparent) {
    // For transparent images, use our custom luxury transparent background
    return 'luxury-transparent-bg transparent-image-container';
  }
  
  return defaultBg;
}

/**
 * Gets appropriate CSS styles for Next.js Image components with transparency
 * @param url - The image URL
 * @returns CSS style object
 */
export function getImageStyles(url: string): React.CSSProperties {
  const isTransparent = hasTransparentBackground(url);
  
  if (isTransparent) {
    return {
      backgroundColor: 'transparent',
      backgroundImage: 'none',
      mixBlendMode: 'normal' as const,
    };
  }
  
  return {};
}

/**
 * Gets appropriate object-fit class for images based on transparency
 * @param url - The image URL
 * @param defaultFit - Default object-fit class
 * @returns object-fit class
 */
export function getImageObjectFit(
  url: string,
  defaultFit: string = 'object-cover'
): string {
  const isTransparent = hasTransparentBackground(url);
  
  // For transparent images, use object-contain to preserve transparency
  // For non-transparent images, use object-cover for better visual consistency
  return isTransparent ? 'object-contain' : defaultFit;
}

/**
 * Product-specific image handling for luxury items
 * @param productTitle - The product title
 * @param imageUrl - The image URL
 * @returns styling configuration for the product image
 */
export function getProductImageConfig(productTitle: string, imageUrl: string): {
  isTransparent: boolean;
  containerClasses: string;
  imageStyles: React.CSSProperties & { className?: string };
  objectFit: string;
  premiumContainer: boolean;
  isChanelBag: boolean;
} {
  const isTransparent = hasTransparentBackground(imageUrl);
  
  // Luxury brands that commonly use transparent backgrounds
  const luxuryBrands = [
    'chanel', 'louis vuitton', 'hermès', 'gucci', 'prada',
    'bottega veneta', 'saint laurent', 'balenciaga', 'dior',
    'rolex', 'patek philippe', 'audemars piguet'
  ];
  
  const isLuxuryBrand = luxuryBrands.some(brand => 
    productTitle.toLowerCase().includes(brand)
  );
  
  // Special handling for Chanel bags
  const isChanelBag = productTitle.toLowerCase().includes('chanel') && 
                     productTitle.toLowerCase().includes('bag');
  
  let containerClasses = getImageContainerClasses(imageUrl);
  
  // Add special Chanel bag styling
  if (isChanelBag && isTransparent) {
    containerClasses += ' chanel-bag-container';
  }
  
  // Add hover effects for transparent images
  if (isTransparent || isLuxuryBrand) {
    containerClasses += ' transparent-image-hover';
  }
  
  return {
    isTransparent: isTransparent || isLuxuryBrand,
    containerClasses,
    imageStyles: { 
      ...getImageStyles(imageUrl),
      // Add preserve transparency class
      ...(isTransparent && { className: 'preserve-transparency' })
    },
    objectFit: getImageObjectFit(imageUrl),
    // For luxury items with transparent backgrounds, use a premium container
    premiumContainer: isLuxuryBrand && isTransparent,
    // Special Chanel bag handling
    isChanelBag
  };
}
