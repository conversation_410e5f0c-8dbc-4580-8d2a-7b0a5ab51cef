import { Metadata } from "next";
import { Suspense } from "react";
import { WarrantyForm } from "@/components/warranty/WarrantyForm";
import { InlineLoader } from "@/components/common";
import { CommunityLayout } from "@/components/layouts/CommunityLayout";

export const metadata: Metadata = {
  title: "Warranty Check | MODA",
  description: "Professional warranty verification and product authenticity services.",
  openGraph: {
    title: "Warranty Check | MODA",
    description: "Professional warranty verification and product authenticity services.",
    type: "website",
  },
};

export default function WarrantyPage() {
  return (
    <CommunityLayout>
      <div className="min-h-screen bg-gradient-to-br from-zinc-50 to-zinc-100 dark:from-zinc-900 dark:to-zinc-800">
        {/* Header */}
        <div className="border-b bg-white/80 dark:bg-zinc-900/80 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-6 py-8">
            <div className="text-center">
              <h1 className="text-3xl font-light text-zinc-900 dark:text-zinc-100 mb-2">
                Warranty Check
              </h1>
              <p className="text-zinc-600 dark:text-zinc-400 max-w-2xl mx-auto">
                Professional warranty verification and product authenticity services. 
                Get expert authentication for your luxury goods and verify warranty status.
              </p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-6 py-8">
          <Suspense fallback={<InlineLoader message="Loading warranty check form..." />}>
            <WarrantyForm />
          </Suspense>
        </div>

      </div>
    </CommunityLayout>
  );
}
