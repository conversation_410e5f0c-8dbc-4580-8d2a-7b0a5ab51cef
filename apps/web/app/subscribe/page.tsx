"use client";

import { useState } from "react";
import { useAuth } from "@/hooks/useBetterAuth";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { But<PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Check, CreditCard, Lock, Shield, Star, Truck } from "lucide-react";
import { toast } from "sonner";

export default function SubscribePage() {
  const { user } = useAuth();
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubscribe = async () => {
    setIsProcessing(true);
    
    try {
      // TODO: Integrate with payment processor (Stripe, etc.)
      // For now, simulate successful subscription
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success("Subscription activated!", {
        description: "Welcome to MODA Premium! You now have full access to our marketplace.",
      });
      
      // Redirect to dashboard after successful subscription
      router.push("/dashboard");
    } catch (error) {
      toast.error("Subscription failed", {
        description: "Please try again or contact support.",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSkipForNow = () => {
    router.push("/dashboard");
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Access Restricted</h2>
          <p className="text-muted-foreground mb-6">
            Please sign in to access this page.
          </p>
          <Button asChild>
            <a href="/login">Sign In</a>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-950 dark:to-neutral-900">
      <div className="container mx-auto py-12 px-6">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-light text-foreground mb-4">
              Complete Your MODA Setup
            </h1>
            <p className="text-xl text-muted-foreground font-light max-w-2xl mx-auto">
              Welcome, {user.name}! To access our premium marketplace of authenticated luxury goods, 
              please complete your subscription setup.
            </p>
          </div>

          {/* Subscription Plans */}
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {/* Premium Plan */}
            <Card className="border-2 border-primary/20 bg-card shadow-xl">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Star className="w-8 h-8 text-primary" />
                </div>
                <CardTitle className="text-2xl font-light text-foreground">Premium Access</CardTitle>
                <CardDescription className="text-muted-foreground">
                  Full access to MODA's luxury marketplace
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="text-center">
                  <div className="mb-2">
                    <span className="text-4xl font-light text-foreground">$20</span>
                    <span className="text-muted-foreground">/month</span>
                  </div>
                  <Badge className="bg-primary/10 text-primary border-primary/20">
                    Most Popular
                  </Badge>
                </div>

                <ul className="space-y-3">
                  {[
                    "Unlimited access to luxury listings",
                    "Authenticated goods only",
                    "Verified seller network",
                    "Buyer protection guarantee",
                    "White-glove customer service",
                    "Priority support",
                    "Exclusive member events"
                  ].map((feature, index) => (
                    <li key={index} className="flex items-center text-sm text-foreground">
                      <Check className="w-4 h-4 text-primary mr-3 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>

                <Button 
                  onClick={handleSubscribe}
                  disabled={isProcessing}
                  className="w-full bg-primary text-primary-foreground hover:bg-primary/90 font-medium h-12"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <CreditCard className="w-4 h-4 mr-2" />
                      Subscribe Now
                    </>
                  )}
                </Button>

                <p className="text-xs text-muted-foreground text-center">
                  Cancel anytime. No hidden fees.
                </p>
              </CardContent>
            </Card>

            {/* Free Trial Info */}
            <Card className="border border-border bg-card/50">
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                  <Lock className="w-8 h-8 text-muted-foreground" />
                </div>
                <CardTitle className="text-xl font-light text-foreground">Limited Access</CardTitle>
                <CardDescription className="text-muted-foreground">
                  What you can do without a subscription
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Check className="w-4 h-4 text-muted-foreground mr-3 flex-shrink-0" />
                    Browse limited product listings
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Check className="w-4 h-4 text-muted-foreground mr-3 flex-shrink-0" />
                    View basic product information
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Check className="w-4 h-4 text-muted-foreground mr-3 flex-shrink-0" />
                    Create account and profile
                  </div>
                </div>

                <div className="pt-4 border-t border-border">
                  <p className="text-sm text-muted-foreground text-center mb-4">
                    Upgrade to Premium for full access
                  </p>
                  <Button 
                    variant="outline" 
                    onClick={handleSkipForNow}
                    className="w-full"
                  >
                    Skip for Now
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Trust Indicators */}
          <div className="text-center">
            <div className="flex items-center justify-center gap-8 text-muted-foreground mb-4">
              <div className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                <span className="text-sm">Secure Payment</span>
              </div>
              <div className="flex items-center gap-2">
                <Truck className="w-5 h-5" />
                <span className="text-sm">Fast Delivery</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-5 h-5" />
                <span className="text-sm">Premium Quality</span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              Your payment information is encrypted and secure. We never store your payment details.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
