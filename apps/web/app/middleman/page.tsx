import { Metadata } from "next";
import { Suspense } from "react";
import { MiddlemanForm } from "@/components/middleman/MiddlemanForm";
import { InlineLoader } from "@/components/common";
import { CommunityLayout } from "@/components/layouts/CommunityLayout";

export const metadata: Metadata = {
  title: "Middleman Service | MODA",
  description: "Secure transaction mediation service for luxury goods transactions.",
  openGraph: {
    title: "Middleman Service | MODA",
    description: "Secure transaction mediation service for luxury goods transactions.",
    type: "website",
  },
};

export default function MiddlemanServicePage() {
  return (
    <CommunityLayout>
    <div className="min-h-screen bg-gradient-to-br from-zinc-50 to-zinc-100 dark:from-zinc-900 dark:to-zinc-800">
      {/* Header */}
      <div className="border-b bg-white/80 dark:bg-zinc-900/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-light text-zinc-900 dark:text-zinc-100 mb-2">
              Middleman Service
            </h1>
            <p className="text-zinc-600 dark:text-zinc-400 max-w-2xl mx-auto">
              Secure transaction mediation service for luxury goods. We facilitate safe exchanges 
              between buyers and sellers, ensuring both parties are protected throughout the process.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <Suspense fallback={<InlineLoader message="Loading middleman service form..." />}>
          <MiddlemanForm />
        </Suspense>
      </div>
    </div>
    </CommunityLayout>
  );
}
