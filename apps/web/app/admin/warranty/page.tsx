import { Metada<PERSON> } from "next";
import { Suspense } from "react";
import { AdminWarrantyManagement } from "@/components/admin/AdminWarrantyManagement";
import { InlineLoader } from "@/components/common";

export const metadata: Metadata = {
  title: "Warranty Check Management | Admin",
  description: "Manage warranty check requests and authentication results.",
};

export default function AdminWarrantyPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Warranty Check Management</h1>
          <p className="text-muted-foreground">
            Review warranty check requests and manage authentication results.
          </p>
        </div>
      </div>

      <Suspense fallback={<InlineLoader message="Loading warranty check requests..." />}>
        <AdminWarrantyManagement />
      </Suspense>
    </div>
  );
}
