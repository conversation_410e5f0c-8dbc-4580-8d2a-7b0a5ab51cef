import { Metadata } from "next";
import { AdminUserView } from "@/components/admin/AdminUserView";

export const metadata: Metadata = {
  title: "User Details | MODA Admin",
  description: "View user information and activity on MODA's premium marketplace.",
  openGraph: {
    title: "User Details | MODA Admin",
    description: "View user information and activity on MODA's premium marketplace.",
    type: "website",
  },
};

export default async function AdminUserViewPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;

  return <AdminUserView userId={id} />; 
}
