import { Metadata } from "next";
import { AdminUserEdit } from "@/components/admin/AdminUserEdit";

export const metadata: Metadata = {
  title: "Edit User | MODA Admin",
  description: "Edit user information and settings on MODA's premium marketplace.",
  openGraph: {
    title: "Edit User | MODA Admin",
    description: "Edit user information and settings on MODA's premium marketplace.",
    type: "website",
  },
};

export default async function AdminUserEditPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  return <AdminUserEdit userId={id} />;
}
