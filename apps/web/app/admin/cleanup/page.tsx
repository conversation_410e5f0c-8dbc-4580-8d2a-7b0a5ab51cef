"use client";

import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { useAuth } from "@/hooks/useBetterAuth";
import { InlineLoader } from "@/components/common";

export default function AdminCleanupPage() {
  const { user } = useAuth();
  const [orderId, setOrderId] = useState("");
  const [correctPlatformFee, setCorrectPlatformFee] = useState("20");
  const [fixResult, setFixResult] = useState<any>(null);
  const [reviewsCheckResult, setReviewsCheckResult] = useState<any>(null);
  const [isCleaningUp, setIsCleaningUp] = useState(false);
  const [memberSeedResult, setMemberSeedResult] = useState<any>(null);
  const [cleanupResult, setCleanupResult] = useState<any>(null);

  // Queries
  // TODO: Uncomment when Convex types are properly generated
  // const suspiciousOrders = useQuery(api.adminCleanup.findOrdersWithHighPlatformFees, { threshold: 0.5 });

  // Mutations
  const fixPlatformFee = useMutation(api.adminCleanup.fixIncorrectPlatformFees);
  const createCleanProfile = useMutation(api.members.createCleanMemberProfile);
  const cleanupObsoleteFields = useMutation(api.members.cleanupObsoleteProfileFields);
  // TODO: Uncomment when Convex types are properly generated
  // const cleanupCorruptedReviews = useMutation(api.adminCleanup.cleanupCorruptedReviews);
  
  // Queries
  // TODO: Uncomment when Convex types are properly generated
  // const checkForCorruptedReviews = useQuery(api.adminCleanup.checkForCorruptedReviews);

  // Check if user is admin
  if (user?.userType !== "admin") {
    return (
      <div className="container mx-auto px-6 py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">Admin access required for this page.</p>
        </div>
      </div>
    );
  }

  const handleFixPlatformFee = async () => {
    if (!orderId || !correctPlatformFee) {
      alert("Please enter both order ID and correct platform fee");
      return;
    }

    try {
      const result = await fixPlatformFee({
        orderId: orderId as any,
        correctPlatformFee: parseFloat(correctPlatformFee)
      });
      setFixResult(result);
      
      if (result.success) {
        setOrderId("");
        setCorrectPlatformFee("20");
      }
    } catch (error) {
      console.error("Error fixing platform fee:", error);
      setFixResult({ success: false, message: "Error: " + (error as Error).message });
    }
  };

  const handleCheckReviews = () => {
    // TODO: Implement when Convex types are properly generated
    alert("Reviews check functionality temporarily disabled - Convex types need to be regenerated");
  };

  const handleCleanupReviews = async () => {
    // TODO: Implement when Convex types are properly generated
    alert("Reviews cleanup functionality temporarily disabled - Convex types need to be regenerated");
  };

  const handleCreateCleanProfiles = async () => {
    try {
      // This is just a demo function - in practice you'd want to create profiles individually
      setMemberSeedResult({ 
        message: "Clean profile creation is now available. Use createCleanMemberProfile mutation for individual users.",
        created: 0,
        skipped: 0,
        total: 0
      });
    } catch (error) {
      console.error("Error with clean profiles:", error);
      setMemberSeedResult({ 
        message: "Error: " + (error as Error).message,
        created: 0,
        skipped: 0,
        total: 0
      });
    }
  };

  const handleCleanupObsoleteFields = async () => {
    try {
      const result = await cleanupObsoleteFields();
      setCleanupResult(result);
    } catch (error) {
      console.error("Error cleaning up obsolete fields:", error);
      setCleanupResult({ 
        message: "Error: " + (error as Error).message,
        totalProfiles: 0,
        updated: 0,
        skipped: 0
      });
    }
  };

  return (
    <div className="container mx-auto px-6 py-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Admin Data Cleanup
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Fix data issues and clean up problematic records
        </p>
      </div>

      {/* Platform Fee Fix Section */}
      <Card>
        <CardHeader>
          <CardTitle>Fix Incorrect Platform Fees</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="orderId">Order ID</Label>
              <Input
                id="orderId"
                value={orderId}
                onChange={(e) => setOrderId(e.target.value)}
                placeholder="Enter order ID"
              />
            </div>
            <div>
              <Label htmlFor="correctPlatformFee">Correct Platform Fee ($)</Label>
              <Input
                id="correctPlatformFee"
                type="number"
                value={correctPlatformFee}
                onChange={(e) => setCorrectPlatformFee(e.target.value)}
                placeholder="20"
              />
            </div>
          </div>
          
          <Button onClick={handleFixPlatformFee} className="w-full md:w-auto">
            Fix Platform Fee
          </Button>

          {fixResult && (
            <div className={`p-4 rounded-lg ${
              fixResult.success 
                ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200' 
                : 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200'
            }`}>
              <h4 className="font-semibold mb-2">
                {fixResult.success ? 'Success!' : 'Error'}
              </h4>
              <p className="text-sm">{fixResult.message}</p>
              {fixResult.success && (
                <div className="mt-2 text-sm">
                  <p><strong>Order ID:</strong> {fixResult.orderId}</p>
                  <p><strong>Old Platform Fee:</strong> ${fixResult.oldPlatformFee}</p>
                  <p><strong>New Platform Fee:</strong> ${fixResult.newPlatformFee}</p>
                  <p><strong>Old Seller Earnings:</strong> ${fixResult.oldSellerEarnings}</p>
                  <p><strong>New Seller Earnings:</strong> ${fixResult.newSellerEarnings}</p>
                  <p><strong>Subtotal:</strong> ${fixResult.subtotal}</p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Corrupted Reviews Cleanup Section - Temporarily disabled */}
      <Card>
        <CardHeader>
          <CardTitle>Corrupted Reviews Cleanup</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">
              Check for and clean up corrupted review data where sellerId references invalid users.
            </p>
            
            <div className="p-4 bg-amber-100 dark:bg-amber-900/20 text-amber-800 dark:text-amber-200 rounded-lg">
              <p className="text-sm">
                ⚠️ <strong>Functionality Temporarily Disabled</strong><br/>
                The reviews cleanup functionality is temporarily disabled because Convex types need to be regenerated. 
                Please ensure the Convex development server is running and restart it if necessary.
              </p>
            </div>
            
            <div className="flex gap-2">
              <Button 
                onClick={handleCheckReviews} 
                variant="outline"
                className="w-full md:w-auto"
                disabled
              >
                Check for Corrupted Reviews
              </Button>
              
              <Button 
                onClick={handleCleanupReviews} 
                className="w-full md:w-auto bg-red-600 hover:bg-red-700"
                disabled
              >
                Clean Up Corrupted Reviews
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Database Cleanup Section */}
      <Card>
        <CardHeader>
          <CardTitle>🚨 Database Schema Migration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="p-4 bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200 rounded-lg">
              <h4 className="font-semibold mb-2">⚠️ Schema Validation Error Detected</h4>
              <p className="text-sm mb-2">
                Existing member profiles contain obsolete calculated fields that need to be removed.
              </p>
              <ul className="text-sm space-y-1">
                <li>• <code>totalForumPosts</code>, <code>totalComments</code>, <code>helpfulVotes</code></li>
                <li>• <code>lastActiveAt</code>, <code>postKarma</code>, <code>commentKarma</code></li>
                <li>• <code>totalKarma</code>, <code>isTopContributor</code>, <code>isPremium</code></li>
              </ul>
            </div>
            
            <Button 
              onClick={handleCleanupObsoleteFields} 
              className="w-full md:w-auto"
              variant="destructive"
            >
              🧹 Clean Up Database Schema
            </Button>
            <p className="text-xs text-muted-foreground">
              This will remove obsolete fields from all member profiles to match the new schema.
            </p>

            {cleanupResult && (
              <div className="p-4 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded-lg">
                <h4 className="font-semibold mb-2">Cleanup Results</h4>
                <p className="text-sm">{cleanupResult.message}</p>
                <div className="mt-2 text-sm">
                  <p><strong>Total Profiles:</strong> {cleanupResult.totalProfiles}</p>
                  <p><strong>Updated:</strong> {cleanupResult.updated}</p>
                  <p><strong>Skipped:</strong> {cleanupResult.skipped}</p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Clean Member Profiles Section */}
      <Card>
        <CardHeader>
          <CardTitle>Clean Member Profile System</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">
              The member directory now uses dynamic calculations for forum posts, comments, and helpful votes. 
              No more stored calculated values that can get out of sync!
            </p>
            
            <div className="p-4 bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 rounded-lg">
              <h4 className="font-semibold mb-2">✅ Dynamic Calculations Enabled</h4>
              <ul className="text-sm space-y-1">
                <li>• Forum posts calculated from forumPosts table</li>
                <li>• Comments calculated from forumComments table</li>
                <li>• Helpful votes calculated from comment likes</li>
                <li>• Karma calculated from post upvotes + comment likes</li>
                <li>• Last active time calculated from multiple sources</li>
              </ul>
            </div>
            
            <Button 
              onClick={handleCreateCleanProfiles} 
              className="w-full md:w-auto"
              variant="outline"
            >
              View System Info
            </Button>

            {memberSeedResult && (
              <div className="p-4 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded-lg">
                <h4 className="font-semibold mb-2">System Info</h4>
                <p className="text-sm">{memberSeedResult.message}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Suspicious Orders Section - Temporarily disabled until Convex types are regenerated */}
      {/* 
      <Card>
        <CardHeader>
          <CardTitle>Orders with High Platform Fees</CardTitle>
        </CardHeader>
        <CardContent>
          {suspiciousOrders === undefined ? (
            <InlineLoader message="Loading..." />
          ) : suspiciousOrders.length === 0 ? (
            <p className="text-center py-4 text-muted-foreground">
              No orders with suspiciously high platform fees found.
            </p>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Found {suspiciousOrders.length} orders with platform fees higher than 50% of subtotal.
              </p>
              
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Order #</th>
                      <th className="text-left p-2">Subtotal</th>
                      <th className="text-left p-2">Platform Fee</th>
                      <th className="text-left p-2">Seller Earnings</th>
                      <th className="text-left p-2">Ratio</th>
                      <th className="text-left p-2">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {suspiciousOrders.map((order) => (
                      <tr key={order._id} className="border-b">
                        <td className="p-2 font-mono text-xs">{order.orderNumber}</td>
                        <td className="p-2">${order.subtotal.toLocaleString()}</td>
                        <td className="p-2">${order.platformFee?.toLocaleString() || 'N/A'}</td>
                        <td className="p-2">${order.sellerEarnings?.toLocaleString() || 'N/A'}</td>
                        <td className="p-2">{(order.ratio * 100).toFixed(1)}%</td>
                        <td className="p-2">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            order.orderStatus === 'delivered' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {order.orderStatus}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      */}
    </div>
  );
}
