import { Metada<PERSON> } from "next";
import { Suspense } from "react";
import { AdminMiddlemanManagement } from "@/components/admin/AdminMiddlemanManagement";
import { InlineLoader } from "@/components/common";

export const metadata: Metadata = {
  title: "Middleman Service Management | Admin",
  description: "Manage middleman service requests and transactions.",
};

export default function AdminMiddlemanPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Middleman Service Management</h1>
          <p className="text-muted-foreground">
            Review and manage middleman service requests from users.
          </p>
        </div>
      </div>

      <Suspense fallback={<InlineLoader message="Loading middleman requests..." />}>
        <AdminMiddlemanManagement />
      </Suspense>
    </div>
  );
}
