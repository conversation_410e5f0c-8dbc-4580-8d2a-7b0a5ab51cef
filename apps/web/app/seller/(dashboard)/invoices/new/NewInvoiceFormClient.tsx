"use client";

import { Badge } from "@repo/ui/components/badge";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Separator } from "@repo/ui/components/separator";
import { Textarea } from "@repo/ui/components/textarea";
import { useMutation, useAction, useQuery } from "convex/react";
import { Download, Mail, Eye, DollarSign, ArrowLeft, Calendar, MapPin, Phone, Mail as MailIcon, Building2, Plus, Trash2, Clock, CheckCircle, AlertCircle, Save, Loader2, FileText } from "lucide-react";
import React, { useState, useCallback, useEffect } from "react";
import { toast } from "sonner";
import { api } from "@repo/backend/convex/_generated/api";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Id } from "@repo/backend/convex/_generated/dataModel";

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  amount: number;
}

interface InvoiceFormData {
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  clientAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  dueDate: string;
  paymentTerms: string;
  notes: string;
  items: InvoiceItem[];
}

export function NewInvoiceFormClient() {
  const router = useRouter();
  const [isCreating, setIsCreating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);
  const [currentInvoiceId, setCurrentInvoiceId] = useState<Id<"invoices"> | null>(null);

  const [formData, setFormData] = useState<InvoiceFormData>({
    clientName: "",
    clientEmail: "",
    clientPhone: "",
    clientAddress: {
      street: "",
      city: "",
      state: "",
      zipCode: "",
      country: "",
    },
    dueDate: "",
    paymentTerms: "Net 30",
    notes: "",
    items: [
      {
        id: "1",
        description: "",
        quantity: 1,
        unitPrice: 0,
        amount: 0,
      },
    ],
  });

  const createInvoice = useMutation(api.invoices.createInvoice);
  const createOfflineSale = useMutation(api.offlineSales.createOfflineSale);
  const createCustomInvoice = useMutation(api.invoices.createCustomInvoice);
  const saveInvoiceDraft = useMutation(api.invoices.saveInvoiceDraft);
  const updateInvoiceDraft = useMutation(api.invoices.updateInvoiceDraft);
  const convertDraftToInvoice = useMutation(api.invoices.convertDraftToInvoice);
  const deleteInvoiceDraft = useMutation(api.invoices.deleteInvoiceDraft);

  // Validate form data
  useEffect(() => {
    const isValid = 
      formData.clientName.trim() !== "" &&
      formData.clientEmail.trim() !== "" &&
      formData.clientAddress.street.trim() !== "" &&
      formData.clientAddress.city.trim() !== "" &&
      formData.clientAddress.state.trim() !== "" &&
      formData.clientAddress.zipCode.trim() !== "" &&
      formData.clientAddress.country.trim() !== "" &&
      formData.items.every(item => 
        item.description.trim() !== "" && 
        item.unitPrice > 0
      );
    
    setIsFormValid(isValid);
  }, [formData]);

  // Track unsaved changes
  useEffect(() => {
    setHasUnsavedChanges(true);
  }, [formData]);

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateAddress = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      clientAddress: {
        ...prev.clientAddress,
        [field]: value
      }
    }));
  };

  const updateItem = (id: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map(item => {
        if (item.id === id) {
          const updatedItem = { ...item, [field]: value };
          // Recalculate amount if quantity or unitPrice changes
          if (field === "quantity" || field === "unitPrice") {
            updatedItem.amount = updatedItem.quantity * updatedItem.unitPrice;
          }
          return updatedItem;
        }
        return item;
      })
    }));
  };

  const addItem = () => {
    const newId = (formData.items.length + 1).toString();
    setFormData(prev => ({
      ...prev,
      items: [
        ...prev.items,
        {
          id: newId,
          description: "",
          quantity: 1,
          unitPrice: 0,
          amount: 0,
        }
      ]
    }));
  };

  const removeItem = (id: string) => {
    if (formData.items.length > 1) {
      setFormData(prev => ({
        ...prev,
        items: prev.items.filter(item => item.id !== id)
      }));
    }
  };

  const calculateSubtotal = () => {
    return formData.items.reduce((sum, item) => sum + item.amount, 0);
  };

  const calculateTax = () => {
    return calculateSubtotal() * 0.08; // 8% tax
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateTax();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const handleSave = useCallback(async () => {
    if (!isFormValid) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsCreating(true);
    try {
      // Filter out the id field from items since it's only used for frontend state
      const itemsForBackend = formData.items.map(({ id, ...item }) => item);
      
      if (currentInvoiceId) {
        // Convert existing draft to final invoice
        await convertDraftToInvoice({ invoiceId: currentInvoiceId });
        toast.success("Draft converted to invoice successfully! You can now generate a PDF.");
      } else {
        // Create new invoice
        const dueDate = formData.dueDate ? new Date(formData.dueDate).getTime() : undefined;
        
        const { invoiceId } = await createCustomInvoice({
          clientName: formData.clientName,
          clientEmail: formData.clientEmail,
          clientPhone: formData.clientPhone,
          clientAddress: formData.clientAddress,
          items: itemsForBackend,
          subtotal: calculateSubtotal(),
          tax: calculateTax(),
          totalAmount: calculateTotal(),
          paymentTerms: formData.paymentTerms,
          notes: formData.notes,
          dueDate,
        });
        
        setCurrentInvoiceId(invoiceId);
        toast.success("Invoice created successfully! You can now generate a PDF.");
      }
      
      setHasUnsavedChanges(false);
      
      // Don't redirect immediately, let user generate PDF first
      // router.push(`/seller/invoices/${currentInvoiceId}`);
    } catch (error) {
      toast.error("Failed to create invoice");
      console.error(error);
    } finally {
      setIsCreating(false);
    }
  }, [formData, isFormValid, createCustomInvoice, convertDraftToInvoice, currentInvoiceId]);

  const handleCancel = useCallback(() => {
    if (hasUnsavedChanges) {
      if (confirm("You have unsaved changes. Are you sure you want to leave?")) {
        router.back();
      }
    } else {
      router.back();
    }
  }, [router, hasUnsavedChanges]);

  const handleSaveDraft = async () => {
    try {
      setIsSaving(true);
      
      // Filter out the id field from items since it's only used for frontend state
      const itemsForBackend = formData.items.map(({ id, ...item }) => item);
      
      if (currentInvoiceId) {
        // Update existing draft
        await updateInvoiceDraft({
          invoiceId: currentInvoiceId,
          clientName: formData.clientName,
          clientEmail: formData.clientEmail,
          clientPhone: formData.clientPhone,
          clientAddress: formData.clientAddress,
          items: itemsForBackend,
          subtotal: calculateSubtotal(),
          tax: calculateTax(),
          totalAmount: calculateTotal(),
          paymentTerms: formData.paymentTerms,
          notes: formData.notes,
          dueDate: formData.dueDate ? new Date(formData.dueDate).getTime() : undefined,
        });
        
        toast.success("Draft updated successfully!");
      } else {
        // Create new draft
        const { invoiceId } = await saveInvoiceDraft({
          clientName: formData.clientName,
          clientEmail: formData.clientEmail,
          clientPhone: formData.clientPhone,
          clientAddress: formData.clientAddress,
          items: itemsForBackend,
          subtotal: calculateSubtotal(),
          tax: calculateTax(),
          totalAmount: calculateTotal(),
          paymentTerms: formData.paymentTerms,
          notes: formData.notes,
          dueDate: formData.dueDate ? new Date(formData.dueDate).getTime() : undefined,
        });
        
        setCurrentInvoiceId(invoiceId);
        toast.success("Draft saved successfully!");
      }
      
      setLastSaved(new Date());
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error("Save draft failed:", error);
      toast.error("Failed to save draft");
    } finally {
      setIsSaving(false);
    }
  };

  // Load draft from database on component mount
  useEffect(() => {
    // This useEffect is no longer needed as we are not loading drafts
    // The handleSaveDraft function now handles saving/updating drafts
  }, []);

  return (
    <div className="bg-background">
      {/* Header Bar - Full Width */}
      <div className="border-b border-border bg-card">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={handleCancel}
                className="rounded-xl font-light"
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-light text-primary tracking-wide">
                  NEW INVOICE
                </h1>
                <p className="text-sm text-muted-foreground font-light">
                  Create a new invoice for offline sales
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              {lastSaved && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span>Last saved: {lastSaved.toLocaleTimeString()}</span>
                </div>
              )}
              
              <Button
                variant="outline"
                onClick={handleCancel}
                className="rounded-xl font-light"
                disabled={isCreating || isSaving}
              >
                CANCEL
              </Button>
              
              <Button
                variant="outline"
                onClick={handleSaveDraft}
                disabled={isSaving}
                className="rounded-xl font-light"
              >
                {isSaving ? (
                  <Loader2 className="w-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 w-4 mr-2" />
                )}
                SAVE DRAFT
              </Button>


              
              <Button
                onClick={handleSave}
                disabled={!isFormValid || isCreating || isSaving}
                className="bg-accent text-accent-foreground hover:bg-accent/90 rounded-xl font-light px-6"
              >
                {isCreating ? (
                  <Loader2 className="w-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Eye className="w-4 w-4 mr-2" />
                )}
                {currentInvoiceId ? "CONVERT TO INVOICE" : "CREATE INVOICE"}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Side - Edit Form */}
          <div className="space-y-6">
            {/* Client Information */}
            <Card className="rounded-xl border-border">
              <CardHeader>
                <CardTitle className="font-light text-primary">Client Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-light text-muted-foreground mb-2 block">Client Name *</Label>
                    <Input
                      value={formData.clientName}
                      onChange={(e) => updateFormData("clientName", e.target.value)}
                      placeholder="John Doe"
                      className="rounded-xl bg-primary/5 border-border font-light"
                    />
                  </div>
                  <div>
                    <Label className="text-sm font-light text-muted-foreground mb-2 block">Email *</Label>
                    <Input
                      type="email"
                      value={formData.clientEmail}
                      onChange={(e) => updateFormData("clientEmail", e.target.value)}
                      placeholder="<EMAIL>"
                      className="rounded-xl bg-primary/5 border-border font-light"
                    />
                  </div>
                </div>
                
                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Phone</Label>
                  <Input
                    value={formData.clientPhone}
                    onChange={(e) => updateFormData("clientPhone", e.target.value)}
                    placeholder="+****************"
                    className="rounded-xl bg-primary/5 border-border font-light"
                  />
                </div>

                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Address</Label>
                  <div className="space-y-4 mt-1">
                    <Input
                      placeholder="Street Address"
                      value={formData.clientAddress.street}
                      onChange={(e) => updateAddress("street", e.target.value)}
                      className="rounded-xl bg-primary/5 border-border font-light"
                    />
                    <div className="grid grid-cols-3 gap-4">
                      <Input
                        placeholder="City"
                        value={formData.clientAddress.city}
                        onChange={(e) => updateAddress("city", e.target.value)}
                        className="rounded-xl bg-primary/5 border-border font-light"
                      />
                      <Input
                        placeholder="State"
                        value={formData.clientAddress.state}
                        onChange={(e) => updateAddress("state", e.target.value)}
                        className="rounded-xl bg-primary/5 border-border font-light"
                      />
                      <Input
                        placeholder="ZIP"
                        value={formData.clientAddress.zipCode}
                        onChange={(e) => updateAddress("zipCode", e.target.value)}
                        className="rounded-xl bg-primary/5 border-border font-light"
                      />
                    </div>
                    <Input
                      placeholder="Country"
                      value={formData.clientAddress.country}
                      onChange={(e) => updateAddress("country", e.target.value)}
                      className="rounded-xl bg-primary/5 border-border font-light"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Invoice Details */}
            <Card className="rounded-xl border-border">
              <CardHeader>
                <CardTitle className="font-light text-primary">Invoice Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-light text-muted-foreground mb-2 block">Due Date</Label>
                    <Input
                      type="date"
                      value={formData.dueDate}
                      onChange={(e) => updateFormData("dueDate", e.target.value)}
                      className="rounded-xl bg-primary/5 border-border font-light"
                    />
                  </div>
                  <div>
                    <Label className="text-sm font-light text-muted-foreground mb-2 block">Payment Terms</Label>
                    <Input
                      value={formData.paymentTerms}
                      onChange={(e) => updateFormData("paymentTerms", e.target.value)}
                      placeholder="Net 30"
                      className="rounded-xl bg-primary/5 border-border font-light"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Invoice Items */}
            <Card className="rounded-xl border-border">
              <CardHeader>
                <CardTitle className="font-light text-primary">Invoice Items</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.items.map((item, index) => (
                  <div key={item.id} className="border border-border rounded-xl p-4 bg-primary/5">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-light text-primary">Item {index + 1}</h4>
                      {formData.items.length > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeItem(item.id)}
                          className="text-destructive hover:text-destructive hover:bg-destructive/10 rounded-xl"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <Label className="text-sm font-light text-muted-foreground mb-2 block">Description *</Label>
                        <Input
                          placeholder="Product or service description"
                          value={item.description}
                          onChange={(e) => updateItem(item.id, "description", e.target.value)}
                          className="rounded-xl bg-primary/5 border-border font-light"
                        />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label className="text-sm font-light text-muted-foreground mb-2 block">Quantity</Label>
                          <Input
                            type="number"
                            min="1"
                            value={item.quantity}
                            onChange={(e) => updateItem(item.id, "quantity", parseInt(e.target.value) || 1)}
                            className="rounded-xl bg-primary/5 border-border font-light"
                          />
                        </div>
                        <div>
                          <Label className="text-sm font-light text-muted-foreground mb-2 block">Unit Price *</Label>
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.unitPrice}
                            onChange={(e) => updateItem(item.id, "unitPrice", parseFloat(e.target.value) || 0)}
                            placeholder="0.00"
                            className="rounded-xl bg-primary/5 border-border font-light"
                          />
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <span className="text-sm text-muted-foreground font-light">Amount: </span>
                        <span className="font-medium text-primary">{formatCurrency(item.amount)}</span>
                      </div>
                    </div>
                  </div>
                ))}
                
                <Button
                  type="button"
                  variant="outline"
                  onClick={addItem}
                  className="w-full border-dashed border-border text-muted-foreground hover:text-primary hover:border-primary rounded-xl font-light h-12"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>
              </CardContent>
            </Card>

            {/* Notes */}
            <Card className="rounded-xl border-border">
              <CardHeader>
                <CardTitle className="font-light text-primary">Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  placeholder="Additional notes or terms..."
                  value={formData.notes}
                  onChange={(e) => updateFormData("notes", e.target.value)}
                  rows={3}
                  className="rounded-xl bg-primary/5 border-border font-light resize-none"
                />
              </CardContent>
            </Card>
          </div>

          {/* Right Side - Live Preview */}
          <div className="lg:sticky lg:top-8">
            <Card className="rounded-xl border-border shadow-lg">
              <CardContent className="p-8">
                {/* Invoice Header */}
                <div className="flex justify-between items-start mb-8">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center">
                      <Building2 className="w-6 h-6 text-primary-foreground" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-light text-primary tracking-wide">MODA</h2>
                      <p className="text-sm text-muted-foreground font-light">Luxury Marketplace</p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="inline-flex items-center px-4 py-2 bg-accent text-accent-foreground rounded-full text-sm font-light">
                      INVOICE
                    </div>
                    <h3 className="text-3xl font-light text-primary mt-2">#NEW</h3>
                    <p className="text-muted-foreground text-sm mt-1 font-light">
                      Invoice Date: {formData.dueDate ? new Date(formData.dueDate).toLocaleDateString() : 'Not set'}
                    </p>
                  </div>
                </div>

                <Separator className="my-8" />

                {/* Billing Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-8">
                  {/* From */}
                  <div>
                    <h4 className="text-sm font-light text-primary uppercase tracking-wide mb-4">From</h4>
                    <div className="space-y-2">
                      <p className="font-light text-primary">MODA Seller</p>
                      <div className="flex items-center text-muted-foreground text-sm">
                        <MailIcon className="w-4 h-4 mr-2" />
                        <span className="font-light"><EMAIL></span>
                      </div>
                      <div className="flex items-center text-muted-foreground text-sm">
                        <Phone className="w-4 h-4 mr-2" />
                        <span className="font-light">+****************</span>
                      </div>
                      <div className="flex items-start text-muted-foreground text-sm">
                        <MapPin className="w-4 h-4 mr-2 mt-0.5" />
                        <div>
                          <p className="font-light">123 Luxury Lane</p>
                          <p className="font-light">Beverly Hills, CA 90210</p>
                          <p className="font-light">United States</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Bill To */}
                  <div>
                    <h4 className="text-sm font-light text-primary uppercase tracking-wide mb-4">Bill To</h4>
                    <div className="space-y-2">
                      <p className="font-light text-primary">
                        {formData.clientName || "Client Name"}
                      </p>
                      <div className="flex items-center text-muted-foreground text-sm">
                        <MailIcon className="w-4 h-4 mr-2" />
                        <span className="font-light">{formData.clientEmail || "<EMAIL>"}</span>
                      </div>
                      {formData.clientPhone && (
                        <div className="flex items-center text-muted-foreground text-sm">
                          <Phone className="w-4 h-4 mr-2" />
                          <span className="font-light">{formData.clientPhone}</span>
                        </div>
                      )}
                      <div className="flex items-start text-muted-foreground text-sm">
                        <MapPin className="w-4 h-4 mr-2 mt-0.5" />
                        <div>
                          <p className="font-light">{formData.clientAddress.street || "Street Address"}</p>
                          <p className="font-light">
                            {formData.clientAddress.city || "City"}, {formData.clientAddress.state || "State"} {formData.clientAddress.zipCode || "ZIP"}
                          </p>
                          <p className="font-light">{formData.clientAddress.country || "Country"}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Invoice Details */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 p-4 bg-primary/5 rounded-xl">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <p className="text-xs text-muted-foreground uppercase tracking-wide font-light">Invoice Date</p>
                      <p className="font-light text-primary">{new Date().toLocaleDateString()}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <p className="text-xs text-muted-foreground uppercase tracking-wide font-light">Due Date</p>
                      <p className="font-light text-primary">
                        {formData.dueDate ? new Date(formData.dueDate).toLocaleDateString() : 'Not set'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <DollarSign className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <p className="text-xs text-muted-foreground uppercase tracking-wide font-light">Payment Terms</p>
                      <p className="font-light text-primary">{formData.paymentTerms}</p>
                    </div>
                  </div>
                </div>

                {/* Items Table */}
                <div className="mb-8">
                  <h4 className="text-sm font-light text-primary uppercase tracking-wide mb-4">Item Details</h4>
                  <div className="border border-border rounded-xl overflow-hidden">
                    <div className="bg-primary/5 px-6 py-4 grid grid-cols-12 gap-4 text-sm font-light text-muted-foreground">
                      <div className="col-span-6">Description</div>
                      <div className="col-span-2 text-center">Quantity</div>
                      <div className="col-span-2 text-center">Unit Price</div>
                      <div className="col-span-2 text-right">Amount</div>
                    </div>
                    
                    {formData.items.map((item) => (
                      <div key={item.id} className="px-6 py-4 grid grid-cols-12 gap-4 text-sm border-t border-border">
                        <div className="col-span-6">
                          <p className="font-light text-primary">
                            {item.description || "Item description"}
                          </p>
                          <p className="text-muted-foreground text-sm mt-1 font-light">Product/Service</p>
                        </div>
                        <div className="col-span-2 text-center text-primary font-light">{item.quantity}</div>
                        <div className="col-span-2 text-center text-primary font-light">{formatCurrency(item.unitPrice)}</div>
                        <div className="col-span-2 text-right font-light text-primary">{formatCurrency(item.amount)}</div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Totals */}
                <div className="flex justify-end">
                  <div className="w-80 space-y-3">
                    <div className="flex justify-between text-sm text-muted-foreground font-light">
                      <span>Subtotal:</span>
                      <span>{formatCurrency(calculateSubtotal())}</span>
                    </div>
                    
                    <div className="flex justify-between text-sm text-muted-foreground font-light">
                      <span>Tax (8%):</span>
                      <span>{formatCurrency(calculateTax())}</span>
                    </div>
                    
                    <Separator />
                    
                    <div className="flex justify-between text-lg font-light text-primary">
                      <span>Total:</span>
                      <span>{formatCurrency(calculateTotal())}</span>
                    </div>
                  </div>
                </div>

                {/* Notes */}
                {formData.notes && (
                  <>
                    <Separator className="my-8" />
                    <div>
                      <h4 className="text-sm font-light text-primary uppercase tracking-wide mb-3">Notes</h4>
                      <div className="p-4 bg-primary/5 rounded-xl">
                        <p className="text-muted-foreground font-light">{formData.notes}</p>
                      </div>
                    </div>
                  </>
                )}

                {/* Footer */}
                <div className="mt-12 pt-8 border-t border-border">
                  <div className="text-center text-muted-foreground text-sm font-light">
                    <p>Thank you for your business!</p>
                    <p className="mt-1">Please make payment by the due date to avoid any late fees.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
