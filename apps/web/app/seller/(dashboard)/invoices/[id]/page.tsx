"use client";

import { Badge } from "@repo/ui/components/badge";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Separator } from "@repo/ui/components/separator";
import { useMutation, useQuery, useAction } from "convex/react";
import { Download, Mail, Eye, DollarSign, ArrowLeft, Calendar, MapPin, Phone, Mail as MailIcon, Building2, Loader2, Globe } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import Link from "next/link";
import { ProfileImage } from "@/components/common/ProfileImage";

export default function InvoiceDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const [isSending, setIsSending] = useState(false);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const { id } = React.use(params);
  const invoiceId = id as Id<"invoices">;
  
  const invoice = useQuery(api.invoices.getInvoice, { invoiceId });
  const sendInvoiceEmail = useAction(api.invoices.sendInvoiceEmail);
  const getStorageUrl = useAction(api.invoices.getStorageUrl);
  const updateStatus = useMutation(api.invoices.updateInvoiceStatus);

  const handleSendEmail = async () => {
    setIsSending(true);
    try {
      await sendInvoiceEmail({ invoiceId });
      toast.success("Invoice sent successfully!");
    } catch (error) {
      toast.error("Failed to send invoice");
      console.error(error);
    } finally {
      setIsSending(false);
    }
  };

  const handleMarkAsPaid = async () => {
    try {
      await updateStatus({ invoiceId, status: "paid" });
      toast.success("Invoice marked as paid!");
    } catch (error) {
      toast.error("Failed to mark invoice as paid");
    }
  };

  // Helper function to convert image URL to base64
  const getImageAsBase64 = async (url: string): Promise<{ data: string; format: string } | null> => {
    try {
      // Add CORS mode and credentials for better compatibility
      const response = await fetch(url, {
        mode: 'cors',
        credentials: 'omit'
      });
      
      if (!response.ok) {
        console.warn(`Failed to fetch image: ${response.status} ${response.statusText}`);
        return null;
      }
      
      const blob = await response.blob();
      
      // Determine image format more accurately
      const contentType = response.headers.get('content-type') || blob.type;
      let format = 'JPEG'; // default
      
      if (contentType.includes('png')) {
        format = 'PNG';
      } else if (contentType.includes('gif')) {
        format = 'GIF';
      } else if (contentType.includes('webp')) {
        format = 'WEBP';
      }
      
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          if (result) {
            resolve({ data: result, format });
          } else {
            resolve(null);
          }
        };
        reader.onerror = () => {
          console.error("FileReader error");
          resolve(null);
        };
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error("Failed to convert image to base64:", error);
      return null;
    }
  };

  // Helper function to generate initials from company name
  const generateInitials = (companyName: string): string => {
    if (!companyName) return "M";
    
    // Split by spaces and take first letter of each word
    const words = companyName.trim().split(/\s+/);
    if (words.length === 1) {
      // Single word - take first 2 characters
      return words[0]?.substring(0, 2).toUpperCase() || "M";
    } else {
      // Multiple words - take first letter of each word (max 3)
      return words
        .slice(0, 3)
        .map(word => word.charAt(0))
        .join('')
        .toUpperCase();
    }
  };

  // Helper function to generate a professional color based on company name
  const generateLogoColor = (companyName: string): [number, number, number] => {
    if (!companyName) return [15, 23, 42]; // default slate-900
    
    // Predefined professional color palette
    const professionalColors: [number, number, number][] = [
      [15, 23, 42],    // slate-900
      [30, 41, 59],    // slate-800  
      [55, 65, 81],    // gray-700
      [17, 24, 39],    // gray-900
      [31, 41, 55],    // slate-800
      [75, 85, 99],    // gray-600
      [107, 114, 128], // gray-500
      [156, 163, 175], // gray-400
    ];
    
    // Generate consistent index based on company name
    let hash = 0;
    for (let i = 0; i < companyName.length; i++) {
      hash = companyName.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    const colorIndex = Math.abs(hash) % professionalColors.length;
    return professionalColors[colorIndex]!;
  };

  const handleGeneratePDF = async () => {
    if (!invoice) return;
    setIsGeneratingPDF(true);
    
    try {
      console.log("Generating PDF for invoice:", invoice.invoiceNumber);
      toast.info("Generating PDF... This may take a moment.");
      
      // Import jsPDF dynamically
      const { jsPDF } = await import("jspdf");
      
      // Generate PDF with proper dimensions - A4 size
      const pdfDoc = new jsPDF('p', 'mm', 'a4');
      const pageWidth = pdfDoc.internal.pageSize.getWidth();
      const pageHeight = pdfDoc.internal.pageSize.getHeight();
      const margin = 20;
      const contentWidth = pageWidth - (2 * margin);
      
      // Colors matching the actual web interface (using proper Tailwind CSS values)
      const colors = {
        primary: [15, 23, 42] as const,           // slate-900 for primary text
        muted: [100, 116, 139] as const,          // slate-500 for muted text
        accent: [240, 253, 244] as const,         // green-50 for badge background
        accentText: [22, 163, 74] as const,       // green-600 for badge text
        border: [226, 232, 240] as const,         // slate-200 for borders
        cardBg: [255, 255, 255] as const,         // White background
        lightBg: [248, 250, 252] as const,        // slate-50 for light backgrounds
        green: [22, 163, 74] as const,            // green-600
        red: [220, 38, 38] as const,              // red-600
      };
      
      let yPosition = 25;
      
      // Clean white background (no card styling for PDF)
      pdfDoc.setFillColor(...colors.cardBg);
      pdfDoc.rect(0, 0, pageWidth, pageHeight, 'F');
      
      // Load company logo if available
      let logoImage = null;
      if (invoice.sellerProfile?.companyLogo) {
        try {
          console.log("Loading company logo for PDF...");
          const logoUrl = await getStorageUrl({ storageId: invoice.sellerProfile.companyLogo });
          console.log("Logo URL:", logoUrl);
          
          if (logoUrl) {
            logoImage = await getImageAsBase64(logoUrl);
            if (logoImage) {
              console.log("Successfully loaded logo image:", logoImage.format);
            } else {
              console.warn("Failed to convert logo to base64");
            }
          } else {
            console.warn("No logo URL returned from storage");
          }
        } catch (error) {
          console.warn("Failed to load company logo for PDF:", error);
        }
      } else {
        console.log("No company logo available in seller profile");
      }
      
      // === INVOICE HEADER - Matching web layout exactly ===
      const headerY = yPosition;
      
      // Left side - Company Logo and Info (matching web flex items-center space-x-3)
      let leftContentX = margin;
      
      // Logo or fallback icon
      if (logoImage) {
        try {
          // Company logo sized like web (w-12 h-12 = 48x48px ≈ 17mm)
          const logoSize = 17;
          pdfDoc.addImage(logoImage.data, logoImage.format, leftContentX, headerY, logoSize, logoSize);
          leftContentX += logoSize + 5; // Space after logo (space-x-3)
        } catch (error) {
          console.warn("Failed to add logo to PDF:", error);
          // Fallback: draw a rounded square like the web fallback
          pdfDoc.setFillColor(...colors.primary);
          pdfDoc.roundedRect(leftContentX, headerY, 17, 17, 3, 3, 'F');
          // Add building icon representation (simplified)
          pdfDoc.setFillColor(...colors.cardBg);
          pdfDoc.rect(leftContentX + 6, headerY + 4, 2, 6, 'F');
          pdfDoc.rect(leftContentX + 9, headerY + 4, 2, 6, 'F');
          pdfDoc.rect(leftContentX + 6, headerY + 11, 5, 4, 'F');
          leftContentX += 17 + 5;
        }
      } else {
        // Generate a company logo with initials
        const companyName = invoice.sellerProfile?.businessName || "MODA";
        const initials = generateInitials(companyName);
        const logoColor = generateLogoColor(companyName);
        
        // Draw rounded square background with company-specific color
        pdfDoc.setFillColor(...logoColor);
        pdfDoc.roundedRect(leftContentX, headerY, 17, 17, 3, 3, 'F');
        
        // Add company initials in white
        pdfDoc.setTextColor(...colors.cardBg);
        pdfDoc.setFontSize(8);
        pdfDoc.setFont("helvetica", "bold");
        pdfDoc.text(initials, leftContentX + 8.5, headerY + 10, { align: 'center' });
        
        leftContentX += 17 + 5;
      }
      
      // Company name (text-2xl font-light = 24px ≈ 8.5mm)
      pdfDoc.setFontSize(18);
      pdfDoc.setFont("helvetica", "normal");
      pdfDoc.setTextColor(...colors.primary);
      pdfDoc.text(invoice.sellerProfile?.businessName || "MODA", leftContentX, headerY + 10);
      
      // Subtitle (text-sm font-light)
      pdfDoc.setFontSize(10);
      pdfDoc.setTextColor(...colors.muted);
      pdfDoc.text(invoice.sellerProfile?.businessName ? "Invoice" : "Luxury Marketplace", leftContentX, headerY + 16);
      
      // Right side - Invoice badge and details (text-right alignment)
      const rightContentWidth = 60;
      const rightSideX = pageWidth - margin - rightContentWidth;
      
      // Invoice badge (inline-flex items-center px-4 py-2 bg-accent rounded-full)
      pdfDoc.setFillColor(...colors.accent);
      pdfDoc.roundedRect(rightSideX + 15, headerY - 1, 30, 8, 4, 4, 'F');
      pdfDoc.setTextColor(...colors.accentText);
      pdfDoc.setFontSize(8);
      pdfDoc.setFont("helvetica", "normal");
      pdfDoc.text("INVOICE", rightSideX + 30, headerY + 4, { align: 'center' });
      
      // Invoice number (text-3xl font-light)
      pdfDoc.setTextColor(...colors.primary);
      pdfDoc.setFontSize(20);
      pdfDoc.setFont("helvetica", "normal");
      pdfDoc.text(`#${invoice.invoiceNumber}`, pageWidth - margin, headerY + 18, { align: 'right' });
      
      // Invoice date (text-sm font-light)
      pdfDoc.setFontSize(9);
      pdfDoc.setTextColor(...colors.muted);
      pdfDoc.text(`Invoice Date: ${formatDate(invoice.updatedAt)}`, pageWidth - margin, headerY + 25, { align: 'right' });
      
      yPosition = headerY + 35;
      
      // Separator line (matching web Separator)
      pdfDoc.setDrawColor(...colors.border);
      pdfDoc.setLineWidth(0.5);
      pdfDoc.line(margin, yPosition, pageWidth - margin, yPosition);
      yPosition += 15;
      
      // === BILLING INFORMATION - Two column layout (grid-cols-2 gap-12) ===
      const col1X = margin;
      const col2X = margin + (contentWidth / 2) + 6; // gap-12 ≈ 6mm
      
      // FROM section (text-sm font-light uppercase tracking-wide)
      pdfDoc.setFontSize(9);
      pdfDoc.setFont("helvetica", "normal");
      pdfDoc.setTextColor(...colors.primary);
      pdfDoc.text("FROM", col1X, yPosition);
      
      let fromY = yPosition + 8;
      
      // Company name (font-light text-primary)
      pdfDoc.setFontSize(12);
      pdfDoc.setFont("helvetica", "normal");
      pdfDoc.setTextColor(...colors.primary);
      pdfDoc.text(invoice.sellerProfile?.businessName || "MODA Seller", col1X, fromY);
      fromY += 10;
      
      // Contact details (text-muted-foreground text-sm)
      pdfDoc.setFontSize(9);
      pdfDoc.setTextColor(...colors.muted);
      
      if (invoice.sellerProfile?.companyEmail) {
        pdfDoc.text(invoice.sellerProfile.companyEmail, col1X, fromY);
        fromY += 6;
      }
      
      if (invoice.sellerProfile?.companyPhone || invoice.sellerProfile?.phone) {
        pdfDoc.text(invoice.sellerProfile.companyPhone || invoice.sellerProfile.phone, col1X, fromY);
        fromY += 6;
      }
      
      if (invoice.sellerProfile?.website) {
        pdfDoc.text(invoice.sellerProfile.website, col1X, fromY);
        fromY += 6;
      }
      
      if (invoice.sellerProfile?.address) {
        pdfDoc.text(invoice.sellerProfile.address.street, col1X, fromY);
        fromY += 5;
        pdfDoc.text(`${invoice.sellerProfile.address.city}, ${invoice.sellerProfile.address.state} ${invoice.sellerProfile.address.zipCode}`, col1X, fromY);
        fromY += 5;
        pdfDoc.text(invoice.sellerProfile.address.country, col1X, fromY);
      } else {
        // Fallback address matching web
        pdfDoc.text("<EMAIL>", col1X, fromY);
        fromY += 6;
        pdfDoc.text("+****************", col1X, fromY);
        fromY += 6;
        pdfDoc.text("123 Luxury Lane", col1X, fromY);
        fromY += 5;
        pdfDoc.text("Beverly Hills, CA 90210", col1X, fromY);
        fromY += 5;
        pdfDoc.text("United States", col1X, fromY);
      }
      
      // BILL TO section
      pdfDoc.setFontSize(9);
      pdfDoc.setFont("helvetica", "normal");
      pdfDoc.setTextColor(...colors.primary);
      pdfDoc.text("BILL TO", col2X, yPosition);
      
      let billToY = yPosition + 8;
      
      // Client name
      pdfDoc.setFontSize(12);
      pdfDoc.setFont("helvetica", "normal");
      pdfDoc.setTextColor(...colors.primary);
      pdfDoc.text(invoice.clientName, col2X, billToY);
      billToY += 10;
      
      // Client details
      pdfDoc.setFontSize(9);
      pdfDoc.setTextColor(...colors.muted);
      
      pdfDoc.text(invoice.clientEmail, col2X, billToY);
      billToY += 6;
      
      if (invoice.clientPhone) {
        pdfDoc.text(invoice.clientPhone, col2X, billToY);
        billToY += 6;
      }
      
      pdfDoc.text(invoice.clientAddress.street, col2X, billToY);
      billToY += 5;
      pdfDoc.text(`${invoice.clientAddress.city}, ${invoice.clientAddress.state} ${invoice.clientAddress.zipCode}`, col2X, billToY);
      billToY += 5;
      pdfDoc.text(invoice.clientAddress.country, col2X, billToY);
      
      yPosition += 65;
      
      // === INVOICE DETAILS SECTION - Matching web bg-primary/5 box ===
      const detailsBoxY = yPosition;
      const detailsBoxHeight = 25;
      
      // Light background box (p-4 bg-primary/5 rounded-xl)
      pdfDoc.setFillColor(...colors.lightBg);
      pdfDoc.roundedRect(margin, detailsBoxY, contentWidth, detailsBoxHeight, 3, 3, 'F');
      
      // Three columns matching web grid-cols-3 gap-6
      const detailCol1X = margin + 8;
      const detailCol2X = margin + (contentWidth / 3);
      const detailCol3X = margin + (2 * contentWidth / 3) - 8;
      
      // Column 1 - Invoice Date (flex items-center space-x-2)
      pdfDoc.setFontSize(7);
      pdfDoc.setTextColor(...colors.muted);
      pdfDoc.text("INVOICE DATE", detailCol1X, detailsBoxY + 8);
      pdfDoc.setFontSize(10);
      pdfDoc.setFont("helvetica", "normal");
      pdfDoc.setTextColor(...colors.primary);
      pdfDoc.text(formatDate(invoice.updatedAt), detailCol1X, detailsBoxY + 16);
      
      // Column 2 - Due Date
      pdfDoc.setFontSize(7);
      pdfDoc.setTextColor(...colors.muted);
      pdfDoc.text("DUE DATE", detailCol2X, detailsBoxY + 8);
      pdfDoc.setFontSize(10);
      
      const isOverdue = invoice.status !== "paid" && invoice.dueDate && new Date(invoice.dueDate) < new Date();
      if (isOverdue) {
        pdfDoc.setTextColor(...colors.red);
      } else {
        pdfDoc.setTextColor(...colors.primary);
      }
      pdfDoc.text(invoice.dueDate ? formatDate(invoice.dueDate) : 'Not set', detailCol2X, detailsBoxY + 16);
      
      // Column 3 - Payment Terms
      pdfDoc.setFontSize(7);
      pdfDoc.setTextColor(...colors.muted);
      pdfDoc.text("PAYMENT TERMS", detailCol3X, detailsBoxY + 8);
      pdfDoc.setFontSize(10);
      pdfDoc.setTextColor(...colors.primary);
      pdfDoc.text(invoice.paymentTerms || 'Net 30', detailCol3X, detailsBoxY + 16);
      
      yPosition += 35;
      
      // === ITEM DETAILS TABLE - Exactly matching web design ===
      pdfDoc.setFontSize(9);
      pdfDoc.setFont("helvetica", "normal");
      pdfDoc.setTextColor(...colors.primary);
      pdfDoc.text("ITEM DETAILS", margin, yPosition);
      yPosition += 10;
      
      // Table with proper borders (border border-border rounded-xl)
      const tableStartY = yPosition;
      const tableHeaderHeight = 12;
      const tableRowHeight = 18;
      const tableHeight = tableHeaderHeight + tableRowHeight;
      
      // Table border
      pdfDoc.setDrawColor(...colors.border);
      pdfDoc.setLineWidth(0.5);
      pdfDoc.roundedRect(margin, tableStartY, contentWidth, tableHeight, 3, 3, 'S');
      
      // Table header background (bg-primary/5)
      pdfDoc.setFillColor(...colors.lightBg);
      pdfDoc.roundedRect(margin + 0.5, tableStartY + 0.5, contentWidth - 1, tableHeaderHeight - 1, 2, 2, 'F');
      
      // Header text (grid-cols-12 gap-4)
      pdfDoc.setFontSize(8);
      pdfDoc.setTextColor(...colors.muted);
      pdfDoc.text("Description", margin + 8, tableStartY + 8);
      pdfDoc.text("Quantity", margin + (contentWidth * 0.6), tableStartY + 8, { align: 'center' });
      pdfDoc.text("Unit Price", margin + (contentWidth * 0.75), tableStartY + 8, { align: 'center' });
      pdfDoc.text("Amount", pageWidth - margin - 8, tableStartY + 8, { align: 'right' });
      
      // Separator line (border-t border-border)
      pdfDoc.setDrawColor(...colors.border);
      pdfDoc.setLineWidth(0.3);
      pdfDoc.line(margin + 2, tableStartY + tableHeaderHeight, pageWidth - margin - 2, tableStartY + tableHeaderHeight);
      
      // Table row content
      const rowY = tableStartY + tableHeaderHeight + 6;
      
      // Item description (font-light text-primary)
      pdfDoc.setFontSize(10);
      pdfDoc.setFont("helvetica", "normal");
      pdfDoc.setTextColor(...colors.primary);
      pdfDoc.text(invoice.itemDescription, margin + 8, rowY);
      
      // Product/Service label (text-muted-foreground text-sm)
      pdfDoc.setFontSize(7);
      pdfDoc.setTextColor(...colors.muted);
      pdfDoc.text("Product/Service", margin + 8, rowY + 6);
      
      // Quantity, Price, Amount (text-primary font-light)
      pdfDoc.setFontSize(9);
      pdfDoc.setFont("helvetica", "normal");
      pdfDoc.setTextColor(...colors.primary);
      pdfDoc.text("1", margin + (contentWidth * 0.6), rowY, { align: 'center' });
      pdfDoc.text(formatCurrency(invoice.salePrice), margin + (contentWidth * 0.75), rowY, { align: 'center' });
      pdfDoc.text(formatCurrency(invoice.salePrice), pageWidth - margin - 8, rowY, { align: 'right' });
      
      yPosition = tableStartY + tableHeight + 15;
      
      // === TOTALS SECTION - Right aligned like web (w-80 space-y-3) ===
      const totalsStartX = pageWidth - margin - 70;
      
      // Subtotal (text-sm text-muted-foreground font-light)
      pdfDoc.setFontSize(9);
      pdfDoc.setFont("helvetica", "normal");
      pdfDoc.setTextColor(...colors.muted);
      pdfDoc.text("Subtotal:", totalsStartX, yPosition);
      pdfDoc.text(formatCurrency(invoice.salePrice), pageWidth - margin - 8, yPosition, { align: 'right' });
      yPosition += 8;
      
      // Tax (if applicable)
      if (invoice.tax && invoice.tax > 0) {
        pdfDoc.text("Tax:", totalsStartX, yPosition);
        pdfDoc.text(formatCurrency(invoice.tax), pageWidth - margin - 8, yPosition, { align: 'right' });
        yPosition += 8;
      }
      
      // Separator line
      pdfDoc.setDrawColor(...colors.border);
      pdfDoc.setLineWidth(0.3);
      pdfDoc.line(totalsStartX, yPosition + 2, pageWidth - margin - 8, yPosition + 2);
      yPosition += 8;
      
      // Total - prominent styling (text-lg font-light text-primary)
      pdfDoc.setFontSize(12);
      pdfDoc.setFont("helvetica", "normal");
      pdfDoc.setTextColor(...colors.primary);
      pdfDoc.text("Total:", totalsStartX, yPosition);
      pdfDoc.text(formatCurrency(invoice.totalAmount), pageWidth - margin - 8, yPosition, { align: 'right' });
      
      // Paid status if applicable (text-sm text-green-600 font-light)
      if (invoice.status === "paid") {
        yPosition += 10;
        pdfDoc.setFontSize(9);
        pdfDoc.setTextColor(...colors.green);
        pdfDoc.text("Paid on:", totalsStartX, yPosition);
        pdfDoc.text(invoice.paidDate ? formatDate(invoice.paidDate) : 'N/A', pageWidth - margin - 8, yPosition, { align: 'right' });
      }
      
      yPosition += 20;
      
      // === NOTES SECTION ===
      if (invoice.notes) {
        // Separator
        pdfDoc.setDrawColor(...colors.border);
        pdfDoc.setLineWidth(0.5);
        pdfDoc.line(margin, yPosition, pageWidth - margin, yPosition);
        yPosition += 10;
        
        pdfDoc.setFontSize(9);
        pdfDoc.setFont("helvetica", "normal");
        pdfDoc.setTextColor(...colors.primary);
        pdfDoc.text("NOTES", margin, yPosition);
        yPosition += 8;
        
        // Notes background box (p-4 bg-primary/5 rounded-xl)
        const notesHeight = 20;
        pdfDoc.setFillColor(...colors.lightBg);
        pdfDoc.roundedRect(margin, yPosition, contentWidth, notesHeight, 3, 3, 'F');
        
        pdfDoc.setFontSize(9);
        pdfDoc.setTextColor(...colors.muted);
        
        // Properly wrap notes text
        const wrappedNotes = pdfDoc.splitTextToSize(invoice.notes, contentWidth - 16);
        pdfDoc.text(wrappedNotes, margin + 8, yPosition + 8);
        
        yPosition += 25;
      }
      
      // === FOOTER === (border-t border-border text-center text-muted-foreground)
      pdfDoc.setDrawColor(...colors.border);
      pdfDoc.setLineWidth(0.5);
      pdfDoc.line(margin, yPosition, pageWidth - margin, yPosition);
      yPosition += 12;
      
      pdfDoc.setFontSize(9);
      pdfDoc.setFont("helvetica", "normal");
      pdfDoc.setTextColor(...colors.muted);
      pdfDoc.text("Thank you for your business!", pageWidth / 2, yPosition, { align: 'center' });
      pdfDoc.text("Please make payment by the due date to avoid any late fees.", pageWidth / 2, yPosition + 6, { align: 'center' });
      
      // Download the PDF
      pdfDoc.save(`invoice-${invoice.invoiceNumber}.pdf`);
      toast.success("PDF generated and downloaded successfully!");
      
    } catch (error) {
      console.error("PDF generation failed:", error);
      toast.error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  if (!invoice) {
    return (
      <div className="min-h-screen bg-background">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground font-light">Loading invoice...</p>
          </div>
        </div>
      </div>
    );
  }

  const isOverdue = invoice.status !== "paid" && invoice.dueDate && new Date(invoice.dueDate) < new Date();
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-background">
      {/* Header Bar - Full Width */}
      <div className="border-b border-border bg-card">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/seller/invoices">
                <Button variant="ghost" className="rounded-xl font-light">
                  <ArrowLeft className="h-5 w-5" />
                </Button>
              </Link>
              <div>
                <h1 className="text-2xl font-light text-primary tracking-wide">
                  INVOICE #{invoice.invoiceNumber}
                </h1>
                <p className="text-sm text-muted-foreground font-light">
                  Created {formatDate(invoice.updatedAt)}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Badge
                variant={
                  invoice.status === "paid" ? "default" :
                  isOverdue ? "destructive" :
                  invoice.status === "sent" ? "secondary" : "outline"
                }
                className="px-3 py-1 text-sm font-light rounded-xl"
              >
                {isOverdue ? "Overdue" : invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
              </Badge>
              
              <Button
                variant="outline"
                onClick={handleGeneratePDF}
                disabled={isGeneratingPDF}
                className="rounded-xl font-light"
              >
                {isGeneratingPDF ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Download className="w-4 h-4 mr-2" />
                )}
                {isGeneratingPDF ? "Generating..." : "Generate PDF"}
              </Button>
              
              {invoice.status !== "paid" && (
                <>
                  <Button
                    variant="outline"
                    onClick={handleSendEmail}
                    disabled={isSending}
                    className="rounded-xl font-light"
                  >
                    {isSending ? (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <Mail className="w-4 h-4 mr-2" />
                    )}
                    {isSending ? "Sending..." : "Send Email"}
                  </Button>
                  
                  <Button
                    onClick={handleMarkAsPaid}
                    className="bg-accent text-accent-foreground hover:bg-accent/90 rounded-xl font-light px-6"
                  >
                    <DollarSign className="w-4 h-4 mr-2" />
                    Mark as Paid
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        {/* Invoice Card */}
        <Card className="rounded-xl border-border shadow-lg">
          <CardContent className="p-8">
            {/* Invoice Header */}
            <div className="flex justify-between items-start mb-8">
              <div className="flex items-center space-x-3">
                {invoice.sellerProfile?.companyLogo ? (
                  <ProfileImage
                    storageId={invoice.sellerProfile.companyLogo}
                    name={invoice.sellerProfile.businessName || "Company"}
                    size="lg"
                    variant="company"
                    className="rounded-xl"
                  />
                ) : (
                  <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center">
                    <Building2 className="w-6 h-6 text-primary-foreground" />
                  </div>
                )}
                <div>
                  <h2 className="text-2xl font-light text-primary tracking-wide">
                    {invoice.sellerProfile?.businessName || "MODA"}
                  </h2>
                  <p className="text-sm text-muted-foreground font-light">
                    {invoice.sellerProfile?.businessName ? "Invoice" : "Luxury Marketplace"}
                  </p>
                </div>
              </div>
              
              <div className="text-right">
                <div className="inline-flex items-center px-4 py-2 bg-accent text-accent-foreground rounded-full text-sm font-light">
                  INVOICE
                </div>
                <h3 className="text-3xl font-light text-primary mt-2">#{invoice.invoiceNumber}</h3>
                <p className="text-muted-foreground text-sm mt-1 font-light">Invoice Date: {formatDate(invoice.updatedAt)}</p>
              </div>
            </div>

            <Separator className="my-8" />

            {/* Billing Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-8">
              {/* From */}
              <div>
                <h4 className="text-sm font-light text-primary uppercase tracking-wide mb-4">From</h4>
                <div className="space-y-2">
                  <p className="font-light text-primary">
                    {invoice.sellerProfile?.businessName || "MODA Seller"}
                  </p>
                  
                  {/* Company Email */}
                  {invoice.sellerProfile?.companyEmail && (
                    <div className="flex items-center text-muted-foreground text-sm">
                      <MailIcon className="w-4 h-4 mr-2" />
                      <span className="font-light">{invoice.sellerProfile.companyEmail}</span>
                    </div>
                  )}
                  
                  {/* Company Phone */}
                  {(invoice.sellerProfile?.companyPhone || invoice.sellerProfile?.phone) && (
                    <div className="flex items-center text-muted-foreground text-sm">
                      <Phone className="w-4 h-4 mr-2" />
                      <span className="font-light">
                        {invoice.sellerProfile.companyPhone || invoice.sellerProfile.phone}
                      </span>
                    </div>
                  )}
                  
                  {/* Company Website */}
                  {invoice.sellerProfile?.website && (
                    <div className="flex items-center text-muted-foreground text-sm">
                      <Globe className="w-4 h-4 mr-2" />
                      <span className="font-light">{invoice.sellerProfile.website}</span>
                    </div>
                  )}
                  
                  {/* Company Address */}
                  {invoice.sellerProfile?.address && (
                    <div className="flex items-start text-muted-foreground text-sm">
                      <MapPin className="w-4 h-4 mr-2 mt-0.5" />
                      <div>
                        <p className="font-light">{invoice.sellerProfile.address.street}</p>
                        <p className="font-light">
                          {invoice.sellerProfile.address.city}, {invoice.sellerProfile.address.state} {invoice.sellerProfile.address.zipCode}
                        </p>
                        <p className="font-light">{invoice.sellerProfile.address.country}</p>
                      </div>
                    </div>
                  )}
                  
                  {/* Fallback for no seller profile */}
                  {!invoice.sellerProfile && (
                    <>
                      <div className="flex items-center text-muted-foreground text-sm">
                        <MailIcon className="w-4 h-4 mr-2" />
                        <span className="font-light"><EMAIL></span>
                      </div>
                      <div className="flex items-center text-muted-foreground text-sm">
                        <Phone className="w-4 h-4 mr-2" />
                        <span className="font-light">+****************</span>
                      </div>
                      <div className="flex items-start text-muted-foreground text-sm">
                        <MapPin className="w-4 h-4 mr-2 mt-0.5" />
                        <div>
                          <p className="font-light">123 Luxury Lane</p>
                          <p className="font-light">Beverly Hills, CA 90210</p>
                          <p className="font-light">United States</p>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Bill To */}
              <div>
                <h4 className="text-sm font-light text-primary uppercase tracking-wide mb-4">Bill To</h4>
                <div className="space-y-2">
                  <p className="font-light text-primary">{invoice.clientName}</p>
                  <div className="flex items-center text-muted-foreground text-sm">
                    <MailIcon className="w-4 h-4 mr-2" />
                    <span className="font-light">{invoice.clientEmail}</span>
                  </div>
                  {invoice.clientPhone && (
                    <div className="flex items-center text-muted-foreground text-sm">
                      <Phone className="w-4 h-4 mr-2" />
                      <span className="font-light">{invoice.clientPhone}</span>
                    </div>
                  )}
                  <div className="flex items-start text-muted-foreground text-sm">
                    <MapPin className="w-4 h-4 mr-2 mt-0.5" />
                    <div>
                      <p className="font-light">{invoice.clientAddress.street}</p>
                      <p className="font-light">{invoice.clientAddress.city}, {invoice.clientAddress.state} {invoice.clientAddress.zipCode}</p>
                      <p className="font-light">{invoice.clientAddress.country}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Invoice Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 p-4 bg-primary/5 rounded-xl">
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground uppercase tracking-wide font-light">Invoice Date</p>
                  <p className="font-light text-primary">{formatDate(invoice.updatedAt)}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground uppercase tracking-wide font-light">Due Date</p>
                  <p className={`font-light ${isOverdue ? 'text-destructive' : 'text-primary'}`}>
                    {invoice.dueDate ? formatDate(invoice.dueDate) : 'Not set'}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <DollarSign className="w-4 h-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground uppercase tracking-wide font-light">Payment Terms</p>
                  <p className="font-light text-primary">{invoice.paymentTerms || 'Net 30'}</p>
                </div>
              </div>
            </div>

            {/* Items Table */}
            <div className="mb-8">
              <h4 className="text-sm font-light text-primary uppercase tracking-wide mb-4">Item Details</h4>
              <div className="border border-border rounded-xl overflow-hidden">
                <div className="bg-primary/5 px-6 py-4 grid grid-cols-12 gap-4 text-sm font-light text-muted-foreground">
                  <div className="col-span-6">Description</div>
                  <div className="col-span-2 text-center">Quantity</div>
                  <div className="col-span-2 text-center">Unit Price</div>
                  <div className="col-span-2 text-right">Amount</div>
                </div>
                
                <div className="px-6 py-4 grid grid-cols-12 gap-4 text-sm border-t border-border">
                  <div className="col-span-6">
                    <p className="font-light text-primary">{invoice.itemDescription}</p>
                    <p className="text-muted-foreground text-sm mt-1 font-light">Product/Service</p>
                  </div>
                  <div className="col-span-2 text-center text-primary font-light">1</div>
                  <div className="col-span-2 text-center text-primary font-light">{formatCurrency(invoice.salePrice)}</div>
                  <div className="col-span-2 text-right font-light text-primary">{formatCurrency(invoice.salePrice)}</div>
                </div>
              </div>
            </div>

            {/* Totals */}
            <div className="flex justify-end">
              <div className="w-80 space-y-3">
                <div className="flex justify-between text-sm text-muted-foreground font-light">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(invoice.salePrice)}</span>
                </div>
                
                {invoice.tax && invoice.tax > 0 && (
                  <div className="flex justify-between text-sm text-muted-foreground font-light">
                    <span>Tax:</span>
                    <span>{formatCurrency(invoice.tax)}</span>
                  </div>
                )}
                
                <Separator />
                
                <div className="flex justify-between text-lg font-light text-primary">
                  <span>Total:</span>
                  <span>{formatCurrency(invoice.totalAmount)}</span>
                </div>
                
                {invoice.status === "paid" && (
                  <div className="flex justify-between text-sm text-green-600 font-light">
                    <span>Paid on:</span>
                    <span>{invoice.paidDate ? formatDate(invoice.paidDate) : 'N/A'}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Notes */}
            {invoice.notes && (
              <>
                <Separator className="my-8" />
                <div>
                  <h4 className="text-sm font-light text-primary uppercase tracking-wide mb-3">Notes</h4>
                  <div className="p-4 bg-primary/5 rounded-xl">
                    <p className="text-muted-foreground font-light">{invoice.notes}</p>
                  </div>
                </div>
              </>
            )}

            {/* Footer */}
            <div className="mt-12 pt-8 border-t border-border">
              <div className="text-center text-muted-foreground text-sm font-light">
                <p>Thank you for your business!</p>
                <p className="mt-1">Please make payment by the due date to avoid any late fees.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}