import { Metadata } from "next";
import { OffersDashboard } from "@/components/seller/offers/OffersDashboard";

export const metadata: Metadata = {
  title: "Offers | MODA",
  description: "Manage product offers, respond to buyers, and track negotiation status on MODA's premium marketplace.",
  openGraph: {
    title: "Offers | MODA",
    description: "Manage product offers, respond to buyers, and track negotiation status on MODA's premium marketplace.",
    type: "website",
  },
};

export default function OffersPage() {
  return <OffersDashboard />;
}
