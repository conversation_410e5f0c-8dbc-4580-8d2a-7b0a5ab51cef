"use client";

import { useState, useMemo } from "react";
import { useAuth } from "@/hooks/useBetterAuth";
import { useMessages, useSearchMessages } from "@/hooks/useMessages";
import { ConversationView } from "@/components/seller/messages/ConversationView";

import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import { 
  MessageCircle, 
  Inbox,
  Search,
  Filter,
  X,
  User,
  Package
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

// Move all hooks to top-level, and avoid conditional hooks
export default function MessagesPage() {
  const { user } = useAuth();
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  // Use the new search functionality
  const { conversations: allConversations, unreadCount, markAsRead } = useMessages();
  const { searchResults } = useSearchMessages(searchQuery);

  // Use search results if there's a search query, otherwise use all conversations
  const conversations = searchQuery.trim() ? searchResults : allConversations;

  const handleSelectConversation = async (conversationId: string, otherUserId: string) => {
    setSelectedConversation(otherUserId);

    // Mark messages as read
    try {
      await markAsRead({ conversationId });
    } catch (error) {
      console.error("Error marking messages as read:", error);
    }
  };

  const clearSearch = () => {
    setSearchQuery("");
  };

  // Type guard to check if conversation has enriched data
  const hasEnrichedData = (conversation: any): conversation is any & {
    otherParticipant: any;
    product: any;
    unreadCount: number;
  } => {
    return conversation && 'otherParticipant' in conversation;
  };

  // Memoize selected conversation details for performance and to avoid repeated finds
  const selectedConversationDetails = useMemo(() => {
    if (!conversations || !selectedConversation) return null;
    const found = conversations.find(
      (c: any) => c && hasEnrichedData(c) && (c as any).otherParticipant?._id === selectedConversation
    );
    return found as any;
  }, [conversations, selectedConversation]);

  if (!user) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <MessageCircle className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-neutral-900 dark:text-white mb-2">
            Authentication Required
          </h3>
          <p className="text-neutral-600 dark:text-neutral-400">
            Please sign in to view your messages.
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Full Width Header with Search and Actions */}
      <div className="border-b border-border bg-card">
        <div className="p-3.5">
          <div className="flex items-center justify-between">
            {/* Search and Filters */}
            <div className="flex items-center gap-4">
              <div className="relative max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search messages in conversations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-10 rounded-xl bg-primary/5 border-border font-light placeholder:text-primary transition-all duration-300 focus:ring-2 focus:ring-primary/20"
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearSearch}
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-muted"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                )}
              </div>
              
              <Button 
                variant="outline" 
                className="rounded-xl font-light"
              >
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Conversations List */}
        <div className="w-96 border-r border-border bg-card">
          <div className="p-4 border-b border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm font-light text-primary/80 uppercase tracking-wide">
                <Inbox className="w-4 h-4" />
                {searchQuery ? 'Search Results' : 'Conversations'}
              </div>
              {searchQuery && conversations && (
                <Badge variant="secondary" className="text-xs">
                  {conversations.length} result{conversations.length !== 1 ? 's' : ''}
                </Badge>
              )}
            </div>
          </div>
          
          <div className="overflow-y-auto h-[calc(100vh-200px)]">
            {conversations?.length === 0 && (
              <div className="p-6 text-center">
                <MessageCircle className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
                <p className="text-muted-foreground font-light">
                  {searchQuery ? 'No messages found' : 'No conversations yet'}
                </p>
                {searchQuery && (
                  <p className="text-xs text-muted-foreground mt-2">
                    Try a different search term
                  </p>
                )}
              </div>
            )}

            {conversations?.map((conversation: any) => {
              if (!conversation || !hasEnrichedData(conversation)) return null;
              
              // Type assertion after type guard
              const enrichedConversation = conversation as any;
              
              return (
                <div
                  key={enrichedConversation.conversationId}
                  className={`p-4 border-b border-border cursor-pointer hover:bg-muted/50 transition-colors ${
                    selectedConversation === enrichedConversation.otherParticipant?._id 
                      ? 'bg-muted' 
                      : ''
                  }`}
                  onClick={() => handleSelectConversation(
                    enrichedConversation.conversationId, 
                    enrichedConversation.otherParticipant?._id || ''
                  )}
                >
                  <div className="flex items-start space-x-3">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                      <User className="w-5 h-5 text-primary" />
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-foreground truncate">
                          {enrichedConversation.otherParticipant?.name || 'Unknown User'}
                        </h4>
                        <div className="flex items-center gap-2">
                          {enrichedConversation.unreadCount > 0 && (
                            <Badge className="bg-blue-500 text-white text-xs">
                              {enrichedConversation.unreadCount}
                            </Badge>
                          )}
                          <span className="text-xs text-muted-foreground">
                            {formatDistanceToNow(enrichedConversation.lastMessageAt, { addSuffix: true })}
                          </span>
                        </div>
                      </div>

                      {/* Show search preview if available */}
                      {searchQuery && 'searchResults' in enrichedConversation && enrichedConversation.searchResults ? (
                        <div className="mt-1">
                          <p className="text-sm text-muted-foreground truncate font-light">
                            <span className="text-primary font-medium">
                              {enrichedConversation.searchResults.matchCount} match{enrichedConversation.searchResults.matchCount !== 1 ? 'es' : ''}:
                            </span> {enrichedConversation.searchResults.mostRecentMatch.content}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {formatDistanceToNow(enrichedConversation.searchResults.mostRecentMatch._creationTime, { addSuffix: true })}
                          </p>
                        </div>
                      ) : (
                        <p className="text-sm text-muted-foreground truncate mt-1 font-light">
                          {enrichedConversation.lastMessagePreview}
                        </p>
                      )}

                      {enrichedConversation.product && (
                        <div className="flex items-center gap-2 mt-2">
                          <Package className="w-3 h-3 text-muted-foreground" />
                          <span className="text-xs text-muted-foreground truncate font-light">
                            {enrichedConversation.product.title}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col bg-background">
          {selectedConversation ? (
            <ConversationView
              conversation={selectedConversationDetails}
              onBack={() => setSelectedConversation(null)}
              isSupportMode={false}
            />
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <MessageCircle className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-light text-foreground mb-2">
                  Select a conversation
                </h3>
                <p className="text-muted-foreground font-light">
                  Choose a conversation from the left to start messaging
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
