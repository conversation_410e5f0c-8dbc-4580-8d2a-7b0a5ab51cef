import { Metadata } from "next";
import NewSupplierFormClient from "./NewSupplierFormClient";

export const metadata: Metadata = {
  title: "Add New Supplier | MODA",
  description: "Add a new supplier to your network on MODA's premium marketplace. Manage relationships and track financial transactions.",
  openGraph: {
    title: "Add New Supplier | MODA",
    description: "Add a new supplier to your network on MODA's premium marketplace. Manage relationships and track financial transactions.",
    type: "website",
  },
};

export default function AddSupplierPage() {
  return <NewSupplierFormClient />;
}
