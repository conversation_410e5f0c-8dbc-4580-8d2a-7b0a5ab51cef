import { SellerProfileContent } from "@/components/seller/SellerProfileContent";
import { Metadata } from "next";

interface SellerProfilePageProps {
  params: Promise<{ 
    id: string 
  }>;
}

export async function generateMetadata({ params }: SellerProfilePageProps): Promise<Metadata> {
    const { id } = await params;
  return {
    title: `Seller Profile - Moda`,
    description: `Browse luxury items from this verified seller on Moda marketplace`,
  };
}

export default async function SellerProfilePage({ params }: SellerProfilePageProps) {
  const { id } = await params;
  return <SellerProfileContent sellerId={id} />;
}
