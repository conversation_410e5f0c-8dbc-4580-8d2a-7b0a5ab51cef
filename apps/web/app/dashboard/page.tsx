"use client";

import { DashboardLanding } from "@/components/dashboard";
import { MarketplaceHeader } from "@/components/marketplace/MarketplaceHeader";
import { Authenticated, Unauthenticated, AuthLoading } from "convex/react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Lock, Shield, Star } from "lucide-react";
import { FullScreenLoader } from "@/components/common";

export default function DashboardPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen">
      {/* Loading State */}
      <AuthLoading>
        <FullScreenLoader message="Loading dashboard..." theme="light" />
      </AuthLoading>

      {/* Authenticated Users - Show Dashboard with Header */}
      <Authenticated>
        <div className="bg-card min-h-screen">
          <MarketplaceHeader />
          <DashboardLanding />
        </div>
      </Authenticated>

      {/* Unauthenticated Users - Show Access Required */}
      <Unauthenticated>
        <div className="min-h-screen flex items-center justify-center">
          <div className="max-w-md mx-auto text-center p-8">
            <div className="w-16 h-16 bg-gradient-to-br from-zinc-200 to-zinc-400 rounded-full flex items-center justify-center mx-auto mb-6">
              <Lock className="w-8 h-8 text-black" />
            </div>

            <h1 className="text-3xl font-bold mb-4">Dashboard Access Required</h1>
            <p className="text-zinc-400 mb-8 leading-relaxed">
              Please sign in to access your personalized dashboard and explore our luxury marketplace.
            </p>

            <div className="space-y-4 mb-8">
              <div className="flex items-center text-left">
                <Shield className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                <span className="text-sm text-zinc-300">Personalized marketplace experience</span>
              </div>
              <div className="flex items-center text-left">
                <Star className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                <span className="text-sm text-zinc-300">Access to premium communities</span>
              </div>
              <div className="flex items-center text-left">
                <Lock className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                <span className="text-sm text-zinc-300">Secure account management</span>
              </div>
            </div>

            <div className="space-y-3">
              <Button
                className="w-full bg-white text-black hover:bg-zinc-200 font-medium"
                onClick={() => router.push("/")}
              >
                Sign In to Continue
              </Button>
              <Button
                variant="outline"
                className="w-full border-zinc-700 text-white hover:bg-zinc-800"
                onClick={() => router.push("/")}
              >
                Learn More
              </Button>
            </div>

            <p className="text-xs text-zinc-500 mt-6">
              New to MODA? <button
                onClick={() => router.push("/")}
                className="text-white hover:underline"
              >
                Create an account
              </button>
            </p>
          </div>
        </div>
      </Unauthenticated>
    </div>
  );
}
