import { NextRequest, NextResponse } from 'next/server';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '@repo/backend/convex/_generated/api';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-07-30.basil',
});

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export async function GET(request: NextRequest) {
  try {
    // Get user's Stripe customer ID
    const customerResult = await convex.action(api.stripeCustomers.getOrCreateCustomer);
    
    if (!customerResult.success || !customerResult.customerId) {
      return NextResponse.json(
        { error: 'Failed to get customer information' },
        { status: 400 }
      );
    }

    // Get payment methods from Stripe
    const paymentMethods = await stripe.paymentMethods.list({
      customer: customerResult.customerId,
      type: 'card',
    });

    // Get default payment method
    const customer = await stripe.customers.retrieve(customerResult.customerId);
    const defaultPaymentMethodId = typeof customer !== 'string' && 
      'invoice_settings' in customer && 
      customer.invoice_settings?.default_payment_method;

    const formattedMethods = paymentMethods.data.map(pm => ({
      id: pm.id,
      type: pm.type,
      card: pm.card,
      isDefault: pm.id === defaultPaymentMethodId,
    }));

    return NextResponse.json({
      success: true,
      paymentMethods: formattedMethods,
    });
  } catch (error) {
    console.error('Error fetching payment methods:', error);
    return NextResponse.json(
      { error: 'Failed to fetch payment methods' },
      { status: 500 }
    );
  }
}
