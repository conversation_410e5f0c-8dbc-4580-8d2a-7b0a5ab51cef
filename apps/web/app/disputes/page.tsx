import { Metadata } from "next";
import { Suspense } from "react";
import { DisputeForm } from "@/components/disputes/DisputeForm";
import { InlineLoader } from "@/components/common";
import { CommunityLayout } from "@/components/layouts/CommunityLayout";

export const metadata: Metadata = {
  title: "Dispute Center | MODA",
  description: "Submit and track disputes for your MODA transactions and interactions.",
  openGraph: {
    title: "Dispute Center | MODA",
    description: "Submit and track disputes for your MODA transactions and interactions.",
    type: "website",
  },
};

export default function DisputeCenterPage() {
  return (
    <CommunityLayout>
    <div className="min-h-screen bg-gradient-to-br from-zinc-50 to-zinc-100 dark:from-zinc-900 dark:to-zinc-800">
      {/* Header */}
      <div className="border-b bg-white/80 dark:bg-zinc-900/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-light text-zinc-900 dark:text-zinc-100 mb-2">
              Dispute Center
            </h1>
            <p className="text-zinc-600 dark:text-zinc-400 max-w-2xl mx-auto">
              Need help with a transaction or have an issue with our platform? 
              Submit a dispute and our support team will assist you in resolving it.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <Suspense fallback={<InlineLoader message="Loading dispute form..." />}>
          <DisputeForm />
        </Suspense>
      </div>

    </div>
    </CommunityLayout>
  );
}