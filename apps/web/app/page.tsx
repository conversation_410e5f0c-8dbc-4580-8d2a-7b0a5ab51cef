"use client";

import { LandingPage } from "@/components/landing/LandingPage";
import { Authenticated, Unauthenticated, AuthLoading } from "convex/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { FullScreenLoader } from "@/components/common";
import { ConditionalFloatingButton } from "@/components/marketplace/ConditionalFloatingButton";

export default function HomePage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Loading State */}
      <AuthLoading>
        <FullScreenLoader message="Loading..." theme="light" />
      </AuthLoading>

      {/* Authenticated Users - Redirect to Dashboard */}
      <Authenticated>
        <AuthenticatedRedirect />
        <ConditionalFloatingButton />
      </Authenticated>

      {/* Unauthenticated Users - Show Landing Page */}
      <Unauthenticated>
        <LandingPage />
      </Unauthenticated>
    </div>
  );
}

function AuthenticatedRedirect() {
  const router = useRouter();
  
  useEffect(() => {
    router.replace("/dashboard");
  }, [router]);

  return <FullScreenLoader message="Loading dashboard..." theme="light" />;
}
