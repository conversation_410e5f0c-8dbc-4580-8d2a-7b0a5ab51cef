"use client";

import { ProductLayout } from "@/components/layouts/ProductLayout";
import { useCart } from "@/hooks/useCart";
import type { CartItem } from "@/hooks/useCart";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Separator } from "@repo/ui/components/separator";
import { 
  ShoppingBag, 
  Minus, 
  Plus, 
  Trash2, 
  ArrowRight,
  CreditCard 
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { cn } from "@repo/ui/lib/utils";
import { Id } from "@repo/backend/convex/_generated/dataModel";

const CONDITION_LABELS = {
  new: "New",
  like_new: "Like New",
  good: "Good",
  fair: "Fair",
};

const CONDITION_COLORS = {
  new: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  like_new: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  good: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  fair: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
};

export default function CartPage() {
  const { 
    cartItems, 
    cartTotal, 
    isLoading, 
    updateQuantity, 
    removeFromCart,
    clearCart 
  } = useCart();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const handleQuantityChange = async (productId: string, newQuantity: number) => {
    if (newQuantity >= 1 && newQuantity <= 10) {
      await updateQuantity(productId as any, newQuantity);
    }
  };

  const handleRemoveItem = async (productId: string) => {
    await removeFromCart(productId as any);
  };

  const handleClearCart = async () => {
    if (confirm("Are you sure you want to clear your cart?")) {
      await clearCart();
    }
  };

  return (
    <ProtectedRoute requireAuth={true}>
      <ProductLayout showSearchBar={true}>
        <div className="container mx-auto px-6 py-12">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-black dark:text-white mb-4">
              Shopping Cart
            </h1>
            <p className="text-lg text-neutral-600 dark:text-neutral-400">
              {cartItems.length > 0 
                ? `${cartItems.length} item${cartItems.length > 1 ? 's' : ''} in your cart`
                : "Your cart is empty"
              }
            </p>
          </div>

          {/* Empty State */}
          {cartItems.length === 0 && !isLoading && (
            <div className="text-center py-16">
              <ShoppingBag className="w-24 h-24 text-neutral-300 dark:text-neutral-700 mx-auto mb-6" />
              <h2 className="text-2xl font-semibold text-neutral-800 dark:text-neutral-200 mb-4">
                Your cart is empty
              </h2>
              <p className="text-neutral-600 dark:text-neutral-400 mb-8 max-w-md mx-auto">
                Start exploring our curated collection of luxury items and add something special to your cart.
              </p>
              <Button asChild className="px-8 py-3">
                <Link href="/marketplace">
                  <ShoppingBag className="w-5 h-5 mr-2" />
                  Browse Marketplace
                </Link>
              </Button>
            </div>
          )}

          {/* Cart Content */}
          {cartItems.length > 0 && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Cart Items */}
              <div className="lg:col-span-2 space-y-6">
                <div className="flex justify-between items-center">
                  <h2 className="text-2xl font-semibold text-black dark:text-white">
                    Cart Items
                  </h2>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={handleClearCart}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Clear Cart
                  </Button>
                </div>

                <div className="space-y-4">
                  {cartItems.map((item: any) => (
                    <div 
                      key={item._id}
                      className="bg-white dark:bg-neutral-900 rounded-2xl p-6 border border-neutral-200/50 dark:border-neutral-800/50 shadow-sm"
                    >
                      <div className="flex gap-6">
                        {/* Product Image */}
                        <div className="flex-shrink-0">
                          <Link href={`/marketplace/product/${item.product._id}`}>
                            <div className="w-24 h-24 bg-neutral-100 dark:bg-neutral-800 rounded-xl overflow-hidden">
                              {item.product.images.length > 0 && item.product.images[0] ? (
                                <Image
                                  src={item.product.images[0]}
                                  alt={item.product.title}
                                  width={96}
                                  height={96}
                                  className="w-full h-full object-cover hover:scale-105 transition-transform"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center">
                                  <ShoppingBag className="w-8 h-8 text-neutral-400" />
                                </div>
                              )}
                            </div>
                          </Link>
                        </div>

                        {/* Product Info */}
                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <Link 
                                href={`/marketplace/product/${item.product._id}`}
                                className="text-lg font-semibold text-black dark:text-white hover:text-neutral-600 dark:hover:text-neutral-300 transition-colors"
                              >
                                {item.product.title}
                              </Link>
                              <p className="text-neutral-600 dark:text-neutral-400 text-sm">
                                {item.product.brand}
                              </p>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRemoveItem(item.product._id)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 p-2"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>

                          <div className="flex items-center gap-4 mb-3">
                            <Badge 
                              className={cn(
                                "text-xs",
                                CONDITION_COLORS[item.product.condition as keyof typeof CONDITION_COLORS]
                              )}
                            >
                              {CONDITION_LABELS[item.product.condition as keyof typeof CONDITION_LABELS]}
                            </Badge>
                            <span className="text-sm text-neutral-600 dark:text-neutral-400 capitalize">
                              {item.product.category}
                            </span>
                          </div>

                          <div className="flex items-center justify-between">
                            {/* Quantity Controls */}
                            <div className="flex items-center space-x-3">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleQuantityChange(item.product._id, item.quantity - 1)}
                                disabled={item.quantity <= 1 || isLoading}
                                className="w-8 h-8 p-0 rounded-full"
                              >
                                <Minus className="w-4 h-4" />
                              </Button>
                              <span className="w-8 text-center font-medium">
                                {item.quantity}
                              </span>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleQuantityChange(item.product._id, item.quantity + 1)}
                                disabled={item.quantity >= 10 || isLoading}
                                className="w-8 h-8 p-0 rounded-full"
                              >
                                <Plus className="w-4 h-4" />
                              </Button>
                            </div>

                            {/* Price */}
                            <div className="text-right">
                              <div className="text-lg font-semibold text-black dark:text-white">
                                {formatCurrency(item.product.price * item.quantity)}
                              </div>
                              {item.quantity > 1 && (
                                <div className="text-sm text-neutral-600 dark:text-neutral-400">
                                  {formatCurrency(item.product.price)} each
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Order Summary */}
              <div className="lg:col-span-1">
                <div className="bg-white dark:bg-neutral-900 rounded-2xl p-6 border border-neutral-200/50 dark:border-neutral-800/50 shadow-sm sticky top-6">
                  <h3 className="text-xl font-semibold text-black dark:text-white mb-6">
                    Order Summary
                  </h3>

                  {cartTotal && (
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-neutral-600 dark:text-neutral-400">
                          Subtotal ({cartTotal.itemCount} items)
                        </span>
                        <span className="font-medium">
                          {formatCurrency(cartTotal.subtotal)}
                        </span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-neutral-600 dark:text-neutral-400">
                          Estimated tax
                        </span>
                        <span className="font-medium">
                          {formatCurrency(cartTotal.tax)}
                        </span>
                      </div>

                      <div className="flex justify-between">
                        <span className="text-neutral-600 dark:text-neutral-400">
                          Shipping
                        </span>
                        <span className="font-medium">
                          {cartTotal.shipping === 0 ? (
                            <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 text-xs">
                              Free
                            </Badge>
                          ) : (
                            formatCurrency(cartTotal.shipping)
                          )}
                        </span>
                      </div>

                      <Separator />

                      <div className="flex justify-between text-lg font-semibold">
                        <span className="text-black dark:text-white">Total</span>
                        <span className="text-black dark:text-white">
                          {formatCurrency(cartTotal.total)}
                        </span>
                      </div>

                      <Button 
                        className="w-full h-12 text-base font-medium mt-6"
                        disabled={isLoading}
                      >
                        <CreditCard className="w-5 h-5 mr-2" />
                        Proceed to Checkout
                      </Button>

                      <Button 
                        variant="ghost" 
                        asChild
                        className="w-full h-12 text-base"
                      >
                        <Link href="/marketplace">
                          <ArrowRight className="w-5 h-5 mr-2" />
                          Continue Shopping
                        </Link>
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </ProductLayout>
    </ProtectedRoute>
  );
}
