"use client";

import { Card } from "@repo/ui/components/card";
import { But<PERSON> } from "@repo/ui/components/button";
import { FileCheck, ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";

export default function ReferencesPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-50 to-zinc-100">
      <div className="max-w-4xl mx-auto px-6 py-12">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-6 text-zinc-600 hover:text-zinc-900"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Dashboard
        </Button>

        <Card className="p-12 text-center bg-white">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <FileCheck className="w-8 h-8 text-green-600" />
          </div>
          
          <h1 className="text-3xl font-bold text-zinc-900 mb-4">
            References
          </h1>
          
          <p className="text-zinc-600 text-lg mb-8 max-w-2xl mx-auto">
            Build trust in the marketplace through our comprehensive review and 
            reference system. View detailed seller and buyer histories to make 
            informed decisions with confidence.
          </p>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <h3 className="font-semibold text-green-800 mb-2">Coming Soon Features:</h3>
            <ul className="text-green-700 text-sm space-y-1">
              <li>• Detailed seller profiles</li>
              <li>• Transaction history reviews</li>
              <li>• Verified buyer feedback</li>
              <li>• Trust score algorithms</li>
              <li>• Reference verification system</li>
            </ul>
          </div>

          <Button
            onClick={() => router.push("/dashboard")}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            Return to Dashboard
          </Button>
        </Card>
      </div>
    </div>
  );
}
