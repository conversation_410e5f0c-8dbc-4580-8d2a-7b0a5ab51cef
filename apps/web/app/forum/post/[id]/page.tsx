import { Suspense } from "react";
import { notFound } from "next/navigation";
import RedditStylePostDetail from "@/components/forum/RedditStylePostDetail";
import { Skeleton } from "@repo/ui/components/skeleton";
import { CommunityLayout } from "@/components/layouts/CommunityLayout";

interface ForumPostPageProps {
  params: Promise<{ id: string }>;
}

export default async function ForumPostPage({ params }: ForumPostPageProps) {
  const { id } = await params;

  return (
    <CommunityLayout>
      <div className="container mx-auto py-6 px-4">
        <Suspense fallback={<PostDetailSkeleton />}>
          <RedditStylePostDetail postId={id} />
        </Suspense>
      </div>
    </CommunityLayout>
  );
}

function PostDetailSkeleton() {
  return (
    <div className="flex gap-6">
      {/* Main Content */}
      <div className="flex-1">
        <Skeleton className="h-4 w-96 mb-4" />
        <div className="flex gap-4">
          <div className="bg-muted/30 rounded-l-md p-2 min-w-[48px]">
            <Skeleton className="h-6 w-6 mx-auto mb-2" />
            <Skeleton className="h-4 w-8 mx-auto mb-2" />
            <Skeleton className="h-6 w-6 mx-auto" />
          </div>
          <div className="flex-1 bg-white border rounded-r-md">
            <div className="p-4 border-b">
              <Skeleton className="h-6 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2" />
            </div>
            <div className="p-4">
              <Skeleton className="h-32 w-full mb-4" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-2/3" />
            </div>
            <div className="px-4 pb-2 border-b">
              <div className="flex gap-4">
                <Skeleton className="h-8 w-20" />
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-8 w-16" />
              </div>
            </div>
            <div className="p-4 space-y-4">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-20 rounded-lg" />
              ))}
            </div>
          </div>
        </div>
      </div>
      
      {/* Sidebar */}
      <div className="w-80 space-y-4">
        <Skeleton className="h-10 w-full" />
        <div className="space-y-4">
          <Skeleton className="h-32 w-full rounded-lg" />
          <Skeleton className="h-24 w-full rounded-lg" />
        </div>
      </div>
    </div>
  );
}
