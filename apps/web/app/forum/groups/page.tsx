import { Suspense } from "react";
import Link from "next/link";
import ForumGroups from "@/components/forum/ForumGroups";
import { Skeleton } from "@repo/ui/components/skeleton";
import { Button } from "@repo/ui/components/button";
import { CommunityLayout } from "@/components/layouts/CommunityLayout";
import { ArrowLeft } from "lucide-react";

export default function ForumGroupsPage() {
  return (
    <CommunityLayout>
      <div className="container mx-auto py-8 px-4">
        {/* Breadcrumb Navigation */}
        <div className="flex items-center gap-2 mb-4 text-sm text-muted-foreground">
          <Link href="/forum" className="hover:text-primary">
            Forum
          </Link>
          <span>•</span>
          <span className="text-foreground font-medium">Communities</span>
        </div>

        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Communities</h1>
            <p className="text-muted-foreground mt-2">
              Discover and join topic-based communities
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" asChild>
              <Link href="/forum">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Forum
              </Link>
            </Button>
          </div>
        </div>

        <Suspense fallback={<GroupsSkeleton />}>
          <ForumGroups />
        </Suspense>
      </div>
    </CommunityLayout>
  );
}

function GroupsSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {[...Array(6)].map((_, i) => (
        <Skeleton key={i} className="h-48 rounded-lg" />
      ))}
    </div>
  );
}
