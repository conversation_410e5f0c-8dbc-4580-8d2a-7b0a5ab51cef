import { Suspense } from "react";
import CommunityDetail from "@/components/forum/CommunityDetail";
import { Skeleton } from "@repo/ui/components/skeleton";
import { CommunityLayout } from "@/components/layouts/CommunityLayout";

interface CommunityPageProps {
  params: Promise<{ slug: string }>;
}

export default async function CommunityPage({ params }: CommunityPageProps) {
  const { slug } = await params;

  return (
    <CommunityLayout>
      <div className="container mx-auto py-6 px-4">
        <Suspense fallback={<CommunityDetailSkeleton />}>
          <CommunityDetail slug={slug} />
        </Suspense>
      </div>
    </CommunityLayout>
  );
}

function CommunityDetailSkeleton() {
  return (
    <div className="flex gap-6">
      {/* Main Content */}
      <div className="flex-1 space-y-4">
        <Skeleton className="h-8 w-64" />
        <div className="space-y-4">
          <Skeleton className="h-32 w-full rounded-lg" />
          <Skeleton className="h-24 w-full rounded-lg" />
          <Skeleton className="h-24 w-full rounded-lg" />
        </div>
      </div>
      
      {/* Sidebar */}
      <div className="w-80 space-y-4">
        <Skeleton className="h-10 w-full" />
        <div className="space-y-4">
          <Skeleton className="h-32 w-full rounded-lg" />
          <Skeleton className="h-24 w-full rounded-lg" />
        </div>
      </div>
    </div>
  );
}
