"use client";

import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import Link from "next/link";
import { Clock, Car, Crown, Palette, Users, MessageSquare, TrendingUp, Plus, Globe } from "lucide-react";
import { Skeleton } from "@repo/ui/components/skeleton";
import { CommunityLayout } from "@/components/layouts/CommunityLayout";

const MARKETPLACE_ICONS = {
  "moda-watch": Clock,
  "real-watch-buyers": Clock,
  "moda-car": Car,
  "moda-lifestyle": Crown,
  "moda-misc": Palette,
};

const MARKETPLACE_COLORS = {
  "moda-watch": "from-blue-500 to-indigo-600",
  "real-watch-buyers": "from-slate-500 to-zinc-600",
  "moda-car": "from-red-500 to-orange-600",
  "moda-lifestyle": "from-pink-500 to-rose-600",
  "moda-misc": "from-purple-500 to-violet-600",
};

export default function MarketplaceForumsPage() {
  const marketplaceTypes = useQuery(api.marketplaceQueries.getMarketplaceTypes);
  
  // Get forum stats for each marketplace (we could create a new query for this)
  // For now, we'll use placeholder data
  const forumStats = {
    "general": { posts: 1250, members: 3400 },
    "moda-watch": { posts: 245, members: 1250 },
    "real-watch-buyers": { posts: 189, members: 890 },
    "moda-car": { posts: 156, members: 670 },
    "moda-lifestyle": { posts: 203, members: 980 },
    "moda-misc": { posts: 134, members: 540 },
  };

  if (!marketplaceTypes) {
    return (
      <CommunityLayout>
        <div className="container mx-auto px-6 py-8">
          <div className="mb-8">
            <Skeleton className="h-10 w-64 mb-2" />
            <Skeleton className="h-6 w-96" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(5)].map((_, i) => (
              <Card key={i} className="h-64">
                <CardHeader>
                  <Skeleton className="h-6 w-32 mb-2" />
                  <Skeleton className="h-4 w-48" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-20 w-full mb-4" />
                  <Skeleton className="h-10 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </CommunityLayout>
    );
  }

  return (
    <CommunityLayout>
      <div className="container mx-auto py-6 px-4">
        <Tabs defaultValue="communities" className="w-full">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold">MODA Forums</h1>
              <p className="text-sm text-muted-foreground mt-1">
                Choose your community and join the conversation
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={() => window.location.href = "/forum/groups"}>
                <Users className="mr-2 h-4 w-4" />
                Browse Communities
              </Button>
              <Button onClick={() => window.location.href = "/forum/submit"}>
                <Plus className="mr-2 h-4 w-4" />
                Create Post
              </Button>
            </div>
          </div>

          <TabsContent value="communities" className="mt-0">
            {/* Forums Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* General Forum */}
              <Card className="group hover:shadow-lg transition-all duration-300 border-border/50">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-emerald-500 to-teal-600 text-white">
                      <Globe className="h-6 w-6" />
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      r/general
                    </Badge>
                  </div>
                  <CardTitle className="text-xl group-hover:text-primary transition-colors">
                    General Forum
                  </CardTitle>
                  <CardDescription className="text-sm">
                    General discussions, announcements, and community-wide conversations
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="pt-0">
                  {/* Forum Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="text-center p-3 bg-muted/30 rounded-lg">
                      <div className="flex items-center justify-center gap-1 mb-1">
                        <MessageSquare className="h-3 w-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">Posts</span>
                      </div>
                      <p className="text-sm font-semibold">{forumStats.general.posts}</p>
                    </div>
                    <div className="text-center p-3 bg-muted/30 rounded-lg">
                      <div className="flex items-center justify-center gap-1 mb-1">
                        <Users className="h-3 w-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">Members</span>
                      </div>
                      <p className="text-sm font-semibold">{forumStats.general.members}</p>
                    </div>
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="space-y-2">
                    <Link href="/forum" className="block">
                      <Button className="w-full group-hover:bg-primary/90 transition-colors">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Join Discussion
                      </Button>
                    </Link>
                    <div className="flex gap-2">
                      <Link href="/forum/groups" className="flex-1">
                        <Button variant="outline" className="w-full text-xs">
                          Browse Groups
                        </Button>
                      </Link>
                      <Link href="/forum/submit" className="flex-1">
                        <Button variant="outline" className="w-full text-xs">
                          Create Post
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Marketplace Forums */}
              {marketplaceTypes.map((marketplace) => {
                const Icon = MARKETPLACE_ICONS[marketplace.type as keyof typeof MARKETPLACE_ICONS];
                const colorClass = MARKETPLACE_COLORS[marketplace.type as keyof typeof MARKETPLACE_COLORS];
                const stats = forumStats[marketplace.type as keyof typeof forumStats];
                
                return (
                  <Card key={marketplace.type} className="group hover:shadow-lg transition-all duration-300 border-border/50">
                    <CardHeader className="pb-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className={`p-3 rounded-xl bg-gradient-to-br ${colorClass} text-white`}>
                          <Icon className="h-6 w-6" />
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          r/{marketplace.type}
                        </Badge>
                      </div>
                      <CardTitle className="text-xl group-hover:text-primary transition-colors">
                        {marketplace.name}
                      </CardTitle>
                      <CardDescription className="text-sm">
                        {marketplace.description}
                      </CardDescription>
                    </CardHeader>
                    
                    <CardContent className="pt-0">
                      {/* Forum Stats */}
                      <div className="grid grid-cols-2 gap-4 mb-6">
                        <div className="text-center p-3 bg-muted/30 rounded-lg">
                          <div className="flex items-center justify-center gap-1 mb-1">
                            <MessageSquare className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs text-muted-foreground">Posts</span>
                          </div>
                          <p className="text-sm font-semibold">{stats.posts}</p>
                        </div>
                        <div className="text-center p-3 bg-muted/30 rounded-lg">
                          <div className="flex items-center justify-center gap-1 mb-1">
                            <Users className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs text-muted-foreground">Members</span>
                          </div>
                          <p className="text-sm font-semibold">{stats.members}</p>
                        </div>
                      </div>
                      
                      {/* Action Buttons */}
                      <div className="space-y-2">
                        <Link href={`/marketplace/${marketplace.type}/forum`} className="block">
                          <Button className="w-full group-hover:bg-primary/90 transition-colors">
                            <MessageSquare className="mr-2 h-4 w-4" />
                            Join Discussion
                          </Button>
                        </Link>
                        <div className="flex gap-2">
                          <Link href={`/marketplace/${marketplace.type}`} className="flex-1">
                            <Button variant="outline" className="w-full text-xs">
                              Browse Market
                            </Button>
                          </Link>
                          <Link href={`/forum/submit?community=${marketplace.type}`} className="flex-1">
                            <Button variant="outline" className="w-full text-xs">
                              Create Post
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>
        </Tabs>

      </div>
    </CommunityLayout>
  );
}
