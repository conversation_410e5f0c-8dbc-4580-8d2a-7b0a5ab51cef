import { Suspense } from "react";
import CreateCommunityForm from "@/components/forum/CreateCommunityForm";
import { Skeleton } from "@repo/ui/components/skeleton";
import { CommunityLayout } from "@/components/layouts/CommunityLayout";

export default function CreateCommunityPage() {
  return (
    <CommunityLayout>
      <div className="container mx-auto py-6 px-4">
        <Suspense fallback={<CreateCommunitySkeleton />}>
          <CreateCommunityForm />
        </Suspense>
      </div>
    </CommunityLayout>
  );
}

function CreateCommunitySkeleton() {
  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <Skeleton className="h-8 w-64" />
      <div className="space-y-4">
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-10 w-32" />
      </div>
    </div>
  );
}
