"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { CommunityLayout } from "@/components/layouts/CommunityLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { But<PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Textarea } from "@repo/ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import RichTextEditor from "@/components/forum/RichTextEditor";
import { useAuth } from "@/hooks/useBetterAuth";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { toast } from "sonner";
import { 
  FileText, 
  Image as ImageIcon, 
  Link as LinkIcon,
  Upload,
  X,
  Hash,
} from "lucide-react";
import { Id } from "@repo/backend/convex/_generated/dataModel";

function SubmitPostPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user: currentUser } = useAuth();
  const [postType, setPostType] = useState<"text" | "image" | "link">("text");
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [contentPlainText, setContentPlainText] = useState("");
  const [linkUrl, setLinkUrl] = useState("");
  const [selectedGroup, setSelectedGroup] = useState<string>("no-specific-community");
  const [category, setCategory] = useState<string>("general");
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const groups = useQuery(api.forum.getForumGroups, {
    includePrivate: true, // Include private communities for authenticated users
  });

  // Debug: Log groups data
  useEffect(() => {
    if (groups) {
      console.log("Groups loaded:", groups.length, groups);
    }
  }, [groups]);

  // Set initial group from URL parameter
  useEffect(() => {
    const groupSlug = searchParams.get('group');
    if (groupSlug && groups) {
      // Find the group by slug
      const targetGroup = groups.find((group: any) => group.slug === groupSlug);
      console.log("Looking for group with slug:", groupSlug, "Found:", targetGroup);
      if (targetGroup) {
        setSelectedGroup(targetGroup._id);
      }
    }
  }, [searchParams, groups]);

  const createPost = useMutation(api.forum.createForumPost);
  const generateUploadUrl = useMutation(api.forum.generateUploadUrl);
  const seedTestCommunities = useMutation(api.forum.seedTestCommunities);

  const handleImageUpload = async () => {
    try {
      const uploadUrl = await generateUploadUrl();
      const fileInput = document.createElement("input");
      fileInput.type = "file";
      fileInput.accept = "image/*";
      fileInput.multiple = true;
      
      fileInput.onchange = async (e) => {
        const files = (e.target as HTMLInputElement).files;
        if (!files) return;
        
        const newImages = [];
        for (const file of Array.from(files)) {
          const response = await fetch(uploadUrl, {
            method: "POST",
            headers: { "Content-Type": file.type },
            body: file,
          });
          
          if (response.ok) {
            const { storageId } = await response.json();
            newImages.push(storageId);
          }
        }
        
        setUploadedImages([...uploadedImages, ...newImages]);
        toast.success(`${newImages.length} image(s) uploaded`);
      };
      
      fileInput.click();
    } catch (error) {
      toast.error("Failed to upload images");
    }
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const handleSubmit = async () => {
    if (!currentUser) {
      toast.error("Please sign in to post");
      return;
    }

    if (!title.trim()) {
      toast.error("Please provide a title");
      return;
    }

    if (postType === "text" && !content.trim()) {
      toast.error("Please provide content");
      return;
    }

    if (postType === "link" && !linkUrl.trim()) {
      toast.error("Please provide a link");
      return;
    }

    setIsSubmitting(true);

    try {
      const postContent = postType === "link" 
        ? `[${linkUrl}](${linkUrl})`
        : content;

      // Ensure groupId is properly handled
      const groupId = selectedGroup === "no-specific-community" || selectedGroup === "" 
        ? undefined 
        : selectedGroup as Id<"forumGroups">;

      console.log("Selected group:", selectedGroup, "Processed groupId:", groupId);

      const post = await createPost({
        groupId,
        title,
        content: postContent,
        contentPlainText: postType === "link" ? linkUrl : contentPlainText,
        images: postType === "image" && uploadedImages.length > 0 ? uploadedImages as Id<"_storage">[] : undefined,
        category: category as any,
        tags,
      });
      
      toast.success("Post created successfully!");
      router.push(`/forum/post/${post?._id}`);
    } catch (error: any) {
      toast.error(error.message || "Failed to create post");
      setIsSubmitting(false);
    }
  };

  if (!currentUser) {
    return (
      <CommunityLayout>
        <div className="container mx-auto py-8 px-4">
          <Card>
            <CardContent className="py-12 text-center">
              <p className="text-muted-foreground">
                Please sign in to create a post
              </p>
              <Button className="mt-4" onClick={() => router.push("/login")}>
                Sign In
              </Button>
            </CardContent>
          </Card>
        </div>
      </CommunityLayout>
    );
  }

  return (
    <CommunityLayout>
      <div className="container mx-auto py-6 px-4 max-w-4xl">
        <h1 className="text-2xl font-bold mb-6">
          {selectedGroup !== "no-specific-community" && groups ? (
            (() => {
              const selectedGroupData = groups.find((g: any) => g._id === selectedGroup);
              return selectedGroupData ? `Create a post in r/${selectedGroupData.slug}` : "Create a post";
            })()
          ) : (
            "Create a post"
          )}
        </h1>

        <div className="flex gap-6">
          {/* Main Form */}
          <div className="flex-1 space-y-4">
            {/* Community Selection */}
            <Card>
              <CardContent className="p-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Choose a community</label>
                  <Select value={selectedGroup} onValueChange={setSelectedGroup}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a community (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="no-specific-community">No specific community</SelectItem>
                      {groups === undefined ? (
                        <SelectItem value="loading" disabled>Loading communities...</SelectItem>
                      ) : groups === null ? (
                        <SelectItem value="error" disabled>Error loading communities</SelectItem>
                      ) : groups.length === 0 ? (
                        <SelectItem value="empty" disabled>No communities available</SelectItem>
                      ) : (
                        groups.map((group: any) => (
                          <SelectItem key={group._id} value={group._id}>
                            <span className="flex items-center gap-2">
                              {group.icon && <span>{group.icon}</span>}
                              r/{group.slug}
                            </span>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  
                  {/* Show helper when no communities exist */}
                  {groups && groups.length === 0 && currentUser && (
                    <div className="text-sm text-muted-foreground">
                      <div className="flex items-center gap-2 mt-2 p-3 bg-muted rounded-md">
                        <div className="flex-1">
                          <p className="font-medium">No communities found</p>
                          <p className="text-xs">Create some test communities to get started</p>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={async () => {
                            try {
                              await seedTestCommunities();
                              toast.success("Test communities created!");
                            } catch (error: any) {
                              toast.error(error.message || "Failed to create communities");
                            }
                          }}
                        >
                          Create Test Communities
                        </Button>
                      </div>
                    </div>
                  )}
                  
                  {/* Show selected community info */}
                  {selectedGroup !== "no-specific-community" && groups && (
                    <div className="text-sm text-muted-foreground">
                      {(() => {
                        const selectedGroupData = groups.find((g: any) => g._id === selectedGroup);
                        return selectedGroupData ? (
                          <div className="flex items-center gap-2 mt-2 p-2 bg-muted rounded-md">
                            <span>{selectedGroupData.icon}</span>
                            <span>Posting to <strong>r/{selectedGroupData.slug}</strong></span>
                            {selectedGroupData.memberCount && (
                              <span className="text-xs">({selectedGroupData.memberCount} members)</span>
                            )}
                          </div>
                        ) : null;
                      })()}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Post Type Tabs */}
            <Card>
              <CardContent className="p-0">
                <Tabs value={postType} onValueChange={(v) => setPostType(v as any)}>
                  <TabsList className="w-full rounded-none h-12">
                    <TabsTrigger value="text" className="flex-1 gap-2">
                      <FileText className="h-4 w-4" />
                      Post
                    </TabsTrigger>
                    <TabsTrigger value="image" className="flex-1 gap-2">
                      <ImageIcon className="h-4 w-4" />
                      Images
                    </TabsTrigger>
                    <TabsTrigger value="link" className="flex-1 gap-2">
                      <LinkIcon className="h-4 w-4" />
                      Link
                    </TabsTrigger>
                  </TabsList>

                  <div className="p-4 space-y-4">
                    {/* Title */}
                    <div>
                      <Input
                        placeholder="Title"
                        value={title}
                        onChange={(e) => setTitle(e.target.value)}
                        className="text-base"
                        maxLength={300}
                      />
                      <p className="text-xs text-muted-foreground mt-1 text-right">
                        {title.length}/300
                      </p>
                    </div>

                    <TabsContent value="text" className="mt-0 space-y-4">
                      <RichTextEditor
                        content={content}
                        onChange={(html, text) => {
                          setContent(html);
                          setContentPlainText(text);
                        }}
                        placeholder="Text (optional)"
                      />
                    </TabsContent>

                    <TabsContent value="image" className="mt-0 space-y-4">
                      <div 
                        className="border-2 border-dashed rounded-lg p-8 text-center cursor-pointer hover:border-primary transition-colors"
                        onClick={handleImageUpload}
                      >
                        <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                        <p className="text-sm text-muted-foreground">
                          Drag and drop images or click to upload
                        </p>
                      </div>

                      {uploadedImages.length > 0 && (
                        <div className="grid grid-cols-3 gap-4">
                          {uploadedImages.map((imageId, index) => (
                            <div key={index} className="relative">
                              <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
                                <ImageIcon className="h-8 w-8 text-muted-foreground" />
                              </div>
                              <button
                                onClick={() => setUploadedImages(uploadedImages.filter((_, i) => i !== index))}
                                className="absolute -top-2 -right-2 bg-destructive text-white rounded-full w-6 h-6 flex items-center justify-center"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            </div>
                          ))}
                        </div>
                      )}

                      <Textarea
                        placeholder="Add a caption (optional)"
                        value={content}
                        onChange={(e) => {
                          setContent(e.target.value);
                          setContentPlainText(e.target.value);
                        }}
                        rows={3}
                      />
                    </TabsContent>

                    <TabsContent value="link" className="mt-0 space-y-4">
                      <Input
                        type="url"
                        placeholder="URL"
                        value={linkUrl}
                        onChange={(e) => setLinkUrl(e.target.value)}
                      />
                    </TabsContent>

                    {/* Category */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Category</label>
                      <Select value={category} onValueChange={setCategory}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="general">General</SelectItem>
                          <SelectItem value="marketplace">Marketplace</SelectItem>
                          <SelectItem value="authentication">Authentication</SelectItem>
                          <SelectItem value="deals">Deals</SelectItem>
                          <SelectItem value="questions">Questions</SelectItem>
                          <SelectItem value="announcements">Announcements</SelectItem>
                          <SelectItem value="discussion">Discussion</SelectItem>
                          <SelectItem value="showcase">Showcase</SelectItem>
                          <SelectItem value="help">Help</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Tags */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">Tags</label>
                      <div className="flex gap-2 mb-2 flex-wrap">
                        {tags.map((tag) => (
                          <div
                            key={tag}
                            className="flex items-center gap-1 px-2 py-1 bg-secondary rounded-md text-sm"
                          >
                            <Hash className="h-3 w-3" />
                            {tag}
                            <button
                              onClick={() => setTags(tags.filter(t => t !== tag))}
                              className="ml-1 hover:text-destructive"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </div>
                        ))}
                      </div>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Add a tag"
                          value={tagInput}
                          onChange={(e) => setTagInput(e.target.value)}
                          onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), handleAddTag())}
                        />
                        <Button onClick={handleAddTag} variant="outline">
                          Add
                        </Button>
                      </div>
                    </div>
                  </div>
                </Tabs>
              </CardContent>
            </Card>

            {/* Submit Buttons */}
            <div className="flex justify-end gap-2">
              <Button 
                variant="outline" 
                onClick={() => router.back()}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button 
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? "Posting..." : "Post"}
              </Button>
            </div>
          </div>

          {/* Sidebar */}
          <div className="w-80">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Posting to MODA</CardTitle>
              </CardHeader>
              <CardContent className="text-sm space-y-2">
                <p>1. Remember the human</p>
                <p>2. Behave like you would in real life</p>
                <p>3. Look for the original source of content</p>
                <p>4. Search for duplicates before posting</p>
                <p>5. Read the community's rules</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </CommunityLayout>
  );
}

export default function SubmitPostPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SubmitPostPageContent />
    </Suspense>
  );
}
