import { OrderDetailsPage } from "@/components/orders/OrderDetailsPage";
import { Metadata } from "next";

interface OrderDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export const metadata: Metadata = {
  title: "Order Details | MODA",
  description: "View detailed information about your order and track its status.",
};

export default async function OrderDetail({ params }: OrderDetailPageProps) {
  const { id } = await params;
  return <OrderDetailsPage orderId={id} />;
}
