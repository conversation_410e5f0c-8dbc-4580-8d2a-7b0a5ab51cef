import ArcGalleryHero from "./arc-gallery-hero"

export default function Home() {
  // Using actual images from public folder
  const images = [
    "/bottle.png",
    "/card.png",
    "/item1.png",
    "/item2.png",
    "/item3.png",
    "/pants.png",
    "/purse.png",
    "/purse2.png",
    "/watch.png",
    "/watch2.png"
  ]

  return (
    <main className="relative min-h-screen bg-black">
      <ArcGalleryHero
        images={images}
        startAngle={20}
        endAngle={160}
        radiusLg={480}
        radiusMd={360}
        radiusSm={260}
        cardSizeLg={120}
        cardSizeMd={100}
        cardSizeSm={80}
        className="pt-16 pb-16 md:pt-20 md:pb-20 lg:pt-24 lg:pb-24"
      />
    </main>
  )
}
