"use client";

import { Suspense } from "react";
import dynamic from "next/dynamic";
import { useParams, useRouter } from "next/navigation";
import { Skeleton } from "@repo/ui/components/skeleton";
import { Button } from "@repo/ui/components/button";
import { CommunityLayout } from "@/components/layouts/CommunityLayout";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@repo/ui/components/tabs";
import { Users, Plus } from "lucide-react";

// Valid marketplace types
const VALID_MARKETPLACE_TYPES = [
  "moda-watch",
  "real-watch-buyers", 
  "moda-car",
  "moda-lifestyle",
  "moda-misc"
];

// Marketplace type to forum group mapping
const MARKETPLACE_TO_FORUM_MAP = {
  "moda-watch": "moda-watch",
  "real-watch-buyers": "real-watch-buyers",
  "moda-car": "moda-car", 
  "moda-lifestyle": "moda-lifestyle",
  "moda-misc": "moda-misc"
};

// Dynamic imports to avoid SSR issues
const RedditForumHome = dynamic(
  () => import("@/components/forum/RedditForumHome"),
  { 
    ssr: false,
    loading: () => <ForumSkeleton />
  }
);

export default function MarketplaceForumPage() {
  const params = useParams();
  const router = useRouter();
  const marketplaceType = params?.type as string;

  // Validate marketplace type
  if (!VALID_MARKETPLACE_TYPES.includes(marketplaceType)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-8">
          <h1 className="text-3xl font-bold mb-4">Forum Not Found</h1>
          <p className="text-zinc-400 mb-8">
            The forum for marketplace type "{marketplaceType}" does not exist.
          </p>
          <Button
            onClick={() => router.push("/dashboard")}
            className="bg-white text-black hover:bg-zinc-200 font-medium"
          >
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  const forumSlug = MARKETPLACE_TO_FORUM_MAP[marketplaceType as keyof typeof MARKETPLACE_TO_FORUM_MAP];

  return (
    <CommunityLayout marketplaceType={marketplaceType as any}>
      <div className="container mx-auto py-6 px-4">
        <Tabs defaultValue="reddit" className="w-full">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold">
                {MARKETPLACE_TO_FORUM_MAP[marketplaceType as keyof typeof MARKETPLACE_TO_FORUM_MAP]
                  .replace(/-/g, " ")
                  .replace(/\b\w/g, (char) => char.toUpperCase())
                } Community
              </h1>
              <p className="text-sm text-muted-foreground mt-1">
                Connect, share, and learn with fellow luxury enthusiasts
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={() => window.location.href = "/forum/groups"}>
                <Users className="mr-2 h-4 w-4" />
                Browse Communities
              </Button>
              <Button onClick={() => window.location.href = `/forum/submit?community=${forumSlug}`}>
                <Plus className="mr-2 h-4 w-4" />
                Create Post
              </Button>
            </div>
          </div>

          <TabsContent value="reddit" className="mt-0">
            <RedditForumHome forumSlug={forumSlug} />
          </TabsContent>
        </Tabs>
      </div>
    </CommunityLayout>
  );
}

function ForumSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex gap-4 mb-6">
        <Skeleton className="h-10 flex-1" />
        <Skeleton className="h-10 w-32" />
      </div>
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} className="h-24 rounded-lg" />
        ))}
      </div>
    </div>
  );
}
