import { Suspense } from "react";
import MemberDirectory from "@/components/members/MemberDirectory";
import { Skeleton } from "@repo/ui/components/skeleton";
import { CommunityLayout } from "@/components/layouts/CommunityLayout";

export default function MembersDirectoryPage() {
  return (
    <CommunityLayout>
      <div className="container mx-auto py-6 px-4">
        <div className="mb-6">
          <h1 className="text-2xl sm:text-3xl font-bold">Member Directory</h1>
          <p className="text-muted-foreground mt-2 text-sm sm:text-base">
            Connect with our community of luxury marketplace members
          </p>
        </div>
        
        <Suspense fallback={<DirectorySkeleton />}>
          <MemberDirectory />
        </Suspense>
      </div>
    </CommunityLayout>
  );
}

function DirectorySkeleton() {
  return (
    <div className="space-y-6">
      {/* Stats Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Skeleton key={i} className="h-20 rounded-lg" />
        ))}
      </div>
      
      {/* Table Skeleton */}
      <div className="space-y-4">
        <Skeleton className="h-16 w-full rounded-lg" /> {/* Filters */}
        <Skeleton className="h-96 w-full rounded-lg" /> {/* Table */}
        <Skeleton className="h-12 w-full rounded-lg" /> {/* Pagination */}
      </div>
    </div>
  );
}
