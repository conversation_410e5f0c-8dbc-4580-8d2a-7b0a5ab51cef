import { Suspense } from "react";
import { notFound } from "next/navigation";
import { getToken } from "@convex-dev/better-auth/nextjs";
import { auth } from "@repo/backend/better-auth/server";
import { api } from "@repo/backend/convex/_generated/api";
import { fetchQuery } from "convex/nextjs";
import MemberProfilePage from "@/components/members/MemberProfilePage";
import { Skeleton } from "@repo/ui/components/skeleton";
import { CommunityLayout } from "@/components/layouts/CommunityLayout";

interface MemberPageProps {
  params: Promise<{ id: string }>;
}

export default async function MemberPage({ params }: MemberPageProps) {
  const { id } = await params;
  const token = await getToken(auth);

  // Decode the ID in case it's URL encoded
  const decodedId = decodeURIComponent(id);

  try {
    const memberData = await fetchQuery(
      api.members.getMemberProfile,
      { userId: decodedId as any },
      { token }
    );

    if (!memberData) {
      notFound();
    }

    return (
      <CommunityLayout>
        <Suspense fallback={<MemberProfileSkeleton />}>
          <MemberProfilePage memberData={memberData} />
        </Suspense>
      </CommunityLayout>
    );
  } catch (error) {
    console.error("Failed to fetch member profile:", error);
    notFound();
  }
}

function MemberProfileSkeleton() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-1">
          <Skeleton className="h-64 w-full rounded-lg" />
        </div>
        <div className="lg:col-span-2">
          <Skeleton className="h-32 w-full rounded-lg mb-4" />
          <Skeleton className="h-48 w-full rounded-lg" />
        </div>
      </div>
    </div>
  );
}
