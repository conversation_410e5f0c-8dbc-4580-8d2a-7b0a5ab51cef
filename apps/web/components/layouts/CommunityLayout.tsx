"use client";

import { ReactNode } from "react";
import { MarketplaceHeader } from "@/components/marketplace/MarketplaceHeader";

interface CommunityLayoutProps {
  children: ReactNode;
  showHeader?: boolean;
  marketplaceType?: "moda-watch" | "real-watch-buyers" | "moda-car" | "moda-lifestyle" | "moda-misc";
}

export function CommunityLayout({ children, showHeader = true, marketplaceType }: CommunityLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      {showHeader && <MarketplaceHeader marketplaceType={marketplaceType} />}
      <main className="flex-1">
        {children}
      </main>
    </div>
  );
}
