"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { But<PERSON> } from "@repo/ui/components/button";
import { Textarea } from "@repo/ui/components/textarea";
import { Badge } from "@repo/ui/components/badge";
import { Card, CardContent } from "@repo/ui/components/card";
import { ProfileImage } from "@/components/common/ProfileImage";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import { format, formatDistanceToNow } from "date-fns";
import {
  ArrowUp,
  ArrowDown,
  MessageSquare,
  Share,
  Bookmark,
  Award,
  MoreHorizontal,
  Reply as ReplyIcon,
  ArrowLeft,
  Pin,
  Lock,
  Eye,
  ExternalLink,
  Plus,
  Users,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import RedditStyleComment from "./RedditStyleComment";

interface RedditStylePostDetailProps {
  postId: string;
}

export default function RedditStylePostDetail({ postId }: RedditStylePostDetailProps) {
  const router = useRouter();
  const { user: currentUser } = useAuth();
  const [commentContent, setCommentContent] = useState("");
  const [showCommentForm, setShowCommentForm] = useState(false);
  const [cursor, setCursor] = useState<string | null>(null);

  const post = useQuery(api.forum.getForumPost, {
    postId: postId as any,
  });

  const comments = useQuery(api.forum.getForumComments, {
    postId: postId as any,
    paginationOpts: { numItems: 50, cursor },
  });

  const addComment = useMutation(api.forum.addForumComment);
  const incrementViews = useMutation(api.forum.incrementPostViews);

  // Fetch real community data like RedditForumHome
  const communityStats = useQuery(api.members.getCommunityStats, {});

  // Increment view count when post is viewed
  useEffect(() => {
    if (post && postId) {
      incrementViews({ postId: postId as any }).catch(console.error);
    }
  }, [postId]);

  const handleAddComment = async () => {
    if (!currentUser) {
      toast.error("Please sign in to comment");
      return;
    }

    if (!commentContent.trim()) {
      toast.error("Please write a comment");
      return;
    }

    try {
      await addComment({
        postId: postId as any,
        content: commentContent,
      });
      setCommentContent("");
      setShowCommentForm(false);
      toast.success("Comment added!");
    } catch (error: any) {
      toast.error(error.message || "Failed to add comment");
    }
  };

  const formatTimeAgo = (timestamp: number) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
  };

  if (!post) {
    return (
      <div className="flex gap-6">
        <div className="flex-1">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Post not found</p>
              <Button className="mt-4" onClick={() => router.push("/forum")}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Forum
              </Button>
            </div>
          </div>
        </div>
        <div className="w-80" /> {/* Empty sidebar space */}
      </div>
    );
  }

  return (
    <div className="flex gap-6">
      {/* Main Content */}
      <div className="flex-1">
        {/* Breadcrumb */}
        <div className="flex items-center gap-2 mb-4 text-sm">
          <Link href="/forum" className="text-blue-500 hover:underline">
            Back to Forum
          </Link>
          <span className="text-muted-foreground">•</span>
          <span className="text-muted-foreground">Posted by</span>
          <Link 
            href={`/members/${post.userId}`}
            className="text-blue-500 hover:underline"
          >
            {post.memberProfile?.displayName || post.user?.name}
          </Link>
          <span className="text-muted-foreground">{formatTimeAgo(post.updatedAt)}</span>
        </div>

        {/* Post Content Card */}
        <Card className="hover:shadow-md transition-shadow">
          <CardContent className="pt-4">
            <div className="flex items-start gap-3">
              {/* Voting Column */}
              <div className="flex flex-col items-center gap-1 pt-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 hover:bg-orange-100 hover:text-orange-600"
                  disabled={!currentUser}
                >
                  <ArrowUp className="w-4 h-4" />
                </Button>
                <span className="text-sm font-medium">{post.score || post.likes || 0}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
                  disabled={!currentUser}
                >
                  <ArrowDown className="w-4 h-4" />
                </Button>
              </div>

              {/* Main Content */}
              <div className="flex-1 space-y-3">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Link href="/forum" className="font-medium hover:text-primary">
                    r/MODA
                  </Link>
                  <span>•</span>
                  <span>Posted by</span>
                  <Link 
                    href={`/members/${post.userId}`}
                    className="font-medium hover:text-primary"
                  >
                  {post.memberProfile?.displayName || post.user?.name}
                  </Link>
                  <span>•</span>
                  <span>{formatTimeAgo(post.updatedAt)}</span>
                  {post.memberProfile?.flair && (
                    <>
                      <span>•</span>
                      <Badge variant="secondary" className="text-xs">
                        {post.memberProfile.flair.text}
                      </Badge>
                    </>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  {post.isPinned && <Pin className="h-4 w-4 text-green-600" />}
                  {post.isLocked && <Lock className="h-4 w-4 text-yellow-600" />}
                  <h1 className="text-2xl font-bold leading-tight">{post.title}</h1>
                </div>
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>Save</DropdownMenuItem>
                  <DropdownMenuItem>Hide</DropdownMenuItem>
                  <DropdownMenuItem>Report</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Post Content */}
            <div className="mt-4 ml-12">
              <div className="prose prose-sm max-w-none mb-4">
                <div 
                  className="text-foreground leading-relaxed whitespace-pre-wrap"
                  dangerouslySetInnerHTML={{ __html: post.content }}
                />
              </div>

              {/* Post Images */}
              {post.imageUrls && post.imageUrls.length > 0 && (
                <div className="mb-4 space-y-2">
                  {post.imageUrls.map((url: string | null, index: number) => 
                    url && (
                      <img
                        key={index}
                        src={url}
                        alt={`Post image ${index + 1}`}
                        className="max-w-full h-auto rounded border"
                      />
                    )
                  )}
                </div>
              )}

              {/* Post Tags */}
              {post.tags && post.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-4">
                  {post.tags.map((tag: string) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>
              )}

              {/* Post Actions */}
              <div className="flex items-center gap-4 pt-2">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="gap-2 text-muted-foreground hover:text-foreground"
                  onClick={() => setShowCommentForm(!showCommentForm)}
                >
                  <MessageSquare className="w-4 h-4" />
                  {post.commentCount} Comments
                </Button>
                <Button variant="ghost" size="sm" className="gap-2 text-muted-foreground hover:text-foreground">
                  <Share className="w-4 h-4" />
                  Share
                </Button>
                <Button variant="ghost" size="sm" className="gap-2 text-muted-foreground hover:text-foreground">
                  <Bookmark className="w-4 h-4" />
                  Save
                </Button>
                <Button variant="ghost" size="sm" className="gap-2 text-muted-foreground hover:text-foreground">
                  <Award className="w-4 h-4" />
                  Give Award
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Comment Form */}
        {showCommentForm && (
          <Card className="mt-4">
            <CardContent className="pt-4">
              {currentUser ? (
                <div className="flex items-start gap-3">
                  <ProfileImage
                    storageId={currentUser?.profileImage}
                    name={currentUser?.name}
                    size="sm"
                  />
                  <div className="flex-1 space-y-3">
                    <div className="text-sm text-muted-foreground">
                      Comment as{" "}
                      <span className="text-primary font-medium">
                        {currentUser?.name}
                      </span>
                    </div>
                    <Textarea
                      placeholder="What are your thoughts?"
                      value={commentContent}
                      onChange={(e) => setCommentContent(e.target.value)}
                      rows={3}
                    />
                    <div className="flex gap-2">
                      <Button 
                        onClick={handleAddComment}
                        disabled={!commentContent.trim()}
                      >
                        Comment
                      </Button>
                      <Button 
                        variant="outline"
                        onClick={() => setShowCommentForm(false)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-muted-foreground">
                    <Button variant="link" className="p-0" onClick={() => router.push("/login")}>
                      Sign in
                    </Button>{" "}
                    to join the discussion
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Comments Section */}
        <div className="mt-6">
          <div className="border-t pt-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">
                Comments ({comments?.page?.length || 0})
              </h3>
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Sort by:</span>
                <Button variant="ghost" size="sm" className="text-sm font-medium">
                  Best
                </Button>
              </div>
            </div>

            <div className="space-y-4">
              {comments?.page && comments.page.length > 0 ? (
                comments.page.map((comment: any) => (
                  <RedditStyleComment
                    key={comment._id}
                    comment={comment}
                    postAuthorId={post.userId}
                    level={0}
                  />
                ))
              ) : (
                <Card>
                  <CardContent className="pt-8 pb-8">
                    <div className="text-center text-muted-foreground">
                      <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No comments yet</p>
                      <p className="text-xs">Be the first to share what you think!</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Load More Comments */}
            {comments && !comments.isDone && (
              <div className="mt-6 text-center">
                <Button
                  variant="outline"
                  onClick={() => setCursor(comments.continueCursor)}
                >
                  Load more comments
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Sidebar - Same as RedditForumHome */}
      <div className="w-80 space-y-4">
        {/* Create Post Button */}
        {currentUser && (
          <div className="space-y-2">
            <Button 
              className="w-full" 
              onClick={() => router.push("/forum/submit")}
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Post
            </Button>
            {/* Create Community Button - Admin Only */}
            {currentUser.userType === "admin" && (
              <Button 
                variant="outline"
                className="w-full" 
                onClick={() => router.push("/forum/create-community")}
              >
                <Users className="mr-2 h-4 w-4" />
                Create Community
              </Button>
            )}
          </div>
        )}

        {/* About Community */}
        <Card className="p-4">
          <h3 className="font-semibold mb-2">About Community</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Welcome to MODA's community forum. Share knowledge, ask questions, 
            and connect with fellow luxury enthusiasts.
          </p>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Created</span>
              <span>{post ? format(new Date(post.updatedAt), "MMM d, yyyy") : "N/A"}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Members</span>
              <span className="font-medium">
                {communityStats?.totalMembers?.toLocaleString() || "0"}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Online</span>
              <span className="font-medium">
                {communityStats?.onlineMembers?.toLocaleString() || "0"}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Posts</span>
              <span className="font-medium">
                {communityStats?.totalPosts?.toLocaleString() || "0"}
              </span>
            </div>
          </div>
        </Card>

        {/* Community Rules */}
        <Card className="p-4">
          <h3 className="font-semibold mb-3">Community Rules</h3>
          <ol className="space-y-2 text-sm">
            <li>1. Be respectful and civil</li>
            <li>2. No spam or self-promotion</li>
            <li>3. Keep content relevant</li>
            <li>4. No personal information</li>
            <li>5. Follow authentication guidelines</li>
          </ol>
        </Card>
      </div>
    </div>
  );
}
