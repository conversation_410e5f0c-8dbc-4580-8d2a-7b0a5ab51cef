"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Textarea } from "@repo/ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Badge } from "@repo/ui/components/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import { ProfileImage } from "@/components/common/ProfileImage";
import RichTextEditor from "./RichTextEditor";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import { format } from "date-fns";
import {
  MessageSquare,
  ThumbsUp,
  Eye,
  Pin,
  Lock,
  TrendingUp,
  Plus,
  Search,
  Users,
  ChevronRight,
  Image as ImageIcon,
  Hash,
  Globe,
  Shield,
  Star,
  ArrowUp,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@repo/ui/components/dialog";

export default function EnhancedForumHome() {
  const router = useRouter();
  const { user: currentUser } = useAuth();
  const [cursor, setCursor] = useState<string | null>(null);
  const [selectedGroup, setSelectedGroup] = useState<string | undefined>(undefined);
  const [category, setCategory] = useState<string | undefined>(undefined);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<"recent" | "popular" | "mostViewed" | "mostCommented">("recent");
  const [isCreatePostOpen, setIsCreatePostOpen] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);

  // New post form state
  const [newPostTitle, setNewPostTitle] = useState("");
  const [newPostContent, setNewPostContent] = useState("");
  const [newPostPlainText, setNewPostPlainText] = useState("");
  const [newPostCategory, setNewPostCategory] = useState<string>("general");
  const [newPostTags, setNewPostTags] = useState("");

  // Fetch forum groups
  const groups = useQuery(api.forum.getForumGroups, {
    includePrivate: false,
  });

  // Fetch forum posts
  const posts = useQuery(api.forum.getForumPosts, {
    groupId: selectedGroup as any,
    category: category as any,
    sortBy,
    paginationOpts: { numItems: 10, cursor },
  });

  const createPost = useMutation(api.forum.createForumPost);
  const generateUploadUrl = useMutation(api.forum.generateUploadUrl);
  const joinGroup = useMutation(api.forum.joinForumGroup);

  const handleCreatePost = async () => {
    if (!currentUser) {
      toast.error("Please sign in to create a post");
      return;
    }

    if (!newPostTitle.trim() || !newPostContent.trim()) {
      toast.error("Please provide both title and content");
      return;
    }

    try {
      const post = await createPost({
        groupId: selectedGroup as any,
        title: newPostTitle,
        content: newPostContent,
        contentPlainText: newPostPlainText,
        images: uploadedImages as any[],
        category: newPostCategory as any,
        tags: newPostTags.split(",").map(tag => tag.trim()).filter(Boolean),
      });
      
      toast.success("Post created successfully!");
      setIsCreatePostOpen(false);
      setNewPostTitle("");
      setNewPostContent("");
      setNewPostPlainText("");
      setNewPostCategory("general");
      setNewPostTags("");
      setUploadedImages([]);
      
      // Navigate to the new post
      router.push(`/forum/post/${post?._id}`);
    } catch (error: any) {
      toast.error(error.message || "Failed to create post");
    }
  };

  const handleImageUpload = async () => {
    try {
      const uploadUrl = await generateUploadUrl();
      const fileInput = document.createElement("input");
      fileInput.type = "file";
      fileInput.accept = "image/*";
      fileInput.multiple = true;
      
      fileInput.onchange = async (e) => {
        const files = (e.target as HTMLInputElement).files;
        if (!files) return;
        
        const newImages = [];
        for (const file of Array.from(files)) {
          const response = await fetch(uploadUrl, {
            method: "POST",
            headers: { "Content-Type": file.type },
            body: file,
          });
          
          if (response.ok) {
            const { storageId } = await response.json();
            newImages.push(storageId);
          }
        }
        
        setUploadedImages([...uploadedImages, ...newImages]);
        toast.success(`${newImages.length} image(s) uploaded`);
      };
      
      fileInput.click();
    } catch (error) {
      toast.error("Failed to upload images");
    }
  };

  const handleJoinGroup = async (groupId: string) => {
    if (!currentUser) {
      toast.error("Please sign in to join groups");
      return;
    }

    try {
      await joinGroup({ groupId: groupId as any });
      toast.success("Successfully joined the group!");
    } catch (error: any) {
      toast.error(error.message || "Failed to join group");
    }
  };

  const getCategoryIcon = (category: string) => {
    const icons: Record<string, string> = {
      general: "💬",
      marketplace: "🛍️",
      authentication: "✅",
      deals: "💰",
      questions: "❓",
      announcements: "📢",
      discussion: "🗣️",
      showcase: "🎨",
      help: "🆘",
    };
    return icons[category] || "💬";
  };

  return (
    <div className="space-y-6">
      {/* Forum Groups Section */}
      {groups && groups.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <Users className="h-5 w-5" />
            Forum Groups
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {groups.slice(0, 8).map((group: any) => (
              <Card 
                key={group._id}
                className={`hover:shadow-lg transition-all cursor-pointer ${
                  selectedGroup === group._id ? "ring-2 ring-primary" : ""
                }`}
                onClick={() => setSelectedGroup(
                  selectedGroup === group._id ? undefined : group._id
                )}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {group.icon && (
                        <span className="text-2xl">{group.icon}</span>
                      )}
                      <div>
                        <h3 className="font-semibold">{group.name}</h3>
                        <p className="text-xs text-muted-foreground">
                          {group.memberCount} members
                        </p>
                      </div>
                    </div>
                    {group.isPrivate && (
                      <Shield className="h-4 w-4 text-muted-foreground" />
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                    {group.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">
                      {group.postCount} posts
                    </span>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleJoinGroup(group._id);
                      }}
                    >
                      Join
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="mt-4 text-center">
            <Link href="/forum/groups">
              <Button variant="outline">
                View All Groups
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      )}

      {/* Top Actions Bar */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search forum posts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={category || "all"} onValueChange={(v) => setCategory(v === "all" ? undefined : v)}>
          <SelectTrigger className="w-full md:w-48">
            <SelectValue placeholder="All Categories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="general">General</SelectItem>
            <SelectItem value="marketplace">Marketplace</SelectItem>
            <SelectItem value="authentication">Authentication</SelectItem>
            <SelectItem value="deals">Deals</SelectItem>
            <SelectItem value="questions">Questions</SelectItem>
            <SelectItem value="announcements">Announcements</SelectItem>
            <SelectItem value="discussion">Discussion</SelectItem>
            <SelectItem value="showcase">Showcase</SelectItem>
            <SelectItem value="help">Help</SelectItem>
          </SelectContent>
        </Select>
        <Select value={sortBy} onValueChange={(v) => setSortBy(v as any)}>
          <SelectTrigger className="w-full md:w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="recent">Most Recent</SelectItem>
            <SelectItem value="popular">Most Popular</SelectItem>
            <SelectItem value="mostViewed">Most Viewed</SelectItem>
            <SelectItem value="mostCommented">Most Commented</SelectItem>
          </SelectContent>
        </Select>
        
        {currentUser && (
          <Dialog open={isCreatePostOpen} onOpenChange={setIsCreatePostOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Post
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Forum Post</DialogTitle>
                <DialogDescription>
                  Share your thoughts, ask questions, or start a discussion
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 mt-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Title</label>
                  <Input
                    placeholder="What's on your mind?"
                    value={newPostTitle}
                    onChange={(e) => setNewPostTitle(e.target.value)}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Category</label>
                    <Select value={newPostCategory} onValueChange={setNewPostCategory}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="general">General</SelectItem>
                        <SelectItem value="marketplace">Marketplace</SelectItem>
                        <SelectItem value="authentication">Authentication</SelectItem>
                        <SelectItem value="deals">Deals</SelectItem>
                        <SelectItem value="questions">Questions</SelectItem>
                        <SelectItem value="announcements">Announcements</SelectItem>
                        <SelectItem value="discussion">Discussion</SelectItem>
                        <SelectItem value="showcase">Showcase</SelectItem>
                        <SelectItem value="help">Help</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  {selectedGroup && groups && (
                    <div>
                      <label className="text-sm font-medium mb-2 block">Posting to Group</label>
                      <div className="flex items-center gap-2 p-2 border rounded-md bg-muted/30">
                        {groups.find((g: any) => g._id === selectedGroup)?.icon}
                        <span className="text-sm font-medium">
                          {groups.find((g: any) => g._id === selectedGroup)?.name}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Content</label>
                  <RichTextEditor
                    content={newPostContent}
                    onChange={(html, text) => {
                      setNewPostContent(html);
                      setNewPostPlainText(text);
                    }}
                    onImageUpload={handleImageUpload}
                  />
                </div>
                {uploadedImages.length > 0 && (
                  <div>
                    <label className="text-sm font-medium mb-2 block">Uploaded Images</label>
                    <div className="flex gap-2 flex-wrap">
                      {uploadedImages.map((imageId, index) => (
                        <div key={index} className="relative">
                          <div className="w-20 h-20 bg-muted rounded-lg flex items-center justify-center">
                            <ImageIcon className="h-8 w-8 text-muted-foreground" />
                          </div>
                          <button
                            onClick={() => setUploadedImages(uploadedImages.filter((_, i) => i !== index))}
                            className="absolute -top-2 -right-2 bg-destructive text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Tags (comma-separated, optional)
                  </label>
                  <Input
                    placeholder="e.g., sneakers, authentication, help"
                    value={newPostTags}
                    onChange={(e) => setNewPostTags(e.target.value)}
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsCreatePostOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreatePost}>
                    Create Post
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Posts List */}
      <div className="space-y-4">
        {posts?.page && posts.page.length > 0 ? (
          posts.page
            .filter(post => !searchTerm || 
              post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
              post.contentPlainText?.toLowerCase().includes(searchTerm.toLowerCase())
            )
            .map((post: any) => (
              <Card key={post._id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <Link href={`/forum/post/${post._id}`}>
                    <div className="flex items-start gap-4">
                      <ProfileImage
                        storageId={post.user?.profileImage}
                        name={post.user?.name}
                        size="md"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              {post.isStickied === true && (
                                <ArrowUp className="h-4 w-4 text-green-500" />
                              )}
                              {post.isPinned && (
                                <Pin className="h-4 w-4 text-yellow-500" />
                              )}
                              {post.isLocked && (
                                <Lock className="h-4 w-4 text-red-500" />
                              )}
                              <h3 className="font-semibold text-lg hover:underline">
                                {post.title}
                              </h3>
                            </div>
                            <div className="flex items-center gap-3 text-sm text-muted-foreground mb-2">
                              <span>{post.memberProfile?.displayName || post.user?.name}</span>
                              <span>•</span>
                              <span>{format(new Date(post.lastActivityAt), "MMM d, yyyy")}</span>
                              {post.group && (
                                <>
                                  <span>•</span>
                                  <Badge variant="outline" className="text-xs">
                                    {post.group.icon} {post.group.name}
                                  </Badge>
                                </>
                              )}
                              <span>•</span>
                              <Badge variant="outline" className="text-xs">
                                {getCategoryIcon(post.category)} {post.category}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        
                        <div 
                          className="text-muted-foreground line-clamp-3 mb-3 prose prose-sm max-w-none"
                          dangerouslySetInnerHTML={{ __html: post.content }}
                        />

                        {post.imageUrls && post.imageUrls.length > 0 && (
                          <div className="flex gap-2 mb-3">
                            {post.imageUrls.slice(0, 3).map((url: string, index: number) => (
                              <div key={index} className="relative">
                                <img
                                  src={url}
                                  alt=""
                                  className="h-20 w-20 object-cover rounded-lg"
                                />
                                {index === 2 && post.imageUrls.length > 3 && (
                                  <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center">
                                    <span className="text-white text-sm font-medium">
                                      +{post.imageUrls.length - 3}
                                    </span>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        )}

                        {post.tags && post.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mb-3">
                            {post.tags.map((tag: string) => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                <Hash className="h-3 w-3 mr-1" />
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        )}

                        <div className="flex items-center gap-6 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <MessageSquare className="h-4 w-4" />
                            <span>{post.commentCount} replies</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <ThumbsUp className="h-4 w-4" />
                            <span>{post.score || post.likes || 0} points</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Eye className="h-4 w-4" />
                            <span>{post.views} views</span>
                          </div>
                        </div>
                      </div>
                      <ChevronRight className="h-5 w-5 text-muted-foreground mt-2" />
                    </div>
                  </Link>
                </CardContent>
              </Card>
            ))
        ) : (
          <Card>
            <CardContent className="py-12 text-center">
              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {searchTerm
                  ? "No posts found matching your search"
                  : selectedGroup
                  ? "No posts in this group yet"
                  : "No posts yet. Be the first to start a discussion!"}
              </p>
              {currentUser && !searchTerm && (
                <Button className="mt-4" onClick={() => setIsCreatePostOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create First Post
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Load More */}
      {posts && !posts.isDone && (
        <div className="flex justify-center mt-6">
          <Button
            variant="outline"
            onClick={() => setCursor(posts.continueCursor)}
          >
            Load More Posts
          </Button>
        </div>
      )}
    </div>
  );
}
