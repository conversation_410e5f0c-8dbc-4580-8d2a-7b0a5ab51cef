"use client";

import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Skeleton } from "@repo/ui/components/skeleton";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { MessageCircle, ThumbsUp, Clock, Users, TrendingUp } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { useState } from "react";

interface MarketplaceForumHomeProps {
  marketplaceType: string;
  forumSlug: string;
}

export default function MarketplaceForumHome({ marketplaceType, forumSlug }: MarketplaceForumHomeProps) {
  const [sortBy, setSortBy] = useState<"hot" | "new" | "top">("hot");
  
  // Get the specific forum group for this marketplace type
  const forumGroup = useQuery(api.forum.getForumGroupBySlug, { slug: forumSlug });
  
  // Get posts for this specific forum group
  const posts = useQuery(api.forumPosts.getPostsByGroup, { 
    groupId: forumGroup?._id || null,
    sortBy: sortBy,
    limit: 20
  });

  // Get group member count and recent activity
  const groupStats = useQuery(api.forum.getForumGroupStats, { slug: forumSlug });

  if (forumGroup === undefined || posts === undefined) {
    return <ForumSkeleton />;
  }

  if (!forumGroup) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-semibold text-muted-foreground">
          Forum community not found
        </h3>
        <p className="text-sm text-muted-foreground mt-2">
          The forum for this marketplace type hasn't been set up yet.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Forum Group Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            <div className="text-3xl">{forumGroup.icon}</div>
            <div className="flex-1">
              <CardTitle className="flex items-center gap-2">
                {forumGroup.name}
                <Badge variant="secondary">{groupStats?.memberCount || 0} members</Badge>
              </CardTitle>
              <CardDescription>{forumGroup.description}</CardDescription>
            </div>
          </div>
        </CardHeader>
        {groupStats && (
          <CardContent>
            <div className="flex items-center gap-6 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                <span>{groupStats.memberCount} members</span>
              </div>
              <div className="flex items-center gap-1">
                <MessageCircle className="h-4 w-4" />
                <span>{groupStats.postCount} posts</span>
              </div>
              <div className="flex items-center gap-1">
                <TrendingUp className="h-4 w-4" />
                <span>{groupStats.activeToday || 0} active today</span>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Sort Controls */}
      <div className="flex items-center gap-2">
        <Button
          variant={sortBy === "hot" ? "default" : "outline"}
          size="sm"
          onClick={() => setSortBy("hot")}
        >
          🔥 Hot
        </Button>
        <Button
          variant={sortBy === "new" ? "default" : "outline"}
          size="sm"
          onClick={() => setSortBy("new")}
        >
          <Clock className="mr-1 h-3 w-3" />
          New
        </Button>
        <Button
          variant={sortBy === "top" ? "default" : "outline"}
          size="sm"
          onClick={() => setSortBy("top")}
        >
          <ThumbsUp className="mr-1 h-3 w-3" />
          Top
        </Button>
      </div>

      {/* Posts List */}
      <div className="space-y-4">
        {(!posts || posts.length === 0) ? (
          <Card>
            <CardContent className="text-center py-12">
              <MessageCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-muted-foreground">
                No posts yet
              </h3>
              <p className="text-sm text-muted-foreground mt-2">
                Be the first to start a conversation in this community!
              </p>
              <Button 
                className="mt-4"
                onClick={() => window.location.href = `/forum/submit?community=${forumSlug}`}
              >
                Create First Post
              </Button>
            </CardContent>
          </Card>
        ) : (
          posts.map((post: any) => (
            <PostCard key={post._id} post={post} />
          ))
        )}
      </div>
    </div>
  );
}

interface PostCardProps {
  post: {
    _id: string;
    _creationTime: number;
    title: string;
    content?: string;
    authorName: string;
    upvotes?: number;
    isPinned?: boolean;
    tags?: string[];
    commentCount?: number;
  };
}

function PostCard({ post }: PostCardProps) {
  const timeAgo = formatDistanceToNow(new Date(post._creationTime), { addSuffix: true });

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer">
      <CardContent className="p-4">
        <div className="flex gap-4">
          {/* Vote Section */}
          <div className="flex flex-col items-center gap-1 min-w-[40px]">
            <Button variant="ghost" size="sm" className="p-1 h-6 w-6">
              <ThumbsUp className="h-3 w-3" />
            </Button>
            <span className="text-sm font-medium">{post.upvotes || 0}</span>
            <Button variant="ghost" size="sm" className="p-1 h-6 w-6">
              <ThumbsUp className="h-3 w-3 rotate-180" />
            </Button>
          </div>

          {/* Post Content */}
          <div className="flex-1">
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
              <span>Posted by u/{post.authorName}</span>
              <span>•</span>
              <span>{timeAgo}</span>
              {post.isPinned && (
                <Badge variant="secondary" className="text-xs">
                  📌 Pinned
                </Badge>
              )}
            </div>
            
            <h3 className="font-semibold text-lg mb-2 line-clamp-2">
              {post.title}
            </h3>
            
            {post.content && (
              <p className="text-muted-foreground text-sm line-clamp-3 mb-3">
                {post.content}
              </p>
            )}

            {/* Post Tags */}
            {post.tags && post.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-3">
                {post.tags.slice(0, 3).map((tag: string) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}

            {/* Post Footer */}
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <MessageCircle className="h-4 w-4" />
                <span>{post.commentCount || 0} comments</span>
              </div>
              <Button variant="ghost" size="sm" className="p-0 h-auto">
                Share
              </Button>
              <Button variant="ghost" size="sm" className="p-0 h-auto">
                Save
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function ForumSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            <Skeleton className="h-12 w-12 rounded" />
            <div className="flex-1">
              <Skeleton className="h-6 w-48 mb-2" />
              <Skeleton className="h-4 w-96" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-6">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-20" />
          </div>
        </CardContent>
      </Card>

      {/* Sort Controls Skeleton */}
      <div className="flex items-center gap-2">
        <Skeleton className="h-8 w-16" />
        <Skeleton className="h-8 w-16" />
        <Skeleton className="h-8 w-16" />
      </div>

      {/* Posts Skeleton */}
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="flex gap-4">
                <div className="flex flex-col items-center gap-1 min-w-[40px]">
                  <Skeleton className="h-6 w-6" />
                  <Skeleton className="h-4 w-6" />
                  <Skeleton className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <Skeleton className="h-4 w-48 mb-2" />
                  <Skeleton className="h-6 w-full mb-2" />
                  <Skeleton className="h-4 w-3/4 mb-3" />
                  <div className="flex items-center gap-4">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-12" />
                    <Skeleton className="h-4 w-12" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
