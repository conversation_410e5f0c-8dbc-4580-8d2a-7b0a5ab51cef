"use client";

import { useState } from "react";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Button } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { useAuth } from "@/hooks/useBetterAuth";
import { ProfileImage } from "@/components/common/ProfileImage";
import { toast } from "sonner";
import {
  ArrowUp,
  ArrowDown,
  MessageSquare,
  Award,
  Share2,
  MoreHorizontal,
  Eye,
  Bookmark,
  Flag,
  Link as LinkIcon,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { cn } from "@repo/ui/lib/utils";
import { PostActions } from "./PostActions";

interface RedditStylePostProps {
  post: any;
  showContent?: boolean;
  onAwardClick?: () => void;
  viewMode?: "card" | "compact";
  onPostClick?: (post: any) => void;
}

export default function RedditStylePost({ 
  post, 
  showContent = false,
  onAwardClick,
  viewMode = "card",
  onPostClick
}: RedditStylePostProps) {
  const { user: currentUser } = useAuth();
  const [userVote, setUserVote] = useState<"upvote" | "downvote" | null>(null);
  const [score, setScore] = useState(post.score || 0);
  const [isSaved, setIsSaved] = useState(false);

  const voteOnPost = useMutation(api.redditForum.voteOnPost);

  const handleVote = async (voteType: "upvote" | "downvote") => {
    if (!currentUser) {
      toast.error("Please sign in to vote");
      return;
    }

    const newVoteType = userVote === voteType ? "none" : voteType;
    
    try {
      const result = await voteOnPost({
        postId: post._id,
        voteType: newVoteType as any,
      });
      
      setUserVote(result.userVote);
      setScore(result.score);
    } catch (error) {
      toast.error("Failed to vote");
    }
  };

  const handleShare = async () => {
    const url = `${window.location.origin}/forum/post/${post._id}`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: post.title,
          url: url,
        });
      } catch (err) {
        // User cancelled share
      }
    } else {
      // Fallback to copying link
      navigator.clipboard.writeText(url);
      toast.success("Link copied to clipboard");
    }
  };

  const formatScore = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M";
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + "k";
    }
    return num.toString();
  };

  if (viewMode === "compact") {
    return (
      <Card className="hover:shadow-md transition-shadow p-3">
        <div className="flex items-center gap-3">
          {/* Voting Section - Horizontal */}
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "h-6 w-6 p-0 hover:bg-green-100 hover:text-green-600",
                userVote === "upvote" && "text-green-600 bg-green-100"
              )}
              onClick={() => handleVote("upvote")}
              disabled={!currentUser}
            >
              <ArrowUp className="w-4 h-4" />
            </Button>
            <span className="text-sm font-medium min-w-[2rem] text-center">
              {formatScore(score)}
            </span>
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600",
                userVote === "downvote" && "text-red-600 bg-red-100"
              )}
              onClick={() => handleVote("downvote")}
              disabled={!currentUser}
            >
              <ArrowDown className="w-4 h-4" />
            </Button>
          </div>

          {/* Post Info */}
          <div className="flex-1">
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
              {post.group && (
                <>
                  <Link 
                    href={`/forum/groups/${post.group.slug}`}
                    className="font-medium hover:text-primary"
                  >
                    r/{post.group.slug}
                  </Link>
                  <span>•</span>
                </>
              )}
              <span>
                Posted by{" "}
                <Link 
                  href={`/members/${post.userId}`}
                  className="hover:text-primary font-medium"
                >
                  {post.memberProfile?.displayName || post.user?.name}
                </Link>
              </span>
              <span>•</span>
              <span>{formatDistanceToNow(new Date(post._creationTime))} ago</span>
            </div>
            
            <h3 
              className="text-base font-semibold hover:text-primary cursor-pointer"
              onClick={() => onPostClick?.(post)}
            >
              {post.title}
            </h3>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="gap-1 text-muted-foreground hover:text-foreground"
              onClick={() => onPostClick?.(post)}
            >
              <MessageSquare className="w-4 h-4" />
              {post.commentCount}
            </Button>
            <PostActions 
              post={{
                _id: post._id,
                title: post.title,
                content: post.content,
                userId: post.userId
              }}
              onUpdate={() => window.location.reload()}
              onDelete={() => window.location.href = '/forum'}
            />
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <div className="flex items-start gap-3 p-4">
        {/* Voting Section */}
        <div className="flex flex-col items-center gap-1 pt-1">
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-6 w-6 p-0 hover:bg-green-100 hover:text-green-600",
              userVote === "upvote" && "text-green-600 bg-green-100"
            )}
            onClick={() => handleVote("upvote")}
            disabled={!currentUser}
          >
            <ArrowUp className="w-4 h-4" />
          </Button>
          <span className="text-sm font-medium">
            {formatScore(score)}
          </span>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600",
              userVote === "downvote" && "text-red-600 bg-red-100"
            )}
            onClick={() => handleVote("downvote")}
            disabled={!currentUser}
          >
            <ArrowDown className="w-4 h-4" />
          </Button>
        </div>

        {/* Post Content */}
        <div className="flex-1 space-y-2">
          {/* Post Header */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {post.group && (
              <>
                <Link 
                  href={`/forum/groups/${post.group.slug}`}
                  className="font-medium hover:text-primary"
                >
                  r/{post.group.slug}
                </Link>
                <span>•</span>
              </>
            )}
            
            <span>
              Posted by{" "}
              <Button
                variant="link"
                className="p-0 h-auto text-sm font-medium hover:text-primary"
                onClick={() => {}}
              >
                {post.memberProfile?.displayName || post.user?.name}
              </Button>
            </span>
            <span>•</span>
            <span>{formatDistanceToNow(new Date(post._creationTime))} ago</span>
            
            {/* User badges */}
            {post.memberProfile?.isModerator && (
              <>
                <span>•</span>
                <Badge variant="secondary" className="text-xs">
                  MOD
                </Badge>
              </>
            )}
            {post.tags && post.tags.length > 0 && (
              <>
                <span>•</span>
                <Badge variant="secondary" className="text-xs">
                  {post.tags[0]}
                </Badge>
              </>
            )}
          </div>

          {/* Post Title */}
          <h3 
            className="text-lg font-semibold leading-tight hover:text-primary cursor-pointer"
            onClick={() => onPostClick?.(post)}
          >
            {post.title}
          </h3>

          {/* Post Preview (if showing content) */}
          {showContent && post.contentPlainText && (
            <p className="text-muted-foreground mb-4 leading-relaxed">
              {post.contentPlainText}
            </p>
          )}

          {/* Post Images Preview */}
          {post.imageUrls && post.imageUrls.length > 0 && (
            <div className="flex gap-2 mb-3">
              {post.imageUrls.slice(0, 3).map((url: string, index: number) => (
                <div key={index} className="relative">
                  <img
                    src={url}
                    alt=""
                    className="h-16 w-16 object-cover rounded"
                  />
                  {index === 2 && post.imageUrls.length > 3 && (
                    <div className="absolute inset-0 bg-black/60 rounded flex items-center justify-center">
                      <span className="text-white text-xs font-medium">
                        +{post.imageUrls.length - 3}
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Post Actions */}
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              className="gap-2 text-muted-foreground hover:text-foreground"
              onClick={() => onPostClick?.(post)}
            >
              <MessageSquare className="w-4 h-4" />
              {post.commentCount} Comments
            </Button>
            
            <Button 
              variant="ghost" 
              size="sm" 
              className="gap-2 text-muted-foreground hover:text-foreground"
              onClick={handleShare}
            >
              <Share2 className="w-4 h-4" />
              Share
            </Button>
          
          </div>
        </div>

        {/* More Options */}
        <PostActions 
          post={{
            _id: post._id,
            title: post.title,
            content: post.content,
            userId: post.userId
          }}
          onUpdate={() => window.location.reload()}
          onDelete={() => window.location.href = '/forum'}
        />
      </div>
    </Card>
  );
}
