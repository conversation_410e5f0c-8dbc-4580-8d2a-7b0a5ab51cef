"use client";

import { useState } from "react";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Textarea } from "@repo/ui/components/textarea";
import { useAuth } from "@/hooks/useBetterAuth";
import { ProfileImage } from "@/components/common/ProfileImage";
import { toast } from "sonner";
import {
  ArrowUp,
  ArrowDown,
  MessageSquare,
  Award,
  Share2,
  MoreHorizontal,
  Flag,
  Edit,
  Trash,
  ChevronDown,
  ChevronUp,
  Pin,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { cn } from "@repo/ui/lib/utils";
import { CommentActions } from "./CommentActions";
import { EditCommentForm } from "./EditCommentForm";

interface RedditStyleCommentProps {
  comment: any;
  postAuthorId?: string;
  level?: number;
  onReply?: (commentId: string) => void;
  onAwardClick?: () => void;
}

export default function RedditStyleComment({ 
  comment, 
  postAuthorId,
  level = 0,
  onReply,
  onAwardClick 
}: RedditStyleCommentProps) {
  const { user: currentUser } = useAuth();
  const [userVote, setUserVote] = useState<"upvote" | "downvote" | null>(null);
  const [score, setScore] = useState(comment.score || 0);
  const [isCollapsed, setIsCollapsed] = useState(comment.isCollapsed || false);
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [replyContent, setReplyContent] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(comment.content);

  const voteOnComment = useMutation(api.redditForum.voteOnComment);
  const toggleCollapse = useMutation(api.redditForum.toggleCommentCollapse);
  const addComment = useMutation(api.forum.addForumComment);

  const handleVote = async (voteType: "upvote" | "downvote") => {
    if (!currentUser) {
      toast.error("Please sign in to vote");
      return;
    }

    const newVoteType = userVote === voteType ? "none" : voteType;
    
    try {
      const result = await voteOnComment({
        commentId: comment._id,
        voteType: newVoteType as any,
      });
      
      setUserVote(result.userVote);
      setScore(result.score);
    } catch (error) {
      toast.error("Failed to vote");
    }
  };

  const handleReply = async () => {
    if (!currentUser) {
      toast.error("Please sign in to reply");
      return;
    }

    if (!replyContent.trim()) {
      toast.error("Reply cannot be empty");
      return;
    }

    try {
      await addComment({
        postId: comment.postId,
        content: replyContent,
        parentCommentId: comment._id,
      });
      
      setReplyContent("");
      setShowReplyForm(false);
      toast.success("Reply posted!");
      // Refresh comments would happen here
    } catch (error) {
      toast.error("Failed to post reply");
    }
  };

  const handleToggleCollapse = async () => {
    try {
      const result = await toggleCollapse({
        commentId: comment._id,
      });
      setIsCollapsed(result.isCollapsed);
    } catch (error) {
      // Just toggle locally on error
      setIsCollapsed(!isCollapsed);
    }
  };

  const formatScore = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + "k";
    }
    return num.toString();
  };

  // Calculate indent based on comment level
  const indentClass = level > 0 ? "ml-4 border-l-2 border-gray-200 dark:border-gray-700" : "";

  if (comment.isDeleted) {
    return (
      <div className={cn("py-2", indentClass)}>
        <div className="text-sm text-muted-foreground italic pl-2">
          [deleted]
        </div>
      </div>
    );
  }

  return (
    <div className={cn("", indentClass)}>
      <div className="pl-2">
        {/* Comment Header */}
        <div className="flex items-center gap-2 text-xs text-muted-foreground mb-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0 hover:bg-transparent"
            onClick={handleToggleCollapse}
          >
            {isCollapsed ? <ChevronDown className="h-3 w-3" /> : <ChevronUp className="h-3 w-3" />}
          </Button>
          
          <Link 
            href={`/members/${comment.userId}`}
            className="hover:underline flex items-center gap-1"
          >
            <ProfileImage
              storageId={comment.user?.profileImage}
              name={comment.user?.name}
              size="sm"
            />
            <span className="font-medium">
              {comment.memberProfile?.displayName || comment.user?.name}
            </span>
          </Link>

          {/* OP badge */}
          {comment.userId === postAuthorId && (
            <Badge variant="outline" className="text-xs h-4 px-1 bg-blue-500 text-white border-0">
              OP
            </Badge>
          )}
          
          {/* User badges */}
          {comment.memberProfile?.isModerator && (
            <Badge variant="outline" className="text-xs h-4 px-1 bg-green-500 text-white border-0">
              MOD
            </Badge>
          )}
          {comment.memberProfile?.isTopContributor && (
            <Badge variant="outline" className="text-xs h-4 px-1">
              Top 1%
            </Badge>
          )}

          {/* User flair */}
          {comment.memberProfile?.flair && (
            <Badge 
              variant="outline" 
              className="text-xs h-4 px-1"
              style={{
                backgroundColor: comment.memberProfile.flair.backgroundColor,
                color: comment.memberProfile.flair.textColor,
              }}
            >
              {comment.memberProfile.flair.text}
            </Badge>
          )}

          <span>•</span>
          <span className="font-medium">{formatScore(score)} points</span>
          <span>•</span>
          <span>{formatDistanceToNow(new Date(comment._creationTime))} ago</span>
          
          {comment.isEdited && (
            <>
              <span>•</span>
              <span className="italic">edited</span>
            </>
          )}

          {comment.isBestAnswer && (
            <>
              <span>•</span>
              <Pin className="h-3 w-3 text-green-500" />
              <span className="text-green-500 font-medium">Best Answer</span>
            </>
          )}

          {/* Awards */}
          {comment.awards && comment.awards.length > 0 && (
            <>
              <span>•</span>
              <div className="flex items-center gap-1">
                {comment.awards.slice(0, 3).map((award: any, index: number) => (
                  <span key={index} className="text-sm" title={award.name}>
                    {award.icon}
                  </span>
                ))}
                {comment.awards.length > 3 && (
                  <span className="text-xs">+{comment.awards.length - 3}</span>
                )}
              </div>
            </>
          )}
        </div>

        {/* Comment Content */}
        {!isCollapsed && (
          <>
            {isEditing ? (
              <EditCommentForm
                comment={{
                  _id: comment._id,
                  content: comment.content
                }}
                onCancel={() => setIsEditing(false)}
                onSuccess={() => {
                  setIsEditing(false);
                  window.location.reload();
                }}
              />
            ) : (
              <div className="text-sm my-2 prose prose-sm dark:prose-invert max-w-none">
                {comment.isDeleted ? (
                  <span className="italic text-muted-foreground">[deleted]</span>
                ) : (
                  comment.content
                )}
              </div>
            )}

            {/* Comment Actions */}
            <div className="flex items-center gap-1 text-xs -ml-1">
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "h-6 px-1 gap-1",
                  userVote === "upvote" && "text-orange-500"
                )}
                onClick={() => handleVote("upvote")}
              >
                <ArrowUp className={cn(
                  "h-3 w-3",
                  userVote === "upvote" ? "fill-current" : ""
                )} />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "h-6 px-1 gap-1",
                  userVote === "downvote" && "text-blue-500"
                )}
                onClick={() => handleVote("downvote")}
              >
                <ArrowDown className={cn(
                  "h-3 w-3",
                  userVote === "downvote" ? "fill-current" : ""
                )} />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 gap-1"
                onClick={() => setShowReplyForm(!showReplyForm)}
              >
                <MessageSquare className="h-3 w-3" />
                <span>Reply</span>
              </Button>

              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 gap-1"
                onClick={onAwardClick}
              >
                <Award className="h-3 w-3" />
                <span>Award</span>
              </Button>

              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 gap-1"
                onClick={() => {
                  const url = `${window.location.origin}/forum/post/${comment.postId}#comment-${comment._id}`;
                  navigator.clipboard.writeText(url);
                  toast.success("Link copied");
                }}
              >
                <Share2 className="h-3 w-3" />
                <span>Share</span>
              </Button>

              {currentUser?._id === comment.userId && !comment.isDeleted && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2 gap-1"
                    onClick={() => setIsEditing(true)}
                  >
                    <Edit className="h-3 w-3" />
                    <span>Edit</span>
                  </Button>
                  <CommentActions
                    comment={{
                      _id: comment._id,
                      content: comment.content,
                      userId: comment.userId,
                      isDeleted: comment.isDeleted
                    }}
                    onEdit={() => setIsEditing(true)}
                    onDelete={() => window.location.reload()}
                  />
                </>
              )}

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreHorizontal className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Flag className="h-4 w-4 mr-2" />
                    Report
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Reply Form */}
            {showReplyForm && (
              <div className="mt-3">
                <Textarea
                  placeholder="What are your thoughts?"
                  value={replyContent}
                  onChange={(e) => setReplyContent(e.target.value)}
                  className="min-h-[100px]"
                />
                <div className="flex gap-2 mt-2">
                  <Button size="sm" onClick={handleReply}>
                    Reply
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => {
                      setShowReplyForm(false);
                      setReplyContent("");
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}

            {/* Nested Replies */}
            {comment.replies && comment.replies.length > 0 && (
              <div className="mt-2">
                {comment.replies.map((reply: any) => (
                  <RedditStyleComment
                    key={reply._id}
                    comment={reply}
                    postAuthorId={postAuthorId}
                    level={level + 1}
                    onReply={onReply}
                    onAwardClick={onAwardClick}
                  />
                ))}
              </div>
            )}

            {/* Load more replies */}
            {comment.hasMoreReplies && (
              <Button
                variant="link"
                size="sm"
                className="h-auto p-0 text-xs text-blue-500"
              >
                {comment.moreRepliesCount} more replies
              </Button>
            )}
          </>
        )}

        {/* Collapsed state */}
        {isCollapsed && (
          <div className="text-xs text-muted-foreground">
            {comment.replies?.length || 0} children
          </div>
        )}
      </div>
    </div>
  );
}
