"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Textarea } from "@repo/ui/components/textarea";
import { Badge } from "@repo/ui/components/badge";
import { ProfileImage } from "@/components/common/ProfileImage";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import { format } from "date-fns";
import {
  MessageSquare,
  ThumbsUp,
  Eye,
  Pin,
  Lock,
  ArrowLeft,
  CheckCircle,
  Reply as ReplyIcon,
  MoreVertical,
  Flag
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";

interface ForumPostDetailProps {
  postId: string;
}

export default function ForumPostDetail({ postId }: ForumPostDetailProps) {
  const router = useRouter();
  const { user: currentUser } = useAuth();
  const [commentContent, setCommentContent] = useState("");
  const [replyContent, setReplyContent] = useState<{ [key: string]: string }>({});
  const [showReplyForm, setShowReplyForm] = useState<{ [key: string]: boolean }>({});
  const [cursor, setCursor] = useState<string | null>(null);

  const post = useQuery(api.forum.getForumPost, {
    postId: postId as any,
  });

  const addComment = useMutation(api.forum.addForumComment);
  const incrementViews = useMutation(api.forum.incrementPostViews);

  // Increment view count when post is viewed
  useEffect(() => {
    if (post && postId) {
      incrementViews({ postId: postId as any }).catch(console.error);
    }
  }, [postId]);

  const handleAddComment = async () => {
    if (!currentUser) {
      toast.error("Please sign in to comment");
      return;
    }

    if (!commentContent.trim()) {
      toast.error("Please write a comment");
      return;
    }

    try {
      await addComment({
        postId: postId as any,
        content: commentContent,
      });
      setCommentContent("");
      toast.success("Comment added!");
    } catch (error: any) {
      toast.error(error.message || "Failed to add comment");
    }
  };

  const handleAddReply = async (parentCommentId: string) => {
    if (!currentUser) {
      toast.error("Please sign in to reply");
      return;
    }

    const content = replyContent[parentCommentId];
    if (!content?.trim()) {
      toast.error("Please write a reply");
      return;
    }

    try {
      await addComment({
        postId: postId as any,
        content,
        parentCommentId: parentCommentId as any,
      });
      setReplyContent({ ...replyContent, [parentCommentId]: "" });
      setShowReplyForm({ ...showReplyForm, [parentCommentId]: false });
      toast.success("Reply added!");
    } catch (error: any) {
      toast.error(error.message || "Failed to add reply");
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      general: "bg-blue-500",
      marketplace: "bg-green-500",
      authentication: "bg-purple-500",
      deals: "bg-yellow-500",
      questions: "bg-orange-500",
      announcements: "bg-red-500",
    };
    return colors[category] || "bg-gray-500";
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "announcements":
        return "📢";
      case "marketplace":
        return "🛍️";
      case "authentication":
        return "✅";
      case "deals":
        return "💰";
      case "questions":
        return "❓";
      default:
        return "💬";
    }
  };

  if (!post) {
    return (
      <Card>
        <CardContent className="py-12 text-center">
          <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">Post not found</p>
          <Button className="mt-4" onClick={() => router.push("/forum")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Forum
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Button variant="ghost" onClick={() => router.push("/forum")}>
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Forum
      </Button>

      {/* Post Content */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-start gap-4">
            <Link href={`/members/${post.userId}`}>
              <ProfileImage
                storageId={post.user?.profileImage}
                name={post.user?.name}
                size="lg"
              />
            </Link>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                {post.isPinned && (
                  <Pin className="h-5 w-5 text-yellow-500" />
                )}
                {post.isLocked && (
                  <Lock className="h-5 w-5 text-red-500" />
                )}
                <h1 className="text-2xl font-bold">{post.title}</h1>
              </div>
              <div className="flex items-center gap-3 text-sm text-muted-foreground">
                <Link 
                  href={`/members/${post.userId}`}
                  className="font-medium hover:underline"
                >
                  {post.memberProfile?.displayName || post.user?.name}
                </Link>
                <span>•</span>
                <span>{format(new Date(post.updatedAt), "MMM d, yyyy 'at' h:mm a")}</span>
                <span>•</span>
                <Badge variant="outline" className="text-xs">
                  {getCategoryIcon(post.category)} {post.category}
                </Badge>
                {post.isEdited && (
                  <>
                    <span>•</span>
                    <span className="text-xs">(edited)</span>
                  </>
                )}
              </div>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <Flag className="mr-2 h-4 w-4" />
                  Report Post
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent>
          <div className="prose prose-sm max-w-none mb-6">
            <p className="whitespace-pre-wrap">{post.content}</p>
          </div>

          {post.tags && post.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-6">
              {post.tags.map((tag: string) => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          )}

          <div className="flex items-center gap-6 text-sm text-muted-foreground pt-4 border-t">
            <div className="flex items-center gap-1">
              <MessageSquare className="h-4 w-4" />
              <span>{post.commentCount} replies</span>
            </div>
            <div className="flex items-center gap-1">
              <ThumbsUp className="h-4 w-4" />
              <span>{post.likes} likes</span>
            </div>
            <div className="flex items-center gap-1">
              <Eye className="h-4 w-4" />
              <span>{post.views} views</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add Comment Form */}
      {!post.isLocked && (
        <Card>
          <CardHeader>
            <h3 className="font-semibold">Add a Reply</h3>
          </CardHeader>
          <CardContent>
            {currentUser ? (
              <div className="flex gap-3">
                <ProfileImage
                  storageId={currentUser.profileImage}
                  name={currentUser.name}
                  size="sm"
                />
                <div className="flex-1">
                  <Textarea
                    placeholder="Share your thoughts..."
                    value={commentContent}
                    onChange={(e) => setCommentContent(e.target.value)}
                    rows={4}
                    className="mb-2"
                  />
                  <Button onClick={handleAddComment}>
                    Post Reply
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-muted-foreground mb-2">
                  Sign in to join the discussion
                </p>
                <Button size="sm" asChild>
                  <Link href="/login">Sign In</Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Comments/Replies Section */}
      <div className="space-y-4">
        <h3 className="font-semibold text-lg">Replies ({post.commentCount})</h3>
        
        {/* Placeholder for comments - you'll need to add a getForumComments query */}
        <Card>
          <CardContent className="py-8 text-center">
            <MessageSquare className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">
              Be the first to reply to this post
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
