"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Textarea } from "@repo/ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Badge } from "@repo/ui/components/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import { ProfileImage } from "@/components/common/ProfileImage";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import { format } from "date-fns";
import {
  MessageSquare,
  ThumbsUp,
  Eye,
  Pin,
  Lock,
  TrendingUp,
  Plus,
  Search,
  Filter,
  ChevronRight
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@repo/ui/components/dialog";

export default function ForumHome() {
  const router = useRouter();
  const { user: currentUser } = useAuth();
  const [cursor, setCursor] = useState<string | null>(null);
  const [category, setCategory] = useState<string | undefined>(undefined);
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreatePostOpen, setIsCreatePostOpen] = useState(false);

  // New post form state
  const [newPostTitle, setNewPostTitle] = useState("");
  const [newPostContent, setNewPostContent] = useState("");
  const [newPostCategory, setNewPostCategory] = useState<string>("general");
  const [newPostTags, setNewPostTags] = useState("");

  // Fetch forum posts
  const posts = useQuery(api.forum.getForumPosts, {
    category: category as any,
    sortBy: "recent",
    paginationOpts: { numItems: 10, cursor },
  });

  const createPost = useMutation(api.forum.createForumPost);

  const handleCreatePost = async () => {
    if (!currentUser) {
      toast.error("Please sign in to create a post");
      return;
    }

    if (!newPostTitle.trim() || !newPostContent.trim()) {
      toast.error("Please provide both title and content");
      return;
    }

    try {
      const post = await createPost({
        title: newPostTitle,
        content: newPostContent,
        contentPlainText: newPostContent, // For plain text search
        category: newPostCategory as any,
        tags: newPostTags.split(",").map(tag => tag.trim()).filter(Boolean),
      });
      
      toast.success("Post created successfully!");
      setIsCreatePostOpen(false);
      setNewPostTitle("");
      setNewPostContent("");
      setNewPostCategory("general");
      setNewPostTags("");
      
      // Navigate to the new post
      router.push(`/forum/post/${post?._id}`);
    } catch (error: any) {
      toast.error(error.message || "Failed to create post");
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      general: "bg-blue-500",
      marketplace: "bg-green-500",
      authentication: "bg-purple-500",
      deals: "bg-yellow-500",
      questions: "bg-orange-500",
      announcements: "bg-red-500",
    };
    return colors[category] || "bg-gray-500";
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "announcements":
        return "📢";
      case "marketplace":
        return "🛍️";
      case "authentication":
        return "✅";
      case "deals":
        return "💰";
      case "questions":
        return "❓";
      default:
        return "💬";
    }
  };

  return (
    <div className="space-y-6">
      {/* Top Actions Bar */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search forum posts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={category || "all"} onValueChange={(v) => setCategory(v === "all" ? undefined : v)}>
          <SelectTrigger className="w-full md:w-48">
            <SelectValue placeholder="All Categories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="general">General</SelectItem>
            <SelectItem value="marketplace">Marketplace</SelectItem>
            <SelectItem value="authentication">Authentication</SelectItem>
            <SelectItem value="deals">Deals</SelectItem>
            <SelectItem value="questions">Questions</SelectItem>
            <SelectItem value="announcements">Announcements</SelectItem>
          </SelectContent>
        </Select>
        
        {currentUser && (
          <Dialog open={isCreatePostOpen} onOpenChange={setIsCreatePostOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Post
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Forum Post</DialogTitle>
                <DialogDescription>
                  Share your thoughts, ask questions, or start a discussion
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 mt-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Title</label>
                  <Input
                    placeholder="What's on your mind?"
                    value={newPostTitle}
                    onChange={(e) => setNewPostTitle(e.target.value)}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Category</label>
                  <Select value={newPostCategory} onValueChange={setNewPostCategory}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="general">General</SelectItem>
                      <SelectItem value="marketplace">Marketplace</SelectItem>
                      <SelectItem value="authentication">Authentication</SelectItem>
                      <SelectItem value="deals">Deals</SelectItem>
                      <SelectItem value="questions">Questions</SelectItem>
                      <SelectItem value="announcements">Announcements</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Content</label>
                  <Textarea
                    placeholder="Write your post content..."
                    value={newPostContent}
                    onChange={(e) => setNewPostContent(e.target.value)}
                    rows={8}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Tags (comma-separated, optional)
                  </label>
                  <Input
                    placeholder="e.g., sneakers, authentication, help"
                    value={newPostTags}
                    onChange={(e) => setNewPostTags(e.target.value)}
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsCreatePostOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreatePost}>
                    Create Post
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Category Quick Filters */}
      <div className="flex gap-2 flex-wrap">
        <Button
          size="sm"
          variant={!category ? "default" : "outline"}
          onClick={() => setCategory(undefined)}
        >
          All
        </Button>
        {["general", "marketplace", "authentication", "deals", "questions", "announcements"].map((cat) => (
          <Button
            key={cat}
            size="sm"
            variant={category === cat ? "default" : "outline"}
            onClick={() => setCategory(cat)}
          >
            <span className="mr-1">{getCategoryIcon(cat)}</span>
            {cat.charAt(0).toUpperCase() + cat.slice(1)}
          </Button>
        ))}
      </div>

      {/* Posts List */}
      <div className="space-y-4">
        {posts?.page && posts.page.length > 0 ? (
          posts.page
            .filter(post => !searchTerm || 
              post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
              post.content.toLowerCase().includes(searchTerm.toLowerCase())
            )
            .map((post: any) => (
              <Card key={post._id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <Link href={`/forum/post/${post._id}`}>
                    <div className="flex items-start gap-4">
                      <ProfileImage
                        storageId={post.user?.profileImage}
                        name={post.user?.name}
                        size="md"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              {post.isPinned && (
                                <Pin className="h-4 w-4 text-yellow-500" />
                              )}
                              {post.isLocked && (
                                <Lock className="h-4 w-4 text-red-500" />
                              )}
                              <h3 className="font-semibold text-lg hover:underline">
                                {post.title}
                              </h3>
                            </div>
                            <div className="flex items-center gap-3 text-sm text-muted-foreground mb-2">
                              <span>{post.memberProfile?.displayName || post.user?.name}</span>
                              <span>•</span>
                              <span>{format(new Date(post.lastActivityAt), "MMM d, yyyy")}</span>
                              <span>•</span>
                              <Badge variant="outline" className="text-xs">
                                {getCategoryIcon(post.category)} {post.category}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        
                        <p className="text-muted-foreground line-clamp-2 mb-3">
                          {post.content}
                        </p>

                        {post.tags && post.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mb-3">
                            {post.tags.map((tag: string) => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        )}

                        <div className="flex items-center gap-6 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <MessageSquare className="h-4 w-4" />
                            <span>{post.commentCount} replies</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <ThumbsUp className="h-4 w-4" />
                            <span>{post.likes} likes</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Eye className="h-4 w-4" />
                            <span>{post.views} views</span>
                          </div>
                          {post.isBestAnswer && (
                            <Badge variant="default" className="text-xs">
                              ✓ Answered
                            </Badge>
                          )}
                        </div>
                      </div>
                      <ChevronRight className="h-5 w-5 text-muted-foreground mt-2" />
                    </div>
                  </Link>
                </CardContent>
              </Card>
            ))
        ) : (
          <Card>
            <CardContent className="py-12 text-center">
              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {searchTerm
                  ? "No posts found matching your search"
                  : "No posts yet. Be the first to start a discussion!"}
              </p>
              {currentUser && !searchTerm && (
                <Button className="mt-4" onClick={() => setIsCreatePostOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create First Post
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Load More */}
      {posts && !posts.isDone && (
        <div className="flex justify-center mt-6">
          <Button
            variant="outline"
            onClick={() => setCursor(posts.continueCursor)}
          >
            Load More Posts
          </Button>
        </div>
      )}
    </div>
  );
}
