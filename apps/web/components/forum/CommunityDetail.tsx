"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Button } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import RedditStylePost from "./RedditStylePost";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import { format } from "date-fns";
import {
  ArrowLeft,
  Users,
  Calendar,
  Globe,
  Lock,
  Plus,
  Settings,
  Shield,
  TrendingUp,
  Clock,
  Award as AwardIcon,
  MessageSquare,
} from "lucide-react";

interface CommunityDetailProps {
  slug: string;
}

export default function CommunityDetail({ slug }: CommunityDetailProps) {
  const router = useRouter();
  const { user: currentUser } = useAuth();
  const [activeTab, setActiveTab] = useState<"posts" | "about">("posts");
  const [cursor, setCursor] = useState<string | null>(null);

  // Fetch community details
  const community = useQuery(api.forum.getForumGroup, { slug });
  
  // Fetch community posts
  const posts = useQuery(api.forum.getForumPosts, {
    groupId: community?._id as any,
    sortBy: "recent",
    paginationOpts: { numItems: 25, cursor },
  });

  // Mutations
  const joinCommunity = useMutation(api.forum.joinForumGroup);
  const leaveCommunity = useMutation(api.forum.leaveForumGroup);

  // Use backend-provided membership status
  const isCreator = community?.isCreator || false;
  const isModerator = community?.isModerator || false;
  const isMember = community?.isMember || false;

  const handleJoinToggle = async () => {
    if (!currentUser) {
      toast.error("Please sign in to join communities");
      return;
    }

    if (!community) return;

    // Prevent creator from leaving their own community
    if (isCreator && isMember) {
      toast.error("Community creators cannot leave their own community");
      return;
    }

    try {
      if (isMember) {
        await leaveCommunity({ groupId: community._id });
        toast.success("Left community");
        // Data will refresh automatically via Convex reactivity
      } else {
        await joinCommunity({ groupId: community._id });
        toast.success("Joined community!");
        // Data will refresh automatically via Convex reactivity
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to update membership");
    }
  };

  if (!community) {
    return (
      <div className="flex gap-6">
        <div className="flex-1">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Community not found</p>
              <Button className="mt-4" onClick={() => router.push("/forum")}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Forum
              </Button>
            </div>
          </div>
        </div>
        <div className="w-80" />
      </div>
    );
  }

  return (
    <div className="flex gap-6">
      {/* Main Content */}
      <div className="flex-1">
        {/* Breadcrumb Navigation */}
        <div className="flex items-center gap-2 mb-4 text-sm text-muted-foreground">
          <Link href="/forum" className="hover:text-primary">
            Forum
          </Link>
          <span>•</span>
          <Link href="/forum/groups" className="hover:text-primary">
            Communities
          </Link>
          <span>•</span>
          <span className="text-foreground font-medium">r/{slug}</span>
        </div>

        {/* Community Header */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-4">
                <div 
                  className="w-16 h-16 rounded-full flex items-center justify-center text-2xl text-white font-bold"
                  style={{ backgroundColor: community.color || "#4ECDC4" }}
                >
                  {community.icon || "💬"}
                </div>
                <div className="space-y-1">
                  <h1 className="text-3xl font-bold">r/{community.slug}</h1>
                  <p className="text-lg text-muted-foreground">{community.name}</p>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Users className="w-4 h-4" />
                      {community.memberCount} members
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      Created {format(new Date(community.updatedAt), "MMM d, yyyy")}
                    </div>
                    <div className="flex items-center gap-1">
                      {community.isPrivate ? (
                        <>
                          <Lock className="w-4 h-4" />
                          Private
                        </>
                      ) : (
                        <>
                          <Globe className="w-4 h-4" />
                          Public
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                {currentUser && (isCreator || isModerator) ? (
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="gap-1">
                      <Shield className="w-3 h-3" />
                      {isCreator ? "Creator" : "Moderator"}
                    </Badge>
                    <Button variant="outline" size="icon">
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                ) : currentUser && isMember ? (
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="gap-1">
                      <Users className="w-3 h-3" />
                      Member
                    </Badge>
                    <Button
                      variant="outline"
                      onClick={handleJoinToggle}
                      className="gap-2"
                    >
                      Leave
                    </Button>
                  </div>
                ) : currentUser ? (
                  <Button
                    variant="default"
                    onClick={handleJoinToggle}
                    className="gap-2"
                  >
                    <Plus className="w-4 h-4" />
                    Join
                  </Button>
                ) : null}
              </div>
            </div>

            <p className="mt-4 text-muted-foreground leading-relaxed">
              {community.description}
            </p>

            {/* Tabs */}
            <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)} className="mt-6">
              <TabsList>
                <TabsTrigger value="posts">Posts</TabsTrigger>
                <TabsTrigger value="about">About</TabsTrigger>
              </TabsList>
            </Tabs>
          </CardContent>
        </Card>

        {/* Tab Content */}
        <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)}>
          <TabsContent value="posts" className="mt-0">
            {/* Create Post Button */}
            {currentUser && (isCreator || isModerator || isMember) && (
              <Button 
                className="w-fit mb-4" 
                onClick={() => router.push(`/forum/submit?group=${community.slug}`)}
              >
                <Plus className="mr-2 h-4 w-4" />
                Create Post in r/{community.slug}
              </Button>
            )}

            {/* Posts List */}
            <div className="space-y-2">
              {posts?.page && posts.page.length > 0 ? (
                posts.page.map((post: any) => (
                  <RedditStylePost
                    key={post._id}
                    post={post}
                    showContent={true}
                    onAwardClick={() => {/* Open award modal */}}
                  />
                ))
              ) : (
                <Card className="p-8 text-center flex items-center justify-center">
                  <p className="text-muted-foreground">
                    No posts yet. Be the first to share something!
                  </p>
                  {currentUser && (isCreator || isModerator || isMember) && (
                    <Button 
                      className="mt-4 w-fit"
                      onClick={() => router.push(`/forum/submit?group=${community.slug}`)}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Create Post
                    </Button>
                  )}
                </Card>
              )}
            </div>

            {/* Load More */}
            {posts && !posts.isDone && (
              <div className="flex justify-center mt-6">
                <Button
                  variant="outline"
                  onClick={() => setCursor(posts.continueCursor)}
                >
                  Load More
                </Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="about" className="mt-0">
            <div className="space-y-6">
              {/* Community Rules */}
              {community.rules && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="w-5 h-5" />
                      Community Rules
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="whitespace-pre-wrap text-sm leading-relaxed">
                      {community.rules}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Moderators */}
              <Card>
                <CardHeader>
                  <CardTitle>Moderators</CardTitle>
                </CardHeader>
                                  <CardContent className="space-y-3">
                  {community.moderators && community.moderators.length > 0 ? (
                    community.moderators.map((moderator: any, index: number) => {
                      // Handle both string IDs and user objects
                      const isString = typeof moderator === 'string';
                      const displayName = isString ? "Moderator" : (moderator?.name || "Moderator");
                      const initial = isString ? "M" : (moderator?.name?.charAt(0).toUpperCase() || "M");
                      const key = isString ? moderator : (moderator?._id || index);
                      
                      return (
                        <div key={key} className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-bold">
                            {initial}
                          </div>
                          <div>
                            <p className="font-medium text-sm">{displayName}</p>
                            <p className="text-xs text-muted-foreground">Community moderator</p>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <p className="text-muted-foreground text-sm">No moderators listed</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Sidebar */}
      <div className="w-80 space-y-4">
        {/* Create Post Button */}
        {currentUser && (isCreator || isModerator || isMember) && (
          <Button 
            className="w-full" 
            onClick={() => router.push(`/forum/submit?group=${community.slug}`)}
          >
            <Plus className="mr-2 h-4 w-4" />
            Create Post
          </Button>
        )}

        {/* Community Stats */}
        <Card className="p-4">
          <h3 className="font-semibold mb-2">About r/{community.slug}</h3>
          <p className="text-sm text-muted-foreground mb-4">
            {community.description}
          </p>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Members</span>
              <span className="font-medium">{community.memberCount}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Posts</span>
              <span className="font-medium">{community.postCount}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Created</span>
              <span className="font-medium">
                {format(new Date(community.updatedAt), "MMM d, yyyy")}
              </span>
            </div>
          </div>
        </Card>

        {/* Community Type */}
        <Card className="p-4">
          <h3 className="font-semibold mb-2">Community Type</h3>
          <Badge variant="secondary" className="gap-1">
            {community.isPrivate ? (
              <>
                <Lock className="w-3 h-3" />
                Private
              </>
            ) : (
              <>
                <Globe className="w-3 h-3" />
                Public
              </>
            )}
          </Badge>
          <p className="text-xs text-muted-foreground mt-2">
            {community.isPrivate
              ? "Only approved members can view and post"
              : "Anyone can view and participate"
            }
          </p>
        </Card>
      </div>
    </div>
  );
}
