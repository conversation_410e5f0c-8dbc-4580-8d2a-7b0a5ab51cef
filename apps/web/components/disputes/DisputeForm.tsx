"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { AlertTriangle, CheckCircle2 } from "lucide-react";

const disputeSchema = z.object({
  topic: z.enum(["condition", "warranty", "not_delivered", "unresponsive_seller", "forum_issue", "misc"], {
    required_error: "Please select a topic",
  }),
  dateOfIssue: z.string().min(1, "Date of issue is required"),
  memberVendorInQuestion: z.string().optional(),
  explanation: z.string().min(10, "Please provide at least 10 characters of explanation"),
});

type DisputeFormData = z.infer<typeof disputeSchema>;

const topicOptions = [
  { value: "condition", label: "Product Condition Issue" },
  { value: "warranty", label: "Warranty Claim" },
  { value: "not_delivered", label: "Item Not Delivered" },
  { value: "unresponsive_seller", label: "Unresponsive Seller" },
  { value: "forum_issue", label: "Forum/Community Issue" },
  { value: "misc", label: "Other/Miscellaneous" },
];

export function DisputeForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  
  const { user, isLoading } = useAuth();
  const submitDispute = useMutation(api.disputes.submitDispute);

  const form = useForm<DisputeFormData>({
    resolver: zodResolver(disputeSchema),
    defaultValues: {
      topic: undefined,
      dateOfIssue: "",
      memberVendorInQuestion: "",
      explanation: "",
    },
  });

  const { handleSubmit, formState: { errors }, reset, watch } = form;
  const selectedTopic = watch("topic");

  const onSubmit = async (data: DisputeFormData) => {
    if (!user) {
      toast.error("Please sign in to submit a dispute");
      return;
    }

    setIsSubmitting(true);
    
    try {
      await submitDispute({
        topic: data.topic,
        dateOfIssue: data.dateOfIssue,
        memberVendorInQuestion: data.memberVendorInQuestion || undefined,
        explanation: data.explanation,
      });
      
      toast.success("Dispute submitted successfully!", {
        description: "Our support team will review your dispute and get back to you soon.",
      });
      
      setIsSubmitted(true);
      reset();
    } catch (error: any) {
      console.error("Submission error:", error);
      toast.error("Failed to submit dispute", {
        description: error?.message || "Please try again or contact support.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
          <CardContent className="p-8 text-center">
            <div className="animate-pulse">
              <div className="h-4 bg-muted rounded w-1/4 mx-auto mb-4"></div>
              <div className="h-4 bg-muted rounded w-1/2 mx-auto"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
          <CardContent className="p-8 text-center">
            <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-4 text-foreground">Authentication Required</h2>
            <p className="text-muted-foreground mb-6 font-light">
              Please sign in to submit a dispute.
            </p>
            <Button asChild className="rounded-xl font-medium transition-all duration-300 hover:ring-2 hover:ring-primary/20">
              <a href="/login">Sign In</a>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isSubmitted) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
          <CardContent className="p-8 text-center">
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle2 className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <h2 className="text-2xl font-semibold text-foreground mb-4">Dispute Submitted Successfully!</h2>
            <p className="text-muted-foreground font-light mb-6">
              Thank you for submitting your dispute. Our support team will review your case and get back to you within 24-48 hours.
            </p>
            <div className="bg-muted/50 rounded-lg p-4 mb-6">
              <p className="text-sm text-muted-foreground">
                <strong>What happens next?</strong><br />
                • Our team will review your dispute<br />
                • We may reach out for additional information<br />
                • You'll receive updates via email and in your account
              </p>
            </div>
            <Button 
              onClick={() => setIsSubmitted(false)}
              className="rounded-xl font-medium transition-all duration-300 hover:ring-2 hover:ring-primary/20"
            >
              Submit Another Dispute
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
        <CardHeader>
          <CardTitle className="text-2xl font-light tracking-wide text-foreground">Submit a Dispute</CardTitle>
          <p className="text-muted-foreground font-light">
            Please provide details about your issue so we can assist you effectively.
          </p>
        </CardHeader>

        <CardContent className="pt-0">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Topic Dropdown */}
            <div className="space-y-2">
              <Label htmlFor="topic" className="text-sm font-medium text-foreground">
                Topic <span className="text-red-500">*</span>
              </Label>
              <Select onValueChange={(value) => form.setValue("topic", value as any)}>
                <SelectTrigger className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4">
                  <SelectValue placeholder="Select the type of issue you're experiencing" />
                </SelectTrigger>
                <SelectContent className="bg-card border-border rounded-xl shadow-xl">
                  {topicOptions.map((option) => (
                    <SelectItem 
                      key={option.value} 
                      value={option.value}
                      className="rounded-lg hover:bg-muted/20"
                    >
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.topic && (
                <p className="text-sm text-red-600 font-medium">{errors.topic.message}</p>
              )}
            </div>

            {/* Date of Issue */}
            <div className="space-y-2">
              <Label htmlFor="dateOfIssue" className="text-sm font-medium text-foreground">
                Date of Issue <span className="text-red-500">*</span>
              </Label>
              <Input
                id="dateOfIssue"
                type="date"
                {...form.register("dateOfIssue")}
                className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground placeholder:text-muted transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
              />
              {errors.dateOfIssue && (
                <p className="text-sm text-red-600 font-medium">{errors.dateOfIssue.message}</p>
              )}
            </div>

            {/* Member/Vendor in Question */}
            <div className="space-y-2">
              <Label htmlFor="memberVendorInQuestion" className="text-sm font-medium text-foreground">
                Member/Vendor in Question
              </Label>
              <Input
                id="memberVendorInQuestion"
                {...form.register("memberVendorInQuestion")}
                placeholder="Enter the name or username of the member/vendor (if applicable)"
                className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground placeholder:text-muted transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
              />
              <p className="text-xs text-muted-foreground">
                Optional - Only fill this if your dispute involves a specific member or vendor
              </p>
            </div>

            {/* Explanation */}
            <div className="space-y-2">
              <Label htmlFor="explanation" className="text-sm font-medium text-foreground">
                Detailed Explanation <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="explanation"
                {...form.register("explanation")}
                placeholder="Please provide a detailed explanation of your issue, including any relevant order numbers, dates, or other important information..."
                className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground placeholder:text-muted transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 min-h-[120px] px-4 py-3 resize-none"
                rows={6}
              />
              {errors.explanation && (
                <p className="text-sm text-red-600 font-medium">{errors.explanation.message}</p>
              )}
              <p className="text-xs text-muted-foreground">
                The more details you provide, the better we can assist you
              </p>
            </div>

            {/* Topic-specific help text */}
            {selectedTopic && (
              <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                  Tips for "{topicOptions.find(opt => opt.value === selectedTopic)?.label}" disputes:
                </h4>
                <div className="text-blue-700 dark:text-blue-300 text-sm space-y-1">
                  {selectedTopic === "condition" && (
                    <>
                      <p>• Include photos of the item's condition</p>
                      <p>• Reference the original product listing</p>
                      <p>• Describe how the condition differs from what was advertised</p>
                    </>
                  )}
                  {selectedTopic === "warranty" && (
                    <>
                      <p>• Include proof of purchase and warranty information</p>
                      <p>• Describe the defect or issue clearly</p>
                      <p>• Mention any previous warranty claims</p>
                    </>
                  )}
                  {selectedTopic === "not_delivered" && (
                    <>
                      <p>• Include tracking information if available</p>
                      <p>• Mention the expected delivery date</p>
                      <p>• Note any communication with the seller about delivery</p>
                    </>
                  )}
                  {selectedTopic === "unresponsive_seller" && (
                    <>
                      <p>• Include timestamps of your attempts to contact the seller</p>
                      <p>• Mention the method of communication used</p>
                      <p>• Describe what you were trying to resolve</p>
                    </>
                  )}
                  {selectedTopic === "forum_issue" && (
                    <>
                      <p>• Include links to relevant forum posts or comments</p>
                      <p>• Describe the nature of the issue (harassment, spam, etc.)</p>
                      <p>• Mention any community guidelines that were violated</p>
                    </>
                  )}
                  {selectedTopic === "misc" && (
                    <>
                      <p>• Be as specific as possible about your issue</p>
                      <p>• Include any relevant screenshots or documentation</p>
                      <p>• Mention what resolution you're seeking</p>
                    </>
                  )}
                </div>
              </div>
            )}

            <Button 
              type="submit" 
              className="w-full h-12 rounded-xl font-medium text-sm transition-all duration-300 hover:ring-2 hover:ring-primary/20"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Submitting Dispute..." : "Submit Dispute"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
