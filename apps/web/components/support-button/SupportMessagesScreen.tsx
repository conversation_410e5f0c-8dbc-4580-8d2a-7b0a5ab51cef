"use client";

import { <PERSON>Circle, ArrowRight, ChevronRight, X, FileText, User, Search, Filter } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { SupportScreen, SupportItem } from "./types";
import { SupportBottomNavigation } from "./SupportBottomNavigation";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import React, { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Avatar, AvatarFallback } from "@repo/ui/components/avatar";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Separator } from "@repo/ui/components/separator";

interface SupportMessagesScreenProps {
  onClose: () => void;
  onNavigate: (screen: SupportScreen) => void;
  onOpenConversation: (conversation: any) => void;
  unreadCount?: number; 
}

export function SupportMessagesScreen({ 
  onClose, 
  onNavigate, 
  onOpenConversation,
  unreadCount
}: SupportMessagesScreenProps) {
  const [searchQuery, setSearchQuery] = useState("");
  
  // Fetch both support tickets and regular user messages
  const supportItems = useQuery(api.supportTickets.getUserSupportItems, {});
  const userConversations = useQuery(api.messages.getConversations);

  // Combine and sort all items by most recent activity
  const allItems = React.useMemo(() => {
    const items: Array<{
      id: string;
      type: 'support' | 'message';
      data: any;
      lastActivity: number;
      unreadCount: number;
    }> = [];

    // Add support tickets
    if (supportItems) {
      supportItems.forEach((item: any) => {
        items.push({
          id: item._id,
          type: 'support',
          data: item,
          lastActivity: item.lastMessageAt,
          unreadCount: 0, // Support tickets don't have unread counts in the same way
        });
      });
    }

    // Add user conversations
    if (userConversations) {
      userConversations.forEach((conversation: any) => {
        items.push({
          id: conversation._id,
          type: 'message',
          data: conversation,
          lastActivity: conversation.lastMessageAt,
          unreadCount: conversation.unreadCount || 0,
        });
      });
    }

    // Sort by most recent activity (newest first)
    return items.sort((a: any, b: any) => b.lastActivity - a.lastActivity);
  }, [supportItems, userConversations]);

  // Filter items based on search query
  const filteredItems = React.useMemo(() => {
    if (!searchQuery.trim()) return allItems;
    
    return allItems.filter((item) => {
      if (item.type === 'support') {
        return item.data.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
               item.data.ticketNumber.toString().includes(searchQuery);
      } else {
        const conversation = item.data;
        const otherParticipant = conversation.otherParticipant;
        return (otherParticipant?.name || otherParticipant?.email || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
               conversation.lastMessagePreview.toLowerCase().includes(searchQuery.toLowerCase());
      }
    });
  }, [allItems, searchQuery]);

  const renderSupportItem = (item: SupportItem) => {
    return (
      <div 
        key={item._id}
        className="flex items-center p-3 hover:bg-muted/30 cursor-pointer transition-colors border-b border-border/50 last:border-b-0"
        onClick={() => onOpenConversation({ type: "ticket", ticket: item.ticket })}
      >
        <Avatar className="w-12 h-12 mr-3 flex-shrink-0">
          <AvatarFallback className="bg-primary/10 text-primary border border-primary/20">
            <FileText className="w-5 h-5" />
          </AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h4 className="font-medium text-foreground truncate">
              {item.subject}
            </h4>
            <div className="flex items-center gap-2 flex-shrink-0">
              <span className="text-xs text-muted-foreground">
                {formatDistanceToNow(item.lastMessageAt, { addSuffix: true })}
              </span>
              <ChevronRight className="w-4 h-4 text-muted-foreground" />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">#{item.ticketNumber}</span>
            <Badge variant="secondary" className="text-xs">
              {item.status}
            </Badge>
          </div>
        </div>
      </div>
    );
  };

  const renderUserMessage = (item: any) => {
    const conversation = item.data;
    const otherParticipant = conversation.otherParticipant;
    const product = conversation.product;
    
    return (
      <div 
        key={item.id}
        className="flex items-center p-3 hover:bg-muted/30 cursor-pointer transition-colors border-b border-border/50 last:border-b-0 relative"
        onClick={() => onOpenConversation({ type: "message", conversation })}
      >
        <Avatar className="w-12 h-12 mr-3 flex-shrink-0">
          <AvatarFallback className="bg-accent/10 text-accent border border-accent/20">
            <User className="w-5 h-5" />
          </AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h4 className="font-medium text-foreground truncate">
              {otherParticipant?.name || otherParticipant?.email || 'Unknown User'}
            </h4>
            <div className="flex items-center gap-2 flex-shrink-0">
              <span className="text-xs text-muted-foreground">
                {formatDistanceToNow(conversation.lastMessageAt, { addSuffix: true })}
              </span>
              {item.unreadCount > 0 && (
                <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0" />
              )}
              <ChevronRight className="w-4 h-4 text-muted-foreground" />
            </div>
          </div>
          <p className="text-sm text-muted-foreground truncate">
            {conversation.lastMessagePreview}
            {product && (
              <span className="text-xs text-muted-foreground/70">
                {' • '}{product.name}
              </span>
            )}
          </p>
        </div>
      </div>
    );
  };

  const renderItem = (item: any) => {
    if (item.type === 'support') {
      return renderSupportItem(item.data);
    } else {
      return renderUserMessage(item);
    }
  };

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header */}
      <div className="p-4 border-b border-border bg-background">
        <div className="flex items-center justify-between mb-4">
          <CardTitle className="text-lg">Messages</CardTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-8 w-8"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
        
        {/* Search Bar */}
        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 h-10"
            />
          </div>
          <Button variant="outline" size="icon" className="h-10 w-10">
            <Filter className="w-4 h-4" />
          </Button>
        </div>
      </div>
      
      {/* Messages List */}
      <div className="flex-1 overflow-y-auto">
        {filteredItems && filteredItems.length > 0 ? (
          <div className="divide-y divide-border/50">
            {filteredItems.map(renderItem)}
          </div>
        ) : searchQuery ? (
          <div className="flex flex-col items-center justify-center h-full p-8 text-center">
            <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mb-4">
              <Search className="w-8 h-8 text-muted-foreground/50" />
            </div>
            <h4 className="text-lg font-medium text-foreground mb-2">No results found</h4>
            <p className="text-sm text-muted-foreground font-light mb-6 max-w-sm">
              Try adjusting your search terms or browse all messages
            </p>
            <Button 
              variant="outline" 
              onClick={() => setSearchQuery("")}
              size="sm"
            >
              Clear search
            </Button>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full p-8 text-center">
            <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mb-4">
              <MessageCircle className="w-8 h-8 text-muted-foreground/50" />
            </div>
            <h4 className="text-lg font-medium text-foreground mb-2">No messages yet</h4>
            <p className="text-sm text-muted-foreground font-light mb-6 max-w-sm">
              Start a conversation or contact support to get help with your questions
            </p>
          </div>
        )}
      </div>

      {/* Send Message Button */}
      <div className="p-4 border-t border-border bg-background">
        <Button 
          onClick={() => onNavigate("compose")}
          className="w-full"
          size="lg"
        >
          Send us a message
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
      </div>
      
      {/* Bottom Navigation */}
      <SupportBottomNavigation
        activeScreen="messages"
        onNavigate={onNavigate}
        unreadCount={unreadCount}
      />
    </div>
  );
}
