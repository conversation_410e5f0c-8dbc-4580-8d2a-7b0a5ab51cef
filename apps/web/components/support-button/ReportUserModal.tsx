"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Flag } from "lucide-react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Textarea } from "@repo/ui/components/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from "@repo/ui/components/dialog";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { Label } from "@repo/ui/components/label";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";

interface ReportUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  reportedUser: {
    _id: string;
    name?: string;
    email?: string;
    profileImage?: string;
    userType?: string;
  };
  reportContext?: string; // Optional context about where the report is coming from
}

const REPORT_REASONS = [
  { value: "inappropriate_behavior", label: "Inappropriate Behavior", description: "Harassment, bullying, or offensive conduct" },
  { value: "spam", label: "Spam", description: "Unsolicited messages or repetitive content" },
  { value: "fake_items", label: "Fake Items", description: "Selling counterfeit or fake products" },
  { value: "scam", label: "Scam", description: "Attempting to defraud or deceive users" },
  { value: "inappropriate_content", label: "Inappropriate Content", description: "Sharing inappropriate or offensive content" },
  { value: "impersonation", label: "Impersonation", description: "Pretending to be someone else" },
  { value: "other", label: "Other", description: "Other violations not listed above" },
];

export function ReportUserModal({ 
  isOpen, 
  onClose, 
  reportedUser, 
  reportContext 
}: ReportUserModalProps) {
  const [reason, setReason] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Create report mutation
  const createReport = useMutation(api.reports.createUserReport);

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleSubmit = async () => {
    if (!reason.trim()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await createReport({
        reportedUserId: reportedUser._id as Id<"users">,
        reason,
        description: description.trim(),
        context: reportContext || "chat_conversation",
      });
      
      setIsSubmitted(true);
      // Reset form after successful submission
      setTimeout(() => {
        setIsSubmitted(false);
        setReason("");
        setDescription("");
        onClose();
      }, 2000);
    } catch (error) {
      console.error("Failed to submit report:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setReason("");
      setDescription("");
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Flag className="h-5 w-5 text-destructive" />
            Report User
          </DialogTitle>
          <DialogDescription>
            Help us maintain a safe community by reporting users who violate our community guidelines.
          </DialogDescription>
        </DialogHeader>

        {isSubmitted ? (
          <div className="text-center py-8">
            <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Report Submitted
            </h3>
            <p className="text-muted-foreground">
              Thank you for your report. Our team will review it and take appropriate action.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* User Info */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Reporting User
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3">
                  <Avatar className="w-10 h-10">
                    <AvatarImage 
                      src={reportedUser.profileImage} 
                      alt={reportedUser.name || 'User'} 
                    />
                    <AvatarFallback className="bg-accent/10 text-accent border border-accent/20">
                      {getInitials(reportedUser.name || reportedUser.email || 'User')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-foreground text-sm truncate">
                      {reportedUser.name || reportedUser.email || 'Unknown User'}
                    </h4>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary" className="text-xs">
                        {reportedUser.userType === 'consumer' ? 'Buyer' : 'Seller'}
                      </Badge>
                      {reportContext && (
                        <Badge variant="outline" className="text-xs">
                          {reportContext}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Report Form */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="reason">Reason for Report *</Label>
                <Select value={reason} onValueChange={setReason}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a reason" />
                  </SelectTrigger>
                  <SelectContent>
                    {REPORT_REASONS.map((reportReason) => (
                      <SelectItem key={reportReason.value} value={reportReason.value}>
                        <div className="flex flex-col">
                          <span className="font-medium">{reportReason.label}</span>
                          <span className="text-xs text-muted-foreground">
                            {reportReason.description}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">
                  Additional Details
                  <span className="text-muted-foreground text-xs ml-1">(Optional)</span>
                </Label>
                <Textarea
                  id="description"
                  placeholder="Please provide any additional details about the incident..."
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={4}
                  maxLength={1000}
                />
                <div className="text-xs text-muted-foreground text-right">
                  {description.length}/1000
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3 pt-4">
              <Button 
                variant="outline" 
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button 
                onClick={handleSubmit}
                disabled={!reason.trim() || isSubmitting}
                className="bg-destructive hover:bg-destructive/90"
              >
                {isSubmitting ? "Submitting..." : "Submit Report"}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
