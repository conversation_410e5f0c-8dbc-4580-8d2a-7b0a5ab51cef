"use client";

import { useState } from "react";
import { User<PERSON>, AlertTriangle } from "lucide-react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Textarea } from "@repo/ui/components/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from "@repo/ui/components/dialog";
import { Label } from "@repo/ui/components/label";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";

interface BlockUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  blockedUser: {
    _id: string;
    name?: string;
    email?: string;
    profileImage?: string;
    userType?: string;
  };
}

export function BlockUserModal({ 
  isOpen, 
  onClose, 
  blockedUser 
}: BlockUserModalProps) {
  const [reason, setReason] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Block user mutation
  const blockUser = useMutation(api.reports.blockUser);

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await blockUser({
        blockedUserId: blockedUser._id as Id<"users">,
        reason: reason.trim(),
      });
      
      setIsSubmitted(true);
      // Reset form after successful submission
      setTimeout(() => {
        setIsSubmitted(false);
        setReason("");
        onClose();
      }, 2000);
    } catch (error) {
      console.error("Failed to block user:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setReason("");
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserX className="h-5 w-5 text-destructive" />
            Block User
          </DialogTitle>
          <DialogDescription>
            Block this user to prevent them from messaging you or viewing your profile. You can unblock them later from your settings.
          </DialogDescription>
        </DialogHeader>

        {isSubmitted ? (
          <div className="text-center py-8">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <UserX className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">
              User Blocked
            </h3>
            <p className="text-muted-foreground">
              This user has been blocked and can no longer contact you.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* User Info */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Blocking User
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3">
                  <Avatar className="w-10 h-10">
                    <AvatarImage 
                      src={blockedUser.profileImage} 
                      alt={blockedUser.name || 'User'} 
                    />
                    <AvatarFallback className="bg-accent/10 text-accent border border-accent/20">
                      {getInitials(blockedUser.name || blockedUser.email || 'User')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-foreground text-sm truncate">
                      {blockedUser.name || blockedUser.email || 'Unknown User'}
                    </h4>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary" className="text-xs">
                        {blockedUser.userType === 'consumer' ? 'Buyer' : 'Seller'}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Block Form */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="reason">
                  Reason for Blocking
                  <span className="text-muted-foreground text-xs ml-1">(Optional)</span>
                </Label>
                <Textarea
                  id="reason"
                  placeholder="Why are you blocking this user? (optional)"
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  rows={3}
                  maxLength={500}
                />
                <div className="text-xs text-muted-foreground text-right">
                  {reason.length}/500
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3 pt-4">
              <Button 
                variant="outline" 
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button 
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="bg-destructive hover:bg-destructive/90"
              >
                {isSubmitting ? "Blocking..." : "Block User"}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
