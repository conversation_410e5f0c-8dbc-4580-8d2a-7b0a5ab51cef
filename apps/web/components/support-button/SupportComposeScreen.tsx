"use client";

import { useState } from "react";
import { Send, Smile, Paperclip } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { SupportScreen } from "./types";
import { SupportHeader } from "./SupportHeader";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";

interface SupportComposeScreenProps {
  onClose: () => void;
  onNavigate: (screen: SupportScreen) => void;
  onSendMessage: (message: string) => Promise<void>;
  adminUsers?: Array<{
    _id: string;
    name: string;
    profileImage?: string;
  }>;
}

export function SupportComposeScreen({ 
  onClose, 
  onNavigate, 
  onSendMessage,
  adminUsers 
}: SupportComposeScreenProps) {
  const [message, setMessage] = useState("");

  const createTicket = useMutation(api.supportTickets.createSupportTicket);

  const handleSendMessage = async () => {
    if (!message.trim()) return;
    
    try {
      // Create a support ticket from the message
      await createTicket({
        subject: "Support Request",
        description: message,
        priority: "medium",
        category: "general",
      });
      
      setMessage("");
      // Navigate back to home after creating ticket
      onNavigate("home");
    } catch (error) {
      console.error("Failed to create support ticket:", error);
      // You can add toast notification here if you want
    }
  };

  return (
    <div className="bg-background flex flex-col h-full">
      {/* Header with MODA Team */}
      <SupportHeader 
        title="MODA Team"
        onClose={onClose}
        onBack={() => onNavigate("messages")}
        showBackButton={true}
        adminUsers={adminUsers}
        showTeamAvatars={true}
        showStatus={true}
      />

      {/* Main content area */}
      <div className="flex-1 p-4 bg-background overflow-y-auto">
        <div className="text-center mb-6">
          <p className="text-lg text-foreground font-medium">Want to chat with MODA?</p>
        </div>
        
        <div className="bg-muted/20 rounded-xl border border-border p-4 mb-6">
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-white text-xs font-medium">i</span>
            </div>
            <div className="text-sm text-foreground italic">
              Your message will be converted into a support ticket. Our team will review it and respond as soon as possible.
            </div>
          </div>
        </div>
      </div>

      {/* Chat input at the bottom - Intercom style */}
      <div className="px-4 pb-2 -mt-5 z-10">
        <div className="relative rounded-3xl shadow-lg border border-border overflow-hidden">
          {/* Input container */}
          <div className="relative min-h-[48px] max-h-[200px] p-2">
            {/* Hidden pre element for sizing (like Intercom) */}
            <pre 
              className="invisible whitespace-pre-wrap break-words p-3 pt-3 pb-1 pl-4 pr-12 text-sm leading-5 max-h-[158px] overflow-hidden font-sans"
            >
              {message}
            </pre>
            
            {/* Actual textarea */}
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Message..."
              rows={1}
              className="absolute inset-0 w-full h-full resize-none outline-none border-none bg-transparent p-3 pt-3 pb-1 pl-4 pr-12 text-sm leading-5 max-h-[158px] overflow-auto font-sans"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
            />
          </div>
          
          {/* Bottom action bar */}
          <div className="flex items-center gap-4 min-h-[32px] h-8 px-4 pb-2">
            <div className="flex items-center gap-4">
              {/* GIF and Attachment buttons - animate out when there's content */}
              <AnimatePresence>
                <div>
                  <motion.button 
                    key="smile-button"
                    className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                    layout
                  >
                    <Smile className="w-4 h-4 text-gray-500" />
                  </motion.button>
                </div>
                {!message.trim() && (
                  <>
                    <motion.button 
                      key="gif-button"
                      className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                      initial={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      transition={{ duration: 0.2 }}
                    >
                      <span className="text-xs font-medium text-gray-500">GIF</span>
                    </motion.button>
                    <motion.button 
                      key="attachment-button"
                      className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                      initial={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Paperclip className="w-4 h-4 text-gray-500" />
                    </motion.button>
                  </>
                )}
              </AnimatePresence>
            </div>
            
            {/* Right side spacer */}
            <div className="flex-1"></div>
            
            {/* Send button */}
            <button 
              onClick={handleSendMessage}
              className="p-1.5 bg-primary text-white rounded-full hover:bg-primary/90 transition-colors"
            >
              <Send className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
