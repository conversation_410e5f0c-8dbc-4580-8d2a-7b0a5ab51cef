"use client";

import { useState, useEffect, useRef } from "react";
import { Send, Smile, Paperclip, ChevronRight, FileText } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { SupportScreen, SupportItem, SupportMessage } from "./types";
import { SupportHeader } from "./SupportHeader";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";

interface SupportConversationScreenProps {
  onClose: () => void;
  onNavigate: (screen: SupportScreen) => void;
  conversation: { type: string; ticket: any };
  messages?: any[];
  onSendMessage: (message: string) => Promise<void>;
}

export function SupportConversationScreen({ 
  onClose, 
  onNavigate, 
  conversation,
  messages = [],
  onSendMessage
}: SupportConversationScreenProps) {
  const [message, setMessage] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch ticket messages if we have a ticket
  const ticketData = useQuery(
    api.supportTickets.getTicketMessages,
    conversation.type === "ticket" && conversation.ticket?._id 
      ? { ticketId: conversation.ticket._id }
      : "skip"
  );

  const addMessage = useMutation(api.supportTickets.addSupportMessage);

  // Auto-scroll to bottom when messages change
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [ticketData?.messages]);

  const handleSendMessage = async () => {
    if (!message.trim() || !conversation.ticket?._id) return;
    
    try {
      // Add message to the support ticket
      await addMessage({
        ticketId: conversation.ticket._id,
        content: message,
        messageType: "text",
      });
      
      setMessage("");
      // The message will automatically appear in the list due to Convex reactivity
    } catch (error) {
      console.error("Failed to send message:", error);
      // You can add toast notification here if you want
    }
  };

  const renderHeader = () => {
    if (conversation.type === "ticket" && ticketData?.ticket) {
      const ticket = ticketData.ticket;
      return (
        <div className="bg-primary px-4 py-3 border-b border-border flex-shrink-0">
          <div className="flex items-center space-x-3">
            <button 
              onClick={() => onNavigate("messages")}
              className="p-1 hover:bg-muted rounded-full transition-colors"
            >
              <ChevronRight className="w-5 h-5 text-accent-foreground rotate-180" />
            </button>
            
            <div className="flex-1">
              <h3 className="font-semibold text-primary-foreground">
                Ticket #{ticket.ticketNumber}
              </h3>
              <p className="text-sm text-primary-foreground/80">
                {ticket.subject}
              </p>
            </div>
          </div>
        </div>
      );
    }
    
    return null;
  };

  const renderMessages = () => {
    if (!ticketData?.messages) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-center p-8">
          <div className="w-12 h-12 text-muted-foreground/50 mb-3 flex items-center justify-center">
            <Send className="w-8 h-8" />
          </div>
          <h4 className="font-medium text-foreground mb-2">Loading messages...</h4>
        </div>
      );
    }

    if (ticketData.messages.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-center p-8">
          <div className="w-12 h-12 text-muted-foreground/50 mb-3 flex items-center justify-center">
            <Send className="w-8 h-8" />
          </div>
          <h4 className="font-medium text-foreground mb-2">No messages yet</h4>
          <p className="text-sm text-muted-foreground">
            Start the conversation by sending a message
          </p>
        </div>
      );
    }

    // Sort messages by creation time (oldest first, so newest appears at bottom)
    const sortedMessages = [...ticketData.messages].sort((a, b) => a._creationTime - b._creationTime);

    return (
      <div className="p-4 space-y-4 bg-background">
        {sortedMessages.map((msg: SupportMessage) => (
          <div 
            key={msg._id}
            className={`flex ${msg.senderType === "customer" ? "justify-end" : "justify-start"}`}
          >
            <div 
              className={`max-w-[80%] rounded-lg p-3 ${
                msg.senderType === "customer" 
                  ? "bg-primary text-primary-foreground" 
                  : "bg-muted text-foreground"
              }`}
            >
              <div className="text-sm">{msg.content}</div>
              <div className={`text-xs mt-1 ${
                msg.senderType === "customer" 
                  ? "text-primary-foreground/70" 
                  : "text-muted-foreground"
              }`}>
                {msg.senderType === "customer" ? "You" : "Support Team"} • {formatDistanceToNow(msg.updatedAt, { addSuffix: true })}
              </div>
            </div>
          </div>
        ))}
        {/* Invisible div for auto-scrolling to bottom */}
        <div ref={messagesEndRef} />
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full">
      {renderHeader()}

      {/* Messages container with proper scrolling */}
      <div className="flex-1 overflow-y-auto min-h-0">
        {renderMessages()}
      </div>

      {/* Message Input */}
      <div className="p-4 border-t border-border bg-background flex-shrink-0">
        <div className="relative">
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Add a note to this ticket..."
            className="w-full p-3 pl-4 pr-24 border-2 border-border rounded-full bg-background text-foreground placeholder:text-muted-foreground focus:border-primary focus:outline-none transition-colors"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
          />
          
          <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center space-x-2">
            <button className="p-1 hover:bg-muted rounded-full transition-colors">
              <Smile className="w-4 h-4 text-muted-foreground" />
            </button>
            <button className="p-1 hover:bg-muted rounded-full transition-colors">
              <Paperclip className="w-4 h-4 text-muted-foreground" />
            </button>
            <button 
              onClick={handleSendMessage}
              className="p-1.5 bg-primary text-primary-foreground rounded-full hover:bg-primary/90 transition-colors"
            >
              <Send className="w-3 h-3" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
