"use client";

import { useState, useEffect, useRef } from "react";
import { Send, ChevronRight, User, Clock, CheckCheck, Star, Tag, Plus, MoreVertical, Flag, Shield, Settings, UserX } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { SupportScreen } from "./types";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Card, CardContent } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Separator } from "@repo/ui/components/separator";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@repo/ui/components/dropdown-menu";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { ReportUserModal } from "./ReportUserModal";
import { BlockUserModal } from "./BlockUserModal";

interface UserConversationScreenProps {
  onClose: () => void;
  onNavigate: (screen: SupportScreen) => void;
  conversation: any;
  onSendMessage: (message: string) => Promise<void>;
}

export function UserConversationScreen({ 
  onClose, 
  onNavigate, 
  conversation,
  onSendMessage
}: UserConversationScreenProps) {
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [expandedMessages, setExpandedMessages] = useState<Set<string>>(new Set());
  const [showTagItemModal, setShowTagItemModal] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [showBlockModal, setShowBlockModal] = useState(false);
  const { user } = useAuth();
  const router = useRouter();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch messages for this conversation
  const messages = useQuery(
    api.messages.getMessagesByConversationId,
    conversation.conversationId ? {
      conversationId: conversation.conversationId,
    } : "skip"
  );

  // Fetch user rating information for the other participant
  const userRatingInfo = useQuery(
    api.userQueries.getUserRatingInfo,
    conversation.otherParticipant?._id ? {
      userId: conversation.otherParticipant._id,
    } : "skip"
  );

  // Fetch user transaction count
  const userTransactionCount = useQuery(
    api.userQueries.getUserTransactionCount,
    conversation.otherParticipant?._id ? {
      userId: conversation.otherParticipant._id,
    } : "skip"
  );

  // Fetch seller's products for tagging
  const sellerProducts = useQuery(
    api.sellerQueries.getSellerMarketplaceProducts,
    conversation.otherParticipant?._id && conversation.otherParticipant?.userType === 'seller' ? {
      sellerId: conversation.otherParticipant._id,
      limit: 50,
    } : "skip"
  );

  // Mark messages as read when conversation is opened
  const markAsRead = useMutation(api.messages.markMessagesAsRead);

  // Auto-scroll to bottom when messages change
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (conversation.conversationId && user?._id) {
      // Mark messages as read when conversation is opened
      markAsRead({ conversationId: conversation.conversationId })
        .then((result) => {
        })
        .catch((error) => {
          console.error("Error marking messages as read:", error);
        });
    } else {
    }
  }, [conversation.conversationId, user?._id, markAsRead]);

  const handleSendMessage = async () => {
    if (!message.trim()) return;
    
    setIsLoading(true);
    try {
      await onSendMessage(message);
      setMessage("");
    } catch (error) {
      console.error("Failed to send message:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const toggleMessageExpansion = (messageId: string) => {
    setExpandedMessages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(messageId)) {
        newSet.delete(messageId);
      } else {
        newSet.add(messageId);
      }
      return newSet;
    });
  };

  const handleTagItem = (product: any) => {
    setShowTagItemModal(false);
    // Add the product info to the message
    const messageWithProduct = `${message}\n\n[Product: ${product.title} - $${product.price}]`;
    setMessage(messageWithProduct);
  };

  const handleReportUser = () => {
    setShowReportModal(true);
  };

  const handleBlockUser = () => {
    setShowBlockModal(true);
  };

  const handleViewProfile = () => {
    const userId = conversation.otherParticipant?._id;
    if (userId) {
      // Navigate to the user profile page
      router.push(`/members/${userId}`);
    }
  };

  const handleViewShop = () => {
    const sellerId = conversation.otherParticipant?._id;
    if (sellerId) {
      // Navigate to the seller shop page
      router.push(`/seller/${sellerId}`);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center gap-0.5">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star 
            key={star} 
            className={`w-3.5 h-3.5 ${
              star <= rating 
                ? "fill-yellow-400 text-yellow-400" 
                : "text-muted-foreground/30"
            }`} 
          />
        ))}
      </div>
    );
  };

  const renderDropdown = () => {
    const otherParticipant = conversation.otherParticipant;
    const rating = userRatingInfo?.rating || 0;
    const reviewCount = userRatingInfo?.reviewCount || 0;
    const transactionCount = userTransactionCount?.totalTransactions || 0;
    const isSeller = otherParticipant?.userType === 'seller';

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MoreVertical className="h-4 w-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-64">
          {/* User Info Section */}
          <div className="p-4 border-b border-border">
            <div className="flex items-center gap-3 mb-3">
              <Avatar className="w-10 h-10">
                <AvatarImage 
                  src={otherParticipant?.profileImage} 
                  alt={otherParticipant?.name || 'User'} 
                />
                <AvatarFallback className="bg-accent/10 text-accent border border-accent/20">
                  {getInitials(otherParticipant?.name || 'User')}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-foreground text-sm truncate">
                  {otherParticipant?.name || otherParticipant?.email || 'Unknown User'}
                </h4>
                <p className="text-xs text-muted-foreground">
                  {otherParticipant?.userType === 'consumer' ? 'Buyer' : 'Seller'}
                </p>
              </div>
            </div>
            
            {/* Rating and Transaction Info - Only for Sellers */}
            {isSeller && (
              <div className="grid grid-cols-2 gap-3">
                <Card className="border-border/50">
                  <CardContent className="p-2">
                    <div className="flex flex-col items-center">
                      <span className="text-xs font-medium text-muted-foreground mb-1">Rating</span>
                      <div className="flex items-center flex-col gap-1">
                        <span className="text-sm font-semibold text-foreground">
                          {rating.toFixed(1)}
                        </span>
                        {renderStars(rating)}
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {reviewCount} review{reviewCount !== 1 ? 's' : ''}
                      </span>
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="border-border/50">
                  <CardContent className="p-2">
                    <div className="flex flex-col items-center">
                      <User className="w-3 h-3 text-muted-foreground mb-1" />
                      <span className="text-sm font-semibold text-foreground">
                        {transactionCount}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        Transaction{transactionCount !== 1 ? 's' : ''}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <DropdownMenuItem onClick={handleViewProfile}>
            <User className="mr-2 h-4 w-4" />
            <span>View Profile</span>
          </DropdownMenuItem>
          
          {isSeller && (
            <DropdownMenuItem onClick={handleViewShop}>
              <Tag className="mr-2 h-4 w-4" />
              <span>View Shop</span>
            </DropdownMenuItem>
          )}
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem onClick={handleReportUser}>
            <Flag className="mr-2 h-4 w-4" />
            <span>Report User</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={handleBlockUser}>
            <UserX className="mr-2 h-4 w-4" />
            <span>Block User</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  const renderHeader = () => {
    const otherParticipant = conversation.otherParticipant;
    const product = conversation.product;
    
    return (
      <div className="p-4 border-b border-border bg-background">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button 
              variant="ghost"
              size="sm"
              onClick={() => onNavigate("messages")}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4 text-muted-foreground rotate-180" />
            </Button>
            
            <div className="flex items-center space-x-3">
              <Avatar className="w-10 h-10">
                <AvatarImage 
                  src={otherParticipant?.profileImage} 
                  alt={otherParticipant?.name || 'User'} 
                />
                <AvatarFallback className="bg-accent/10 text-accent border border-accent/20">
                  {getInitials(otherParticipant?.name || 'User')}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex flex-col">
                <h3 className="font-medium text-foreground text-base leading-tight">
                  {otherParticipant?.name || otherParticipant?.email || 'Unknown User'}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {otherParticipant?.userType === 'consumer' ? 'Buyer' : 'Seller'}
                </p>
              </div>
            </div>
          </div>
          
          {/* Dropdown Menu */}
          <div className="relative">
            {renderDropdown()}
          </div>
        </div>
      </div>
    );
  };

  const renderProductCard = (product: any) => {
    if (!product) return null;
    
    return (
      <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg border border-border/50 mt-3 hover:bg-muted/50 transition-colors duration-200">
        <div className="w-12 h-12 bg-muted rounded-lg overflow-hidden flex-shrink-0">
          {product.image ? (
            <Image
              src={product.image}
              alt={product.name}
              width={48}
              height={48}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <span className="text-xs text-muted-foreground">No image</span>
            </div>
          )}
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-foreground text-sm truncate">
            {product.name}
          </h4>
          <p className="text-xs text-muted-foreground">
            {product.brand || 'Unknown Brand'}
          </p>
        </div>
        <div className="text-right">
          <p className="font-semibold text-foreground text-sm">
            ${product.price || '0.00'}
          </p>
        </div>
      </div>
    );
  };

  const renderTagItemModal = () => {
    if (!showTagItemModal || !sellerProducts) return null;

    const products = sellerProducts.products || [];

    return (
      <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50 p-4">
        <div className="bg-background rounded-lg border border-border max-w-md w-full max-h-[80vh] overflow-hidden shadow-lg">
          <div className="p-4 border-b border-border">
            <h3 className="text-lg font-medium text-foreground">Tag an Item</h3>
            <p className="text-sm text-muted-foreground mt-1">Select an item to include with your message</p>
          </div>
          
          <div className="p-4 max-h-96 overflow-y-auto">
            {products.length === 0 ? (
              <div className="text-center py-8">
                <Tag className="w-12 h-12 text-muted-foreground/50 mx-auto mb-3" />
                <p className="text-muted-foreground">No items available to tag</p>
              </div>
            ) : (
              <div className="space-y-2">
                {products.map((product: any) => (
                  <button
                    key={product._id}
                    onClick={() => handleTagItem(product)}
                    className="w-full flex items-center gap-3 p-3 rounded-lg border border-border hover:border-accent hover:bg-accent/5 transition-all duration-200 text-left"
                  >
                    <div className="w-12 h-12 bg-muted rounded-lg overflow-hidden flex-shrink-0">
                      {product.images && product.images.length > 0 ? (
                        <Image
                          src={product.images[0]}
                          alt={product.title}
                          width={48}
                          height={48}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-muted flex items-center justify-center">
                          <span className="text-xs text-muted-foreground">No image</span>
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-foreground text-sm truncate">
                        {product.title}
                      </h4>
                      <p className="text-xs text-muted-foreground">
                        {product.brand || 'Unknown Brand'}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-foreground text-sm">
                        ${product.price || '0.00'}
                      </p>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
          
          <div className="p-4 border-t border-border flex gap-3">
            <button
              onClick={() => setShowTagItemModal(false)}
              className="flex-1 px-4 py-2 border border-border rounded-lg text-foreground font-medium hover:bg-muted/30 transition-colors duration-200"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderMessages = () => {
    if (!messages) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-center p-8">
          <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mb-4">
            <Send className="w-8 h-8 text-muted-foreground/50" />
          </div>
          <h4 className="text-lg font-medium text-foreground mb-2">Loading messages...</h4>
          <p className="text-sm text-muted-foreground">Please wait while we load your conversation</p>
        </div>
      );
    }

    if (messages.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-center p-8">
          <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mb-4">
            <Send className="w-8 h-8 text-muted-foreground/50" />
          </div>
          <h4 className="text-lg font-medium text-foreground mb-2">No messages yet</h4>
          <p className="text-sm text-muted-foreground max-w-sm">
            Start the conversation by sending a message to connect with this user
          </p>
        </div>
      );
    }

    // Sort messages by creation time (oldest first, so newest appears at bottom)
    const sortedMessages = [...messages].sort((a, b) => a._creationTime - b._creationTime);
    
    // Group messages by date
    const groupedMessages = sortedMessages.reduce((groups: any, message: any) => {
      const date = new Date(message._creationTime).toLocaleDateString('en-US', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
      
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(message);
      return groups;
    }, {});

    return (
      <div className="p-4 space-y-4">
        {Object.entries(groupedMessages).map(([date, dateMessages]: [string, any]) => (
          <div key={date}>
            {/* Date separator */}
            <div className="flex justify-center mb-4">
              <span className="text-xs font-medium text-muted-foreground bg-muted/50 px-3 py-1 rounded-full">
                {date}
              </span>
            </div>
            
            {/* Messages for this date */}
            <div className="space-y-3">
              {dateMessages.map((msg: any) => {
                const isOwnMessage = msg.senderId === user?._id;
                const isExpanded = expandedMessages.has(msg._id);
                
                return (
                  <motion.div
                    key={msg._id}
                    className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} group`}
                    layout
                  >
                    <div className={`flex flex-col max-w-[75%] ${isOwnMessage ? 'items-end' : 'items-start'}`}>
                      <div className={`flex items-end gap-2 ${isOwnMessage ? 'flex-row-reverse' : 'flex-row'}`}>
                        {/* Avatar for received messages */}
                        {!isOwnMessage && (
                          <Avatar className="flex-shrink-0 w-8 h-8">
                            <AvatarImage 
                              src={conversation.otherParticipant?.profileImage} 
                              alt="User" 
                            />
                            <AvatarFallback className="text-xs bg-accent/10 text-accent border border-accent/20">
                              {getInitials(conversation.otherParticipant?.name || 'User')}
                            </AvatarFallback>
                          </Avatar>
                        )}
                        
                        {/* Message content */}
                        <div className={`flex flex-col ${isOwnMessage ? 'items-end' : 'items-start'}`}>
                          {/* Sender name and timestamp */}
                          <div className={`flex items-center gap-2 mb-1 ${isOwnMessage ? 'flex-row-reverse' : 'flex-row'}`}>
                            <span className="text-xs font-medium text-foreground">
                              {isOwnMessage ? 'You' : (conversation.otherParticipant?.name || 'User')}
                            </span>
                            <div className="w-1 h-1 bg-muted-foreground rounded-full"></div>
                            <span className="text-xs text-muted-foreground">
                              {new Date(msg._creationTime).toLocaleTimeString('en-US', {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true
                              })}
                            </span>
                          </div>
                          
                          {/* Message bubble */}
                          <motion.div 
                            className={`relative rounded-lg px-3 py-2 shadow-sm transition-all duration-200 cursor-pointer ${
                              isOwnMessage
                                ? 'bg-primary text-primary-foreground rounded-br-md hover:bg-primary/90'
                                : 'bg-muted text-foreground rounded-bl-md hover:bg-muted/80'
                            }`}
                            onClick={() => toggleMessageExpansion(msg._id)}
                            whileHover={{ scale: 1.01 }}
                            whileTap={{ scale: 0.99 }}
                          >
                            <p className="text-sm leading-relaxed break-words">
                              {msg.content}
                            </p>
                          </motion.div>
                        </div>
                      </div>
                      
                      {/* Expanded metadata below bubble */}
                      <AnimatePresence>
                        {isExpanded && (
                          <motion.div
                            initial={{ opacity: 0, height: 0, y: -5 }}
                            animate={{ opacity: 1, height: "auto", y: 0 }}
                            exit={{ opacity: 0, height: 0, y: -5 }}
                            transition={{ duration: 0.2, ease: "easeInOut" }}
                            className={`mt-2 px-3 py-2 rounded-lg bg-muted/30 border border-border/50 ${
                              isOwnMessage ? 'text-right' : 'text-left'
                            }`}
                          >
                            <div className={`flex flex-col gap-1 text-xs text-muted-foreground ${
                              isOwnMessage ? 'items-end' : 'items-start'
                            }`}>
                              <div className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                <span>
                                  {formatDistanceToNow(msg._creationTime, { addSuffix: true })}
                                </span>
                              </div>
                              
                              {isOwnMessage && (
                                <div className="flex items-center gap-1">
                                  {msg.isRead ? (
                                    <>
                                      <CheckCheck className="w-3 h-3 text-green-500" />
                                      <span className="text-green-600">Read</span>
                                    </>
                                  ) : (
                                    <span>Delivered</span>
                                  )}
                                </div>
                              )}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        ))}
        {/* Invisible div for auto-scrolling to bottom */}
        <div ref={messagesEndRef} />
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full bg-background">
      {renderHeader()}
      
      {/* Messages container with proper scrolling */}
      <div className="flex-1 overflow-y-auto min-h-0">
        {renderMessages()}
      </div>
      
      {/* Message Input */}
      <div className="p-2 border-t border-border bg-background flex-shrink-0">
        <div className="relative">
          <Input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Type your message..."
            className="w-full p-3 pl-4 pr-28 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:border-accent focus:outline-none transition-all duration-200"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
          />
          
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-2">
            {/* Tag Item Button - Only show for sellers */}
            {conversation.otherParticipant?.userType === 'seller' && (
              <Button
                onClick={() => setShowTagItemModal(true)}
                variant="ghost"
                size="icon"
                className="p-2 text-muted-foreground hover:text-accent hover:bg-accent/10 !rounded-xl transition-all duration-200"
                title="Tag an item"
              >
                <Tag className="w-4 h-4" />
              </Button>
            )}
            
            {/* Send Button */}
            <Button 
              onClick={handleSendMessage}
              disabled={!message.trim() || isLoading}
              className="p-2 bg-primary text-primary-foreground !rounded-xl hover:bg-primary/90 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>
      </div>
      
      {/* Tag Item Modal */}
      {renderTagItemModal()}

      {/* Report User Modal */}
      <ReportUserModal
        isOpen={showReportModal}
        onClose={() => setShowReportModal(false)}
        reportedUser={{
          _id: conversation.otherParticipant?._id || "",
          name: conversation.otherParticipant?.name,
          email: conversation.otherParticipant?.email,
          profileImage: conversation.otherParticipant?.profileImage,
          userType: conversation.otherParticipant?.userType,
        }}
        reportContext="chat_conversation"
      />

      {/* Block User Modal */}
      <BlockUserModal
        isOpen={showBlockModal}
        onClose={() => setShowBlockModal(false)}
        blockedUser={{
          _id: conversation.otherParticipant?._id || "",
          name: conversation.otherParticipant?.name,
          email: conversation.otherParticipant?.email,
          profileImage: conversation.otherParticipant?.profileImage,
          userType: conversation.otherParticipant?.userType,
        }}
      />
    </div>
  );
}
