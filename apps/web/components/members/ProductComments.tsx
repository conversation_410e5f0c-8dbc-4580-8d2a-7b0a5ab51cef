"use client";

import { useState } from "react";
import Link from "next/link";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Textarea } from "@repo/ui/components/textarea";
import { ProfileImage } from "@/components/common/ProfileImage";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import { format } from "date-fns";
import { 
  MessageSquare, 
  ThumbsUp, 
  Reply as ReplyIcon,
  MoreVertical,
  Flag
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { Badge } from "@repo/ui/components/badge";

interface ProductCommentsProps {
  productId: string;
}

export default function ProductComments({ productId }: ProductCommentsProps) {
  const { user: currentUser } = useAuth();
  const [commentContent, setCommentContent] = useState("");
  const [replyContent, setReplyContent] = useState<{ [key: string]: string }>({});
  const [showReplyForm, setShowReplyForm] = useState<{ [key: string]: boolean }>({});
  const [cursor, setCursor] = useState<string | null>(null);

  const comments = useQuery(api.members.getProductComments, {
    productId: productId as any,
    paginationOpts: { numItems: 10, cursor },
  });

  const addComment = useMutation(api.members.addProductComment);
  const toggleLike = useMutation(api.members.toggleCommentLike);

  const handleAddComment = async () => {
    if (!currentUser) {
      toast.error("Please sign in to comment");
      return;
    }

    if (!commentContent.trim()) {
      toast.error("Please write a comment");
      return;
    }

    try {
      await addComment({
        productId: productId as any,
        content: commentContent,
      });
      setCommentContent("");
      toast.success("Comment added!");
    } catch (error: any) {
      toast.error(error.message || "Failed to add comment");
    }
  };

  const handleAddReply = async (parentCommentId: string) => {
    if (!currentUser) {
      toast.error("Please sign in to reply");
      return;
    }

    const content = replyContent[parentCommentId];
    if (!content?.trim()) {
      toast.error("Please write a reply");
      return;
    }

    try {
      await addComment({
        productId: productId as any,
        content,
        parentCommentId: parentCommentId as any,
      });
      setReplyContent({ ...replyContent, [parentCommentId]: "" });
      setShowReplyForm({ ...showReplyForm, [parentCommentId]: false });
      toast.success("Reply added!");
    } catch (error: any) {
      toast.error(error.message || "Failed to add reply");
    }
  };

  const handleToggleLike = async (commentId: string) => {
    if (!currentUser) {
      toast.error("Please sign in to like comments");
      return;
    }

    try {
      const result = await toggleLike({ commentId: commentId as any });
      if (result.liked) {
        toast.success("Comment liked!");
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to like comment");
    }
  };

  return (
    <Card className="mt-8">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Member Comments
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Add Comment Form */}
        {currentUser ? (
          <div className="mb-6">
            <div className="flex gap-3">
              <ProfileImage
                storageId={currentUser.profileImage}
                name={currentUser.name}
                size="sm"
              />
              <div className="flex-1">
                <Textarea
                  placeholder="Share your thoughts about this product..."
                  value={commentContent}
                  onChange={(e) => setCommentContent(e.target.value)}
                  rows={3}
                  className="mb-2"
                />
                <Button onClick={handleAddComment} size="sm">
                  Post Comment
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="mb-6 p-4 bg-muted rounded-lg text-center">
            <p className="text-sm text-muted-foreground mb-2">
              Sign in to join the discussion
            </p>
            <Button size="sm" asChild>
              <Link href="/login">Sign In</Link>
            </Button>
          </div>
        )}

        {/* Comments List */}
        <div className="space-y-4">
          {comments?.page && comments.page.length > 0 ? (
            comments.page.map((comment: any) => (
              <div key={comment._id} className="border rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <Link href={`/members/${comment.userId}`}>
                    <ProfileImage
                      storageId={comment.user?.profileImage}
                      name={comment.user?.name}
                      size="sm"
                    />
                  </Link>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center gap-2">
                        <Link 
                          href={`/members/${comment.userId}`}
                          className="font-semibold hover:underline"
                        >
                          {comment.memberProfile?.displayName || comment.user?.name}
                        </Link>
                        {comment.user?.userType === "seller" && (
                          <Badge variant="secondary" className="text-xs">
                            Seller
                          </Badge>
                        )}
                        <span className="text-xs text-muted-foreground">
                          {format(new Date(comment.updatedAt), "MMM d, yyyy")}
                        </span>
                        {comment.isEdited && (
                          <span className="text-xs text-muted-foreground">(edited)</span>
                        )}
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Flag className="mr-2 h-4 w-4" />
                            Report
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    
                    <p className="text-sm mb-2">{comment.content}</p>
                    
                    <div className="flex items-center gap-4">
                      <button
                        onClick={() => handleToggleLike(comment._id)}
                        className="flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors"
                      >
                        <ThumbsUp className="h-4 w-4" />
                        <span>{comment.likes || 0}</span>
                      </button>
                      <button
                        onClick={() => setShowReplyForm({ 
                          ...showReplyForm, 
                          [comment._id]: !showReplyForm[comment._id] 
                        })}
                        className="flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors"
                      >
                        <ReplyIcon className="h-4 w-4" />
                        <span>Reply</span>
                      </button>
                    </div>

                    {/* Reply Form */}
                    {showReplyForm[comment._id] && (
                      <div className="mt-3 pl-4 border-l-2">
                        <Textarea
                          placeholder="Write a reply..."
                          value={replyContent[comment._id] || ""}
                          onChange={(e) => setReplyContent({
                            ...replyContent,
                            [comment._id]: e.target.value
                          })}
                          rows={2}
                          className="mb-2"
                        />
                        <div className="flex gap-2">
                          <Button 
                            size="sm"
                            onClick={() => handleAddReply(comment._id)}
                          >
                            Post Reply
                          </Button>
                          <Button 
                            size="sm"
                            variant="outline"
                            onClick={() => setShowReplyForm({ 
                              ...showReplyForm, 
                              [comment._id]: false 
                            })}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Replies */}
                    {comment.replies && comment.replies.length > 0 && (
                      <div className="mt-4 space-y-3 pl-4 border-l-2">
                        {comment.replies.map((reply: any) => (
                          <div key={reply._id} className="flex items-start gap-2">
                            <Link href={`/members/${reply.userId}`}>
                              <ProfileImage
                                storageId={reply.user?.profileImage}
                                name={reply.user?.name}
                                size="xs"
                              />
                            </Link>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <Link 
                                  href={`/members/${reply.userId}`}
                                  className="text-sm font-semibold hover:underline"
                                >
                                  {reply.memberProfile?.displayName || reply.user?.name}
                                </Link>
                                <span className="text-xs text-muted-foreground">
                                  {format(new Date(reply.updatedAt), "MMM d, yyyy")}
                                </span>
                              </div>
                              <p className="text-sm">{reply.content}</p>
                              <button
                                onClick={() => handleToggleLike(reply._id)}
                                className="flex items-center gap-1 text-xs text-muted-foreground hover:text-foreground transition-colors mt-1"
                              >
                                <ThumbsUp className="h-3 w-3" />
                                <span>{reply.likes || 0}</span>
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
              <p className="text-muted-foreground">
                No comments yet. Be the first to share your thoughts!
              </p>
            </div>
          )}
        </div>

        {/* Load More */}
        {comments && !comments.isDone && (
          <div className="flex justify-center mt-6">
            <Button
              variant="outline"
              onClick={() => setCursor(comments.continueCursor)}
            >
              Load More Comments
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
