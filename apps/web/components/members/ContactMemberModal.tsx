"use client";

import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { useAuth } from "@/hooks/useBetterAuth";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { Textarea } from "@repo/ui/components/textarea";
import { Label } from "@repo/ui/components/label";
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar";
import { Badge } from "@repo/ui/components/badge";
import { MessageCircle, Send, Shield, Star, User, UserX } from "lucide-react";
import { toast } from "sonner";

interface Member {
  _id: string;
  name: string;
  userType: string;
  profileImage?: string;
  rating?: number;
  reviewCount?: number;
  isVerified?: boolean;
  businessName?: string;
}

interface ContactMemberModalProps {
  member: Member;
  children?: React.ReactNode;
}

export function ContactMemberModal({ member, children }: ContactMemberModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();

  const sendMessage = useMutation(api.messages.sendMessage);

  // Check if user is blocked
  const isUserBlocked = useQuery(
    api.reports.isUserBlocked,
    user?._id ? { userId: member._id as Id<"users"> } : "skip"
  );

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleSendMessage = async () => {
    if (!user) {
      toast.error("Please sign in to contact this member");
      return;
    }

    if (!message.trim()) {
      toast.error("Please enter a message");
      return;
    }

    if (user._id === member._id) {
      toast.error("You cannot message yourself");
      return;
    }

    // Check if user is blocked
    if (isUserBlocked) {
      toast.error("You cannot contact this user");
      return;
    }

    setIsLoading(true);

    try {
      await sendMessage({
        recipientId: member._id as Id<"users">,
        content: message.trim(),
        messageType: "text",
      });

      toast.success("Message sent successfully!");
      setMessage("");
      setIsOpen(false);
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error("Failed to send message. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const defaultMessage = `Hi! I'd like to connect with you.`;

  // If user is blocked, show blocked state
  if (isUserBlocked) {
    return (
      <Button 
        className="w-full bg-gray-100 text-gray-500 cursor-not-allowed" 
        disabled
      >
        <UserX className="w-5 h-5 mr-2" />
        User Blocked
      </Button>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button className="w-full bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200 rounded-xl h-12">
            <MessageCircle className="w-5 h-5 mr-2" />
            Contact Member
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Contact Member</DialogTitle>
          <DialogDescription>
            Send a message to connect with this member.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Member Info */}
          <div className="flex items-center space-x-3 p-4 bg-neutral-50 dark:bg-neutral-800 rounded-xl">
            <Avatar className="w-12 h-12">
              <AvatarImage src={member.profileImage} alt={member.name} />
              <AvatarFallback className="bg-gradient-to-br from-neutral-100 to-neutral-200 dark:from-neutral-800 dark:to-neutral-700">
                {getInitials(member.name)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h4 className="font-semibold text-black dark:text-white">
                  {member.businessName || member.name}
                </h4>
                {member.isVerified && (
                  <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-1">
                    <Shield className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                )}
                <Badge variant="outline" className="text-xs">
                  <User className="w-3 h-3 mr-1" />
                  {member.userType}
                </Badge>
              </div>
              {member.rating && member.rating > 0 && (
                <div className="flex items-center space-x-1 mt-1">
                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm text-neutral-600 dark:text-neutral-400">
                    {member.rating.toFixed(1)} ({member.reviewCount || 0} reviews)
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Message Input */}
          <div className="space-y-3">
            <Label htmlFor="message">Your message</Label>
            <Textarea
              id="message"
              placeholder={defaultMessage}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={4}
              className="resize-none"
            />
            <p className="text-xs text-neutral-500 dark:text-neutral-400">
              Be respectful and specific about what you'd like to discuss.
            </p>
          </div>

          {/* Actions */}
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="flex-1"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSendMessage}
              disabled={isLoading || !message.trim()}
              className="flex-1"
            >
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              ) : (
                <Send className="w-4 h-4 mr-2" />
              )}
              Send Message
            </Button>
          </div>

          {!user && (
            <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-xl">
              <p className="text-sm text-yellow-800 dark:text-yellow-300">
                You need to sign in to contact members.{" "}
                <a href="/login" className="underline font-medium">
                  Sign in here
                </a>
              </p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
