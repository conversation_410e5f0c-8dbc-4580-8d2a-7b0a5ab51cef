"use client";

import { useState } from "react";
import Link from "next/link";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Input } from "@repo/ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { ProfileImage } from "@/components/common/ProfileImage";
import { 
  Search, 
  Building2, 
  MapPin, 
  ShoppingBag,
  MessageSquare,
  Award,
  Users,
  Clock,
  TrendingUp,
  ChevronLeft,
  ChevronRight,
  ArrowUpDown,
  ArrowUp,
  ArrowDown
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import { format } from "date-fns";

type SortType = "name" | "joinDate" | "lastActive" | "userType" | "subscriptionStatus";

export default function MemberDirectory() {
  const [searchTerm, setSearchTerm] = useState("");
  const [userType, setUserType] = useState("");
  const [subscriptionStatus, setSubscriptionStatus] = useState("");
  const [sortBy, setSortBy] = useState<SortType>("joinDate");
  const [cursor, setCursor] = useState<string | null>(null);
  const [pageSize, setPageSize] = useState(25);

  // Get real members data
  const members = useQuery(api.members.getRealMembers, {
    searchTerm: searchTerm || undefined,
    userType: userType as any || undefined,
    subscriptionStatus: subscriptionStatus as any || undefined,
    sortBy,
    paginationOpts: { numItems: pageSize, cursor },
  });

  // Get community stats for summary
  const communityStats = useQuery(api.members.getCommunityStats, {});

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCursor(null); // Reset pagination
  };

  const handleUserTypeFilter = (value: string) => {
    setUserType(value === "all" ? "" : value);
    setCursor(null);
  };

  const handleSubscriptionFilter = (value: string) => {
    setSubscriptionStatus(value === "all" ? "" : value);
    setCursor(null);
  };

  const handleSortChange = (newSort: SortType) => {
    setSortBy(newSort);
    setCursor(null);
  };

  const handlePageSizeChange = (value: string) => {
    setPageSize(parseInt(value));
    setCursor(null);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
      case "trial":
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Trial</Badge>;
      case "inactive":
        return <Badge variant="outline" className="bg-gray-100 text-gray-800">Inactive</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getUserTypeBadge = (type: string) => {
    switch (type) {
      case "seller":
        return <Badge variant="default" className="bg-purple-100 text-purple-800">Seller</Badge>;
      case "admin":
        return <Badge variant="destructive">Admin</Badge>;
      case "consumer":
        return <Badge variant="outline">Consumer</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  const getSortIcon = (column: SortType) => {
    if (sortBy === column) {
      return <ArrowUp className="h-4 w-4" />;
    }
    return <ArrowUpDown className="h-4 w-4 opacity-50" />;
  };

  // Calculate pagination info
  const currentPage = cursor ? Math.floor(parseInt(cursor) / pageSize) + 1 : 1;
  const totalPages = members?.totalCount ? Math.ceil(members.totalCount / pageSize) : 1;
  const startIndex = cursor ? parseInt(cursor) : 0;
  const endIndex = Math.min(startIndex + pageSize, members?.totalCount || 0);

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-2xl font-bold">{members?.totalCount || 0}</p>
                <p className="text-sm text-muted-foreground">Total Members</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-2xl font-bold">{communityStats?.onlineMembers || 0}</p>
                <p className="text-sm text-muted-foreground">Active Today</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-2xl font-bold">{communityStats?.totalPosts || 0}</p>
                <p className="text-sm text-muted-foreground">Forum Posts</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <ShoppingBag className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-2xl font-bold">
                  {members?.page?.filter(m => m.userType === "seller").length || 0}
                </p>
                <p className="text-sm text-muted-foreground">Sellers</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Member Directory</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search members by name or email..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={userType} onValueChange={handleUserTypeFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="All User Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All User Types</SelectItem>
                <SelectItem value="consumer">Consumer</SelectItem>
                <SelectItem value="seller">Seller</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={subscriptionStatus} onValueChange={handleSubscriptionFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="All Subscriptions" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Subscriptions</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="trial">Trial</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>

            <Select value={pageSize.toString()} onValueChange={handlePageSizeChange}>
              <SelectTrigger className="w-full md:w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Members Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">#</TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      onClick={() => handleSortChange("name")}
                      className="h-8 p-0 font-semibold hover:bg-transparent"
                    >
                      Member
                      {getSortIcon("name")}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      onClick={() => handleSortChange("userType")}
                      className="h-8 p-0 font-semibold hover:bg-transparent"
                    >
                      Type
                      {getSortIcon("userType")}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      onClick={() => handleSortChange("subscriptionStatus")}
                      className="h-8 p-0 font-semibold hover:bg-transparent"
                    >
                      Status
                      {getSortIcon("subscriptionStatus")}
                    </Button>
                  </TableHead>
                  <TableHead>Activity</TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      onClick={() => handleSortChange("joinDate")}
                      className="h-8 p-0 font-semibold hover:bg-transparent"
                    >
                      Joined
                      {getSortIcon("joinDate")}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      onClick={() => handleSortChange("lastActive")}
                      className="h-8 p-0 font-semibold hover:bg-transparent"
                    >
                      Last Active
                      {getSortIcon("lastActive")}
                    </Button>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {members?.page && members.page.length > 0 ? (
                  members.page.map((member: any, index: number) => (
                    <TableRow key={member._id} className="hover:bg-muted/50">
                      <TableCell className="font-medium text-muted-foreground">
                        {startIndex + index + 1}
                      </TableCell>
                      <TableCell>
                        <Link 
                          href={`/members/${member._id}`}
                          className="flex items-center gap-3 hover:text-primary transition-colors"
                        >
                          <ProfileImage
                            storageId={member.profileImage}
                            name={member.displayName || member.name}
                            size="sm"
                          />
                          <div>
                            <div className="font-medium">
                              {member.displayName || member.name}
                            </div>
                            <div className="text-sm text-muted-foreground flex items-center gap-2">
                              {member.company && (
                                <div className="flex items-center gap-1">
                                  <Building2 className="h-3 w-3" />
                                  <span className="truncate max-w-32">{member.company}</span>
                                </div>
                              )}
                              {member.location && (
                                <div className="flex items-center gap-1">
                                  <MapPin className="h-3 w-3" />
                                  <span>{member.location}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </Link>
                      </TableCell>
                      <TableCell>
                        {getUserTypeBadge(member.userType)}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(member.subscriptionStatus)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          {member.totalForumPosts > 0 && (
                            <div className="flex items-center gap-1">
                              <MessageSquare className="h-3 w-3" />
                              <span>{member.totalForumPosts}</span>
                            </div>
                          )}
                          {member.activeProductsCount > 0 && (
                            <div className="flex items-center gap-1">
                              <ShoppingBag className="h-3 w-3" />
                              <span>{member.activeProductsCount}</span>
                            </div>
                          )}
                          {member.helpfulVotes > 0 && (
                            <div className="flex items-center gap-1">
                              <Award className="h-3 w-3" />
                              <span>{member.helpfulVotes}</span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {format(new Date(member.memberSince), "MMM d, yyyy")}
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {member.lastActiveAt 
                          ? format(new Date(member.lastActiveAt), "MMM d, yyyy")
                          : "Never"
                        }
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      <div className="flex flex-col items-center gap-2">
                        <Users className="h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground">
                          {searchTerm || userType || subscriptionStatus 
                            ? "No members found matching your filters" 
                            : "No members found"
                          }
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              Showing {startIndex + 1} to {endIndex} of {members?.totalCount || 0} members
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCursor(null)}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              
              <div className="flex items-center gap-1">
                <span className="text-sm">
                  Page {currentPage} of {totalPages}
                </span>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCursor(members?.continueCursor || null)}
                disabled={members?.isDone}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}