"use client";

import { useState, useEffect, useRef } from "react";
import { Search, User, LogOut, ShoppingBag, Heart, Sun, Moon, Monitor, Check, Menu, X, MessageSquare, Package, Shield, Clock, Car, Crown, Palette } from "lucide-react";
import { useAuth } from "@/hooks/useBetterAuth";
import { useRecentSearches } from "@/hooks/useRecentSearches";
import { useMutation, useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Button } from "@repo/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from "@repo/ui/components/dropdown-menu";
import { Input } from "@repo/ui/components/input";
import { Badge } from "@repo/ui/components/badge";
import Link from "next/link";
import { CartDropdown } from "./CartDropdown";
import { SearchResultsPopover } from "./SearchResultsPopover";
import { NotificationDropdown } from "./NotificationDropdown";
import { ProfileImage } from "@/components/common";
import { useTheme } from "next-themes";
import { SellerApplicationModal } from "@/components/seller/SellerApplicationModal";
import Image from "next/image";

interface MarketplaceHeaderProps {
  onSearch?: (query: string) => void;
  marketplaceType?: "moda-watch" | "real-watch-buyers" | "moda-car" | "moda-lifestyle" | "moda-misc";
}

export function MarketplaceHeader({ onSearch, marketplaceType }: MarketplaceHeaderProps) {
  const { user, signOut } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const { theme, setTheme } = useTheme();
  const { addSearch } = useRecentSearches();

  // Marketplace constants
  const MARKETPLACE_ICONS = {
    "marketplace": ShoppingBag,
    "moda-watch": Clock,
    "real-watch-buyers": Clock,
    "moda-car": Car,
    "moda-lifestyle": Crown,
    "moda-misc": Palette,
  };

  const MARKETPLACE_LABELS = {
    "marketplace": "Marketplace",
    "moda-watch": "Moda Watch Club",
    "real-watch-buyers": "Real Watch Buyers",
    "moda-car": "Moda Car Club",
    "moda-lifestyle": "Moda Lifestyle Club",
    "moda-misc": "Moda Misc Club",
  };
  
  // Check if user has an application
  const userApplication = useQuery(
    api.sellerApplicationsSimple.getUserApplication,
    user?.email ? { email: user.email } : "skip"
  );
  
  // Track search events
  const trackSearchEvent = useMutation(api.productQueries.trackSearchEvent);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (searchQuery.trim()) {
      // Track search event for analytics
      try {
        await trackSearchEvent({
          query: searchQuery.trim(),
          resultCount: 0, // We don't have results yet from header search
          source: "search_bar",
        });
      } catch (error) {
        console.warn('Failed to track search event:', error);
      }
      
      // Save to search history
      await addSearch(searchQuery.trim());
      
      // Call the onSearch callback
      onSearch?.(searchQuery);
    }
    
    setShowSearchResults(false);
  };

  const handleSellerSearch = (query: string) => {
    // Navigate to seller directory with search query
    window.location.href = `/seller/directory?search=${encodeURIComponent(query)}`;
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    setShowSearchResults(query.length > 0 || isSearchFocused);
  };

  const handleSearchFocus = () => {
    setIsSearchFocused(true);
    setShowSearchResults(true);
  };

  const handleSearchBlur = () => {
    setIsSearchFocused(false);
    setTimeout(() => setShowSearchResults(false), 200);
  };

  const closeSearchResults = () => {
    setShowSearchResults(false);
    setIsSearchFocused(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        closeSearchResults();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border bg-card backdrop-blur-sm">
      <div className="container mx-auto px-6 h-20 flex items-center justify-between">
        {/* Logo and Navigation */}
        <div className="flex items-center space-x-8">
          <Link href="/" className="flex items-center">
            <div className="flex items-center">
                <Image 
                  src="/logo.png" 
                  alt="Moda Logo" 
                  width={100} 
                  height={100} 
                />
            </div>
          </Link>
          
          {/* Main Navigation */}
          <nav className="hidden lg:flex items-center space-x-6">
            <Link 
              href="/dashboard" 
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Dashboard
            </Link>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <span className="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">
                  Marketplaces
                </span>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="center" className="w-64 bg-card rounded-2xl p-2 shadow-xl border border-border/50">
                <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground">
                  <Link href="/marketplace" className="flex items-center">
                    <svg className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    <span className="group-hover:text-accent-foreground transition-colors">General</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground">
                  <Link href="/marketplace/moda-watch" className="flex items-center">
                    <svg className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="group-hover:text-accent-foreground transition-colors">Moda Watch Club</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground">
                  <Link href="/marketplace/real-watch-buyers" className="flex items-center">
                    <svg className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="group-hover:text-accent-foreground transition-colors">Real Watch Buyers</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground">
                  <Link href="/marketplace/moda-car" className="flex items-center">
                    <svg className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                    </svg>
                    <span className="group-hover:text-accent-foreground transition-colors">Moda Car Club</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground">
                  <Link href="/marketplace/moda-lifestyle" className="flex items-center">
                    <svg className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                    </svg>
                    <span className="group-hover:text-accent-foreground transition-colors">Moda Lifestyle Club</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground">
                  <Link href="/marketplace/moda-misc" className="flex items-center">
                    <svg className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                    </svg>
                    <span className="group-hover:text-accent-foreground transition-colors">Moda Misc Club</span>
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Link 
              href="/auctions" 
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Auctions
            </Link>
            <Link 
              href="/forum/marketplaces" 
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Forum
            </Link>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <span className="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">
                  Services
                </span>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="center" className="w-56 bg-card rounded-2xl p-2 shadow-xl border border-border/50">
                <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground">
                  <Link href="/disputes" className="flex items-center">
                    <svg className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l-3-9m3 9l3-9" />
                    </svg>
                    <span className="group-hover:text-accent-foreground transition-colors">Dispute Center</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground">
                  <Link href="/middleman" className="flex items-center">
                    <svg className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                    <span className="group-hover:text-accent-foreground transition-colors">Middleman Service</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground">
                  <Link href="/warranty" className="flex items-center">
                    <svg className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    <span className="group-hover:text-accent-foreground transition-colors">Warranty Check</span>
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </nav>
        </div>

        {/* Search Bar */}
        <div ref={searchRef} className="flex-1 max-w-2xl mx-8 relative">
          <form onSubmit={handleSearch}>
            <div className="relative"> 
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search luxury watches, handbags, sneakers..."
                value={searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                onFocus={handleSearchFocus}
                onBlur={handleSearchBlur}
                className="text-foreground pl-12 pr-6 py-5 w-full placeholder:text-muted-foreground focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-base transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20"
              />
            </div>
          </form>
          
          {/* Search Results Popover */}
          <SearchResultsPopover
            isOpen={showSearchResults}
            onClose={closeSearchResults}
            searchQuery={searchQuery}
            onSearchChange={handleSearchChange}
            onSellerSearch={handleSellerSearch}
          />
        </div>

        {/* User Actions */}
        <div className="flex items-center space-x-4">
          {/* Apply to Sell Button - Only show if not already a seller */}
          {user && user.userType !== "seller" && user.userType !== "admin" && (
            <SellerApplicationModal>
              <Button variant="outline" size="xl" className="hidden lg:flex px-6 py-2.5 border-2 border-border rounded-full transition-all duration-300 font-medium text-xs">
                {userApplication ? "Application Status" : "Apply to Sell"}
              </Button>
            </SellerApplicationModal>
          )}

          {/* Shopping Cart Dropdown */}
          <CartDropdown />

          {/* Notifications Dropdown */}
          <NotificationDropdown />

          {/* User Menu */}
                  <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
          <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="xl" className="flex items-center p-3 rounded-xl font-light group hover:bg-muted/50">
                <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center group-hover:bg-primary/20 transition-colors duration-300 border border-border">
                  <ProfileImage
                    storageId={user?.profileImage}
                    betterAuthImage={user?.image}
                    name={user?.name}
                    size="sm"
                    className="w-8 h-8"
                  />
                </div>
                <div className="hidden md:flex flex-row items-center gap-2 text-left ">
                  <p className="text-sm font-medium text-foreground">
                    {user?.name || "Account"}
                  </p>
                  <Badge variant="secondary" className="text-[10px] uppercase font-light !p-0.5 rounded-xl border border-border">
                    {user?.subscriptionStatus === "active" ? "Premium Member" : "Trial"}
                  </Badge>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-72 bg-card rounded-2xl p-0 shadow-xl border border-border/50"
              style={{ 
                boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                animation: 'dropdownSlideIn 0.15s ease-out'
              }}
            >
              <div className="p-2 border-b border-border">
                <div className="px-3 py-2">
                  <p className="text-sm font-medium text-foreground">{user?.name}</p>
                  <p className="text-xs text-foreground mt-1">{user?.email}</p>
                  <div className="flex items-center mt-2">
                    <Badge
                      variant={user?.subscriptionStatus === "active" ? "default" : "secondary"}
                      className="text-xs px-2 py-1 font-medium rounded-xl border border-border"
                    >
                      {user?.subscriptionStatus === "active" ? "Premium Member" : "Trial Account"}
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="p-2">
                <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground">
                  <Link href={`/members/${user?.userId || user?._id}`} className="flex items-center">
                    <User className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" />
                    <span className="group-hover:text-accent-foreground transition-colors">My Profile</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground">
                  <Link href="/settings" className="flex items-center">
                    <svg className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span className="group-hover:text-accent-foreground transition-colors">Account Settings</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground">
                  <Link href="/orders" className="flex items-center">
                    <ShoppingBag className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" />
                    <span className="group-hover:text-accent-foreground transition-colors">My Orders</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground">
                  <Link href="/favorites" className="flex items-center">
                    <Heart className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" />
                    <span className="group-hover:text-accent-foreground transition-colors">My Collection</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground">
                  <Link href="/offers" className="flex items-center">
                    <Package className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" />
                    <span className="group-hover:text-accent-foreground transition-colors">My Offers</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground">
                  <Link href="/dashboard/reviews" className="flex items-center">
                    <MessageSquare className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" />
                    <span className="group-hover:text-accent-foreground transition-colors">My Reviews</span>
                  </Link>
                </DropdownMenuItem>

                {/* Theme Toggle */}
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground">
                    <div className="flex items-center">
                      {theme === "light" ? (  
                        <Sun className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" />
                      ) : theme === "dark" ? (
                        <Moon className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" />
                      ) : (
                        <Monitor className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" />
                      )}
                      <span className="group-hover:text-accent-foreground transition-colors">
                        Theme
                        <span className="ml-2 text-muted text-xs font-normal group-hover:text-accent-foreground transition-colors">
                          {theme === "light"
                            ? "Light"
                            : theme === "dark"
                            ? "Dark"
                            : "System"}
                        </span>
                      </span>
                    </div>
                  </DropdownMenuSubTrigger>
                  <DropdownMenuSubContent className="rounded-xl border border-border bg-card shadow-xl">
                    <DropdownMenuItem 
                      onClick={() => setTheme("light")}
                      className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground flex items-center"
                    >
                      <Sun className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" />
                      <span className="group-hover:text-accent-foreground transition-colors flex-1">Light</span>
                      {theme === "light" && (
                        <span className="ml-2">
                          <Check className="w-4 h-4 text-primary" />
                        </span>
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => setTheme("dark")}
                      className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground flex items-center"
                    >
                      <Moon className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" />
                      <span className="group-hover:text-accent-foreground transition-colors flex-1">Dark</span>
                      {theme === "dark" && (
                        <span className="ml-2 text-primary">
                          <svg className="w-4 h-4" viewBox="0 0 20 20" fill="none">
                            <path d="M6 10.8L9 13.8L14 8.8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </span>
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => setTheme("system")}
                      className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium group text-foreground flex items-center"
                    >
                      <Monitor className="w-5 h-5 mr-3 text-foreground group-hover:text-accent-foreground transition-colors" />
                      <span className="group-hover:text-accent-foreground transition-colors flex-1">System</span>
                      {theme === "system" && (
                        <span className="ml-2 text-primary">
                          <svg className="w-4 h-4" viewBox="0 0 20 20" fill="none">
                            <path d="M6 10.8L9 13.8L14 8.8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </span>
                      )}
                    </DropdownMenuItem>
                  </DropdownMenuSubContent>
                </DropdownMenuSub>

                {user?.userType === "seller" && (
                  <>
                    <div className="my-2 border-t border-border"></div>
                    <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium text-foreground">
                      <Link href="/seller/dashboard" className="flex items-center">
                        <svg className="w-5 h-5 mr-3 text-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        Seller Dashboard
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium text-foreground">
                      <Link
                        href={user?.userId ? `/seller/${user.userId}` : "#"}
                        className="flex items-center"
                      >
                        <svg className="w-5 h-5 mr-3 text-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        View My Profile
                      </Link>
                    </DropdownMenuItem>
                  </>
                )}

                {/* Apply to Sell / Application Status for non-sellers */}
                {user && user.userType !== "seller" && (
                  <>
                    <div className="my-2 border-t border-border"></div>
                    <DropdownMenuItem 
                      className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium text-foreground"
                      onSelect={(e) => e.preventDefault()}
                    >
                      <SellerApplicationModal onOpenChange={setIsDropdownOpen}>
                        <div className="flex items-center w-full">
                          <svg className="w-5 h-5 mr-3 text-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          {userApplication ? "Application Status" : "Apply to Sell"}
                        </div>
                      </SellerApplicationModal>
                    </DropdownMenuItem>
                  </>
                )}

                {/* Admin Menu */}
                {user?.userType === "admin" && (
                  <DropdownMenuItem asChild className="rounded-xl px-3 py-2 hover:bg-muted/20 font-medium text-foreground">
                    <Link href="/admin" className="flex items-center">
                      <Shield className="w-5 h-5 mr-1 text-foreground group-hover:text-accent-foreground transition-colors" />
                      Admin Dashboard
                    </Link>
                    </DropdownMenuItem>
                )}
              </div>

              <div className="p-2 border-t">
                <DropdownMenuItem
                  onClick={signOut}
                  className="rounded-xl px-3 py-2 text-destructive hover:bg-destructive/10 hover:!text-destructive flex items-center font-medium"
                >
                  <LogOut className="w-5 h-5 mr-3 text-destructive" />
                  Sign Out
                </DropdownMenuItem>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
          </header>
    );
  }