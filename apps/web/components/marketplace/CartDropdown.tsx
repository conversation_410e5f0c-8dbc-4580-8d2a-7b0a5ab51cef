"use client";

import { useCart } from "@/hooks/useCart";
import { But<PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Separator } from "@repo/ui/components/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { ShoppingBag, Trash2, Plus, Minus, ShoppingCart } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { cn } from "@repo/ui/lib/utils";

const CONDITION_LABELS = {
  new: "New",
  like_new: "Like New", 
  good: "Good",
  fair: "Fair",
};

const CONDITION_COLORS = {
  new: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  like_new: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  good: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  fair: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
};

const STATUS_COLORS = {
  active: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  sold: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
  reserved: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  draft: "bg-neutral-100 text-neutral-800 dark:bg-neutral-900 dark:text-neutral-300",
};

const STATUS_LABELS = {
  active: "Available",
  sold: "Sold",
  reserved: "Reserved",
  draft: "Draft",
};

export function CartDropdown() {
  const { 
    cartItems, 
    cartCount, 
    cartTotal, 
    isLoading, 
    updateQuantity, 
    removeFromCart 
  } = useCart();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const handleQuantityChange = async (productId: string, newQuantity: number) => {
    if (newQuantity >= 1 && newQuantity <= 10) {
      await updateQuantity(productId as any, newQuantity);
    }
  };

  const handleRemoveItem = async (productId: string) => {
    await removeFromCart(productId as any);
  };

  const getStatusBadge = (product: any) => {
    const status = product.status || "active";
    if (status === "active") return null; // Don't show badge for available products
    
    return (
      <Badge
        className={`absolute top-2 left-2 text-xs ${
          STATUS_COLORS[status as keyof typeof STATUS_COLORS]
        }`}
      >
        {STATUS_LABELS[status as keyof typeof STATUS_LABELS]}
      </Badge>
    );
  };

  const getProductActions = (product: any) => {
    if (product.status === "sold") {
      return (
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">
            Sold
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleRemoveItem(product._id)}
            className="text-destructive hover:text-destructive hover:bg-destructive/10 w-6 h-6 p-0 rounded-xl"
          >
            <Trash2 className="w-3 h-3" />
          </Button>
        </div>
      );
    }

    if (product.status === "reserved") {
      return (
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">
            Reserved
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleRemoveItem(product._id)}
            className="text-destructive hover:text-destructive hover:bg-destructive/10 w-6 h-6 p-0 rounded-xl"
          >
            <Trash2 className="w-3 h-3" />
          </Button>
        </div>
      );
    }

    // Available products - show normal quantity controls
    return (
      <div className="flex items-center space-x-2">
        {/* <Button
          variant="outline"
          size="sm"
          onClick={() => handleQuantityChange(product._id, product.quantity - 1)}
          disabled={product.quantity <= 1 || isLoading}
          className="w-6 h-6 p-0 rounded-lg"
        >
          <Minus className="w-3 h-3 text-foreground" />
        </Button>
        <span className="text-sm font-medium w-6 text-center">
          {product.quantity}
        </span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleQuantityChange(product._id, product.quantity + 1)}
          disabled={product.quantity >= 10 || isLoading}
          className="w-6 h-6 p-0 rounded-lg"
        >
          <Plus className="w-3 h-3 text-foreground" />
        </Button> */}
      </div>
    );
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="xl" className="relative p-3 rounded-xl text-primary">
          <ShoppingBag className="w-6 h-6 text-foreground" />
          {cartCount > 0 && (
            <Badge
              variant="secondary" className="rounded-xl absolute -top-1 -right-1 w-5 h-5 p-0 flex items-center justify-center text-xs bg-secondary text-foreground border border-border"
            >
              {cartCount > 99 ? "99+" : cartCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent 
        align="end" 
        className="w-96 p-0 bg-card shadow-xl rounded-2xl border border-border/50"
        style={{ 
          boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
          animation: 'dropdownSlideIn 0.15s ease-out'
        }}
      >
        {/* Header */}
        <div className="p-2">
          <div className="flex items-center justify-between px-3 py-2">
            <h3 className="text-xs text-foreground font-medium">
              Shopping Cart ({cartCount} {cartCount === 1 ? 'item' : 'items'})
            </h3>
          </div>
        </div>

        {/* Cart Items */}
        <div className="max-h-96 overflow-y-auto">
          {cartItems.length === 0 ? (
            <div className="p-6 text-center">
              <ShoppingBag className="w-8 h-8 text-foreground mx-auto mb-2" />
              <div className="text-sm text-foreground">Your cart is empty</div>
              <div className="text-xs text-foreground mt-1">Add some products to get started</div>
            </div>
          ) : (
            <div className="p-2">
              {cartItems.slice(0, 5).map((item: any) => ( // Show max 5 items
                <div key={item._id} className="px-3 py-3 hover:bg-muted/20 rounded-2xl transition-colors duration-150">
                  <div className="flex gap-3">
                    {/* Product Image */}
                    <Link href={`/marketplace/product/${item.product._id}`}>
                      <div className="w-16 h-16 bg-neutral-100 dark:bg-neutral-800 rounded-2xl overflow-hidden flex-shrink-0">
                        {item.product.images.length > 0 && item.product.images[0] ? (
                          <Image
                            src={item.product.images[0]}
                            alt={item.product.title}
                            width={64}
                            height={64}
                            className="w-full h-full object-cover hover:scale-105 transition-transform"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <ShoppingBag className="w-6 h-6 text-neutral-400" />
                          </div>
                        )}
                      </div>
                    </Link>

                    {/* Product Info */}
                    <div className="flex-1 min-w-0">
                      <Link 
                        href={`/marketplace/product/${item.product._id}`}
                        className="text-sm font-medium text-foreground hover:underline w-fit transition-colors line-clamp-1"
                      >
                        {item.product.title}
                      </Link>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge 
                          className={cn(
                            "text-xs h-5",
                            CONDITION_COLORS[item.product.condition as keyof typeof CONDITION_COLORS]
                          )}
                        >
                          {CONDITION_LABELS[item.product.condition as keyof typeof CONDITION_LABELS]}
                        </Badge>
                        <span className="text-xs text-neutral-500 dark:text-neutral-400">
                          {item.product.brand}
                        </span>
                      </div>

                      {/* Quantity and Price */}
                      <div className="flex items-center justify-between mt-2">
                        {getStatusBadge(item.product)}
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-semibold text-foreground">
                            {formatCurrency(item.product.price * item.quantity)}
                          </span>
                          {getProductActions(item.product)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {/* Show more indicator */}
              {cartItems.length > 5 && (
                <div className="text-center py-2">
                  <span className="text-xs text-muted-foreground">
                    +{cartItems.length - 5} more items
                  </span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        {cartItems.length > 0 && cartTotal && (
          <>
            <div className="border-t border-border mt-2 pt-2">
              <div className="p-2 space-y-3">
                {/* Subtotal */}
                <div className="flex justify-between text-sm">
                  <span className="text-foreground">
                    Subtotal ({cartTotal.itemCount} items)
                  </span>
                  <span className="font-medium text-foreground">
                    {formatCurrency(cartTotal.subtotal)}
                  </span>
                </div>

                {/* Tax and Shipping */}
                <div className="flex justify-between text-xs text-foreground">
                  <span>Tax + Shipping</span>
                  <span>
                    {formatCurrency(cartTotal.tax + cartTotal.shipping)}
                  </span>
                </div>

                {/* Total */}
                <div className="flex justify-between text-base font-semibold">
                  <span className="text-foreground">Total</span>
                  <span className="text-foreground">
                    {formatCurrency(cartTotal.total)}
                  </span>
                </div>

                {/* Action Buttons */}
                <div className="space-y-2 pt-2">
                  <Button asChild className="w-full h-9 text-sm">
                    <Link href="/cart">
                      <ShoppingCart className="w-4 h-4 mr-2" />
                      View Cart
                    </Link>
                  </Button>
                  <Button asChild variant="ghost" className="w-full h-9 text-sm">
                    <Link href="/checkout">
                      Checkout
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>

  );
}
