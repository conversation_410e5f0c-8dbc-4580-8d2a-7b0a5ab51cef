"use client";

import { useState } from "react";
import { <PERSON><PERSON>, Facebook, Twitter, Mail, Link2, Check } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";

interface Product {
  _id: string;
  title: string;
  brand: string;
  price: number;
}

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product;
}

export function ShareModal({ isOpen, onClose, product }: ShareModalProps) {
  const [copied, setCopied] = useState(false);
  
  const productUrl = `${window.location.origin}/marketplace/product/${product._id}`;
  const shareText = `Check out this ${product.brand} ${product.title} for $${product.price.toLocaleString()} on MODA`;

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(productUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy link:', err);
    }
  };

  const handleShare = (platform: string) => {
    const encodedUrl = encodeURIComponent(productUrl);
    const encodedText = encodeURIComponent(shareText);
    
    let shareUrl = '';
    
    switch (platform) {
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`;
        break;
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedUrl}`;
        break;
      case 'email':
        shareUrl = `mailto:?subject=${encodeURIComponent(`${product.brand} ${product.title}`)}&body=${encodedText}%0A%0A${encodedUrl}`;
        break;
    }
    
    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Share this item</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Product Preview */}
          <div className="bg-neutral-50 dark:bg-neutral-900 rounded-lg p-4">
            <h4 className="font-medium text-neutral-900 dark:text-neutral-100 line-clamp-2">
              {product.brand} {product.title}
            </h4>
            <p className="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mt-1">
              ${product.price.toLocaleString()}
            </p>
          </div>

          {/* Share Buttons */}
          <div className="grid grid-cols-3 gap-3">
            <Button
              variant="outline"
              onClick={() => handleShare('facebook')}
              className="flex flex-col items-center space-y-2 h-auto py-4"
            >
              <Facebook className="w-5 h-5 text-blue-600" />
              <span className="text-xs">Facebook</span>
            </Button>
            
            <Button
              variant="outline"
              onClick={() => handleShare('twitter')}
              className="flex flex-col items-center space-y-2 h-auto py-4"
            >
              <Twitter className="w-5 h-5 text-blue-400" />
              <span className="text-xs">Twitter</span>
            </Button>
            
            <Button
              variant="outline"
              onClick={() => handleShare('email')}
              className="flex flex-col items-center space-y-2 h-auto py-4"
            >
              <Mail className="w-5 h-5 text-neutral-600 dark:text-neutral-400" />
              <span className="text-xs">Email</span>
            </Button>
          </div>

          {/* Copy Link */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
              Copy link
            </label>
            <div className="flex space-x-2">
              <Input
                value={productUrl}
                readOnly
                className="flex-1"
              />
              <Button
                onClick={handleCopyLink}
                variant="outline"
                className="px-3"
              >
                {copied ? (
                  <Check className="w-4 h-4 text-green-600" />
                ) : (
                  <Copy className="w-4 h-4" />
                )}
              </Button>
            </div>
            {copied && (
              <p className="text-xs text-green-600 dark:text-green-400">
                Link copied to clipboard!
              </p>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}