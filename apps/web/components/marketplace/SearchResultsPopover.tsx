"use client";

import { useState, useEffect, useRef } from "react";
import { Search, Clock, TrendingUp, ChevronRight, Loader2, X, Zap } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useRecentSearches } from "@/hooks/useRecentSearches";
import { usePopularSearches } from "@/hooks/usePopularSearches";

interface SearchResult {
  _id: string;
  title: string;
  brand: string;
  model?: string;
  price: number;
  condition: string;
  category: string;
  size?: string;
  color?: string;
  material?: string;
  imageUrl: string | null;
  sellerName: string;
}

interface SellerSearchResult {
  userId: string;
  businessName: string;
  businessType?: string;
  profileImage?: string;
  rating?: number;
  activeProducts: number;
  categories: string[];
  address?: {
    city: string;
    state: string;
  };
}

interface SearchResultsPopoverProps {
  isOpen: boolean;
  onClose: () => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onSellerSearch?: (query: string) => void;
}

export function SearchResultsPopover({
  isOpen,
  onClose,
  searchQuery,
  onSearchChange,
  onSellerSearch
}: SearchResultsPopoverProps) {
  const [filteredResults, setFilteredResults] = useState<SearchResult[]>([]);
  const [filteredSellerResults, setFilteredSellerResults] = useState<SellerSearchResult[]>([]);
  const [debouncedQuery, setDebouncedQuery] = useState(searchQuery);
  
  // Use the recent searches hook
  const { searches: recentSearches, addSearch, removeSearch, isLoading: isLoadingSearches, clearSearches } = useRecentSearches();
  
  // Use the popular searches hook
  const { 
    popularSearches, 
    isLoading: isLoadingPopular,
    getPopularByCategory,
    getTrendingSearches 
  } = usePopularSearches({ limit: 8, timeRange: '30d' });
  
  // Track search events
  const trackSearchEvent = useMutation(api.productQueries.trackSearchEvent);

  // Debounce search query to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Use Convex query for real search results
  const searchResults = useQuery(
    api.productQueries.getQuickSearchResults,
    debouncedQuery.trim() ? { searchQuery: debouncedQuery.trim(), limit: 5 } : "skip"
  );

  // Use Convex query for seller search results
  const sellerSearchResults = useQuery(
    api.sellerQueries.getPublicSellers,
    debouncedQuery.trim() ? { searchQuery: debouncedQuery.trim(), limit: 3 } : "skip"
  );

  // Update filtered results when search results change
  useEffect(() => {
    if (searchResults && searchResults.results) {
      setFilteredResults(searchResults.results);
    } else {
      setFilteredResults([]);
    }
  }, [searchResults]);

  // Update filtered seller results when seller search results change
  useEffect(() => {
    if (sellerSearchResults && sellerSearchResults.sellers) {
      setFilteredSellerResults(sellerSearchResults.sellers);
    } else {
      setFilteredSellerResults([]);
    }
  }, [sellerSearchResults]);

  const handleSearch = async (query: string) => {
    // Track search event for analytics
    try {
      await trackSearchEvent({
        query,
        resultCount: searchResults?.results?.length || 0,
        source: "search_popover",
        category: searchResults?.results?.[0]?.category,
        brand: searchResults?.results?.[0]?.brand,
      });
    } catch (error) {
      console.warn('Failed to track search event:', error);
    }
    
    // Save to search history
    await addSearch(query, searchResults?.results?.length);
    
    // Update search input and close popover
    onSearchChange(query);
    onClose();
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const getConditionLabel = (condition: string) => {
    const conditionMap: Record<string, string> = {
      new: 'New',
      like_new: 'Like New',
      excellent: 'Excellent',
      very_good: 'Very Good',
      good: 'Good',
      fair: 'Fair'
    };
    return conditionMap[condition] || condition;
  };

  const getRatingDisplay = (rating?: number) => {
    if (!rating) return "No rating";
    return `${rating.toFixed(1)} ⭐`;
  };

  if (!isOpen) return null;

  return (
    <div 
      className="absolute top-full mt-2 left-0 right-0 bg-white border border-gray-200 rounded-2xl shadow-xl"
      style={{ 
        boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        animation: 'dropdownSlideIn 0.15s ease-out'
      }}
    >
      <div className="max-h-120 overflow-y-auto">
        {searchQuery && debouncedQuery.trim() ? (
          // Search Results
          <div className="p-2">
            <div className="text-xs text-gray-500 px-3 py-2 font-medium">
              Search Results ({filteredResults.length + filteredSellerResults.length})
            </div>
            
            {/* Loading State */}
            {searchResults === undefined && (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="w-4 h-4 animate-spin text-gray-400 mr-2" />
                <span className="text-sm text-gray-500">Searching...</span>
              </div>
            )}
            
            {/* Results */}
            {searchResults !== undefined && filteredResults.length > 0 && (
              filteredResults.map((result) => (
                <Link
                  key={result._id}
                  href={`/marketplace/product/${result._id}`}
                  className="block w-full text-left px-3 py-3 hover:bg-gray-50 rounded-md transition-colors duration-150 group"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-100 flex-shrink-0">
                        {result.imageUrl ? (
                          <Image
                            src={result.imageUrl}
                            alt={result.title}
                            width={48}
                            height={48}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-200 flex items-center justify-center text-gray-400 text-xs">
                            {result.brand.charAt(0)}
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-gray-900 truncate group-hover:text-blue-600">
                          {result.title}
                        </div>
                        <div className="text-sm text-gray-500 truncate">
                          {result.brand} {result.model ? `• ${result.model}` : ''} • {getConditionLabel(result.condition)}
                        </div>
                        {result.size && (
                          <div className="text-xs text-gray-400">
                            Size: {result.size} {result.color ? `• ${result.color}` : ''}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="ml-4 font-semibold text-gray-900">
                      {formatPrice(result.price)}
                    </div>
                  </div>
                </Link>
              ))
            )}
            
            {/* Seller Results */}
            {sellerSearchResults !== undefined && filteredSellerResults.length > 0 && (
              <>
                <div className="border-t border-gray-100 mt-2 pt-2">
                  <div className="flex items-center justify-between px-3 py-2">
                    <div className="text-xs text-gray-500 font-medium">
                      Sellers ({filteredSellerResults.length})
                    </div>
                    <button 
                      onClick={() => onSellerSearch?.(debouncedQuery)}
                      className="text-xs text-blue-600 hover:text-blue-800 font-medium hover:underline"
                    >
                      View all sellers →
                    </button>
                  </div>
                  {filteredSellerResults.map((seller) => (
                    <Link
                      key={seller.userId}
                      href={`/seller/${seller.userId}`}
                      className="block w-full text-left px-3 py-3 hover:bg-gray-50 rounded-md transition-colors duration-150 group"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-100 flex-shrink-0">
                            {seller.profileImage ? (
                              <Image
                                src={seller.profileImage}
                                alt={seller.businessName}
                                width={48}
                                height={48}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full bg-gray-200 flex items-center justify-center text-gray-400 text-xs">
                                {seller.businessName.charAt(0)}
                              </div>
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-gray-900 truncate group-hover:text-blue-600">
                              {seller.businessName}
                            </div>
                            <div className="text-sm text-gray-500 truncate">
                              {seller.businessType ? `${seller.businessType} • ` : ''}{getRatingDisplay(seller.rating)} • {seller.activeProducts} products
                            </div>
                            {seller.categories.length > 0 && (
                              <div className="text-xs text-gray-400">
                                {seller.categories.slice(0, 2).join(', ')}
                                {seller.categories.length > 2 && ` +${seller.categories.length - 2} more`}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="ml-4 text-right">
                          <div className="text-xs text-gray-500">
                            {seller.address ? `${seller.address.city}, ${seller.address.state}` : ''}
                          </div>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </>
            )}

            {/* No Results */}
            {searchResults !== undefined && sellerSearchResults !== undefined && 
             filteredResults.length === 0 && filteredSellerResults.length === 0 && (
              <div className="p-6 text-center">
                <Search className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                <div className="text-sm text-gray-500">No results found for "{debouncedQuery}"</div>
                <div className="text-xs text-gray-400 mt-1">Try adjusting your search terms</div>
              </div>
            )}
          </div>
        ) : (
          // Default State - Recent Searches
          <div className="p-2">
            <div className="flex items-center justify-between px-3 py-2">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-gray-400" />
                <span className="text-xs text-gray-500 font-medium">Recent Searches</span>
              </div>
              {recentSearches.length > 0 && (
                <button
                  onClick={() => clearSearches()}
                  className="text-xs text-gray-400 hover:text-gray-600 transition-colors"
                >
                  Clear all
                </button>
              )}
            </div>
            
            {/* Loading state for recent searches */}
            {isLoadingSearches && (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="w-4 h-4 animate-spin text-gray-400 mr-2" />
                <span className="text-sm text-gray-500">Loading...</span>
              </div>
            )}
            
            {/* Recent searches */}
            {!isLoadingSearches && recentSearches.length > 0 ? (
              recentSearches.map((search, index) => (
                <div key={index} className="flex items-center justify-between group">
                  <button
                    onClick={() => handleSearch(search)}
                    className="flex-1 text-left px-3 py-2 hover:bg-gray-50 rounded-md transition-colors duration-150 text-sm text-gray-700 hover:text-gray-900"
                  >
                    {search}
                  </button>
                  <button
                    onClick={() => removeSearch(search)}
                    className="px-2 py-2 text-gray-400 hover:text-gray-600 transition-colors opacity-0 group-hover:opacity-100"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))
            ) : !isLoadingSearches && (
              <div className="px-3 py-2 text-sm text-gray-400">
                No recent searches
              </div>
            )}
            
            {/* Trending Section */}
            <div className="border-t border-gray-100 mt-2 pt-2">
              <div className="flex items-center gap-2 px-3 py-2">
                <Zap className="w-4 h-4 text-yellow-500" />
                <span className="text-xs text-gray-500 font-medium">Trending Now</span>
              </div>
              {isLoadingPopular ? (
                <div className="flex items-center justify-center py-2">
                  <Loader2 className="w-3 h-3 animate-spin text-gray-400 mr-2" />
                  <span className="text-xs text-gray-400">Loading trends...</span>
                </div>
              ) : (
                <div className="px-3 pb-2">
                  <div className="flex flex-wrap gap-1">  
                    {getTrendingSearches().map((tag: any, index: number) => (
                      <button
                        key={index}
                        onClick={() => handleSearch(tag)}
                        className="px-2 py-1 text-xs bg-yellow-50 hover:bg-yellow-100 text-yellow-700 hover:text-yellow-800 rounded transition-colors duration-150 border border-yellow-200"
                      >
                        {tag}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            {/* Popular Searches Section */}
            <div className="border-t border-gray-100 mt-2 pt-2">
              <div className="flex items-center gap-2 px-3 py-2">
                <TrendingUp className="w-4 h-4 text-gray-400" />
                <span className="text-xs text-gray-500 font-medium">Popular Searches</span>
              </div>
              {isLoadingPopular ? (
                <div className="flex items-center justify-center py-2">
                  <Loader2 className="w-3 h-3 animate-spin text-gray-400 mr-2" />
                  <span className="text-xs text-gray-400">Loading popular...</span>
                </div>
              ) : (
                <div className="px-3 pb-2">
                  <div className="flex flex-wrap gap-1">
                    {popularSearches.slice(0, 6).map((tag, index) => (
                      <button
                        key={index}
                        onClick={() => handleSearch(tag)}
                        className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 rounded transition-colors duration-150"
                      >
                        {tag}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        @keyframes dropdownSlideIn {
          from {
            opacity: 0;
            transform: translateY(-8px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
}
