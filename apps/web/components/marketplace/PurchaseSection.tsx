"use client";

import { useState } from "react";
import { ShoppingBag, Heart, Minus, Plus, CreditCard } from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { cn } from "@repo/ui/lib/utils";
import { Id } from "@repo/backend/convex/_generated/dataModel";

interface Product {
  _id: Id<"products">;
  title: string;
  brand: string;
  price: number;
  category: string;
  condition: string;
}

interface PurchaseSectionProps {
  product: Product;
  quantity: number;
  onQuantityChange: (quantity: number) => void;
  onPurchase: () => void;
  isFavorited: boolean;
  onFavorite: () => void;
}

export function PurchaseSection({
  product,
  quantity,
  onQuantityChange,
  onPurchase,
  isFavorited,
  onFavorite,
}: PurchaseSectionProps) {
  const [isAddingToCart, setIsAddingToCart] = useState(false);

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && newQuantity <= 10) {
      onQuantityChange(newQuantity);
    }
  };

  const handleAddToCart = async () => {
    setIsAddingToCart(true);
    // TODO: Implement add to cart functionality
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsAddingToCart(false);
  };

  const totalPrice = product.price * quantity;
  const estimatedTax = totalPrice * 0.08; // 8% tax
  const shippingCost = totalPrice > 500 ? 0 : 25; // Free shipping over $500
  const finalTotal = totalPrice + estimatedTax + shippingCost;

  return (
    <div className="space-y-4">
      <div className="bg-card border-0 rounded-lg p-4 space-y-4 shadow-sm hover:shadow-md transition-shadow duration-300 sticky top-8">
        {/* Quantity Selector */}
        {/* <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Quantity
          </label>
          <div className="flex items-center justify-center space-x-3 bg-muted rounded-lg p-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuantityChange(quantity - 1)}
              disabled={quantity <= 1}
              className="w-8 h-8 p-0 rounded-full border-2 border-border hover:border-foreground transition-colors duration-300 disabled:opacity-50"
            >
              <Minus className="w-4 h-4" />
            </Button>
            <span className="w-12 text-center text-lg font-light text-foreground">
              {quantity}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuantityChange(quantity + 1)}
              disabled={quantity >= 10}
              className="w-8 h-8 p-0 rounded-full border-2 border-border hover:border-foreground transition-colors duration-300 disabled:opacity-50"
            >
              <Plus className="w-4 h-4" />
            </Button>
          </div>
        </div> */}

        {/* Price Breakdown */}
        <div className="space-y-2 pt-3 border-t border-border">
          <div className="flex justify-between text-xs">
            <span className="text-muted-foreground font-light">
              Item price ({quantity}x)
            </span>
            <span className="font-medium text-foreground">
              ${totalPrice.toLocaleString()}
            </span>
          </div>
          
          <div className="flex justify-between text-xs">
            <span className="text-muted-foreground font-light">
              Estimated tax
            </span>
            <span className="font-medium text-foreground">
              ${estimatedTax.toFixed(2)}
            </span>
          </div>
          
          <div className="flex justify-between text-xs">
            <span className="text-muted-foreground font-light">
              Shipping
            </span>
            <span className="font-medium text-foreground">
              {shippingCost === 0 ? (
                <Badge variant="secondary" className="text-xs font-light">Free</Badge>
              ) : (
                `$${shippingCost}`
              )}
            </span>
          </div>
          
          <div className="flex justify-between text-sm font-medium pt-2 border-t border-border">
            <span className="text-foreground">Total</span>
            <span className="text-foreground">
              ${finalTotal.toFixed(2)}
            </span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <Button
            onClick={onPurchase}
            className="w-full h-8 text-sm font-light bg-primary hover:bg-primary/90 transition-colors duration-300 rounded-lg"
          >
            <CreditCard className="w-4 h-4 mr-1" />
            Buy Now - ${finalTotal.toFixed(2)}
          </Button>
          
          <Button
            variant="outline"
            onClick={handleAddToCart}
            disabled={isAddingToCart}
            className="w-full h-8 text-sm font-light rounded-lg transition-colors duration-300"
          >
            <ShoppingBag className="w-4 h-4 mr-1" />
            {isAddingToCart ? "Adding..." : "Add to Cart"}
          </Button>
          
          <Button
            variant="ghost"
            onClick={onFavorite}
            className="w-full h-8 text-sm font-light rounded-lg transition-colors duration-300"
          >
            <Heart
              className={cn(
                "w-4 h-4 mr-1 transition-colors",
                isFavorited ? "fill-red-500 text-red-500" : "text-muted-foreground"
              )}
            />
            {isFavorited ? "Remove from Wishlist" : "Add to Wishlist"}
          </Button>
        </div>

        {/* Security Notice */}
        <div className="bg-muted rounded-lg p-2 text-center">
          <p className="text-xs text-muted-foreground font-light">
            🔒 Secure checkout with 256-bit SSL encryption
          </p>
        </div>
      </div>
    </div>
  );
}