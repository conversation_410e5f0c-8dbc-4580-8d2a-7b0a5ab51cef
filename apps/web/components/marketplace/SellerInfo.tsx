"use client";

import { Star, MessageCircle, Shield } from "lucide-react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar";

interface Seller {
  _id: string;
  name: string;
  userType: string;
}

interface SellerInfoProps {
  seller: Seller;
}

export function SellerInfo({ seller }: SellerInfoProps) {
  // Mock data - in real app, fetch from seller profile
  const sellerStats = {
    rating: 4.8,
    totalSales: 127,
    responseTime: "< 1 hour",
    memberSince: "2023",
    isVerified: true,
  };

  const handleContactSeller = () => {
    // TODO: Implement messaging system
    console.log("Contact seller:", seller._id);
  };

  const handleViewProfile = () => {
    // TODO: Navigate to seller profile
    console.log("View seller profile:", seller._id);
  };

  return (
    <div className="space-y-3">
      <h3 className="text-sm font-semibold text-neutral-900 dark:text-neutral-100">
        Seller Information
      </h3>
      
      <div className="bg-neutral-50 dark:bg-neutral-900 rounded-lg p-3 space-y-3">
        {/* Seller Header */}
        <div className="flex items-start space-x-3">
          <Avatar className="w-8 h-8">
            <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${seller.name}`} />
            <AvatarFallback>
              {seller.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1 space-y-1">
            <div className="flex items-center space-x-2">
              <h4 className="font-semibold text-neutral-900 dark:text-neutral-100 text-sm">
                {seller.name}
              </h4>
              {sellerStats.isVerified && (
                <Badge variant="secondary" className="text-xs">
                  <Shield className="w-2.5 h-2.5 mr-1" />
                  Verified
                </Badge>
              )}
              {seller.userType === "premium_seller" && (
                <Badge className="text-xs bg-gradient-to-r from-amber-500 to-orange-500 text-white">
                  Premium Seller
                </Badge>
              )}
            </div>
            
            {/* Rating */}
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star
                    key={i}
                    className={`w-3 h-3 ${
                      i < Math.floor(sellerStats.rating)
                        ? "fill-yellow-400 text-yellow-400"
                        : "text-neutral-300 dark:text-neutral-600"
                    }`}
                  />
                ))}
              </div>
              <span className="text-xs font-medium text-neutral-900 dark:text-neutral-100">
                {sellerStats.rating}
              </span>
              <span className="text-xs text-neutral-500 dark:text-neutral-400">
                ({sellerStats.totalSales} sales)
              </span>
            </div>
          </div>
        </div>

        {/* Seller Stats */}
        <div className="grid grid-cols-2 gap-3 pt-2 border-t border-neutral-200 dark:border-neutral-700">
          <div className="space-y-1">
            <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wide">
              Response Time
            </p>
            <p className="text-xs font-medium text-neutral-900 dark:text-neutral-100">
              {sellerStats.responseTime}
            </p>
          </div>
          
          <div className="space-y-1">
            <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wide">
              Member Since
            </p>
            <p className="text-xs font-medium text-neutral-900 dark:text-neutral-100">
              {sellerStats.memberSince}
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-2 pt-2">
          <Button
            variant="outline"
            onClick={handleContactSeller}
            className="flex-1 h-7 text-xs"
          >
            <MessageCircle className="w-3 h-3 mr-1" />
            Contact Seller
          </Button>
          <Button
            variant="ghost"
            onClick={handleViewProfile}
            className="flex-1 h-7 text-xs"
          >
            View Profile
          </Button>
        </div>
      </div>
    </div>
  );
}