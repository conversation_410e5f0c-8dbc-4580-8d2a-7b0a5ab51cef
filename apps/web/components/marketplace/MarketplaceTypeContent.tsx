"use client";

import { useState, useMemo, useEffect } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { FilterSidebar } from "./FilterSidebar";
import { ProductGrid } from "./ProductGrid";
import { MarketplaceHeader } from "./MarketplaceHeader";
import { BrowseHeroSection } from "./BrowseHeroSection";
import type { FilterState as ImportedFilterState } from "./MarketplaceContent";
import { 
  Crown, 
  Clock, 
  Car, 
  Palette
} from "lucide-react";

interface MarketplaceTypeContentProps {
  marketplaceType: "moda-watch" | "real-watch-buyers" | "moda-car" | "moda-lifestyle" | "moda-misc";
}

const initialFilters: ImportedFilterState = {
  categories: [],
  priceRange: [null, null],
  brands: [],
  conditions: [],
  searchQuery: "",
};

const MARKETPLACE_CONFIG = {
  "moda-watch": {
    name: "Moda Watch Club",
    description: "Premium timepieces and luxury watches",
    icon: Clock,
    color: "from-blue-500 to-indigo-600",
  },
  "real-watch-buyers": {
    name: "Real Watch Buyers",
    description: "Authentic watch trading and sales",
    icon: Clock,
    color: "from-slate-500 to-zinc-600",
  },
  "moda-car": {
    name: "Moda Car Club",
    description: "Luxury cars and collectible vehicles",
    icon: Car,
    color: "from-red-500 to-orange-600",
  },
  "moda-lifestyle": {
    name: "Moda Lifestyle Club",
    description: "Fashion, jewelry, and luxury lifestyle items",
    icon: Crown,
    color: "from-pink-500 to-rose-600",
  },
  "moda-misc": {
    name: "Moda Misc Club",
    description: "Art, collectibles, and miscellaneous luxury items",
    icon: Palette,
    color: "from-purple-500 to-violet-600",
  },
};

export function MarketplaceTypeContent({ marketplaceType }: MarketplaceTypeContentProps) {
  const [filters, setFilters] = useState<ImportedFilterState>(initialFilters);
  const [sortBy, setSortBy] = useState<"newest" | "price_low" | "price_high" | "popular">("newest");

  const [currentPage, setCurrentPage] = useState(0);
  const [allProducts, setAllProducts] = useState<any[]>([]);
  const ITEMS_PER_PAGE = 24;

  const config = MARKETPLACE_CONFIG[marketplaceType];
  
  // Determine if this marketplace type should have pre-loaded filters removed
  const shouldRemovePreloadedFilters = marketplaceType === "moda-watch" || marketplaceType === "real-watch-buyers";

  // Fetch products with filters and pagination for this marketplace type
  const productsResult = useQuery(api.marketplaceQueries.getProductsByMarketplace, {
    marketplaceType,
    category: filters.categories.length === 1 ? filters.categories[0] as any : undefined,
    minPrice: filters.priceRange[0] !== null ? filters.priceRange[0] : undefined,
    maxPrice: filters.priceRange[1] !== null ? filters.priceRange[1] : undefined,
    brand: filters.brands.length > 0 ? filters.brands[0] : undefined,
    condition: filters.conditions.length > 0 ? filters.conditions[0] : undefined,
    limit: ITEMS_PER_PAGE,
  });

  // Get filter options filtered by marketplace type
  const filterOptions = useQuery(api.productQueries.getAggregatedFilterOptions, {
    marketplaceType,
  });

  // Update allProducts when new results come in
  useEffect(() => {
    if (productsResult) {
      // Filter products by search query if needed
      let filteredProducts = productsResult;
      if (filters.searchQuery) {
        filteredProducts = productsResult.filter((product: any) =>
          product.title.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
          product.brand.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
          product.description.toLowerCase().includes(filters.searchQuery.toLowerCase())
        );
      }
      
      if (currentPage === 0) {
        // Reset products when filters change
        setAllProducts(filteredProducts);
      } else {
        // Append new products for infinite scroll
        setAllProducts(prev => [...prev, ...filteredProducts]);
      }
    }
  }, [productsResult, currentPage, filters.searchQuery]);

  const handleFilterChange = (newFilters: Partial<ImportedFilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(0); // Reset to first page when filters change
    setAllProducts([]); // Clear existing products
  };

  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, searchQuery: query }));
    setCurrentPage(0); // Reset to first page when search changes
    setAllProducts([]); // Clear existing products
  };

  const clearFilters = () => {
    setFilters(initialFilters);
    setCurrentPage(0); // Reset to first page when clearing filters
    setAllProducts([]); // Clear existing products
  };

  const handleSortChange = (newSort: "newest" | "price_low" | "price_high" | "popular") => {
    setSortBy(newSort);
    setCurrentPage(0); // Reset to first page when sort changes
    setAllProducts([]); // Clear existing products
  };

  const handleLoadMore = () => {
    // For now, we'll keep it simple since the backend query doesn't support pagination yet
    // This can be enhanced when pagination is added to the marketplace queries
  };

  const activeFilterCount = useMemo(() => {
    return (
      filters.categories.length +
      filters.brands.length +
      filters.conditions.length +
      (filters.searchQuery ? 1 : 0) +
      (filters.priceRange[0] !== null || filters.priceRange[1] !== null ? 1 : 0)
    );
  }, [filters]);

  // Create a mock pagination object for compatibility
  const mockPagination = {
    total: allProducts.length,
    page: 0,
    totalPages: 1,
    hasMore: false,
    nextCursor: null
  };

  return (  
    <div className="bg-card min-h-screen">
      <MarketplaceHeader onSearch={handleSearch} marketplaceType={marketplaceType} />

      <div className="">
        {/* Browse Hero Section */}
        <BrowseHeroSection
          filters={filters}
          productCount={allProducts.length}
          featuredProducts={allProducts.slice(0, 3)}
        />
        
        <div className="flex gap-6 container mx-auto px-6 py-6">
          {/* Filter Sidebar - Desktop */}
          <div className="hidden md:block w-72 flex-shrink-0">
            <FilterSidebar
              filters={filters}
              filterOptions={filterOptions}
              onFilterChange={handleFilterChange}
              onClearFilters={clearFilters}
              activeFilterCount={activeFilterCount}
              shouldRemovePreloadedFilters={shouldRemovePreloadedFilters}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 min-w-0">
            {/* Mobile Filter Button */}
            <div className="md:hidden mb-6">
              <FilterSidebar
                filters={filters}
                filterOptions={filterOptions}
                onFilterChange={handleFilterChange}
                onClearFilters={clearFilters}
                activeFilterCount={activeFilterCount}
                shouldRemovePreloadedFilters={shouldRemovePreloadedFilters}
              />
            </div>
            
            <ProductGrid
              products={allProducts}
              sortBy={sortBy}
              onSortChange={handleSortChange}
              isLoading={productsResult === undefined}
              activeFilterCount={activeFilterCount}
              pagination={mockPagination}
              onPageChange={handleLoadMore}
              filters={filters}
              onFilterChange={handleFilterChange}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
