"use client";

import { useMemo } from "react";
import Image from "next/image";
import { cn } from "@repo/ui/lib/utils";
import { FilterState } from "./MarketplaceContent";
import { getProductImageConfig } from "@/lib/image-utils";
import Link from "next/link";

interface BrowseHeroSectionProps {
  filters: FilterState;
  productCount?: number;
  featuredProducts?: Array<{
    _id: string;
    title: string;
    brand: string;
    price: number;
    images: string[];
    category: {
      name: string;
      slug: string;
    } | string;
  }>;
}

export function BrowseHeroSection({ filters, productCount = 0, featuredProducts = [] }: BrowseHeroSectionProps) {
  const heroTitle = useMemo(() => {
    if (filters.searchQuery) {
      return `Search Results for "${filters.searchQuery}"`;
    }
    
    if (filters.brands.length === 1) {
      return `${filters.brands[0]} Products`;
    }
    
    if (filters.categories.length === 1) {
      const categoryName = typeof filters.categories[0] === 'string' 
        ? filters.categories[0] 
        : (filters.categories[0] as any)?.name || 'Products';
      return `${categoryName.charAt(0).toUpperCase() + categoryName.slice(1)} Collection`;
    }
    
    if (filters.priceRange[0] !== null || filters.priceRange[1] !== null) {
      if (filters.priceRange[1] !== null && filters.priceRange[1] < 5000) {
        return "Affordable Luxury";
      } else if (filters.priceRange[0] !== null && filters.priceRange[0] > 10000) {
        return "Premium Collection";
      } else {
        return "Mid-Range Selection";
      }
    }
    
    return "Browse Your Way";
  }, [filters, productCount]);

  const heroSubtitle = useMemo(() => {
    if (filters.searchQuery) {
      return `${productCount} items found`;
    }
    
    if (filters.brands.length > 0 || filters.categories.length > 0 || filters.priceRange[0] !== null || filters.priceRange[1] !== null) {
      return `${productCount} items in this collection`;
    }
    
    return `${productCount} LUXURY ITEMS`;
  }, [filters, productCount]);

  const heroDescription = useMemo(() => {
    if (filters.searchQuery) {
      return "Discover exactly what you're looking for with our curated search results.";
    }
    
    if (filters.brands.length === 1) {
      return `Explore the finest ${filters.brands[0]} products, each piece carefully selected for quality and authenticity.`;
    }
    
    if (filters.categories.length === 1) {
      const categoryName = typeof filters.categories[0] === 'string' 
        ? filters.categories[0] 
        : (filters.categories[0] as any)?.name || 'products';
      return `Discover our curated collection of ${categoryName.toLowerCase()}, featuring the latest trends and timeless classics.`;
    }
    
    if (filters.priceRange[0] !== null || filters.priceRange[1] !== null) {
      if (filters.priceRange[1] !== null && filters.priceRange[1] < 5000) {
        return "Find exceptional value without compromising on quality. Our affordable luxury selection offers the perfect balance of style and price.";
      } else if (filters.priceRange[0] !== null && filters.priceRange[0] > 10000) {
        return "Experience the pinnacle of luxury with our premium collection. Each piece represents the finest craftsmanship and design.";
      } else {
        return "Discover the sweet spot between luxury and accessibility with our carefully curated mid-range selection.";
      }
    }
    
    return "Explore our vast collection of luxury items, from timeless classics to contemporary must-haves. Find your perfect piece today.";
  }, [filters]);

  return (
    <div className="w-full relative bg-primary px-4 md:px-0 h-[200px] md:h-[400px]">
      <div className="container py-6 h-full flex flex-row justify-between items-center w-full md:w-4/5 mx-auto">
        {/* Left side - Text content */}
        <div className="h-full flex flex-col justify-center w-full md:w-4/5">
          <div className="text-xs md:text-xs leading-6 opacity-60 tracking-widest font-medium text-accent uppercase">
            {heroSubtitle}
          </div>
          <h1 className="text-2xl md:text-3xl leading-tight md:leading-[56px] w-full md:w-4/5 font-bold text-white mb-4">
            {heroTitle}
          </h1>
          <div className="text-sm leading-6 w-full md:w-4/5">
            <div className="text-sm leading-5 text-secondary mb-0 mt-3">
              <div className="relative">
                <div className="overflow-hidden" style={{ maxHeight: '60px' }}>
                  {heroDescription}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Featured products images - Hidden on mobile */}
        <div className="hidden md:flex flex-row gap-4">
          {[1, 2, 3].map((itemNumber, index) => (
            <div key={`item-${itemNumber}`} className="flex-shrink-0">
              <div className="relative w-64 h-96 rounded-2xl overflow-hidden transition-colors duration-300 bg-transparent" style={{ maxHeight: '384px' }}>
                <Image
                  src={`/item${itemNumber}.png`}
                  alt={`Featured item ${itemNumber}`}
                  fill
                  className="rounded-2xl transition-transform duration-300 hover:scale-105 object-contain"
                  sizes="(max-width: 768px) 100vw, 320px"
                  priority={index === 0}
                  onError={(e) => {
                    console.error(`Failed to load image item${itemNumber}.png:`, e);
                  }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
