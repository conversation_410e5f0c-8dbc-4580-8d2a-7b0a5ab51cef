"use client";

import { useState, useMemo, useEffect, useCallback } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { toast } from "sonner";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { But<PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@repo/ui/components/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { Checkbox } from "@repo/ui/components/checkbox";
import { 
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Archive,
  Star,
  TrendingUp,
  Calendar,
  Package,
  DollarSign,
  BarChart3,
  ShoppingCart,
  Filter,
  Upload,
  Grid,
  List,
  Settings,
  Crown,
  ShoppingBag,
  Heart,
  FileText,
  Download,
  Mail,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Clock
} from "lucide-react";
import Link from "next/link";

interface FilterState {
  search: string;
  status: "all" | "draft" | "sent" | "paid" | "overdue" | "cancelled";
  sortBy: "newest" | "oldest" | "amount_high" | "amount_low" | "due_date" | "status";
  viewMode: "table" | "card";
  limit: number;
  offset: number;
  paymentMethod: string;
  amountRange: { min: string; max: string };
}

const STATUS_COLORS = {
  draft: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  sent: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  paid: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  overdue: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
  cancelled: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
};

export function EnhancedInvoiceManagement() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  // Initialize filters from URL params
  const initializeFiltersFromURL = useCallback((): FilterState => {
    const status = searchParams.get('status') || 'all';
    const sortBy = searchParams.get('sortBy') || 'newest';
    const viewMode = searchParams.get('viewMode') || 'table';
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const paymentMethod = searchParams.get('paymentMethod') || 'all';
    const minAmount = searchParams.get('minAmount') || '';
    const maxAmount = searchParams.get('maxAmount') || '';
    
    return {
      search: searchParams.get('search') || '',
      status: status as FilterState['status'],
      sortBy: sortBy as FilterState['sortBy'],
      viewMode: viewMode as FilterState['viewMode'],
      limit,
      offset,
      paymentMethod,
      amountRange: { min: minAmount, max: maxAmount },
    };
  }, [searchParams]);

  const [filters, setFilters] = useState<FilterState>(initializeFiltersFromURL);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams();
    if (filters.search) params.set('search', filters.search);
    if (filters.status !== 'all') params.set('status', filters.status);
    if (filters.sortBy !== 'newest') params.set('sortBy', filters.sortBy);
    if (filters.viewMode !== 'table') params.set('viewMode', filters.viewMode);
    if (filters.limit !== 50) params.set('limit', filters.limit.toString());
    if (filters.offset !== 0) params.set('offset', filters.offset.toString());
    if (filters.paymentMethod && filters.paymentMethod !== 'all') params.set('paymentMethod', filters.paymentMethod);
    if (filters.amountRange.min) params.set('minAmount', filters.amountRange.min);
    if (filters.amountRange.max) params.set('maxAmount', filters.amountRange.max);

    const newPath = `${pathname}?${params.toString()}`;
    router.replace(newPath);
  }, [filters, pathname, router]);

  // Backend queries
  const invoices = useQuery(api.invoices.getInvoices);
  const offlineSales = useQuery(api.offlineSales.getOfflineSales);

  // Data processing
  const filteredInvoices = useMemo(() => {
    if (!invoices) return [];
    
    let filtered = [...invoices];
    
    // Apply search filter
    if (filters.search) {
      filtered = filtered.filter(invoice =>
        invoice.invoiceNumber.toLowerCase().includes(filters.search.toLowerCase()) ||
        invoice.clientName.toLowerCase().includes(filters.search.toLowerCase()) ||
        invoice.clientEmail.toLowerCase().includes(filters.search.toLowerCase()) ||
        invoice.itemDescription.toLowerCase().includes(filters.search.toLowerCase())
      );
    }
    
    // Apply status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(invoice => invoice.status === filters.status);
    }
    
    // Apply payment method filter
    if (filters.paymentMethod && filters.paymentMethod !== 'all') {
      filtered = filtered.filter(invoice => invoice.paymentMethod === filters.paymentMethod);
    }
    
    // Apply amount range filter
    if (filters.amountRange.min) {
      filtered = filtered.filter(invoice => invoice.totalAmount >= parseFloat(filters.amountRange.min));
    }
    if (filters.amountRange.max) {
      filtered = filtered.filter(invoice => invoice.totalAmount <= parseFloat(filters.amountRange.max));
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'newest':
          return b.updatedAt - a.updatedAt;
        case 'oldest':
          return a.updatedAt - b.updatedAt;
        case 'amount_high':
          return b.totalAmount - a.totalAmount;
        case 'amount_low':
          return a.totalAmount - b.totalAmount;
        case 'due_date':
          if (!a.dueDate || !b.dueDate) return 0;
          return a.dueDate - b.dueDate;
        case 'status':
          return a.status.localeCompare(b.status);
        default:
          return 0;
      }
    });
    
    return filtered;
  }, [invoices, filters]);

  // Calculate summary stats
  const summaryStats = useMemo(() => {
    if (!invoices) return null;
    
    return {
      totalInvoices: invoices.length || 0,
      totalValue: invoices.reduce((sum: number, inv: any) => sum + inv.totalAmount, 0) || 0,
      paidValue: invoices.filter((inv: any) => inv.status === "paid").reduce((sum: number, inv: any) => sum + inv.totalAmount, 0) || 0,
      avgAmount: invoices.length > 0 ? invoices.reduce((sum: number, inv: any) => sum + inv.totalAmount, 0) / invoices.length : 0,
    };
  }, [invoices]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "paid": return "default";
      case "sent": return "secondary";
      case "draft": return "outline";
      case "overdue": return "destructive";
      case "cancelled": return "destructive";
      default: return "outline";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "paid": return <CheckCircle className="w-4 h-4" />;
      case "sent": return <Mail className="w-4 h-4" />;
      case "draft": return <FileText className="h-4 w-4" />;
      case "overdue": return <AlertTriangle className="h-4 w-4" />;
      case "cancelled": return <XCircle className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const handleDownloadInvoice = async (invoiceId: string) => {
    try {
      // For now, we'll redirect to the invoice detail page where PDF download is available
      window.open(`/seller/invoices/${invoiceId}`, '_blank');
    } catch (error) {
      toast.error("Failed to download invoice");
      console.error(error);
    }
  };

  const updateFilters = (newFilters: Partial<FilterState>) => {
    setFilters(prev => {
      const updated = { ...prev, ...newFilters };
      
      // Reset pagination when filters change
      if (newFilters.search !== undefined || newFilters.status !== undefined || 
          newFilters.paymentMethod !== undefined || newFilters.amountRange !== undefined) {
        updated.offset = 0;
      }
      
      return updated;
    });
  };

  const handleFilterChange = (key: keyof FilterState, value: any) => {
    updateFilters({ [key]: value });
  };

  const handleSelectItem = (invoiceId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, invoiceId]);
    } else {
      setSelectedItems(prev => prev.filter(id => id !== invoiceId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(filteredInvoices.map(invoice => invoice._id));
    } else {
      setSelectedItems([]);
    }
  };

  if (!invoices) {
    return (
      <div className="container mx-auto px-6 py-6 space-y-6">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground font-light">Loading invoices...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background">
      {/* Full Width Header with Search and Actions */}
      <div className="border-b border-border bg-card">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between gap-4">
            {/* Search and Filters */}
            <div className="flex items-center gap-4 flex-1">
              <div className="relative max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search invoices..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                  className="pl-10 rounded-xl bg-primary/5 border-border font-light placeholder:text-primary transition-all duration-300 focus:ring-2 focus:ring-primary/20"
                />
              </div>
              
              {/* Status Dropdown */}
              <Select value={filters.status} onValueChange={(value) => handleFilterChange("status", value as FilterState["status"])}>
                <SelectTrigger className="w-40 rounded-xl bg-primary/5 border-border font-light">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <Grid className="w-4 h-4" />
                      ALL
                    </div>
                  </SelectItem>
                  <SelectItem value="draft" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <FileText className="w-4 h-4" />
                      DRAFT
                    </div>
                  </SelectItem>
                  <SelectItem value="sent" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4" />
                      SENT
                    </div>
                  </SelectItem>
                  <SelectItem value="paid" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4" />
                      PAID
                    </div>
                  </SelectItem>
                  <SelectItem value="overdue" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="w-4 h-4" />
                      OVERDUE
                    </div>
                  </SelectItem>
                  <SelectItem value="cancelled" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <XCircle className="w-4 h-4" />
                      CANCELLED
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>

              {/* Filter Popover */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button 
                    variant="outline" 
                    className="rounded-xl font-light"
                  >
                    <Filter className="w-4 h-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-6" align="start">
                  <div className="space-y-4">
                    <h4 className="font-medium text-sm uppercase tracking-wide">Filters</h4>
                    
                    {/* Payment Method Filter */}
                    <div>
                      <label className="text-sm font-light text-muted-foreground mb-2 block">Payment Method</label>
                      <Select value={filters.paymentMethod} onValueChange={(value) => handleFilterChange("paymentMethod", value)}>
                        <SelectTrigger className="rounded-xl">
                          <SelectValue placeholder="All Payment Methods" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Payment Methods</SelectItem>
                          <SelectItem value="cash">Cash</SelectItem>
                          <SelectItem value="check">Check</SelectItem>
                          <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                          <SelectItem value="credit_card">Credit Card</SelectItem>
                          <SelectItem value="paypal">PayPal</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Amount Range */}
                    <div>
                      <label className="text-sm font-light text-muted-foreground mb-2 block">Amount Range</label>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Min"
                          value={filters.amountRange.min}
                          onChange={(e) => handleFilterChange("amountRange", { ...filters.amountRange, min: e.target.value })}
                          className="rounded-xl"
                        />
                        <Input
                          placeholder="Max"
                          value={filters.amountRange.max}
                          onChange={(e) => handleFilterChange("amountRange", { ...filters.amountRange, max: e.target.value })}
                          className="rounded-xl"
                        />
                      </div>
                    </div>

                                          <Button 
                        onClick={() => {
                          const newFilters = { ...filters, paymentMethod: "all", amountRange: { min: "", max: "" } };
                          setFilters(newFilters);
                        }}
                        variant="outline" 
                        className="w-full rounded-xl font-light"
                      >
                        Clear Filters
                      </Button>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-4">
              <Button 
                onClick={() => window.location.href = "/seller/invoices/new"}
                className="bg-accent text-accent-foreground hover:bg-accent/90 font-light rounded-xl px-6 py-2 transition-all duration-300"
              >
                <Plus className="w-4 h-4 mr-2" />
                NEW INVOICE
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Full Width Table */}
      <div className="flex-1">
        <Table>
          <TableHeader>
            <TableRow className="border-b border-border">
              <TableHead className="w-12">
                <Checkbox 
                  checked={selectedItems.length === filteredInvoices.length}
                  onCheckedChange={handleSelectAll}
                  className="rounded border-border"
                />
              </TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Invoice</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Client</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Product</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Sale Price</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Total Amount</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Status</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Due Date</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Payment Method</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredInvoices && filteredInvoices.length > 0 ? (
              filteredInvoices.map((invoice) => (
                <TableRow key={invoice._id} className="hover:bg-primary/5 transition-colors duration-300">
                  <TableCell>
                    <Checkbox 
                      checked={selectedItems.includes(invoice._id)}
                      onCheckedChange={(checked) => handleSelectItem(invoice._id, checked as boolean)}
                      className="rounded border-border"
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-primary/5 rounded-xl overflow-hidden border border-border flex items-center justify-center">
                        <FileText className="w-4 h-4 text-muted-foreground" />
                      </div>
                      <div>
                        <p className="font-light text-foreground text-sm">{invoice.invoiceNumber}</p>
                        <p className="text-muted-foreground text-xs font-light">{formatDate(invoice.updatedAt)}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-light text-foreground text-sm">{invoice.clientName}</p>
                      <p className="text-muted-foreground text-xs font-light">{invoice.clientEmail}</p>
                    </div>
                  </TableCell>
                  <TableCell className="font-light text-muted-foreground max-w-[200px] truncate">
                    {invoice.itemDescription}
                  </TableCell>
                  <TableCell className="font-light text-primary">{formatCurrency(invoice.salePrice)}</TableCell>
                  <TableCell className="font-light text-primary">{formatCurrency(invoice.totalAmount)}</TableCell>
                  <TableCell>
                    <Badge 
                      variant="outline" 
                      className={`text-xs font-light rounded-xl ${STATUS_COLORS[invoice.status as keyof typeof STATUS_COLORS] || 'bg-gray-100 text-gray-800'}`}
                    >
                      {invoice.status === 'draft' ? 'Draft' : 
                       invoice.status === 'sent' ? 'Sent' :
                       invoice.status === 'paid' ? 'Paid' :
                       invoice.status === 'overdue' ? 'Overdue' :
                       invoice.status === 'cancelled' ? 'Cancelled' :
                       invoice.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="font-light text-muted-foreground">
                    {invoice.dueDate ? formatDate(invoice.dueDate) : 'N/A'}
                  </TableCell>
                  <TableCell className="font-light text-muted-foreground">{invoice.paymentMethod}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="p-1 h-8 w-8">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/seller/invoices/${invoice._id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDownloadInvoice(invoice._id)}>
                          <Download className="mr-2 h-4 w-4" />
                          Download PDF
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Mail className="mr-2 h-4 w-4" />
                          Send Email
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Invoice
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={10} className="text-center py-16">
                  <div className="text-center">
                    <FileText className="w-12 h-12 text-muted-foreground/50 mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No invoices found</h3>
                    <p className="text-muted-foreground mb-4">
                      {filters.search || filters.status !== "all" 
                        ? "Try adjusting your search or filters"
                        : "Create your first invoice to get started"
                      }
                    </p>
                    {!filters.search && filters.status === "all" && (
                      <Button asChild>
                        <Link href="/seller/products">
                          <Plus className="h-4 w-4 mr-2" />
                          Create Invoice
                        </Link>
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Summary Footer */}
      {summaryStats && (
        <div className="border-t border-border bg-card px-6 py-4">
          <div className="flex justify-between items-center text-sm text-muted-foreground">
            <div className="flex gap-6">
              <span>Total Invoice Value: {formatCurrency(summaryStats.totalValue)}</span>
              <span>Total Paid: {formatCurrency(summaryStats.paidValue)}</span>
            </div>
            <span>{selectedItems.length}-{filteredInvoices.length} of {summaryStats.totalInvoices}</span>
          </div>
        </div>
      )}
    </div>
  );
}
