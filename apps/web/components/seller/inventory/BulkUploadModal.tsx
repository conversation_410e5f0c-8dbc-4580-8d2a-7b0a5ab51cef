"use client";

import { useState, useRef } from "react";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { toast } from "sonner";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@repo/ui/components/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { Badge } from "@repo/ui/components/badge";
import { Progress } from "@repo/ui/components/progress";
import { 
  Upload, 
  Download, 
  FileSpreadsheet, 
  CheckCircle, 
  AlertCircle, 
  X,
  Info
} from "lucide-react";

interface ProductData {
  title: string;
  description?: string;
  price: number;
  category: "clothing" | "sneakers" | "collectibles" | "accessories" | "handbags" | "jewelry" | "watches" | "sunglasses";
  brand: string;
  condition: "new" | "like_new" | "excellent" | "very_good" | "good" | "fair";
  size?: string;
  color?: string;
  material?: string;
  year?: number;
  originalPrice?: number;
  sku?: string;
  ownershipType?: "owned" | "consigned";
  sourceInfo?: {
    source: string;
    costPaid?: number;
    paymentMethod?: string;
    purchaseDate?: number;
    receipt?: string;
  };
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  shippingCost?: number;
  tags?: string[];
}

interface ValidationError {
  row: number;
  field: string;
  message: string;
}

interface FieldMapping {
  title: string;
  description: string;
  price: string;
  category: string;
  brand: string;
  condition: string;
  size: string;
  color: string;
  material: string;
  year: string;
  originalPrice: string;
  sku: string;
  ownershipType: string;
  source: string;
  costPaid: string;
  paymentMethod: string;
  tags: string;
  weight: string;
  shippingCost: string;
  length: string;
  width: string;
  height: string;
}

interface BulkUploadModalProps {
  onSuccess?: () => void;
}

export function BulkUploadModal({ onSuccess }: BulkUploadModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [products, setProducts] = useState<ProductData[]>([]);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [hasValidData, setHasValidData] = useState(false);
  const [csvHeaders, setCsvHeaders] = useState<string[]>([]);
  const [csvData, setCsvData] = useState<string[][]>([]);
  const [showFieldMapping, setShowFieldMapping] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [fileInfo, setFileInfo] = useState<{
    name: string;
    size: string;
    columns: number;
    rows: number;
  } | null>(null);
  const [fieldMapping, setFieldMapping] = useState<FieldMapping>({
    title: '',
    description: '',
    price: '',
    category: '',
    brand: '',
    condition: '',
    size: '',
    color: '',
    material: '',
    year: '',
    originalPrice: '',
    sku: '',
    ownershipType: '',
    source: '',
    costPaid: '',
    paymentMethod: '',
    tags: '',
    weight: '',
    shippingCost: '',
    length: '',
    width: '',
    height: ''
  });
  const [currentStep, setCurrentStep] = useState<'upload' | 'mapping' | 'review' | 'preview'>('upload');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const bulkCreateProducts = useMutation(api.productManagement.bulkCreateProducts);

  const processFile = async (file: File) => {
    try {
      let text: string;
      
      // Handle different file types
      if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        toast.error('Please export your Excel file as CSV format before uploading. Excel files are not directly supported.');
        return;
      } else if (file.name.endsWith('.csv') || file.name.endsWith('.txt')) {
        text = await file.text();
      } else {
        toast.error('Please upload a CSV or TXT file');
        return;
      }

      const lines = text.split('\n');
      if (lines.length < 2) {
        toast.error('CSV file must have at least a header row and one data row');
        return;
      }

      const headers = lines[0]?.split(',').map(h => h.trim().replace(/"/g, '')) || [];
      const dataRows = lines.slice(1).filter(line => line.trim().length > 0);
      
      // Parse CSV data properly
      const parsedData: string[][] = [];
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i]?.trim();
        if (!line) continue;

        // Handle quoted values properly
        const values: string[] = [];
        let currentValue = '';
        let inQuotes = false;
        
        for (let j = 0; j < line.length; j++) {
          const char = line[j];
          if (char === '"') {
            inQuotes = !inQuotes;
          } else if (char === ',' && !inQuotes) {
            values.push(currentValue.trim());
            currentValue = '';
          } else {
            currentValue += char;
          }
        }
        values.push(currentValue.trim());
        
        // Ensure we have the right number of columns
        while (values.length < headers.length) {
          values.push('');
        }
        parsedData.push(values);
      }
      
      setCsvHeaders(headers);
      setCsvData(parsedData);
      setFileInfo({
        name: file.name,
        size: formatFileSize(file.size),
        columns: headers.length,
        rows: dataRows.length
      });
      
      // Auto-map common field names
      const autoMapping: FieldMapping = {
        title: '',
        description: '',
        price: '',
        category: '',
        brand: '',
        condition: '',
        size: '',
        color: '',
        material: '',
        year: '',
        originalPrice: '',
        sku: '',
        ownershipType: '',
        source: '',
        costPaid: '',
        paymentMethod: '',
        tags: '',
        weight: '',
        shippingCost: '',
        length: '',
        width: '',
        height: ''
      };

      headers.forEach(header => {
        const lowerHeader = header.toLowerCase();
        if (lowerHeader.includes('title') || lowerHeader.includes('name')) autoMapping.title = header;
        if (lowerHeader.includes('desc')) autoMapping.description = header;
        if (lowerHeader.includes('price') || lowerHeader.includes('cost')) autoMapping.price = header;
        if (lowerHeader.includes('category') || lowerHeader.includes('cat')) autoMapping.category = header;
        if (lowerHeader.includes('brand')) autoMapping.brand = header;
        if (lowerHeader.includes('condition') || lowerHeader.includes('cond')) autoMapping.condition = header;
        if (lowerHeader.includes('size')) autoMapping.size = header;
        if (lowerHeader.includes('color') || lowerHeader.includes('colour')) autoMapping.color = header;
        if (lowerHeader.includes('material')) autoMapping.material = header;
        if (lowerHeader.includes('year')) autoMapping.year = header;
        if (lowerHeader.includes('original') && lowerHeader.includes('price')) autoMapping.originalPrice = header;
        if (lowerHeader.includes('sku') || lowerHeader.includes('id')) autoMapping.sku = header;
        if (lowerHeader.includes('ownership') || lowerHeader.includes('type')) autoMapping.ownershipType = header;
        if (lowerHeader.includes('source')) autoMapping.source = header;
        if (lowerHeader.includes('cost') && lowerHeader.includes('paid')) autoMapping.costPaid = header;
        if (lowerHeader.includes('payment') || lowerHeader.includes('method')) autoMapping.paymentMethod = header;
        if (lowerHeader.includes('tags') || lowerHeader.includes('tag')) autoMapping.tags = header;
        if (lowerHeader.includes('weight')) autoMapping.weight = header;
        if (lowerHeader.includes('shipping')) autoMapping.shippingCost = header;
        if (lowerHeader.includes('length')) autoMapping.length = header;
        if (lowerHeader.includes('width')) autoMapping.width = header;
        if (lowerHeader.includes('height')) autoMapping.height = header;
      });

      setFieldMapping(autoMapping);
      setShowFieldMapping(true);
      setCurrentStep('mapping');

    } catch (error) {
      console.error('Error reading file:', error);
      toast.error('Failed to read file. Please check the format and try again.');
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    await processFile(file);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;
    
    if (files.length > 1) {
      toast.error('Please drop only one file at a time');
      return;
    }
    
    const file = files[0];
    await processFile(file as File);
    
    // Update the file input to show the selected file
    if (fileInputRef.current) {
      const dataTransfer = new DataTransfer();
      dataTransfer.items.add(file as File);
      fileInputRef.current.files = dataTransfer.files;
    }
  };

  const handleUpload = async () => {
    if (!hasValidData || products.length === 0) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const result = await bulkCreateProducts({ products });
      
      if (result.success) {
        toast.success(result.message);
        setIsOpen(false);
        setProducts([]);
        setValidationErrors([]);
        setHasValidData(false);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        // Call the success callback to refresh inventory
        onSuccess?.();
      } else {
        toast.error('Failed to create products');
      }
    } catch (error: any) {
      toast.error(`Upload failed: ${error.message}`);
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const downloadTemplate = () => {
    const template = `title,description,price,category,brand,condition,size,color,material,year,originalPrice,sku,ownershipType,source,costPaid,paymentMethod,tags,weight,shippingCost,length,width,height
"Chanel Classic Flap Bag","Authentic Chanel Classic Flap Bag in black caviar leather with gold hardware",8500,handbags,Chanel,excellent,Medium,Black,Caviar Leather,2022,9500,CH-CF-BLK-001,owned,Boutique Purchase,7500,Credit Card,"chanel;classic;flap;black;caviar;luxury;handbag",0.8,50,25,8,16
"Rolex Submariner Date","Stainless steel Rolex Submariner with date function and black dial",12000,watches,Rolex,like_new,40mm,Black,Stainless Steel,2021,13500,ROLEX-SUB-001,owned,Authorized Dealer,11000,Credit Card,"rolex;submariner;stainless;steel;luxury;watch",0.15,25,40,40,12
"Supreme Box Logo Hoodie","Classic Supreme box logo hoodie in black",800,clothing,Supreme,good,L,Black,Cotton,2020,1200,SUP-BOGO-001,owned,Resale Shop,600,Cash,"supreme;box;logo;hoodie;streetwear",0.5,15,28,22,2`;
    
    const blob = new Blob([template], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'inventory_template.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const parseProductsWithMapping = async () => {
    if (!csvHeaders.length || !csvData.length) return;

    try {
      const parsedProducts: ProductData[] = [];
      const errors: ValidationError[] = [];

      for (let i = 0; i < csvData.length; i++) {
        const values = csvData[i];
        if (!values || values.length === 0) continue;

        const product: any = {};

        // Map CSV columns to product fields using field mapping
        Object.entries(fieldMapping).forEach(([field, csvHeader]) => {
          if (!csvHeader || csvHeader === "unmapped") return;
          
          const headerIndex = csvHeaders.indexOf(csvHeader);
          if (headerIndex === -1) return;
          
          const value = values[headerIndex] || '';
          const cleanValue = value.replace(/"/g, '').trim();
          
          console.log(`Processing field: ${field}, CSV header: ${csvHeader}, value: "${value}", cleanValue: "${cleanValue}"`);
          
          switch (field) {
            case 'title':
            case 'description':
            case 'brand':
            case 'size':
            case 'color':
            case 'material':
            case 'sku':
              product[field] = cleanValue;
              break;
            case 'price':
            case 'originalPrice':
            case 'year':
            case 'weight':
            case 'shippingCost':
              const numValue = parseFloat(cleanValue);
              if (!isNaN(numValue)) {
                product[field] = numValue;
                console.log(`Parsed ${field}: ${cleanValue} -> ${numValue}`);
              } else {
                console.log(`Failed to parse ${field}: "${cleanValue}" is not a valid number`);
                if (field === 'price') {
                  errors.push({ row: i + 1, field: 'price', message: `Invalid price: "${cleanValue}". Must be a valid number.` });
                }
              }
              break;
            case 'category':
              const category = cleanValue.toLowerCase();
              if (['clothing', 'sneakers', 'collectibles', 'accessories', 'handbags', 'jewelry', 'watches', 'sunglasses'].includes(category)) {
                product.category = category as ProductData['category'];
              } else {
                errors.push({ row: i + 1, field: 'category', message: `Invalid category: ${cleanValue}. Must be one of: clothing, sneakers, collectibles, accessories, handbags, jewelry, watches, sunglasses` });
              }
              break;
            case 'condition':
              const condition = cleanValue.toLowerCase();
              if (['new', 'like_new', 'excellent', 'very_good', 'good', 'fair'].includes(condition)) {
                product.condition = condition as ProductData['condition'];
              } else {
                errors.push({ row: i + 1, field: 'condition', message: `Invalid condition: ${cleanValue}. Must be one of: new, like_new, excellent, very_good, good, fair` });
              }
              break;
            case 'ownershipType':
              const ownershipType = cleanValue.toLowerCase();
              if (['owned', 'consigned'].includes(ownershipType)) {
                product.ownershipType = ownershipType as ProductData['ownershipType'];
              } else {
                errors.push({ row: i + 1, field: 'ownershipType', message: `Invalid ownership type: ${cleanValue}. Must be one of: owned, consigned` });
              }
              break;
            case 'tags':
              product.tags = cleanValue ? cleanValue.split(';').map(tag => tag.trim()).filter(tag => tag.length > 0) : [];
              break;
            case 'source':
              if (!product.sourceInfo) product.sourceInfo = {};
              (product.sourceInfo as any).source = cleanValue;
              break;
            case 'costPaid':
              const costValue = parseFloat(cleanValue);
              if (!isNaN(costValue)) {
                if (!product.sourceInfo) product.sourceInfo = {};
                (product.sourceInfo as any).costPaid = costValue;
              }
              break;
            case 'paymentMethod':
              if (!product.sourceInfo) product.sourceInfo = {};
              (product.sourceInfo as any).paymentMethod = cleanValue;
              break;
            case 'length':
            case 'width':
            case 'height':
              if (!product.dimensions) product.dimensions = {};
              const dimValue = parseFloat(cleanValue);
              if (!isNaN(dimValue)) {
                (product.dimensions as any)[field] = dimValue;
              }
              break;
          }
        });

        // Validate required fields
        if (!product.title || product.title.length < 3) {
          errors.push({ row: i + 1, field: 'title', message: 'Title must be at least 3 characters' });
        }
        if (!product.price || product.price <= 0) {
          errors.push({ row: i + 1, field: 'price', message: 'Price must be greater than 0' });
        }
        if (!product.category) {
          errors.push({ row: i + 1, field: 'category', message: 'Category is required' });
        }
        if (!product.brand) {
          errors.push({ row: i + 1, field: 'brand', message: 'Brand is required' });
        }
        if (!product.condition) {
          errors.push({ row: i + 1, field: 'condition', message: 'Condition is required' });
        }

        // Set defaults
        if (!product.ownershipType) product.ownershipType = 'owned';
        if (!product.description) product.description = `${product.brand} ${product.title}`;

        parsedProducts.push(product as ProductData);
      }

      setProducts(parsedProducts);
      setValidationErrors(errors);
      setHasValidData(parsedProducts.length > 0 && errors.length === 0);

      console.log('Parsed products:', parsedProducts);
      console.log('Validation errors:', errors);
      console.log('Products state will be set to:', parsedProducts.length);

      if (errors.length > 0) {
        toast.error(`Found ${errors.length} validation errors. Please fix them before uploading.`);
      } else {
        toast.success(`Successfully parsed ${parsedProducts.length} products from CSV`);
      }

    } catch (error) {
      console.error('Error parsing products:', error);
      toast.error('Failed to parse products. Please check your field mapping.');
    }
  };

  // Auto-parse products when field mapping changes
  const handleFieldMappingChange = (field: string, value: string) => {
    const newMapping = { ...fieldMapping, [field]: value === "unmapped" ? "" : value };
    setFieldMapping(newMapping);
    
    // Auto-parse after a short delay to avoid excessive parsing
    setTimeout(() => {
      if (Object.values(newMapping).some(v => v && v !== "unmapped")) {
        parseProductsWithMapping();
      }
    }, 500);
  };

  const clearData = () => {
    setProducts([]);
    setValidationErrors([]);
    setHasValidData(false);
    setCsvHeaders([]);
    setCsvData([]);
    setShowFieldMapping(false);
    setFileInfo(null);
    setCurrentStep('upload');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const goToReview = () => {
    if (Object.values(fieldMapping).some(v => v && v !== "unmapped")) {
      parseProductsWithMapping();
      setCurrentStep('review');
    } else {
      toast.error('Please map at least one field before proceeding');
    }
  };

  const goToPreview = () => {
    if (validationErrors.length === 0) {
      setCurrentStep('preview');
    } else {
      toast.error('Please fix validation errors before proceeding');
    }
  };

  const goBackToMapping = () => {
    setCurrentStep('mapping');
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        // Clear all data when modal is closed
        clearData();
      }
      setIsOpen(open);
    }}>
      <DialogTrigger asChild>
        <Button variant="outline" className="rounded-xl font-light">
          <Upload className="w-4 h-4 mr-2" />
          BULK UPLOAD
        </Button>
      </DialogTrigger>
      <DialogContent className={`max-h-[90vh] overflow-y-auto ${
        showFieldMapping ? 'min-w-6xl max-w-6xl' : 'max-w-4xl'
      }`}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileSpreadsheet className="w-5 h-5" />
            {fileInfo?.name || 'Bulk Upload Inventory'}
          </DialogTitle>
          <DialogDescription>
            {currentStep === 'upload' && 'Upload a CSV file to create multiple products at once. Products will be created as drafts without images.'}
            {currentStep === 'mapping' && 'Map CSV columns to product fields'}
            {currentStep === 'review' && 'Review and validate your mapped data'}
            {currentStep === 'preview' && 'Preview products before upload'}
          </DialogDescription>
        </DialogHeader>

        {/* Progress Steps */}
        {fileInfo && (
          <div className="flex items-center justify-center mb-6">
            <div className="flex items-center space-x-2">
              {['upload', 'mapping', 'review', 'preview'].map((step, index) => (
                <div key={step} className="flex items-center">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    currentStep === step 
                      ? 'border-primary bg-primary text-primary-foreground' 
                      : currentStep === 'upload' || ['mapping', 'review', 'preview'].indexOf(currentStep) >= index
                        ? 'border-primary/30 bg-primary/10 text-primary'
                        : 'border-border bg-muted text-muted-foreground'
                  }`}>
                    {index + 1}
                  </div>
                  {index < 3 && (
                    <div className={`w-16 h-0.5 mx-2 ${
                      ['mapping', 'review', 'preview'].indexOf(currentStep) > index 
                        ? 'bg-primary' 
                        : 'bg-border'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="space-y-6">
          {/* Template Download and File Upload - Only show when not mapping fields */}
          {currentStep === 'upload' && (
            <>
              {/* Template Download */}
              <div className="bg-primary/5 rounded-xl p-4 border border-border">
                <div className="flex items-center gap-2 mb-3">
                  <Info className="w-4 h-4 text-primary" />
                  <h4 className="font-medium">CSV Template</h4>
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  Download our CSV template to ensure proper formatting. Required fields: title, price, category, brand, condition.
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={downloadTemplate}
                  className="rounded-xl font-light"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download Template
                </Button>
              </div>

              {/* File Upload */}
              <div className="space-y-4">
                <Label htmlFor="csv-file">Upload CSV File</Label>
                <div
                  className={`border-2 border-dashed rounded-xl p-8 text-center transition-colors ${
                    isDragOver 
                      ? 'border-primary bg-primary/5' 
                      : 'border-border hover:border-primary/50'
                  }`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <div className="flex items-center gap-3">
                    <Upload className={`w-8 h-8 ${isDragOver ? 'text-primary' : 'text-muted-foreground'}`} />
                    <div>
                      <p className="font-medium">
                        {isDragOver ? 'Drop your CSV file here' : 'Drag and drop your CSV file here'}
                      </p>
                      <p className="text-sm text-muted-foreground mt-1">
                        or click to browse
                      </p>
                    </div>
                    <Input
                      id="csv-file"
                      ref={fileInputRef}
                      type="file"
                      accept=".csv,.txt"
                      onChange={handleFileUpload}
                      className="hidden"
                      disabled={isUploading}
                      placeholder="Select a CSV file to upload"
                    />
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => fileInputRef.current?.click()}
                      className="rounded-xl font-light"
                    >
                      Browse Files
                    </Button>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Supported formats: CSV, TXT. Maximum file size: 10MB
                </p>
              </div>
            </>
          )}

          {/* Field Mapping */}
          {currentStep === 'mapping' && fileInfo && (
            <div className="space-y-6">
              {/* File Information */}
              <div className="bg-primary/5 rounded-xl p-4 border border-border">
                <div className="flex items-center gap-2 mb-3">
                  <FileSpreadsheet className="w-4 h-4 text-primary" />
                  <h4 className="font-medium">Selected file</h4>
                </div>
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                    <FileSpreadsheet className="w-4 h-4 text-primary" />
                  </div>
                  <div>
                    <div className="font-medium">{fileInfo.name}</div>
                    <div className="text-sm text-muted-foreground">CSV Document • {fileInfo.size}</div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-background rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-primary">{fileInfo.columns}</div>
                    <div className="text-sm text-muted-foreground">Columns found</div>
                  </div>
                  <div className="bg-background rounded-lg p-3 text-center">
                    <div className="text-2xl font-bold text-primary">{fileInfo.rows}</div>
                    <div className="text-sm text-muted-foreground">Rows found</div>
                  </div>
                </div>
              </div>

              {/* Field Mapping Table */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Map CSV Fields to Product Fields</h4>
                  <Button 
                    onClick={goToReview}
                    disabled={!Object.values(fieldMapping).some(v => v && v !== "unmapped")}
                    className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-xl font-light"
                  >
                    Continue to Review
                  </Button>
                </div>
                
                <div className="grid grid-cols-3 gap-6">
                  {/* Left: CSV Columns */}
                  <div className="space-y-3">
                    <h5 className="font-medium text-sm text-muted-foreground">File column</h5>
                    <div className="space-y-2">
                      {csvHeaders.map((header, index) => (
                        <div key={index} className="text-sm p-2 bg-muted/30 rounded-lg">
                          {header}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Middle: Mapping */}
                  <div className="space-y-3">
                    <h5 className="font-medium text-sm text-muted-foreground">Attributes</h5>
                    <div className="space-y-2">
                      {Object.entries(fieldMapping).map(([field, csvHeader]) => (
                        <div key={field} className="flex items-center gap-2">
                          <Select 
                            value={csvHeader || "unmapped"} 
                            onValueChange={(value) => setFieldMapping(prev => ({ 
                              ...prev, 
                              [field]: value === "unmapped" ? "" : value 
                            }))}
                          >
                            <SelectTrigger className="w-full rounded-lg">
                              <SelectValue placeholder="Select CSV column" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="unmapped">-- Not Used --</SelectItem>
                              {csvHeaders.map(header => (
                                <SelectItem key={header} value={header}>
                                  {header}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {csvHeader && csvHeader !== "unmapped" && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setFieldMapping(prev => ({ ...prev, [field]: "" }))}
                              className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Right: Preview Data */}
                  <div className="space-y-3">
                    <h5 className="font-medium text-sm text-muted-foreground">Preview</h5>
                    <div className="space-y-2 max-h-80 overflow-y-auto">
                      {csvData.slice(0, 5).map((row, rowIndex) => (
                        <div key={rowIndex} className="text-xs p-2 bg-muted/20 rounded border">
                          <div className="font-medium mb-1">Row {rowIndex + 1}</div>
                          {row.slice(0, 3).map((value, colIndex) => (
                            <div key={colIndex} className="text-muted-foreground">
                              {csvHeaders[colIndex]}: {value || '(empty)'}
                            </div>
                          ))}
                          {row.length > 3 && (
                            <div className="text-muted-foreground">... and {row.length - 3} more columns</div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                
                <div className="bg-primary/5 rounded-xl p-3 border border-border">
                  <div className="flex items-center gap-2 mb-2">
                    <Info className="w-4 h-4 text-primary" />
                    <span className="text-sm font-medium">Field Mapping Tips</span>
                  </div>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    <li>• Required fields are marked with <span className="text-red-500">*</span></li>
                    <li>• Map CSV columns to the appropriate product fields</li>
                    <li>• Leave unused fields as "-- Not Used --"</li>
                    <li>• Preview shows sample data from your CSV</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Review Step */}
          {currentStep === 'review' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Review Mapped Data</h4>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    onClick={goBackToMapping}
                    className="rounded-xl font-light"
                  >
                    Back to Mapping
                  </Button>
                  <Button 
                    onClick={goToPreview}
                    disabled={validationErrors.length > 0}
                    className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-xl font-light"
                  >
                    Continue to Preview
                  </Button>
                </div>
              </div>

              {/* Validation Errors */}
              {validationErrors.length > 0 && (
                <div className="bg-destructive/5 border border-destructive/20 rounded-xl p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <AlertCircle className="w-4 h-4 text-destructive" />
                    <h4 className="font-medium text-destructive">Validation Errors ({validationErrors.length})</h4>
                  </div>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {validationErrors.map((error, index) => (
                      <div key={index} className="text-sm text-destructive">
                        <span className="font-medium">Row {error.row}:</span> {error.field} - {error.message}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Mapped Fields Summary */}
              <div className="bg-primary/5 rounded-xl p-4 border border-border">
                <h5 className="font-medium mb-3">Mapped Fields Summary</h5>
                <div className="grid grid-cols-2 gap-4">
                  {Object.entries(fieldMapping).map(([field, csvHeader]) => {
                    if (!csvHeader || csvHeader === "unmapped") return null;
                    return (
                      <div key={field} className="flex items-center gap-2 text-sm">
                        <span className="font-medium capitalize">{field}:</span>
                        <span className="text-muted-foreground">{csvHeader}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Preview Step */}
          {currentStep === 'preview' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Preview Products</h4>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    onClick={goBackToMapping}
                    className="rounded-xl font-light"
                  >
                    Back to Mapping
                  </Button>
                </div>
              </div>

              {/* Products Preview */}
              {products.length > 0 ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Products to Upload ({products.length})</h4>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={clearData}
                      className="text-muted-foreground hover:text-foreground"
                    >
                      <X className="w-4 h-4 mr-2" />
                      Clear
                    </Button>
                  </div>
                  <div className="max-h-60 overflow-y-auto border border-border rounded-xl">
                    <div className="grid grid-cols-1 gap-2 p-4">
                      {products.slice(0, 10).map((product, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-primary/5 rounded-lg border border-border">
                          <div className="flex-1">
                            <div className="font-medium text-sm">{product.title || 'No title'}</div>
                            <div className="text-xs text-muted-foreground">
                              {product.brand || 'No brand'} • {product.category || 'No category'} • {
                                product.price && !isNaN(product.price) 
                                  ? `$${product.price.toFixed(2)}` 
                                  : 'No price'
                              }
                            </div>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {product.condition || 'No condition'}
                          </Badge>
                        </div>
                      ))}
                      {products.length > 10 && (
                        <div className="text-center text-sm text-muted-foreground py-2">
                          ... and {products.length - 10} more products
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-muted/20 rounded-lg p-6 text-center">
                  <div className="text-muted-foreground mb-2">No products found</div>
                  <div className="text-sm text-muted-foreground">
                    Products should have been parsed in the review step. 
                    Please go back to mapping and ensure fields are properly mapped.
                  </div>
                </div>
              )}

              {/* Upload Progress */}
              {isUploading && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Uploading products...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} className="w-full" />
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => {
              setIsOpen(false);
              clearData();
            }}
            disabled={isUploading}
            className="rounded-xl font-light"
          >
            Cancel
          </Button>
          {currentStep === 'preview' && products.length > 0 && !isUploading && (
            <Button 
              onClick={handleUpload}
              disabled={!hasValidData}
              className="bg-accent text-accent-foreground hover:bg-accent/90 rounded-xl font-light"
            >
              <Upload className="w-4 h-4 mr-2" />
              Upload {products.length} Products
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
