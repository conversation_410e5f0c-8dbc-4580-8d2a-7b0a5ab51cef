"use client";

import { forwardRef } from "react";

interface Product {
  id: string;
  title: string;
  brand: string;
  price: number;
  category: string;
  condition: string;
  images: string[];
}

interface ClientInfo {
  name: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  salePrice: number;
  paymentMethod: string;
}

interface Invoice {
  id: string;
  number: string;
  date: string;
  dueDate: string;
  status: "draft" | "sent" | "paid" | "overdue";
  product: Product;
  client: ClientInfo;
  seller: {
    name: string;
    email: string;
    phone: string;
    address: string;
  };
  subtotal: number;
  tax: number;
  total: number;
  notes: string;
}

interface InvoiceTemplateProps {
  invoice: Invoice;
}

export const InvoiceTemplate = forwardRef<HTMLDivElement, InvoiceTemplateProps>(
  ({ invoice }, ref) => {
    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(amount);
    };

    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    return (
      <div 
        ref={ref}
        className="max-w-4xl mx-auto bg-white p-8 shadow-lg"
        style={{ 
          fontFamily: 'system-ui, -apple-system, sans-serif',
          fontSize: '14px',
          lineHeight: '1.5',
          color: '#000'
        }}
      >
        {/* Header */}
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 
              className="text-4xl font-bold mb-2"
              style={{ color: '#000', fontWeight: 'bold' }}
            >
              MODA
            </h1>
            <p className="text-lg text-gray-600">
              Luxury Marketplace
            </p>
            <div className="mt-4 text-sm text-gray-600">
              <p>123 Luxury Avenue</p>
              <p>New York, NY 10001</p>
              <p><EMAIL></p>
              <p>(555) 123-4567</p>
            </div>
          </div>
          
          <div className="text-right">
            <h2 
              className="text-3xl font-bold mb-4"
              style={{ color: '#000' }}
            >
              INVOICE
            </h2>
            <div className="space-y-2">
              <div>
                <span className="font-semibold">Invoice #: </span>
                <span className="font-mono">{invoice.number}</span>
              </div>
              <div>
                <span className="font-semibold">Date: </span>
                <span>{formatDate(invoice.date)}</span>
              </div>
              <div>
                <span className="font-semibold">Due Date: </span>
                <span>{formatDate(invoice.dueDate)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Seller and Client Information */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          <div>
            <h3 className="text-lg font-bold mb-3" style={{ color: '#000' }}>
              From:
            </h3>
            <div className="space-y-1">
              <p className="font-semibold">{invoice.seller.name}</p>
              <p>{invoice.seller.email}</p>
              <p>{invoice.seller.phone}</p>
              <p>{invoice.seller.address}</p>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-bold mb-3" style={{ color: '#000' }}>
              Bill To:
            </h3>
            <div className="space-y-1">
              <p className="font-semibold">{invoice.client.name}</p>
              <p>{invoice.client.email}</p>
              {invoice.client.phone && <p>{invoice.client.phone}</p>}
              <div>
                <p>{invoice.client.address.street}</p>
                <p>
                  {invoice.client.address.city}, {invoice.client.address.state} {invoice.client.address.zipCode}
                </p>
                <p>{invoice.client.address.country}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Invoice Items */}
        <div className="mb-8">
          <table className="w-full border-collapse">
            <thead>
              <tr style={{ backgroundColor: '#f8f9fa' }}>
                <th 
                  className="border border-gray-300 px-4 py-3 text-left font-bold"
                  style={{ color: '#000' }}
                >
                  Description
                </th>
                <th 
                  className="border border-gray-300 px-4 py-3 text-center font-bold"
                  style={{ color: '#000' }}
                >
                  Qty
                </th>
                <th 
                  className="border border-gray-300 px-4 py-3 text-right font-bold"
                  style={{ color: '#000' }}
                >
                  Unit Price
                </th>
                <th 
                  className="border border-gray-300 px-4 py-3 text-right font-bold"
                  style={{ color: '#000' }}
                >
                  Total
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="border border-gray-300 px-4 py-4">
                  <div>
                    <p className="font-semibold text-base">{invoice.product.title}</p>
                    <p className="text-sm text-gray-600">
                      {invoice.product.brand} • {invoice.product.condition}
                    </p>
                    <p className="text-sm text-gray-600">
                      Category: {invoice.product.category}
                    </p>
                  </div>
                </td>
                <td className="border border-gray-300 px-4 py-4 text-center">
                  1
                </td>
                <td className="border border-gray-300 px-4 py-4 text-right">
                  {formatCurrency(invoice.client.salePrice)}
                </td>
                <td className="border border-gray-300 px-4 py-4 text-right font-semibold">
                  {formatCurrency(invoice.client.salePrice)}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        {/* Totals */}
        <div className="flex justify-end mb-8">
          <div className="w-80">
            <div className="space-y-2">
              <div className="flex justify-between py-2">
                <span>Subtotal:</span>
                <span>{formatCurrency(invoice.subtotal)}</span>
              </div>
              <div className="flex justify-between py-2">
                <span>Tax (8%):</span>
                <span>{formatCurrency(invoice.tax)}</span>
              </div>
              <div 
                className="flex justify-between py-3 border-t-2 border-gray-300 font-bold text-lg"
                style={{ color: '#000' }}
              >
                <span>Total:</span>
                <span>{formatCurrency(invoice.total)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Information */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-3" style={{ color: '#000' }}>
            Payment Information
          </h3>
          <div className="bg-gray-50 p-4 rounded">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="font-semibold">Payment Method: </span>
                <span>{invoice.client.paymentMethod}</span>
              </div>
              <div>
                <span className="font-semibold">Payment Terms: </span>
                <span>Net 30 days</span>
              </div>
            </div>
          </div>
        </div>

        {/* Notes */}
        {invoice.notes && (
          <div className="mb-8">
            <h3 className="text-lg font-bold mb-3" style={{ color: '#000' }}>
              Notes
            </h3>
            <p className="text-gray-700">{invoice.notes}</p>
          </div>
        )}

        {/* Footer */}
        <div className="border-t-2 border-gray-300 pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm text-gray-600">
            <div>
              <h4 className="font-semibold mb-2" style={{ color: '#000' }}>
                Authenticity Guarantee
              </h4>
              <p>
                All items sold through MODA are guaranteed authentic and have been 
                verified by our expert authentication team.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2" style={{ color: '#000' }}>
                Return Policy
              </h4>
              <p>
                Items may be returned within 14 days of purchase in original condition. 
                Please contact us for return authorization.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2" style={{ color: '#000' }}>
                Contact Us
              </h4>
              <p>
                For questions about this invoice, please contact us at 
                <EMAIL> or (555) 123-4567.
              </p>
            </div>
          </div>
          
          <div className="text-center mt-6 pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-500">
              Thank you for choosing MODA - Where Luxury Meets Trust
            </p>
          </div>
        </div>
      </div>
    );
  }
);

InvoiceTemplate.displayName = "InvoiceTemplate";
