"use client";

import { useState, useEffect } from "react";
import { Button } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { 
  FileText, 
  Download, 
  Mail, 
  ArrowLeft,
  CheckCircle,
  Loader2,
  Calendar,
  Hash
} from "lucide-react";
import { InvoiceTemplate } from "./InvoiceTemplate";

interface Product {
  id: string;
  title: string;
  brand: string;
  price: number;
  category: string;
  condition: string;
  images: string[];
}

interface ClientInfo {
  name: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  salePrice: number;
  paymentMethod: string;
}

interface Invoice {
  id: string;
  number: string;
  date: string;
  dueDate: string;
  status: "draft" | "sent" | "paid" | "overdue";
  product: Product;
  client: ClientInfo;
  seller: {
    name: string;
    email: string;
    phone: string;
    address: string;
  };
  subtotal: number;
  tax: number;
  total: number;
  notes: string;
}

interface InvoiceGeneratorProps {
  product: Product;
  client: ClientInfo;
  onInvoiceGenerated: (invoiceId: string) => void;
  onBack: () => void;
}

export function InvoiceGenerator({ 
  product, 
  client, 
  onInvoiceGenerated, 
  onBack 
}: InvoiceGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [isEmailSending, setIsEmailSending] = useState(false);

  useEffect(() => {
    generateInvoice();
  }, []);

  const generateInvoice = async () => {
    setIsGenerating(true);
    
    try {
      // Simulate invoice generation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const invoiceNumber = `HV-${Date.now().toString().slice(-6)}`;
      const currentDate = new Date();
      const dueDate = new Date(currentDate);
      dueDate.setDate(dueDate.getDate() + 30); // 30 days payment terms
      
      const subtotal = client.salePrice;
      const taxRate = 0.08; // 8% tax rate
      const tax = subtotal * taxRate;
      const total = subtotal + tax;
      
      const newInvoice: Invoice = {
        id: `invoice-${Date.now()}`,
        number: invoiceNumber,
        date: currentDate.toISOString().split('T')[0] || "",
        dueDate: dueDate.toISOString().split('T')[0] || "",
        status: "draft",
        product,
        client,
        seller: {
          name: "John Seller", // This would come from user context
          email: "<EMAIL>",
          phone: "(*************",
          address: "123 Seller St, New York, NY 10001"
        },
        subtotal,
        tax,
        total,
        notes: "Thank you for your business! Payment is due within 30 days."
      };
      
      setInvoice(newInvoice);
      onInvoiceGenerated(invoiceNumber);
    } catch (error) {
      console.error("Failed to generate invoice:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownloadPDF = async () => {
    if (!invoice) return;
    
    try {
      // In a real implementation, this would generate and download a PDF
      console.log("Downloading PDF for invoice:", invoice.number);
      
      // Simulate PDF generation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Create a mock download
      const element = document.createElement('a');
      element.href = 'data:text/plain;charset=utf-8,' + encodeURIComponent(`Invoice ${invoice.number}`);
      element.download = `invoice-${invoice.number}.pdf`;
      element.style.display = 'none';
      document.body.appendChild(element);
      element.click();
      document.body.removeChild(element);
    } catch (error) {
      console.error("Failed to download PDF:", error);
    }
  };

  const handleEmailInvoice = async () => {
    if (!invoice) return;
    
    setIsEmailSending(true);
    try {
      // Simulate email sending
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log("Email sent to:", client.email);
      
      // Update invoice status to sent
      setInvoice(prev => prev ? { ...prev, status: "sent" } : null);
    } catch (error) {
      console.error("Failed to send email:", error);
    } finally {
      setIsEmailSending(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isGenerating) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <Loader2 className="w-8 h-8 text-blue-600 dark:text-blue-400 animate-spin" />
          </div>
          <h3 className="text-xl font-bold text-black dark:text-white mb-2">
            Generating Invoice
          </h3>
          <p className="text-neutral-600 dark:text-neutral-400">
            Creating professional invoice for your offline sale...
          </p>
        </div>
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <FileText className="w-8 h-8 text-red-600 dark:text-red-400" />
          </div>
          <h3 className="text-xl font-bold text-black dark:text-white mb-2">
            Failed to Generate Invoice
          </h3>
          <p className="text-neutral-600 dark:text-neutral-400 mb-4">
            There was an error generating the invoice. Please try again.
          </p>
          <Button onClick={generateInvoice} variant="outline">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Invoice Header */}
      <div className="text-center">
        <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
        </div>
        <h3 className="text-xl font-bold text-black dark:text-white mb-2">
          Invoice Generated Successfully
        </h3>
        <p className="text-neutral-600 dark:text-neutral-400">
          Professional invoice ready for your client
        </p>
      </div>

      {/* Invoice Summary */}
      <Card className="p-6 bg-neutral-50 dark:bg-neutral-800">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <Hash className="w-4 h-4 text-neutral-400" />
              <span className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Invoice Number
              </span>
            </div>
            <p className="text-lg font-bold text-black dark:text-white font-mono">
              {invoice.number}
            </p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <Calendar className="w-4 h-4 text-neutral-400" />
              <span className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Invoice Date
              </span>
            </div>
            <p className="text-lg font-bold text-black dark:text-white">
              {formatDate(invoice.date)}
            </p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <span className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Total Amount
              </span>
            </div>
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">
              {formatCurrency(invoice.total)}
            </p>
          </div>
        </div>
      </Card>

      {/* Client Information */}
      <Card className="p-6">
        <h4 className="font-semibold text-black dark:text-white mb-4">
          Bill To:
        </h4>
        <div className="space-y-2 text-neutral-700 dark:text-neutral-300">
          <p className="font-medium">{client.name}</p>
          <p>{client.email}</p>
          {client.phone && <p>{client.phone}</p>}
          <div>
            <p>{client.address.street}</p>
            <p>{client.address.city}, {client.address.state} {client.address.zipCode}</p>
            <p>{client.address.country}</p>
          </div>
        </div>
      </Card>

      {/* Product Details */}
      <Card className="p-6">
        <h4 className="font-semibold text-black dark:text-white mb-4">
          Item Details:
        </h4>
        <div className="flex items-start space-x-4">
          <img
            src={product.images[0] || "/api/placeholder/80/80"}
            alt={product.title}
            className="w-20 h-20 object-cover rounded-lg"
          />
          <div className="flex-1">
            <h5 className="font-semibold text-black dark:text-white">
              {product.title}
            </h5>
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              {product.brand} • {product.condition}
            </p>
            <div className="flex items-center space-x-2 mt-2">
              <Badge variant="outline">{product.category}</Badge>
            </div>
          </div>
          <div className="text-right">
            <p className="text-lg font-bold text-black dark:text-white">
              {formatCurrency(client.salePrice)}
            </p>
            <p className="text-sm text-neutral-500">
              Qty: 1
            </p>
          </div>
        </div>
      </Card>

      {/* Invoice Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Button
          variant="outline"
          onClick={handleDownloadPDF}
          className="h-12"
        >
          <Download className="w-4 h-4 mr-2" />
          Download PDF
        </Button>
        
        <Button
          variant="outline"
          onClick={handleEmailInvoice}
          disabled={isEmailSending}
          className="h-12"
        >
          {isEmailSending ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Mail className="w-4 h-4 mr-2" />
          )}
          {isEmailSending ? "Sending..." : "Email to Client"}
        </Button>
        
        <Button
          onClick={() => onInvoiceGenerated(invoice.number)}
          className="bg-green-600 text-white hover:bg-green-700 h-12"
        >
          <CheckCircle className="w-4 h-4 mr-2" />
          Complete
        </Button>
      </div>

      {/* Back Button */}
      <div className="flex justify-start pt-4">
        <Button
          variant="ghost"
          onClick={onBack}
          className="text-neutral-600 dark:text-neutral-400"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Client Info
        </Button>
      </div>

      {/* Invoice Preview (Hidden) */}
      <div className="hidden">
        <InvoiceTemplate invoice={invoice} />
      </div>
    </div>
  );
}
