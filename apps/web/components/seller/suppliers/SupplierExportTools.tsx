"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { toast } from "sonner";
import { Download, FileText, BarChart3, DollarSign, Users, Calendar } from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { useAuth } from "@/hooks/useBetterAuth";

interface SupplierExportToolsProps {
  onExport?: () => void;
}

export function SupplierExportTools({ onExport }: SupplierExportToolsProps) {
  const [exportFormat, setExportFormat] = useState<"csv" | "pdf">("csv");
  const [selectedData, setSelectedData] = useState<string[]>([
    "suppliers",
    "transactions",
    "summary"
  ]);
  const [isExporting, setIsExporting] = useState(false);

  const { user } = useAuth();

  const exportData = useQuery(api.supplierManagement.getSupplierExportData, {
    sellerId: user?.userId,
  });

  const handleDataToggle = (dataId: string) => {
    setSelectedData(prev => 
      prev.includes(dataId) 
        ? prev.filter(id => id !== dataId)
        : [...prev, dataId]
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (date: number) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const generateCSV = () => {
    if (!exportData) return "";

    let csvContent = "";
    
    // Helper function to escape CSV values
    const escapeCSV = (value: any) => {
      if (value === null || value === undefined) return '';
      const stringValue = String(value);
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      return stringValue;
    };

    // Suppliers Data
    if (selectedData.includes("suppliers") && exportData.suppliers.length > 0) {
      csvContent += "SUPPLIERS DATA\n";
      csvContent += "Supplier Name,Email,Phone,Address,Contact Person,Payment Terms,Credit Limit,Notes,Status,Relationship Start,Last Contact,Tags,Updated\n";
      
      exportData.suppliers.forEach(supplier => {
        csvContent += `${escapeCSV(supplier.supplierName)},${escapeCSV(supplier.supplierEmail || '')},${escapeCSV(supplier.supplierPhone || '')},${escapeCSV(supplier.supplierAddress || '')},${escapeCSV(supplier.contactPerson || '')},${escapeCSV(supplier.paymentTerms || '')},${escapeCSV(supplier.creditLimit || '')},${escapeCSV(supplier.notes || '')},${escapeCSV(supplier.isActive ? 'Active' : 'Inactive')},${escapeCSV(formatDate(supplier.relationshipStartDate))},${escapeCSV(supplier.lastContactDate ? formatDate(supplier.lastContactDate) : '')},${escapeCSV(supplier.tags.join('; ') || '')},${escapeCSV(formatDate(supplier.updatedAt))}\n`;
      });
      csvContent += "\n";
    }

    // Transactions Data
    if (selectedData.includes("transactions") && exportData.transactions.length > 0) {
      csvContent += "SUPPLIER TRANSACTIONS DATA\n";
      csvContent += "Transaction Type,Amount,Currency,Transaction Date,Due Date,Status,Payment Method,Reference Number,Description,Notes,Updated\n";
      
      exportData.transactions.forEach(transaction => {
        csvContent += `${escapeCSV(transaction.transactionType)},${escapeCSV(formatCurrency(transaction.amount))},${escapeCSV(transaction.currency)},${escapeCSV(formatDate(transaction.transactionDate))},${escapeCSV(transaction.dueDate ? formatDate(transaction.dueDate) : '')},${escapeCSV(transaction.status)},${escapeCSV(transaction.paymentMethod || '')},${escapeCSV(transaction.referenceNumber || '')},${escapeCSV(transaction.description)},${escapeCSV(transaction.notes || '')},${escapeCSV(formatDate(transaction.updatedAt))}\n`;
      });
      csvContent += "\n";
    }

    // Summary Data
    if (selectedData.includes("summary") && exportData.summary) {
      csvContent += "SUPPLIER SUMMARY DATA\n";
      csvContent += "Metric,Value\n";
      csvContent += `Total Suppliers,${escapeCSV(exportData.summary.totalSuppliers)}\n`;
      csvContent += `Active Suppliers,${escapeCSV(exportData.summary.activeSuppliers)}\n`;
      csvContent += `Total Spent,${escapeCSV(formatCurrency(exportData.summary.totalSpent))}\n`;
      csvContent += `Total Outstanding,${escapeCSV(formatCurrency(exportData.summary.totalOutstanding))}\n`;
      csvContent += `Overdue Amount,${escapeCSV(formatCurrency(exportData.summary.overdueAmount))}\n`;
      csvContent += `Export Date,${escapeCSV(formatDate(exportData.summary.exportDate))}\n`;
    }

    return csvContent;
  };

  const downloadCSV = () => {
    const csvContent = generateCSV();
    if (!csvContent) {
      toast.error("No data available for export");
      return;
    }

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `supplier-data-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast.success("Supplier data exported successfully");
    onExport?.();
  };

  const handleExport = () => {
    if (selectedData.length === 0) {
      toast.error("Please select at least one data type to export");
      return;
    }

    setIsExporting(true);
    
    try {
      if (exportFormat === "csv") {
        downloadCSV();
      } else {
        // PDF export would go here
        toast.info("PDF export coming soon");
      }
    } catch (error) {
      toast.error("Export failed. Please try again.");
    } finally {
      setIsExporting(false);
    }
  };

  if (!exportData) {
    return (
      <Card className="rounded-xl border border-border bg-card shadow-sm">
        <CardContent className="p-6 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground font-light">Loading export data...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="rounded-xl border border-border bg-card shadow-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-xl font-light">
          <Download className="w-5 h-5" />
          Export Supplier Data
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Data Selection */}
        <div>
          <h3 className="font-medium text-foreground mb-3">Select Data to Export</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <Button
              variant={selectedData.includes("suppliers") ? "default" : "outline"}
              size="sm"
              className="justify-start h-auto p-3"
              onClick={() => handleDataToggle("suppliers")}
            >
              <Users className="w-4 h-4 mr-2" />
              <div className="text-left">
                <div className="font-medium">Suppliers</div>
                <div className="text-xs text-accent">
                  {exportData.suppliers.length} suppliers
                </div>
              </div>
            </Button>

            <Button
              variant={selectedData.includes("transactions") ? "default" : "outline"}
              size="sm"
              className="justify-start h-auto p-3"
              onClick={() => handleDataToggle("transactions")}
            >
              <DollarSign className="w-4 h-4 mr-2" />
              <div className="text-left">
                <div className="font-medium">Transactions</div>
                <div className="text-xs text-accent">
                  {exportData.transactions.length} transactions
                </div>
              </div>
            </Button>

            <Button
              variant={selectedData.includes("summary") ? "default" : "outline"}
              size="sm"
              className="justify-start h-auto p-3"
              onClick={() => handleDataToggle("summary")}
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              <div className="text-left">
                <div className="font-medium">Summary</div>
                <div className="text-xs text-accent">
                  Key metrics & totals
                </div>
              </div>
            </Button>
          </div>
        </div>

        {/* Export Format */}
        <div>
          <h3 className="font-medium text-foreground mb-3">Export Format</h3>
          <div className="flex gap-2">
            <Button
              variant={exportFormat === "csv" ? "default" : "outline"}
              size="sm"
              onClick={() => setExportFormat("csv")}
            >
              <FileText className="w-4 h-4 mr-2" />
              CSV
            </Button>
            <Button
              variant={exportFormat === "pdf" ? "default" : "outline"}
              size="sm"
              onClick={() => setExportFormat("pdf")}
              disabled
            >
              <FileText className="w-4 h-4 mr-2" />
              PDF (Coming Soon)
            </Button>
          </div>
        </div>

        {/* Summary Preview */}
        {selectedData.length > 0 && (
          <div>
            <h3 className="font-medium text-foreground mb-3">Export Preview</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {selectedData.includes("suppliers") && (
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-primary">{exportData.summary.totalSuppliers}</div>
                  <div className="text-sm text-muted-foreground">Total Suppliers</div>
                </div>
              )}
              {selectedData.includes("suppliers") && (
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-accent">{exportData.summary.activeSuppliers}</div>
                  <div className="text-sm text-muted-foreground">Active Suppliers</div>
                </div>
              )}
              {selectedData.includes("transactions") && (
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-secondary">{exportData.transactions.length}</div>
                  <div className="text-sm text-muted-foreground">Transactions</div>
                </div>
              )}
              {selectedData.includes("summary") && (
                <div className="text-center p-3 bg-muted rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{formatCurrency(exportData.summary.totalSpent)}</div>
                  <div className="text-sm text-muted-foreground">Total Spent</div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Export Button */}
        <Button 
          onClick={handleExport}
          disabled={isExporting || selectedData.length === 0}
          className="w-full font-light"
        >
          <Download className="w-4 h-4 mr-2" />
          {isExporting ? "Exporting..." : `Export ${exportFormat.toUpperCase()}`}
        </Button>

        {/* Export Info */}
        <div className="text-xs text-muted-foreground text-center">
          <p>Export will include all selected data types with current filters applied</p>
          <p>Generated on {formatDate(Date.now())}</p>
        </div>
      </CardContent>
    </Card>
  );
}
