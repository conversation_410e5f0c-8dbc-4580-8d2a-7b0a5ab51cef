"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { 
  Building2, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  CreditCard, 
  Tag, 
  X,
  FileText,
  Eye
} from "lucide-react";
import { SupplierFormData } from "./types";

interface SupplierPreviewProps {
  data: SupplierFormData;
  isVisible: boolean;
  isModal?: boolean;
  onClose?: () => void;
}

export function SupplierPreview({ data, isVisible, isModal = false, onClose }: SupplierPreviewProps) {
  if (!isVisible) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const previewContent = (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <Building2 className="w-8 h-8 text-primary" />
        </div>
        <h3 className="text-xl font-semibold text-foreground">
          {data.supplierName || "Supplier Name"}
        </h3>
        <p className="text-sm text-muted-foreground">
          {data.contactPerson ? `Contact: ${data.contactPerson}` : "No contact person"}
        </p>
      </div>

      {/* Basic Information */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Building2 className="w-4 h-4" />
          Basic Information
        </h4>
        
        <div className="space-y-3">
          {data.supplierEmail && (
            <div className="flex items-center gap-3 text-sm">
              <Mail className="w-4 h-4 text-muted-foreground" />
              <span className="text-foreground">{data.supplierEmail}</span>
            </div>
          )}
          
          {data.supplierPhone && (
            <div className="flex items-center gap-3 text-sm">
              <Phone className="w-4 h-4 text-muted-foreground" />
              <span className="text-foreground">{data.supplierPhone}</span>
            </div>
          )}
          
          {data.supplierAddress && (
            <div className="flex items-start gap-3 text-sm">
              <MapPin className="w-4 h-4 text-muted-foreground mt-0.5" />
              <span className="text-foreground">{data.supplierAddress}</span>
            </div>
          )}
        </div>
      </div>

      {/* Business Terms */}
      {(data.paymentTerms || data.creditLimit > 0) && (
        <div className="space-y-4">
          <h4 className="font-medium text-foreground flex items-center gap-2">
            <CreditCard className="w-4 h-4" />
            Business Terms
          </h4>
          
          <div className="space-y-3">
            {data.paymentTerms && (
              <div className="flex items-center gap-3 text-sm">
                <FileText className="w-4 h-4 text-muted-foreground" />
                <span className="text-foreground">Payment: {data.paymentTerms}</span>
              </div>
            )}
            
            {data.creditLimit > 0 && (
              <div className="flex items-center gap-3 text-sm">
                <CreditCard className="w-4 h-4 text-muted-foreground" />
                <span className="text-foreground">Credit Limit: {formatCurrency(data.creditLimit)}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Tags */}
      {data.tags.length > 0 && (
        <div className="space-y-4">
          <h4 className="font-medium text-foreground flex items-center gap-2">
            <Tag className="w-4 h-4" />
            Tags
          </h4>
          
          <div className="flex gap-2 flex-wrap">
            {data.tags.map((tag: any) => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Notes */}
      {data.notes && (
        <div className="space-y-4">
          <h4 className="font-medium text-foreground">Additional Notes</h4>
          <p className="text-sm text-muted-foreground bg-muted p-3 rounded-lg">
            {data.notes}
          </p>
        </div>
      )}

      {/* Empty State */}
      {!data.supplierName && (
        <div className="text-center py-8 text-muted-foreground">
          <Building2 className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p className="text-sm">Start filling out the form to see a preview</p>
        </div>
      )}
    </div>
  );

  if (isModal) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle className="text-xl font-semibold">Supplier Preview</CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </CardHeader>
          <CardContent className="pb-6">
            {previewContent}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <Card className="p-6 bg-white dark:bg-neutral-900 rounded-2xl shadow-lg">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <Eye className="w-5 h-5" />
          Preview
        </CardTitle>
      </CardHeader>
      <CardContent>
        {previewContent}
      </CardContent>
    </Card>
  );
}
