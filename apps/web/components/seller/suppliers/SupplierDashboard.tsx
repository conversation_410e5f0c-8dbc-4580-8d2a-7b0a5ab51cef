"use client";

import { useState, useMemo } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { toast } from "sonner";
import { 
  Search, 
  Plus, 
  FileText, 
  DollarSign, 
  Package, 
  TrendingUp, 
  Users, 
  Clock, 
  Target,
  Download,
  Mail,
  BarChart3,
  Calendar,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Filter,
  Eye,
  MapPin,
  CreditCard,
  Percent,
  Star,
  Info,
  Building2,
  Phone,
  Edit,
  Trash2,
  MoreHorizontal
} from "lucide-react";
import Link from "next/link";
import { useAuth } from "@/hooks/useBetterAuth";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@repo/ui/components/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@repo/ui/components/alert-dialog";

import { SupplierExportTools } from "./SupplierExportTools";



export function SupplierDashboard() {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("overview");
  const [timeRange, setTimeRange] = useState("30d");
  const [statusFilter, setStatusFilter] = useState("all");

  const [isExportModalOpen, setIsExportModalOpen] = useState(false);

  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();



  // Backend queries
  const suppliers = useQuery(api.supplierManagement.getSuppliers, { 
    sellerId: user?.userId 
  });

  // Data processing
  const filteredSuppliers = suppliers?.filter((supplier: any) =>
    supplier.supplierName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.contactPerson?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.supplierEmail?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Calculate metrics
  const totalSuppliers = filteredSuppliers.length;
  const activeSuppliers = filteredSuppliers.filter((s: any) => s.isActive).length;
  const totalCreditLimit = filteredSuppliers.reduce((sum: number, s: any) => sum + (s.creditLimit || 0), 0);
  const averageCreditLimit = totalSuppliers > 0 ? totalCreditLimit / totalSuppliers : 0;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const getSupplierStatusColor = (isActive: boolean) => {
    return isActive ? "default" : "secondary";
  };

  const topSuppliers = useMemo(() => {
    if (!suppliers) return [];
    
    return suppliers
      .sort((a: any, b: any) => (b.creditLimit || 0) - (a.creditLimit || 0))
      .slice(0, 5);
  }, [suppliers]);

  const recentActivity = useMemo(() => {
    if (!suppliers) return [];
    
    return suppliers
      .sort((a: any, b: any) => new Date(b._creationTime).getTime() - new Date(a._creationTime).getTime())
      .slice(0, 5);
  }, [suppliers]);

  const handleSupplierAction = (supplierId: string | null, action: string) => {
    switch (action) {
      case "view":
        // Handle view action
        break;
      case "delete":
        // Handle delete action
        break;
    }
  };

  const handleAddSupplier = () => {
    router.push('/seller/suppliers/new');
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Supplier Management</h1>
          <p className="text-muted-foreground font-light">
            Manage your suppliers, track relationships, and monitor financial transactions
          </p>
        </div>
        
        {/* Quick Actions */}
        <div className="flex flex-wrap gap-2">
          <Button 
            size="sm" 
            variant="outline" 
            className="font-light"
            onClick={() => setIsExportModalOpen(true)}
          >
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
          <Button 
            onClick={handleAddSupplier}
            className="font-light"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Supplier
          </Button>
        </div>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-40 rounded-xl">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32 rounded-xl">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search suppliers, contacts, emails..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 rounded-xl"
          />
        </div>
      </div>

      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-primary/5 rounded-xl border border-border">
          <TabsTrigger value="overview" className="rounded-xl font-light">Overview</TabsTrigger>
          <TabsTrigger value="suppliers" className="rounded-xl font-light">Suppliers</TabsTrigger>
          <TabsTrigger value="analytics" className="rounded-xl font-light">Analytics</TabsTrigger>
          <TabsTrigger value="management" className="rounded-xl font-light">Management</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Overview Header */}
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-light text-foreground">Supplier Overview</h2>
              <p className="text-muted-foreground font-light">Key metrics and insights about your supplier relationships</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExportModalOpen(true)}
              className="font-light"
            >
              <Download className="h-4 w-4 mr-2" />
              Quick Export
            </Button>
          </div>

          {/* KPI Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Suppliers */}
            <Card className="bg-primary text-primary-foreground border-0 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-light text-primary-foreground/80 uppercase tracking-wide">
                    <Building2 className="w-4 h-4 mr-2 inline" />
                    Total Suppliers
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-light text-primary-foreground">
                  {totalSuppliers}
                </div>
                <p className="text-xs text-primary-foreground/80 font-light">
                  Active: {activeSuppliers} • Inactive: {totalSuppliers - activeSuppliers}
                </p>
              </CardContent>
            </Card>

            {/* Active Suppliers */}
            <Card className="bg-accent text-accent-foreground border-0 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-light text-accent-foreground/80 uppercase tracking-wide">
                  <Users className="w-4 h-4 mr-2 inline" />
                  Active Suppliers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-light text-accent-foreground">
                  {activeSuppliers}
                </div>
                <p className="text-xs text-accent-foreground/80 font-light">
                  {totalSuppliers > 0 ? Math.round((activeSuppliers / totalSuppliers) * 100) : 0}% of total
                </p>
              </CardContent>
            </Card>

            {/* Total Credit Limit */}
            <Card className="bg-secondary text-secondary-foreground border-0 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-light text-secondary-foreground/80 uppercase tracking-wide">
                  <CreditCard className="w-4 h-4 mr-2 inline" />
                  Total Credit Limit
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-light text-secondary-foreground">
                  {formatCurrency(totalCreditLimit)}
                </div>
                <p className="text-xs text-secondary-foreground/80 font-light">
                  Average: {formatCurrency(averageCreditLimit)}
                </p>
              </CardContent>
            </Card>

            {/* Supplier Growth */}
            <Card className="bg-muted text-foreground border-0 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-light text-foreground/70 uppercase tracking-wide">
                  <TrendingUp className="w-4 h-4 mr-2 inline" />
                  Growth Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-light text-foreground">
                  +12.5%
                </div>
                <p className="text-xs text-foreground/70 font-light">
                  This month
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Key Metrics Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Suppliers by Credit Limit */}
            <Card className="rounded-xl border border-border bg-card shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                  <Star className="w-5 h-5" />
                  Top Suppliers by Credit Limit
                </CardTitle>
              </CardHeader>
              <CardContent>
                {topSuppliers.length === 0 ? (
                  <div className="text-center py-8">
                    <Building2 className="w-12 h-12 text-muted-foreground/50 mx-auto mb-4" />
                    <p className="text-muted-foreground font-light">No suppliers yet</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {topSuppliers.map((supplier: any, index: number) => (
                      <div key={supplier._id} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                            <span className="text-sm font-light text-primary">#{index + 1}</span>
                          </div>
                          <div>
                            <p className="font-light text-foreground">{supplier.supplierName}</p>
                            <p className="text-xs text-muted-foreground font-light">{supplier.contactPerson}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-light text-foreground">{formatCurrency(supplier.creditLimit || 0)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card className="rounded-xl border border-border bg-card shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((supplier: any) => (
                    <div key={supplier._id} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-2 h-2 rounded-full ${
                          supplier.isActive ? "bg-green-500" : "bg-gray-500"
                        }`} />
                        <div>
                          <p className="font-light text-foreground">{supplier.supplierName}</p>
                          <p className="text-xs text-muted-foreground font-light">
                            {supplier.contactPerson} • {supplier.supplierEmail}
                          </p>
                        </div>
                      </div>
                      <Badge variant={getSupplierStatusColor(supplier.isActive)} className="font-light text-xs">
                         {supplier.isActive ? "Active" : "Inactive"}
                       </Badge>
                    </div>
                  ))}
                  {recentActivity.length === 0 && (
                    <div className="text-center py-8">
                      <Clock className="w-12 h-12 text-muted-foreground/50 mx-auto mb-4" />
                      <p className="text-muted-foreground font-light">No recent activity</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

                {/* Suppliers List Tab */}
        <TabsContent value="suppliers" className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-light text-foreground">Supplier List</h2>
              <p className="text-muted-foreground font-light">View and manage all your suppliers</p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsExportModalOpen(true)}
                className="font-light"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button 
                variant="outline" 
                className="font-light"
                onClick={() => router.push('/seller/suppliers/list')}
              >
                <Eye className="h-4 w-4 mr-2" />
                View Full List
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card className="border border-border rounded-xl">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-light text-foreground">{totalSuppliers}</div>
                <p className="text-sm text-muted-foreground font-light">Total Suppliers</p>
              </CardContent>
            </Card>
            <Card className="border border-border rounded-xl">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-light text-foreground">{activeSuppliers}</div>
                <p className="text-sm text-muted-foreground font-light">Active</p>
              </CardContent>
            </Card>
            <Card className="border border-border rounded-xl">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-light text-foreground">
                  {filteredSuppliers.filter((s: any) => !s.isActive).length}
                </div>
                <p className="text-sm text-muted-foreground font-light">Inactive</p>
              </CardContent>
            </Card>
            <Card className="border border-border rounded-xl">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-light text-foreground">
                  {formatCurrency(totalCreditLimit)}
                </div>
                <p className="text-sm text-muted-foreground font-light">Total Credit</p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Suppliers Preview */}
          <Card className="rounded-xl border border-border bg-card shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-light text-foreground">Recent Suppliers</CardTitle>
            </CardHeader>
            <CardContent>
              {filteredSuppliers.length === 0 ? (
                <div className="text-center py-12">
                  <Building2 className="w-12 h-12 text-muted-foreground/50 mx-auto mb-4" />
                  <h3 className="text-lg font-light mb-2">No suppliers found</h3>
                  <p className="text-muted-foreground font-light mb-4">
                    Suppliers will appear here when you add them
                  </p>
                  <Button 
                    onClick={handleAddSupplier}
                    className="font-light"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Supplier
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredSuppliers.slice(0, 5).map((supplier: any) => (
                    <Card key={supplier._id} className="border border-border rounded-xl">
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center gap-4 mb-4">
                              <div>
                                <h3 className="font-light text-lg text-foreground">
                                  {supplier.supplierName}
                                </h3>
                                <p className="text-muted-foreground font-light">
                                  {supplier.contactPerson} • {supplier.supplierEmail}
                                </p>
                              </div>
                              <Badge variant={getSupplierStatusColor(supplier.isActive)} className="font-light">
                                {supplier.isActive ? "Active" : "Inactive"}
                              </Badge>
                            </div>
                            
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div>
                                <p className="text-muted-foreground font-light">Phone</p>
                                <p className="font-light text-foreground">{supplier.supplierPhone || "—"}</p>
                              </div>
                              <div>
                                <p className="text-muted-foreground font-light">Credit Limit</p>
                                <p className="font-light text-foreground">{formatCurrency(supplier.creditLimit || 0)}</p>
                              </div>
                              <div>
                                <p className="text-muted-foreground font-light">Payment Terms</p>
                                <p className="font-light text-foreground">{supplier.paymentTerms || "—"}</p>
                              </div>
                              <div>
                                <p className="text-muted-foreground font-light">Added Date</p>
                                <p className="font-light text-foreground">
                                  {new Date(supplier._creationTime).toLocaleDateString()}
                                </p>
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex gap-2">
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="font-light"
                              onClick={() => router.push(`/seller/suppliers/${supplier._id}`)}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="font-light"
                              onClick={() => router.push(`/seller/suppliers/${supplier._id}/edit`)}
                            >
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                  
                    {filteredSuppliers.length > 5 && (
                    <div className="text-center py-4">
                      <Button 
                        variant="outline" 
                        className="font-light"
                        onClick={() => router.push('/seller/suppliers/list')}
                      >
                        View All {totalSuppliers} Suppliers
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-light text-foreground">Supplier Analytics</h2>
              <p className="text-muted-foreground font-light">Performance insights and trends</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExportModalOpen(true)}
              className="font-light"
            >
              <Download className="h-4 w-4 mr-2" />
              Export Analytics
            </Button>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Supplier Distribution */}
            <Card className="rounded-xl border border-border bg-card shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Supplier Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="font-light text-foreground">Active Suppliers</span>
                    <span className="font-light text-foreground">{activeSuppliers}</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full" 
                      style={{ width: `${totalSuppliers > 0 ? (activeSuppliers / totalSuppliers) * 100 : 0}%` }}
                    />
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="font-light text-foreground">Inactive Suppliers</span>
                    <span className="font-light text-foreground">{totalSuppliers - activeSuppliers}</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div 
                      className="bg-accent h-2 rounded-full" 
                      style={{ width: `${totalSuppliers > 0 ? ((totalSuppliers - activeSuppliers) / totalSuppliers) * 100 : 0}%` }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Credit Limit Analysis */}
            <Card className="rounded-xl border border-border bg-card shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-light text-foreground flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Credit Limit Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-light text-foreground">High Credit (&gt;$10K)</span>
                      <span className="font-light text-foreground">
                        {filteredSuppliers.filter((s: any) => (s.creditLimit || 0) > 10000).length}
                      </span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full" 
                        style={{ 
                          width: `${totalSuppliers > 0 ? (filteredSuppliers.filter((s: any) => (s.creditLimit || 0) > 10000).length / totalSuppliers) * 100 : 0}%` 
                        }} 
                      />
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-light text-foreground">Medium Credit ($1K-$10K)</span>
                      <span className="font-light text-foreground">
                        {filteredSuppliers.filter((s: any) => (s.creditLimit || 0) >= 1000 && (s.creditLimit || 0) <= 10000).length}
                      </span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div 
                        className="bg-accent h-2 rounded-full" 
                        style={{ 
                          width: `${totalSuppliers > 0 ? (filteredSuppliers.filter((s: any) => (s.creditLimit || 0) >= 1000 && (s.creditLimit || 0) <= 10000).length / totalSuppliers) * 100 : 0}%` 
                        }} 
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Management Tab */}
        <TabsContent value="management" className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-light text-foreground">Supplier Management Tools</h2>
              <p className="text-muted-foreground font-light">Tools and utilities for managing supplier relationships</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExportModalOpen(true)}
              className="font-light"
            >
              <Download className="h-4 w-4 mr-2" />
              Export All Data
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <Plus className="w-12 h-12 text-primary mx-auto mb-4" />
                <h3 className="font-light text-lg text-foreground mb-2">Add New Supplier</h3>
                <p className="text-muted-foreground font-light mb-4">
                  Register a new supplier with complete contact and financial information
                </p>
                <Button className="w-full font-light" onClick={handleAddSupplier}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Supplier
                </Button>
              </CardContent>
            </Card>

            <SupplierExportTools />

            <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <BarChart3 className="w-12 h-12 text-secondary mx-auto mb-4" />
                <h3 className="font-light text-lg text-foreground mb-2">Performance Reports</h3>
                <p className="text-muted-foreground font-light mb-4">
                  Generate detailed performance and financial reports
                </p>
                <Button variant="outline" className="w-full font-light">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  View Reports
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>



      {/* Export Modal */}
      {isExportModalOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-background rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-border">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-light text-foreground">Export Supplier Data</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsExportModalOpen(false)}
                  className="h-8 w-8 p-0"
                >
                  <XCircle className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="p-6">
              <SupplierExportTools onExport={() => setIsExportModalOpen(false)} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
