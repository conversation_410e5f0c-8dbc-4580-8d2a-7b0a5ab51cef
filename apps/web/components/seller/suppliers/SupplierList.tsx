"use client";

import { useState, useMemo, useEffect, useCallback } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { toast } from "sonner";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { But<PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@repo/ui/components/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@repo/ui/components/alert-dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { Checkbox } from "@repo/ui/components/checkbox";
import { 
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Archive,
  Star,
  TrendingUp,
  Calendar,
  Package,
  DollarSign,
  BarChart3,
  ShoppingCart,
  Filter,
  Upload,
  Grid,
  List,
  Settings,
  Crown,
  ShoppingBag,
  Heart,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Building2,
  Phone,
  Mail,
  MapPin,
  User,
  CreditCard,
  CheckCircle,
  XCircle
} from "lucide-react";
import { useAuth } from "@/hooks/useBetterAuth";

interface FilterState {
  search: string;
  status: "all" | "active" | "inactive";
  sortBy: "newest" | "oldest" | "credit_high" | "credit_low" | "name" | "status";
  viewMode: "table" | "card";
  limit: number;
  offset: number;
  paymentTerms: string;
  creditRange: { min: string; max: string };
  tags: string[];
}

const STATUS_COLORS = {
  active: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  inactive: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
};

const ITEMS_PER_PAGE_OPTIONS = [25, 50, 100, 200];

export function SupplierList() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  
  // Initialize filters from URL params
  const initializeFiltersFromURL = useCallback((): FilterState => {
    const status = searchParams.get('status') || 'all';
    const search = searchParams.get('search') || '';
    const sortBy = searchParams.get('sortBy') || 'newest';
    const paymentTerms = searchParams.get('paymentTerms') || 'all';
    const tags = searchParams.get('tags') ? searchParams.get('tags')!.split(',') : [];
    const viewMode = searchParams.get('viewMode') || 'table';
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    return {
      search,
      status: status as FilterState['status'],
      sortBy: sortBy as FilterState['sortBy'],
      viewMode: viewMode as FilterState['viewMode'],
      limit: ITEMS_PER_PAGE_OPTIONS.includes(limit) ? limit : 50,
      offset: Math.max(0, offset),
      paymentTerms,
      creditRange: { min: "", max: "" },
      tags,
    };
  }, [searchParams]);

  const [filters, setFilters] = useState<FilterState>(initializeFiltersFromURL);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // Update filters when URL changes
  useEffect(() => {
    setFilters(initializeFiltersFromURL());
  }, [initializeFiltersFromURL]);

  // Update URL when filters change
  const updateURL = useCallback((newFilters: FilterState) => {
    const params = new URLSearchParams();
    
    if (newFilters.status && newFilters.status !== 'all') {
      params.set('status', newFilters.status);
    }
    if (newFilters.search) {
      params.set('search', newFilters.search);
    }
    if (newFilters.sortBy && newFilters.sortBy !== 'newest') {
      params.set('sortBy', newFilters.sortBy);
    }
    if (newFilters.paymentTerms && newFilters.paymentTerms !== 'all') {
      params.set('paymentTerms', newFilters.paymentTerms);
    }
    if (newFilters.tags.length > 0) {
      params.set('tags', newFilters.tags.join(','));
    }
    if (newFilters.viewMode !== 'table') {
      params.set('viewMode', newFilters.viewMode);
    }
    if (newFilters.limit !== 50) {
      params.set('limit', newFilters.limit.toString());
    }
    if (newFilters.offset > 0) {
      params.set('offset', newFilters.offset.toString());
    }
    
    const newURL = `${pathname}?${params.toString()}`;
    router.push(newURL);
  }, [pathname, router]);

  // Fetch supplier data
  const suppliers = useQuery(api.supplierManagement.getSuppliers, { 
    sellerId: user?.userId 
  });

  // Filter and sort suppliers
  const filteredSuppliers = useMemo(() => {
    if (!suppliers) return [];
    
    let filtered = suppliers.filter((supplier: any) => {
      // Search filter
      const matchesSearch = !filters.search || 
        supplier.supplierName.toLowerCase().includes(filters.search.toLowerCase()) ||
        (supplier.contactPerson?.toLowerCase().includes(filters.search.toLowerCase())) ||
        (supplier.supplierEmail?.toLowerCase().includes(filters.search.toLowerCase()));
      
      // Status filter
      const matchesStatus = filters.status === 'all' || 
        (filters.status === 'active' && supplier.isActive) ||
        (filters.status === 'inactive' && !supplier.isActive);
      
      // Payment terms filter
      const matchesPaymentTerms = filters.paymentTerms === 'all' || 
        supplier.paymentTerms === filters.paymentTerms;
      
      // Credit range filter
      const matchesCreditRange = (!filters.creditRange.min || (supplier.creditLimit || 0) >= parseFloat(filters.creditRange.min)) &&
        (!filters.creditRange.max || (supplier.creditLimit || 0) <= parseFloat(filters.creditRange.max));
      
      // Tags filter
      const matchesTags = filters.tags.length === 0 || 
        filters.tags.some(tag => supplier.tags.includes(tag));
      
      return matchesSearch && matchesStatus && matchesPaymentTerms && matchesCreditRange && matchesTags;
    });
    
    // Sort suppliers
    switch (filters.sortBy) {
      case 'oldest':
        filtered.sort((a: any, b: any) => a._creationTime - b._creationTime);
        break;
      case 'credit_high':
        filtered.sort((a: any, b: any) => (b.creditLimit || 0) - (a.creditLimit || 0));
        break;
      case 'credit_low':
        filtered.sort((a: any, b: any) => (a.creditLimit || 0) - (b.creditLimit || 0));
        break;
      case 'name':
        filtered.sort((a: any, b: any) => a.supplierName.localeCompare(b.supplierName));
        break;
      case 'status':
        filtered.sort((a: any, b: any) => Number(b.isActive) - Number(a.isActive));
        break;
      default: // newest
        filtered.sort((a: any, b: any) => b._creationTime - a._creationTime);
    }
    
    return filtered;
  }, [suppliers, filters]);

  // Pagination
  const paginationInfo = useMemo(() => {
    const total = filteredSuppliers.length;
    const totalPages = Math.ceil(total / filters.limit);
    const currentPage = Math.floor(filters.offset / filters.limit) + 1;
    const startItem = filters.offset + 1;
    const endItem = Math.min(filters.offset + filters.limit, total);
    
    return {
      total,
      totalPages,
      currentPage,
      startItem,
      endItem,
      hasPrevious: currentPage > 1,
      hasNext: currentPage < totalPages,
    };
  }, [filteredSuppliers, filters]);

  const paginatedSuppliers = useMemo(() => {
    return filteredSuppliers.slice(filters.offset, filters.offset + filters.limit);
  }, [filteredSuppliers, filters]);

  // Handle filter changes
  const handleFilterChange = (key: keyof FilterState, value: any) => {
    const newFilters = { ...filters, [key]: value, offset: 0 };
    setFilters(newFilters);
    updateURL(newFilters);
  };

  // Handle pagination
  const goToPage = (page: number) => {
    const newOffset = (page - 1) * filters.limit;
    const newFilters = { ...filters, offset: newOffset };
    setFilters(newFilters);
    updateURL(newFilters);
  };

  const goToNextPage = () => {
    if (paginationInfo.hasNext) {
      goToPage(paginationInfo.currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (paginationInfo.hasPrevious) {
      goToPage(paginationInfo.currentPage - 1);
    }
  };

  const goToFirstPage = () => goToPage(1);
  const goToLastPage = () => goToPage(paginationInfo.totalPages);

  const handleItemsPerPageChange = (newLimit: number) => {
    const newFilters = { ...filters, limit: newLimit, offset: 0 };
    setFilters(newFilters);
    updateURL(newFilters);
  };

  // Handle selection
  const handleSelectItem = (itemId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, itemId]);
    } else {
      setSelectedItems(prev => prev.filter(id => id !== itemId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(paginatedSuppliers.map((item: any) => item._id));
    } else {
      setSelectedItems([]);
    }
  };

  // Handle supplier actions
  const handleSupplierAction = (supplierId: string | null, action: string) => {
    switch (action) {
      case "edit":
        router.push(`/seller/suppliers/${supplierId}/edit`);
        break;
      case "view":
        router.push(`/seller/suppliers/${supplierId}`);
        break;
      case "delete":
        // Handle delete action
        break;
    }
  };

  // Utility functions
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  if (!user) {
    return (
      <div className="p-6 text-center">
        <p className="text-muted-foreground">Please log in to view suppliers</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header with Controls */}
      <div className="border-b border-border bg-card">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between gap-4">
            {/* Search and Filters */}
            <div className="flex items-center gap-4 flex-1">
              <div className="relative max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search suppliers..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                  className="pl-10 rounded-xl bg-primary/5 border-border font-light placeholder:text-primary transition-all duration-300 focus:ring-2 focus:ring-primary/20"
                />
              </div>
              
              {/* Status Dropdown */}
              <Select value={filters.status} onValueChange={(value) => handleFilterChange("status", value as FilterState["status"])}>
                <SelectTrigger className="w-40 rounded-xl bg-primary/5 border-border font-light">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <Grid className="w-4 h-4" />
                      ALL
                    </div>
                  </SelectItem>
                  <SelectItem value="active" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4" />
                      ACTIVE
                    </div>
                  </SelectItem>
                  <SelectItem value="inactive" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <XCircle className="w-4 h-4" />
                      INACTIVE
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>

              {/* Filter Popover */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button 
                    variant="outline" 
                    className="rounded-xl font-light"
                  >
                    <Filter className="w-4 h-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-6" align="start">
                  <div className="space-y-4">
                    <h4 className="font-medium text-sm uppercase tracking-wide">Filters</h4>
                    
                    {/* Payment Terms Filter */}
                    <div>
                      <label className="text-sm font-light text-muted-foreground mb-2 block">Payment Terms</label>
                      <Select value={filters.paymentTerms} onValueChange={(value) => handleFilterChange("paymentTerms", value)}>
                        <SelectTrigger className="rounded-xl">
                          <SelectValue placeholder="All Payment Terms" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Payment Terms</SelectItem>
                          <SelectItem value="Net 30">Net 30</SelectItem>
                          <SelectItem value="Net 60">Net 60</SelectItem>
                          <SelectItem value="Net 90">Net 90</SelectItem>
                          <SelectItem value="COD">COD</SelectItem>
                          <SelectItem value="Immediate">Immediate</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Credit Range */}
                    <div>
                      <label className="text-sm font-light text-muted-foreground mb-2 block">Credit Range</label>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Min"
                          value={filters.creditRange.min}
                          onChange={(e) => handleFilterChange("creditRange", { ...filters.creditRange, min: e.target.value })}
                          className="rounded-xl"
                        />
                        <Input
                          placeholder="Max"
                          value={filters.creditRange.max}
                          onChange={(e) => handleFilterChange("creditRange", { ...filters.creditRange, max: e.target.value })}
                          className="rounded-xl"
                        />
                      </div>
                    </div>

                    <Button 
                      onClick={() => {
                        const newFilters = { ...filters, paymentTerms: "all", creditRange: { min: "", max: "" }, tags: [] };
                        setFilters(newFilters);
                        updateURL(newFilters);
                      }}
                      variant="outline" 
                      className="w-full rounded-xl font-light"
                    >
                      Clear Filters
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-4">
              <Button 
                variant="ghost" 
                className="text-accent hover:text-accent/80 font-light rounded-xl px-4 py-2 transition-all duration-300"
              >
                <Crown className="w-4 h-4 mr-2" />
                EXPORT
              </Button>
              
              <Button 
                onClick={() => router.push('/seller/suppliers/new')}
                className="bg-accent text-accent-foreground hover:bg-accent/90 rounded-xl px-6 py-2 transition-all duration-300"
              >
                <Plus className="w-4 h-4 mr-2" />
                ADD SUPPLIER
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Full Width Table */}
      <div className="flex-1">
        <Table>
          <TableHeader>
            <TableRow className="border-b border-border">
              <TableHead className="w-12">
                <Checkbox 
                  checked={selectedItems.length === paginatedSuppliers.length}
                  onCheckedChange={handleSelectAll}
                  className="rounded border-border"
                />
              </TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Supplier</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Contact Person</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Email</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Phone</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Payment Terms</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Credit Limit</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Added Date</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase text-center">Tags</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Status</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedSuppliers && paginatedSuppliers.length > 0 ? (
              paginatedSuppliers.map((supplier: any) => (
                <TableRow key={supplier._id} className="hover:bg-primary/5 transition-colors duration-300">
                  <TableCell>
                    <Checkbox 
                      checked={selectedItems.includes(supplier._id)}
                      onCheckedChange={(checked) => handleSelectItem(supplier._id, checked as boolean)}
                      className="rounded border-border"
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-primary/5 rounded-xl overflow-hidden border border-border">
                        <div className="w-full h-full bg-primary/5 flex items-center justify-center">
                          <Building2 className="w-4 h-4 text-muted-foreground" />
                        </div>
                      </div>
                      <div>
                        <p className="font-light text-foreground text-sm">{supplier.supplierName}</p>
                        <p className="text-muted-foreground text-xs font-light">{supplier.supplierAddress || "No address"}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="font-light text-muted-foreground">{supplier.contactPerson || "—"}</TableCell>
                  <TableCell className="font-light text-muted-foreground">{supplier.supplierEmail || "—"}</TableCell>
                  <TableCell className="font-light text-muted-foreground">{supplier.supplierPhone || "—"}</TableCell>
                  <TableCell className="font-light text-muted-foreground">{supplier.paymentTerms || "—"}</TableCell>
                  <TableCell className="font-light text-primary">{formatCurrency(supplier.creditLimit || 0)}</TableCell>
                  <TableCell className="font-light text-muted-foreground">{formatDate(supplier._creationTime)}</TableCell>
                  <TableCell className="text-center">
                    <div className="flex items-center justify-center gap-1 text-muted-foreground">
                      {supplier.tags.length > 0 ? (
                        <div className="flex gap-1">
                          {supplier.tags.slice(0, 2).map((tag: any, index: number) => (
                            <Badge key={index} variant="outline" className="text-xs font-light">
                              {tag}
                            </Badge>
                          ))}
                          {supplier.tags.length > 2 && (
                            <Badge variant="outline" className="text-xs font-light">
                              +{supplier.tags.length - 2}
                            </Badge>
                          )}
                        </div>
                      ) : (
                        <span className="text-xs text-muted-foreground">—</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant="outline" 
                      className={`text-xs font-light rounded-xl ${STATUS_COLORS[supplier.isActive ? 'active' : 'inactive']}`}
                    >
                      {supplier.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="p-1 h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleSupplierAction(supplier._id, "edit")}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleSupplierAction(supplier._id, "view")}>
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleSupplierAction(supplier._id, "archive")}>
                          <Archive className="h-4 w-4 mr-2" />
                          Archive
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={11} className="text-center py-16">
                  <div className="flex flex-col items-center justify-center">
                    <div className="w-16 h-16 bg-primary/5 rounded-xl flex items-center justify-center mb-4 border border-border">
                      <Building2 className="w-8 h-8 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-light text-primary mb-2">
                      We could not find any suppliers matching your criteria
                    </h3>
                    <p className="text-muted-foreground mb-6 text-center max-w-md font-light">
                      Select the "Add Supplier" button below to start building your supplier network.
                    </p>
                    <Button 
                      onClick={() => router.push('/seller/suppliers/new')}
                      className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-xl font-light transition-all duration-300"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      ADD SUPPLIER
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      {paginationInfo && paginationInfo.total > 0 && (
        <div className="border-t border-border bg-card">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              {/* Items per page selector */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-light text-muted-foreground">Show</span>
                <Select value={filters.limit.toString()} onValueChange={(value) => handleItemsPerPageChange(parseInt(value))}>
                  <SelectTrigger className="w-20 h-8 rounded-lg">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {ITEMS_PER_PAGE_OPTIONS.map(option => (
                      <SelectItem key={option} value={option.toString()}>
                        {option}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <span className="text-sm font-light text-muted-foreground">items per page</span>
              </div>

              {/* Pagination info */}
              <div className="text-sm font-light text-muted-foreground">
                {paginationInfo.startItem}–{paginationInfo.endItem} of {paginationInfo.total} items
              </div>

              {/* Pagination navigation */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToFirstPage}
                  disabled={!paginationInfo.hasPrevious}
                  className="h-8 w-8 p-0 rounded-lg"
                >
                  <ChevronsLeft className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPreviousPage}
                  disabled={!paginationInfo.hasPrevious}
                  className="h-8 w-8 p-0 rounded-lg"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                
                {/* Page numbers */}
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, paginationInfo.totalPages) }, (_, i) => {
                    let pageNum;
                    if (paginationInfo.totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (paginationInfo.currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (paginationInfo.currentPage >= paginationInfo.totalPages - 2) {
                      pageNum = paginationInfo.totalPages - 4 + i;
                    } else {
                      pageNum = paginationInfo.currentPage - 2 + i;
                    }
                    
                    if (pageNum < 1 || pageNum > paginationInfo.totalPages) return null;
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={pageNum === paginationInfo.currentPage ? "default" : "outline"}
                        size="sm"
                        onClick={() => goToPage(pageNum)}
                        className="h-8 w-8 p-0 rounded-lg"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={!paginationInfo.hasNext}
                  className="h-8 w-8 p-0 rounded-lg"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToLastPage}
                  disabled={!paginationInfo.hasNext}
                  className="h-8 w-8 p-0 rounded-lg"
                >
                  <ChevronsRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
