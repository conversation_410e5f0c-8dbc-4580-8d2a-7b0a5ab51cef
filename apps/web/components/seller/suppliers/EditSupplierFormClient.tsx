"use client";

import { useCallback, useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { toast } from "sonner";
import { SupplierForm } from "./SupplierForm";
import { SupplierFormData } from "./types";
import { Loader2 } from "lucide-react";

export function EditSupplierFormClient() {
  const router = useRouter();
  const params = useParams();
  const supplierId = params.id as string;
  const [isLoading, setIsLoading] = useState(true);

  // Fetch supplier data
  const supplier = useQuery(api.supplierManagement.getSupplier, { 
    supplierId: supplierId as any 
  });

  // Update supplier mutation
  const updateSupplier = useMutation(api.supplierManagement.updateSupplier);

  // Transform supplier data to form data
  const transformSupplierToFormData = useCallback((supplier: any): SupplierFormData => {
    if (!supplier) return {
      supplierName: "",
      supplierEmail: "",
      supplierPhone: "",
      supplierAddress: "",
      contactPerson: "",
      paymentTerms: "",
      creditLimit: 0,
      notes: "",
      tags: [],
    };

    return {
      supplierName: supplier.supplierName || "",
      supplierEmail: supplier.supplierEmail || "",
      supplierPhone: supplier.supplierPhone || "",
      supplierAddress: supplier.supplierAddress || "",
      contactPerson: supplier.contactPerson || "",
      paymentTerms: supplier.paymentTerms || "",
      creditLimit: supplier.creditLimit || 0,
      notes: supplier.notes || "",
      tags: supplier.tags || [],
    };
  }, []);

  const handleSave = useCallback(async (data: SupplierFormData) => {
    try {
      await updateSupplier({
        supplierId: supplierId as any,
        updates: {
          supplierName: data.supplierName,
          supplierEmail: data.supplierEmail,
          supplierPhone: data.supplierPhone,
          supplierAddress: data.supplierAddress,
          contactPerson: data.contactPerson,
          paymentTerms: data.paymentTerms,
          creditLimit: data.creditLimit,
          notes: data.notes,
          tags: data.tags,
        },
      });
      
      toast.success("Supplier updated successfully!");
      router.push('/seller/suppliers');
    } catch (error) {
      console.error("Failed to update supplier:", error);
      toast.error("Failed to update supplier");
    }
  }, [updateSupplier, supplierId, router]);

  const handleCancel = useCallback(() => {
    router.back();
  }, [router]);

  // Set loading state based on supplier data
  useEffect(() => {
    if (supplier !== undefined) {
      setIsLoading(false);
    }
  }, [supplier]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading supplier data...</p>
        </div>
      </div>
    );
  }

  if (!supplier) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-muted-foreground mb-4">Supplier not found</p>
          <button
            onClick={() => router.back()}
            className="text-primary hover:underline"
          >
            Go back
          </button>
        </div>
      </div>
    );
  }

  const initialData = transformSupplierToFormData(supplier);

  return (
    <SupplierForm
      onSave={handleSave}
      onCancel={handleCancel}
      isEditing={true}
      initialData={initialData}
    />
  );
}
