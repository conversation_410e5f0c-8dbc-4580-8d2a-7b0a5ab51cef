"use client";

import { useState, useMemo } from "react";
import { useRouter, useParams } from "next/navigation";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { toast } from "sonner";
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Plus, 
  Building2, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  CreditCard, 
  Calendar, 
  Tag, 
  DollarSign, 
  TrendingUp, 
  FileText, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Package,
  BarChart3,
  Download,
  MoreHorizontal,
  Eye,
  MessageSquare,
  History,
  Settings
} from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@repo/ui/components/alert-dialog";
import { useAuth } from "@/hooks/useBetterAuth";

export function SupplierViewClient() {
  const router = useRouter();
  const params = useParams();
  const supplierId = params.id as string;
  const [activeTab, setActiveTab] = useState("overview");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const { user } = useAuth();

  // Fetch supplier data
  const supplier = useQuery(api.supplierManagement.getSupplier, { 
    supplierId: supplierId as any 
  });

  // Fetch supplier transactions
  const transactions = useQuery(api.supplierManagement.getSupplierTransactions, { 
    supplierId: supplierId as any 
  });

  // Fetch products from this supplier
  const products = useQuery(
    api.productQueries.getSellerInventory, 
    {
      status: undefined,
      limit: 1000,
      includeMetrics: true,
    }
  );

  // Delete supplier mutation
  const deleteSupplier = useMutation(api.supplierManagement.deleteSupplier);

  // Filter products by supplier
  const supplierProducts = useMemo(() => {
    if (!products?.products || !supplier) return [];
    return products.products.filter((product: any) => 
      (product as any).sourceInfo?.source === supplier.supplierName
    );
  }, [products, supplier]);

  // Calculate metrics
  const metrics = useMemo(() => {
    if (!supplier || !transactions) return null;

    const totalTransactions = transactions.length;
    const totalAmount = transactions.reduce((sum: number, t: any) => sum + t.amount, 0);
    const pendingTransactions = transactions.filter((t: any) => t.status === "pending");
    const pendingAmount = pendingTransactions.reduce((sum: number, t: any) => sum + t.amount, 0);
    const overdueTransactions = transactions.filter((t: any) => t.status === "overdue");
    const overdueAmount = overdueTransactions.reduce((sum: number, t: any) => sum + t.amount, 0);

    return {
      totalTransactions,
      totalAmount,
      pendingTransactions: pendingTransactions.length,
      pendingAmount,
      overdueTransactions: overdueTransactions.length,
      overdueAmount,
      productCount: supplierProducts.length,
    };
  }, [supplier, transactions, supplierProducts]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'default';
      case 'pending': return 'secondary';
      case 'overdue': return 'destructive';
      case 'cancelled': return 'outline';
      default: return 'secondary';
    }
  };

  const handleDelete = async () => {
    try {
      await deleteSupplier({ supplierId: supplierId as any });
      toast.success("Supplier deleted successfully");
      router.push('/seller/suppliers');
    } catch (error) {
      console.error("Failed to delete supplier:", error);
      toast.error("Failed to delete supplier");
    }
  };

  const handleEdit = () => {
    router.push(`/seller/suppliers/${supplierId}/edit`);
  };

  if (!supplier) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">Supplier not found</p>
          <Button 
            variant="outline" 
            onClick={() => router.push('/seller/suppliers')}
            className="mt-4 font-light"
          >
            Back to Suppliers
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background min-h-screen">
      {/* Header */}
      <div className="border-b border-border bg-card">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="rounded-xl font-light"
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-light text-primary tracking-wide">
                  {supplier.supplierName}
                </h1>
                <p className="text-sm text-muted-foreground font-light">
                  Supplier Details & Management
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="font-light">
                    <MoreHorizontal className="h-4 w-4 mr-2" />
                    Actions
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem onClick={handleEdit}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Supplier
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Download className="h-4 w-4 mr-2" />
                    Export Data
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    className="text-destructive focus:text-destructive"
                    onClick={() => setIsDeleteDialogOpen(true)}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Supplier
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              
              <Button onClick={handleEdit} className="font-light">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Quick Stats */}
        {metrics ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="border border-border rounded-xl">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-light text-muted-foreground">Total Transactions</p>
                    <p className="text-2xl font-light text-foreground">{metrics.totalTransactions}</p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-primary/60" />
                </div>
              </CardContent>
            </Card>

            <Card className="border border-border rounded-xl">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-light text-muted-foreground">Total Amount</p>
                    <p className="text-2xl font-light text-foreground">{formatCurrency(metrics.totalAmount)}</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-accent/60" />
                </div>
              </CardContent>
            </Card>

            <Card className="border border-border rounded-xl">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-light text-muted-foreground">Pending Amount</p>
                    <p className="text-2xl font-light text-foreground">{formatCurrency(metrics.pendingAmount)}</p>
                  </div>
                  <Clock className="h-8 w-8 text-secondary/60" />
                </div>
              </CardContent>
            </Card>

            <Card className="border border-border rounded-xl">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-light text-muted-foreground">Products</p>
                    <p className="text-2xl font-light text-foreground">{metrics.productCount}</p>
                  </div>
                  <Package className="h-8 w-8 text-muted/60" />
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i} className="border border-border rounded-xl">
                <CardContent className="p-6">
                  <div className="animate-pulse">
                    <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                    <div className="h-8 bg-muted rounded w-1/2"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Main Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-primary/5 rounded-xl border border-border">
            <TabsTrigger value="overview" className="rounded-xl font-light">Overview</TabsTrigger>
            <TabsTrigger disabled value="transactions" className="rounded-xl font-light">Transactions</TabsTrigger>
            <TabsTrigger disabled value="products" className="rounded-xl font-light">Products</TabsTrigger>
            <TabsTrigger disabled value="activity" className="rounded-xl font-light">Activity</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Supplier Information */}
              <div className="lg:col-span-2 space-y-6">
                <Card className="rounded-xl border border-border">
                  <CardHeader>
                    <CardTitle className="font-light text-primary flex items-center gap-2">
                      <Building2 className="w-5 h-5" />
                      Company Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-light text-muted-foreground mb-1">Company Name</p>
                        <p className="font-light text-foreground">{supplier.supplierName}</p>
                      </div>
                      <div>
                        <p className="text-sm font-light text-muted-foreground mb-1">Status</p>
                        <Badge variant={supplier.isActive ? "default" : "secondary"} className="font-light">
                          {supplier.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                      <div>
                        <p className="text-sm font-light text-muted-foreground mb-1">Contact Person</p>
                        <p className="font-light text-foreground">{supplier.contactPerson || "—"}</p>
                      </div>
                      <div>
                        <p className="text-sm font-light text-muted-foreground mb-1">Email</p>
                        <p className="font-light text-foreground">{supplier.supplierEmail || "—"}</p>
                      </div>
                      <div>
                        <p className="text-sm font-light text-muted-foreground mb-1">Phone</p>
                        <p className="font-light text-foreground">{supplier.supplierPhone || "—"}</p>
                      </div>
                      <div>
                        <p className="text-sm font-light text-muted-foreground mb-1">Credit Limit</p>
                        <p className="font-light text-foreground">
                          {supplier.creditLimit ? formatCurrency(supplier.creditLimit) : "—"}
                        </p>
                      </div>
                    </div>
                    
                    {supplier.supplierAddress && (
                      <div>
                        <p className="text-sm font-light text-muted-foreground mb-1">Address</p>
                        <p className="font-light text-foreground">{supplier.supplierAddress}</p>
                      </div>
                    )}

                    {supplier.notes && (
                      <div>
                        <p className="text-sm font-light text-muted-foreground mb-1">Notes</p>
                        <p className="font-light text-foreground">{supplier.notes}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Business Terms */}
                <Card className="rounded-xl border border-border">
                  <CardHeader>
                    <CardTitle className="font-light text-primary flex items-center gap-2">
                      <CreditCard className="w-5 h-5" />
                      Business Terms
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-light text-muted-foreground mb-1">Payment Terms</p>
                        <p className="font-light text-foreground">{supplier.paymentTerms || "—"}</p>
                      </div>
                      <div>
                        <p className="text-sm font-light text-muted-foreground mb-1">Relationship Start</p>
                        <p className="font-light text-foreground">
                          {formatDate(supplier.relationshipStartDate)}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-light text-muted-foreground mb-1">Last Contact</p>
                        <p className="font-light text-foreground">
                          {supplier.lastContactDate ? formatDate(supplier.lastContactDate) : "—"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-light text-muted-foreground mb-1">Last Updated</p>
                        <p className="font-light text-foreground">
                          {formatDate(supplier.updatedAt)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Tags */}
                <Card className="rounded-xl border border-border">
                  <CardHeader>
                    <CardTitle className="font-light text-primary flex items-center gap-2">
                      <Tag className="w-5 h-5" />
                      Tags & Categories
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {supplier.tags && supplier.tags.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {supplier.tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="font-light">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground font-light text-sm">No tags assigned</p>
                    )}
                  </CardContent>
                </Card>

                {/* Quick Actions */}
                <Card className="rounded-xl border border-border">
                  <CardHeader>
                    <CardTitle className="font-light text-primary">Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button onClick={handleEdit} className="w-full font-light">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Supplier
                    </Button>
                    <Button variant="outline" className="w-full font-light">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Transaction
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Transactions Tab */}
          <TabsContent value="transactions" className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-xl font-light text-foreground">Transaction History</h2>
                <p className="text-muted-foreground font-light">All financial interactions with this supplier</p>
              </div>
              <Button className="font-light">
                <Plus className="h-4 w-4 mr-2" />
                Add Transaction
              </Button>
            </div>

            {transactions ? (
              transactions.length > 0 ? (
              <Card className="rounded-xl border border-border">
                <CardContent className="p-0">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="font-light">Date</TableHead>
                        <TableHead className="font-light">Type</TableHead>
                        <TableHead className="font-light">Description</TableHead>
                        <TableHead className="font-light">Amount</TableHead>
                        <TableHead className="font-light">Status</TableHead>
                        <TableHead className="font-light">Due Date</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {transactions.map((transaction: any) => (
                        <TableRow key={transaction._id}>
                          <TableCell className="font-light">
                            {formatDate(transaction.transactionDate)}
                          </TableCell>
                          <TableCell className="font-light">
                            <Badge variant="outline" className="font-light capitalize">
                              {transaction.transactionType.replace('_', ' ')}
                            </Badge>
                          </TableCell>
                          <TableCell className="font-light">{transaction.description}</TableCell>
                          <TableCell className="font-light">
                            {formatCurrency(transaction.amount)}
                          </TableCell>
                          <TableCell className="font-light">
                            <Badge variant={getStatusColor(transaction.status)} className="font-light">
                              {transaction.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="font-light">
                            {transaction.dueDate ? formatDate(transaction.dueDate) : "—"}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
              ) : (
                <Card className="rounded-xl border border-border">
                  <CardContent className="p-12 text-center">
                    <FileText className="h-12 w-12 text-muted-foreground/50 mx-auto mb-4" />
                    <h3 className="text-lg font-light mb-2">No transactions yet</h3>
                    <p className="text-muted-foreground font-light mb-4">
                      Start tracking your financial interactions with this supplier
                    </p>
                    <Button className="font-light">
                      <Plus className="h-4 w-4 mr-2" />
                      Add First Transaction
                    </Button>
                  </CardContent>
                </Card>
              )
            ) : (
              <Card className="rounded-xl border border-border">
                <CardContent className="p-12 text-center">
                  <div className="animate-pulse space-y-4">
                    <div className="h-12 w-12 bg-muted rounded mx-auto"></div>
                    <div className="h-4 bg-muted rounded w-1/2 mx-auto"></div>
                    <div className="h-4 bg-muted rounded w-3/4 mx-auto"></div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Products Tab */}
          <TabsContent value="products" className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-xl font-light text-foreground">Products from Supplier</h2>
                <p className="text-muted-foreground font-light">Inventory sourced from this supplier</p>
              </div>
            </div>

            {products ? (
              supplierProducts.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {supplierProducts.map((product: any) => (
                  <Card key={product._id} className="rounded-xl border border-border hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <h3 className="font-light text-lg text-foreground line-clamp-2">
                            {product.title}
                          </h3>
                          <Badge variant="outline" className="font-light text-xs">
                            {product.status}
                          </Badge>
                        </div>
                        <p className="text-2xl font-light text-foreground">
                          {formatCurrency(product.price)}
                        </p>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Package className="h-4 w-4" />
                          <span className="font-light">Qty: {product.quantity}</span>
                        </div>
                        <Button variant="outline" size="sm" className="w-full font-light">
                          <Eye className="h-4 w-4 mr-2" />
                          View Product
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
                              </div>
                ) : (
                  <Card className="rounded-xl border border-border">
                    <CardContent className="p-12 text-center">
                      <Package className="h-12 w-12 text-muted-foreground/50 mx-auto mb-4" />
                      <h3 className="text-lg font-light mb-2">No products yet</h3>
                      <p className="text-muted-foreground font-light mb-4">
                        Products sourced from this supplier will appear here
                      </p>
                    </CardContent>
                  </Card>
                )
              ) : (
                <Card className="rounded-xl border border-border">
                  <CardContent className="p-12 text-center">
                    <div className="animate-pulse space-y-4">
                      <div className="h-12 w-12 bg-muted rounded mx-auto"></div>
                      <div className="h-4 bg-muted rounded w-1/2 mx-auto"></div>
                      <div className="h-4 bg-muted rounded w-3/4 mx-auto"></div>
                    </div>
                  </CardContent>
                </Card>
              )}
          </TabsContent>

          {/* Activity Tab */}
          <TabsContent value="activity" className="space-y-6">
            <div>
              <h2 className="text-xl font-light text-foreground">Recent Activity</h2>
              <p className="text-muted-foreground font-light">Timeline of supplier interactions</p>
            </div>

            <Card className="rounded-xl border border-border">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-start gap-4">
                    <div className="w-2 h-2 rounded-full bg-primary mt-2" />
                    <div className="flex-1">
                      <p className="font-light text-foreground">Supplier created</p>
                      <p className="text-sm text-muted-foreground font-light">
                        {formatDate(supplier.relationshipStartDate)}
                      </p>
                    </div>
                  </div>
                  
                  {supplier.lastContactDate && (
                    <div className="flex items-start gap-4">
                      <div className="w-2 h-2 rounded-full bg-accent mt-2" />
                      <div className="flex-1">
                        <p className="font-light text-foreground">Last contact made</p>
                        <p className="text-sm text-muted-foreground font-light">
                          {formatDate(supplier.lastContactDate)}
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="flex items-start gap-4">
                    <div className="w-2 h-2 rounded-full bg-secondary mt-2" />
                    <div className="flex-1">
                      <p className="font-light text-foreground">Profile updated</p>
                      <p className="text-sm text-muted-foreground font-light">
                        {formatDate(supplier.updatedAt)}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Supplier</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{supplier.supplierName}"? This action cannot be undone and will remove all associated data including transactions and product references.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="font-light">Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90 font-light"
            >
              Delete Supplier
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
