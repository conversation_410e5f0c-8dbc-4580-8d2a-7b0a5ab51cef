"use client";

import { useState, useMemo, useEffect, useCallback } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { toast } from "sonner";
import { useRouter, useSearchParams, usePathname } from "next/navigation";

import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@repo/ui/components/alert-dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { Checkbox } from "@repo/ui/components/checkbox";
import { 
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Archive,
  Star,
  TrendingUp,
  Calendar,
  Package,
  DollarSign,
  BarChart3,
  ShoppingCart,
  Filter,
  Upload,
  Grid,
  List,
  Settings,
  Crown,
  ShoppingBag,
  Heart,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Building2,
  Phone,
  Mail,
  MapPin,
  User,
  CreditCard,
  CheckCircle,
  XCircle,
  Clock,
  MessageSquare,
  Handshake,
  Ban,
  AlertTriangle,
  Percent,
  Download
} from "lucide-react";
import { useAuth } from "@/hooks/useBetterAuth";
import { CounterOfferModal } from "./CounterOfferModal";
import { OfferDetailsModal } from "./OfferDetailsModal";
import { RespondToOfferModal } from "./RespondToOfferModal";
import { OfferMessageModal } from "../../offers/OfferMessageModal";

interface FilterState {
  search: string;
  status: "all" | "pending" | "countered" | "accepted" | "declined" | "expired" | "withdrawn";
  sortBy: "newest" | "oldest" | "amount_high" | "amount_low" | "product" | "buyer" | "status";
  viewMode: "table" | "card";
  limit: number;
  offset: number;
  amountRange: { min: string; max: string };
  productCategory: string;
}

const STATUS_COLORS = {
  pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  countered: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  accepted: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  declined: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
  expired: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
  withdrawn: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
};

const ITEMS_PER_PAGE_OPTIONS = [25, 50, 100, 200];

export function OffersList() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  
  const [filters, setFilters] = useState<FilterState>(() => {
    const status = searchParams.get('status') || 'all';
    const search = searchParams.get('search') || '';
    const sortBy = searchParams.get('sortBy') || 'newest';
    const productCategory = searchParams.get('productCategory') || 'all';
    const viewMode = searchParams.get('viewMode') || 'table';
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    return {
      search,
      status: status as FilterState['status'],
      sortBy: sortBy as FilterState['sortBy'],
      viewMode: viewMode as FilterState['viewMode'],
      limit: ITEMS_PER_PAGE_OPTIONS.includes(limit) ? limit : 50,
      offset: Math.max(0, offset),
      amountRange: { min: "", max: "" },
      productCategory,
    };
  });
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [selectedOfferId, setSelectedOfferId] = useState<string | null>(null);
  const [isCounterModalOpen, setIsCounterModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isRespondModalOpen, setIsRespondModalOpen] = useState(false);
  const [selectedOffer, setSelectedOffer] = useState<any>(null);
  const [isMessageModalOpen, setIsMessageModalOpen] = useState(false);

  // Update filters when URL changes
  useEffect(() => {
    const status = searchParams.get('status') || 'all';
    const search = searchParams.get('search') || '';
    const sortBy = searchParams.get('sortBy') || 'newest';
    const productCategory = searchParams.get('productCategory') || 'all';
    const viewMode = searchParams.get('viewMode') || 'table';
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    console.log('URL changed, new status:', status, 'searchParams:', Object.fromEntries(searchParams.entries()));
    
    setFilters({
      search,
      status: status as FilterState['status'],
      sortBy: sortBy as FilterState['sortBy'],
      viewMode: viewMode as FilterState['viewMode'],
      limit: ITEMS_PER_PAGE_OPTIONS.includes(limit) ? limit : 50,
      offset: Math.max(0, offset),
      amountRange: { min: "", max: "" },
      productCategory,
    });
  }, [searchParams]);

  // Update URL when filters change
  const updateURL = useCallback((newFilters: FilterState) => {
    const params = new URLSearchParams();
    
    if (newFilters.status && newFilters.status !== 'all') {
      params.set('status', newFilters.status);
    }
    if (newFilters.search) {
      params.set('search', newFilters.search);
    }
    if (newFilters.sortBy && newFilters.sortBy !== 'newest') {
      params.set('sortBy', newFilters.sortBy);
    }
    if (newFilters.productCategory && newFilters.productCategory !== 'all') {
      params.set('productCategory', newFilters.productCategory);
    }
    if (newFilters.viewMode !== 'table') {
      params.set('viewMode', newFilters.viewMode);
    }
    if (newFilters.limit !== 50) {
      params.set('limit', newFilters.limit.toString());
    }
    if (newFilters.offset > 0) {
      params.set('offset', newFilters.offset.toString());
    }
    
    const newURL = params.toString() ? `${pathname}?${params.toString()}` : pathname;
    router.replace(newURL, { scroll: false });
  }, [pathname, router]);

  // Backend queries
  console.log('Filters status:', filters.status, 'Query status:', filters.status === "all" ? undefined : filters.status);
  const allOffers = useQuery(api.offerManagement.getSellerOffers, {
    status: filters.status === "all" ? undefined : filters.status as any
  });
  const offerCounts = useQuery(api.offerManagement.getSellerOfferCounts);
  
  // Get unread message count for seller
  const unreadMessageCount = useQuery(api.offerManagement.getUnreadOfferMessageCount);



  // Mutations
  const acceptOffer = useMutation(api.offerManagement.acceptOffer);
  const declineOffer = useMutation(api.offerManagement.declineOffer);

  // Filter and sort offers
  const filteredOffers = useMemo(() => {
    if (!allOffers) return [];
    
    let filtered = allOffers.filter((offer: any) => {
      // Search filter
      const matchesSearch = !filters.search || 
        offer.product?.title.toLowerCase().includes(filters.search.toLowerCase()) ||
        offer.buyer?.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        offer.buyer?.email.toLowerCase().includes(filters.search.toLowerCase()) ||
        offer.product?.brand.toLowerCase().includes(filters.search.toLowerCase());
      
      // Status filter
      const matchesStatus = filters.status === 'all' || offer.status === filters.status;
      
      // Product category filter (skip for now since category not in product data)
      const matchesCategory = true;
      
      // Amount range filter
      const matchesAmountRange = (!filters.amountRange.min || offer.offerAmount >= parseFloat(filters.amountRange.min)) &&
        (!filters.amountRange.max || offer.offerAmount <= parseFloat(filters.amountRange.max));
      
      return matchesSearch && matchesStatus && matchesCategory && matchesAmountRange;
    });
    
    // Sort offers
    switch (filters.sortBy) {
      case 'oldest':
        filtered.sort((a: any, b: any) => a._creationTime - b._creationTime);
        break;
      case 'amount_high':
        filtered.sort((a: any, b: any) => b.offerAmount - a.offerAmount);
        break;
      case 'amount_low':
        filtered.sort((a: any, b: any) => a.offerAmount - b.offerAmount);
        break;
      case 'product':
        filtered.sort((a: any, b: any) => (a.product?.title || '').localeCompare(b.product?.title || ''));
        break;
      case 'buyer':
        filtered.sort((a: any, b: any) => (a.buyer?.name || '').localeCompare(b.buyer?.name || ''));
        break;
      case 'status':
        filtered.sort((a: any, b: any) => a.status.localeCompare(b.status));
        break;
      default: // newest
        filtered.sort((a: any, b: any) => b._creationTime - a._creationTime);
    }
    
    return filtered;
  }, [allOffers, filters]);

  // Pagination
  const paginationInfo = useMemo(() => {
    const total = filteredOffers.length;
    const totalPages = Math.ceil(total / filters.limit);
    const currentPage = Math.floor(filters.offset / filters.limit) + 1;
    const startItem = filters.offset + 1;
    const endItem = Math.min(filters.offset + filters.limit, total);
    
    return {
      total,
      totalPages,
      currentPage,
      startItem,
      endItem,
      hasPrevious: currentPage > 1,
      hasNext: currentPage < totalPages,
    };
  }, [filteredOffers, filters]);

  const paginatedOffers = useMemo(() => {
    return filteredOffers.slice(filters.offset, filters.offset + filters.limit);
  }, [filteredOffers, filters]);

  // Calculate metrics
  const totalOffers = offerCounts?.total || 0;
  const pendingOffers = offerCounts?.pending || 0;
  const counteredOffers = offerCounts?.countered || 0;
  const acceptedOffers = 0; // Not available in current API
  const declinedOffers = 0; // Not available in current API

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const handleFilterChange = (key: keyof FilterState, value: any) => {
    const newFilters = { ...filters, [key]: value, offset: 0 };
    setFilters(newFilters);
    updateURL(newFilters);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(paginatedOffers.map((item: any) => item._id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (itemId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems((prev: any) => [...prev, itemId]);
    } else {
      setSelectedItems((prev: any) => prev.filter((id: any) => id !== itemId));
    }
  };

  const handleAcceptOffer = async (offerId: string) => {
    try {
      await acceptOffer({ offerId: offerId as any });
      toast.success("Offer accepted successfully!");
    } catch (error: any) {
      toast.error(error.message || "Failed to accept offer");
    }
  };

  const handleDeclineOffer = async (offerId: string) => {
    try {
      await declineOffer({ offerId: offerId as any });
      toast.success("Offer declined successfully!");
    } catch (error: any) {
      toast.error(error.message || "Failed to decline offer");
    }
  };

  const handleCounterOffer = (offerId: string) => {
    setSelectedOfferId(offerId);
    setIsCounterModalOpen(true);
  };

  const handleViewOffer = (offerId: string) => {
    setSelectedOfferId(offerId);
    setIsDetailsModalOpen(true);
  };

  const handleRespondToOffer = (offerId: string) => {
    setSelectedOfferId(offerId);
    setIsRespondModalOpen(true);
  };

  const handleOpenMessage = (offer: any) => {
    setSelectedOffer(offer);
    setIsMessageModalOpen(true);
  };

  const handleCloseMessage = () => {
    setIsMessageModalOpen(false);
    setSelectedOffer(null);
  };

  // Handle pagination
  const goToPage = (page: number) => {
    const newOffset = (page - 1) * filters.limit;
    const newFilters = { ...filters, offset: newOffset };
    setFilters(newFilters);
    updateURL(newFilters);
  };

  const goToNextPage = () => {
    if (paginationInfo.hasNext) {
      goToPage(paginationInfo.currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (paginationInfo.hasPrevious) {
      goToPage(paginationInfo.currentPage - 1);
    }
  };

  const goToFirstPage = () => goToPage(1);
  const goToLastPage = () => goToPage(paginationInfo.totalPages);

  const handleItemsPerPageChange = (newLimit: number) => {
    const newFilters = { ...filters, limit: newLimit, offset: 0 };
    setFilters(newFilters);
    updateURL(newFilters);
  };

  if (!user) {
    return (
      <div className="p-6 text-center">
        <p className="text-muted-foreground">Please log in to view offers</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header with Controls */}
      <div className="border-b border-border bg-card">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between gap-4">
            {/* Search and Filters */}
            <div className="flex items-center gap-4 flex-1">
              <div className="relative max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search offers..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                  className="pl-10 rounded-xl bg-primary/5 border-border font-light placeholder:text-primary transition-all duration-300 focus:ring-2 focus:ring-primary/20"
                />
              </div>
              
              {/* Status Dropdown */}
              <Select value={filters.status} onValueChange={(value) => handleFilterChange("status", value as FilterState["status"])}>
                <SelectTrigger className="w-40 rounded-xl bg-primary/5 border-border font-light">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <Grid className="w-4 h-4" />
                      ALL
                    </div>
                  </SelectItem>
                  <SelectItem value="pending" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      PENDING
                    </div>
                  </SelectItem>
                  <SelectItem value="countered" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <MessageSquare className="w-4 h-4" />
                      COUNTERED
                    </div>
                  </SelectItem>
                  <SelectItem value="accepted" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4" />
                      ACCEPTED
                    </div>
                  </SelectItem>
                  <SelectItem value="declined" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <XCircle className="w-4 h-4" />
                      DECLINED
                    </div>
                  </SelectItem>
                  <SelectItem value="expired" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="w-4 h-4" />
                      EXPIRED
                    </div>
                  </SelectItem>
                  <SelectItem value="withdrawn" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <Ban className="w-4 h-4" />
                      WITHDRAWN
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>

              {/* Filter Popover */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button 
                    variant="outline" 
                    className="rounded-xl font-light"
                  >
                    <Filter className="w-4 h-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-6" align="start">
                  <div className="space-y-4">
                    <h4 className="font-medium text-sm uppercase tracking-wide">Filters</h4>
                    
                    {/* Product Category Filter */}
                    <div>
                      <label className="text-sm font-light text-muted-foreground mb-2 block">Product Category</label>
                      <Select value={filters.productCategory} onValueChange={(value) => handleFilterChange("productCategory", value)}>
                        <SelectTrigger className="rounded-xl">
                          <SelectValue placeholder="All Categories" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Categories</SelectItem>
                          <SelectItem value="clothing">Clothing</SelectItem>
                          <SelectItem value="sneakers">Sneakers</SelectItem>
                          <SelectItem value="handbags">Handbags</SelectItem>
                          <SelectItem value="accessories">Accessories</SelectItem>
                          <SelectItem value="watches">Watches</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Amount Range */}
                    <div>
                      <label className="text-sm font-light text-muted-foreground mb-2 block">Amount Range</label>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Min"
                          value={filters.amountRange.min}
                          onChange={(e) => handleFilterChange("amountRange", { ...filters.amountRange, min: e.target.value })}
                          className="rounded-xl"
                        />
                        <Input
                          placeholder="Max"
                          value={filters.amountRange.max}
                          onChange={(e) => handleFilterChange("amountRange", { ...filters.amountRange, max: e.target.value })}
                          className="rounded-xl"
                        />
                      </div>
                    </div>

                    <Button 
                      onClick={() => {
                        const newFilters: FilterState = { 
                          ...filters, 
                          search: "", 
                          status: "all", 
                          productCategory: "all", 
                          amountRange: { min: "", max: "" } 
                        };
                        setFilters(newFilters);
                        updateURL(newFilters);
                      }}
                      variant="outline" 
                      className="w-full rounded-xl font-light"
                    >
                      Clear Filters
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-4">
              <Button 
                variant="ghost" 
                className="text-accent hover:text-accent/80 font-light rounded-xl px-4 py-2 transition-all duration-300"
              >
                <Download className="h-4 w-4 mr-2" />
                EXPORT
              </Button>
              
              <Button 
                onClick={() => router.push('/seller/offers')}
                className="bg-accent text-accent-foreground hover:bg-accent/90 rounded-xl px-6 py-2 transition-all duration-300"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                DASHBOARD
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Full Width Table */}
      <div className="flex-1">
        <Table>
          <TableHeader>
            <TableRow className="border-b border-border">
              <TableHead className="w-12">
                <Checkbox 
                  checked={selectedItems.length === paginatedOffers.length}
                  onCheckedChange={handleSelectAll}
                  className="rounded border-border"
                />
              </TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Product</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Buyer</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Offer Amount</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">List Price</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Percentage</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Date</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Status</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedOffers && paginatedOffers.length > 0 ? (
              paginatedOffers.map((offer: any, index: number) => (
                <TableRow key={offer._id} className="hover:bg-primary/5 transition-colors duration-300">
                  <TableCell>
                    <Checkbox 
                      checked={selectedItems.includes(offer._id)}
                      onCheckedChange={(checked) => handleSelectItem(offer._id, checked as boolean)}
                      className="rounded border-border"
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-primary/5 rounded-xl overflow-hidden border border-border">
                        {offer.product?.images && offer.product.images.length > 0 ? (
                          <img 
                            src={offer.product.images[0]}
                            alt={offer.product.title || "Product"}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              // Fallback to placeholder if image fails to load
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              target.nextElementSibling?.classList.remove('hidden');
                            }}
                          />
                        ) : null}
                        <div className={`w-full h-full bg-primary/5 flex items-center justify-center ${offer.product?.images && offer.product.images.length > 0 ? 'hidden' : ''}`}>
                          <Package className="w-4 h-4 text-muted-foreground" />
                        </div>
                      </div>
                      <div>
                        <p className="font-light text-foreground text-sm">{offer.product?.title || "—"}</p>
                        <p className="text-muted-foreground text-xs font-light">{offer.product?.brand || "—"}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-light text-foreground text-sm">{offer.buyer?.name || "—"}</p>
                      <p className="text-muted-foreground text-xs font-light">{offer.buyer?.email || "—"}</p>
                    </div>
                  </TableCell>
                  <TableCell className="font-light text-primary">{formatCurrency(offer.offerAmount)}</TableCell>
                  <TableCell className="font-light text-muted-foreground">
                    {offer.product?.price ? formatCurrency(offer.product.price) : "—"}
                  </TableCell>
                  <TableCell className="font-light text-muted-foreground">
                    {offer.product?.price ? `${Math.round((offer.offerAmount / offer.product.price) * 100)}%` : "—"}
                  </TableCell>
                  <TableCell className="font-light text-muted-foreground">{formatDate(offer._creationTime)}</TableCell>
                  <TableCell>
                    <div className="flex flex-col gap-1">
                      <Badge 
                        variant="outline" 
                        className={`text-xs font-light rounded-xl ${STATUS_COLORS[offer.status as keyof typeof STATUS_COLORS] || 'bg-gray-100 text-gray-800'}`}
                      >
                        {offer.status?.charAt(0).toUpperCase() + offer.status?.slice(1)}
                      </Badge>
                      {offer.sellerResponse && (
                        <Badge 
                          variant="secondary" 
                          className="text-xs font-light rounded-lg"
                        >
                          <MessageSquare className="w-3 h-3 mr-1" />
                          Responded
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="p-1 h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewOffer(offer._id)}>
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleOpenMessage(offer)}>
                          <MessageSquare className="h-4 w-4 mr-2" />
                          Message
                        </DropdownMenuItem>
                        {offer.status === "pending" && (
                          <>
                            <DropdownMenuItem onClick={() => handleAcceptOffer(offer._id)}>
                              <CheckCircle className="h-4 w-4 mr-2" />
                              Accept
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDeclineOffer(offer._id)}>
                              <XCircle className="h-4 w-4 mr-2" />
                              Decline
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleCounterOffer(offer._id)}>
                              <MessageSquare className="h-4 w-4 mr-2" />
                              Counter
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleRespondToOffer(offer._id)}>
                              <MessageSquare className="h-4 w-4 mr-2" />
                              {offer.sellerResponse ? "Update Response" : "Respond"}
                            </DropdownMenuItem>
                          </>
                        )}
                        {offer.status === "countered" && (
                          <DropdownMenuItem onClick={() => handleViewOffer(offer._id)}>
                            <MessageSquare className="h-4 w-4 mr-2" />
                            View Counter
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-16">
                  <div className="flex flex-col items-center justify-center">
                    <div className="w-16 h-16 bg-primary/5 rounded-xl flex items-center justify-center mb-4 border border-border">
                      <DollarSign className="w-8 h-8 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-light text-primary mb-2">
                      We could not find any offers matching your criteria
                    </h3>
                    <p className="text-muted-foreground mb-6 text-center max-w-md font-light">
                      Offers will appear here when buyers make them on your products.
                    </p>
                    <Button 
                      onClick={() => {
                        const newFilters: FilterState = { 
                          ...filters, 
                          search: "", 
                          status: "all", 
                          productCategory: "all", 
                          amountRange: { min: "", max: "" } 
                        };
                        setFilters(newFilters);
                        updateURL(newFilters);
                      }}
                      variant="outline"
                      className="rounded-xl font-light"
                    >
                      Clear Filters
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      {paginationInfo && paginationInfo.total > 0 && (
        <div className="border-t border-border bg-card">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              {/* Items per page selector */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-light text-muted-foreground">Show</span>
                <Select value={filters.limit.toString()} onValueChange={(value) => handleItemsPerPageChange(parseInt(value))}>
                  <SelectTrigger className="w-20 h-8 rounded-lg">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {ITEMS_PER_PAGE_OPTIONS.map(option => (
                      <SelectItem key={option} value={option.toString()}>
                        {option}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <span className="text-sm font-light text-muted-foreground">items per page</span>
              </div>

              {/* Pagination info */}
              <div className="text-sm font-light text-muted-foreground">
                {paginationInfo.startItem}–{paginationInfo.endItem} of {paginationInfo.total} items
              </div>

              {/* Pagination navigation */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToFirstPage}
                  disabled={!paginationInfo.hasPrevious}
                  className="h-8 w-8 p-0 rounded-lg"
                >
                  <ChevronsLeft className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPreviousPage}
                  disabled={!paginationInfo.hasPrevious}
                  className="h-8 w-8 p-0 rounded-lg"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                
                {/* Page numbers */}
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, paginationInfo.totalPages) }, (_, i) => {
                    let pageNum;
                    if (paginationInfo.totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (paginationInfo.currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (paginationInfo.currentPage >= paginationInfo.totalPages - 2) {
                      pageNum = paginationInfo.totalPages - 4 + i;
                    } else {
                      pageNum = paginationInfo.currentPage - 2 + i;
                    }
                    
                    if (pageNum < 1 || pageNum > paginationInfo.totalPages) return null;
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={pageNum === paginationInfo.currentPage ? "default" : "outline"}
                        size="sm"
                        onClick={() => goToPage(pageNum)}
                        className="h-8 w-8 p-0 rounded-lg"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={!paginationInfo.hasNext}
                  className="h-8 w-8 p-0 rounded-lg"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToLastPage}
                  disabled={!paginationInfo.hasNext}
                  className="h-8 w-8 p-0 rounded-lg"
                >
                  <ChevronsRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Counter Offer Modal */}
      {isCounterModalOpen && selectedOfferId && (() => {
        const offer = filteredOffers.find((o: any) => o._id === selectedOfferId);
        return offer && offer.product ? (
          <CounterOfferModal
            isOpen={isCounterModalOpen}
            onClose={() => {
              setIsCounterModalOpen(false);
              setSelectedOfferId(null);
            }}
            offer={{
              _id: offer._id,
              offerAmount: offer.offerAmount,
              product: {
                title: offer.product.title,
                brand: offer.product.brand,
                price: offer.product.price,
              }
            }}
          />
        ) : null;
      })()}

      {/* Offer Details Modal */}
      <OfferDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedOfferId(null);
        }}
        offer={selectedOfferId ? filteredOffers.find((o: any) => o._id === selectedOfferId) || null : null}
      />

      {/* Respond to Offer Modal */}
      <RespondToOfferModal
        isOpen={isRespondModalOpen}
        onClose={() => {
          setIsRespondModalOpen(false);
          setSelectedOfferId(null);
        }}
        offer={selectedOfferId ? filteredOffers.find((o: any) => o._id === selectedOfferId) || null : null}
      />

      {/* Offer Message Modal */}
      <OfferMessageModal
        isOpen={isMessageModalOpen}
        onClose={handleCloseMessage}
        offer={selectedOffer}
      />
    </div>
  );
}
