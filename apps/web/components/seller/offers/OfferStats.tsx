"use client";

import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { 
  Clock, 
  TrendingUp, 
  DollarSign, 
  CheckCircle2 
} from "lucide-react";

export function OfferStats() {
  const offerCounts = useQuery(api.offerManagement.getSellerOfferCounts);

  if (!offerCounts) {
    return (
      <div className="grid md:grid-cols-3 gap-6">
        {[1, 2, 3].map((i: number) => (
          <Card key={i} className="bg-card border-0 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Loading...
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">-</div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid md:grid-cols-3 gap-6">
      {/* Pending Offers */}
      <Card className="bg-card border-0 shadow-sm hover:shadow-md transition-shadow duration-300">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Pending Offers
          </CardTitle>
          <Clock className="h-4 w-4 text-amber-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-foreground">
            {offerCounts.pending}
          </div>
          <p className="text-xs text-muted-foreground">
            Awaiting your response
          </p>
        </CardContent>
      </Card>

      {/* Countered Offers */}
      <Card className="bg-card border-0 shadow-sm hover:shadow-md transition-shadow duration-300">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Countered Offers
          </CardTitle>
          <TrendingUp className="h-4 w-4 text-blue-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-foreground">
            {offerCounts.countered}
          </div>
          <p className="text-xs text-muted-foreground">
            Waiting for buyer response
          </p>
        </CardContent>
      </Card>

      {/* Total Offers */}
      <Card className="bg-card border-0 shadow-sm hover:shadow-md transition-shadow duration-300">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Total Offers
          </CardTitle>
          <DollarSign className="h-4 w-4 text-green-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-foreground">
            {offerCounts.total}
          </div>
          <p className="text-xs text-muted-foreground">
            All time offers received
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
