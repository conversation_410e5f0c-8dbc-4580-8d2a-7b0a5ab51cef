"use client";

import { useState } from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@repo/ui/components/popover";
import { Checkbox } from "@repo/ui/components/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { 
  Download, 
  FileText, 
  Table, 
  Calendar,
  CheckCircle,
  AlertTriangle,
  Loader2,
  BarChart3,
  X
} from "lucide-react";

interface SalesExportPopoverProps {
  data: {
    orders: any[];
    offlineSales: any[];
    invoices: any[];
    metrics: any;
  };
  timeRange: string;
  children: React.ReactNode;
}

export function SalesExportPopover({ data, timeRange, children }: SalesExportPopoverProps) {
  const [exportFormat, setExportFormat] = useState<"csv" | "pdf">("csv");
  const [selectedData, setSelectedData] = useState<string[]>([
    "orders",
    "offline-sales",
    "invoices",
    "metrics"
  ]);
  const [isExporting, setIsExporting] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const handleDataToggle = (dataId: string) => {
    setSelectedData(prev => 
      prev.includes(dataId) 
        ? prev.filter(id => id !== dataId)
        : [...prev, dataId]
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (date: string | number) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const generateCSV = () => {
    let csvContent = "";
    
    // Helper function to escape CSV values
    const escapeCSV = (value: any) => {
      if (value === null || value === undefined) return '';
      const stringValue = String(value);
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      return stringValue;
    };
    
    if (selectedData.includes("orders") && data.orders.length > 0) {
      csvContent += "Orders Data\n";
      csvContent += "Order Number,Product,Customer,Status,Total Amount,Seller Earnings,Order Date,Payment Method\n";
      
      data.orders.forEach((order: any) => {
        csvContent += `${escapeCSV(order.orderNumber || '')},${escapeCSV(order.product?.title || '')},${escapeCSV(order.buyer?.name || '')},${escapeCSV(order.orderStatus || '')},${escapeCSV(formatCurrency(order.totalAmount || 0))},${escapeCSV(formatCurrency(order.sellerEarnings || 0))},${escapeCSV(formatDate(order.orderDate || ''))},${escapeCSV(order.paymentMethod || '')}\n`;
      });
      csvContent += "\n";
    }

    if (selectedData.includes("offline-sales") && data.offlineSales.length > 0) {
      csvContent += "Offline Sales Data\n";
      csvContent += "Client Name,Product,Sale Price,Sale Date,Payment Status\n";
      
      data.offlineSales.forEach((sale: any) => {
        csvContent += `${escapeCSV(sale.clientName || '')},${escapeCSV(sale.product?.title || '')},${escapeCSV(formatCurrency(sale.salePrice || 0))},${escapeCSV(formatDate(sale.saleDate || ''))},${escapeCSV(sale.paymentStatus || '')}\n`;
      });
      csvContent += "\n";
    }

    if (selectedData.includes("invoices") && data.invoices.length > 0) {
      csvContent += "Invoices Data\n";
      csvContent += "Invoice Number,Client,Amount,Status,Issue Date,Due Date\n";
      
      data.invoices.forEach((invoice: any) => {
        csvContent += `${escapeCSV(invoice.invoiceNumber || '')},${escapeCSV(invoice.offlineSale?.clientName || '')},${escapeCSV(formatCurrency(invoice.amount || 0))},${escapeCSV(invoice.status || '')},${escapeCSV(formatDate(invoice.issueDate || ''))},${escapeCSV(formatDate(invoice.dueDate || ''))}\n`;
      });
      csvContent += "\n";
    }

    if (selectedData.includes("metrics") && data.metrics) {
      csvContent += "Sales Metrics Summary\n";
      csvContent += "Metric,Value\n";
      csvContent += `Total Revenue,${escapeCSV(formatCurrency(data.metrics.totalRevenue || 0))}\n`;
      csvContent += `Total Orders,${escapeCSV(data.metrics.totalOrders || 0)}\n`;
      csvContent += `Conversion Rate,${escapeCSV((data.metrics.conversionRate || 0) + '%')}\n`;
      csvContent += `Average Order Value,${escapeCSV(formatCurrency(data.metrics.averageOrderValue || 0))}\n`;
      csvContent += `Time Range,${escapeCSV(timeRange)}\n`;
      csvContent += `Report Generated,${escapeCSV(new Date().toLocaleDateString())}\n`;
    }

    return csvContent;
  };

  const downloadCSV = () => {
    const csvContent = generateCSV();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `sales-data-${timeRange}-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const generatePDF = async () => {
    // Check if there's any data to export
    const hasData = selectedData.some(dataType => {
      if (dataType === "metrics") return data.metrics;
      if (dataType === "orders") return data.orders && data.orders.length > 0;
      if (dataType === "offline-sales") return data.offlineSales && data.offlineSales.length > 0;
      if (dataType === "invoices") return data.invoices && data.invoices.length > 0;
      return false;
    });

    if (!hasData) {
      alert("No data available for the selected export options. Please select different data types or check if data exists.");
      return;
    }

    // Create a comprehensive HTML report that can be printed as PDF
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Sales Report - ${timeRange}</title>
        <style>
          body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 40px; 
            line-height: 1.6;
            color: #333;
          }
          .header { 
            text-align: center; 
            border-bottom: 2px solid #333; 
            padding-bottom: 20px; 
            margin-bottom: 30px;
          }
          .header h1 { 
            margin: 0; 
            color: #1a1a1a; 
            font-size: 28px;
          }
          .header p { 
            margin: 10px 0 0 0; 
            color: #666; 
            font-size: 16px;
          }
          .section { 
            margin-bottom: 30px; 
            page-break-inside: avoid;
          }
          .section h2 { 
            color: #1a1a1a; 
            border-bottom: 1px solid #ddd; 
            padding-bottom: 10px;
            margin-bottom: 20px;
          }
          table { 
            border-collapse: collapse; 
            width: 100%; 
            margin-bottom: 20px; 
            font-size: 14px;
          }
          th, td { 
            border: 1px solid #ddd; 
            padding: 12px 8px; 
            text-align: left; 
          }
          th { 
            background-color: #f8f9fa; 
            font-weight: 600;
            color: #495057;
          }
          .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
          }
          .metric-card {
            border: 1px solid #ddd;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            background-color: #f8f9fa;
          }
          .metric-value {
            font-size: 24px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 5px;
          }
          .metric-label {
            font-size: 14px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
          .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
          }
          @media print {
            body { margin: 20px; }
            .section { page-break-inside: avoid; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Sales Dashboard Report</h1>
          <p>Generated on ${new Date().toLocaleDateString()} • Time Range: ${timeRange}</p>
        </div>

        ${selectedData.includes("metrics") && data.metrics ? `
        <div class="section">
          <h2>Key Performance Metrics</h2>
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-value">${formatCurrency(data.metrics.totalRevenue || 0)}</div>
              <div class="metric-label">Total Revenue</div>
            </div>
            <div class="metric-card">
              <div class="metric-value">${data.metrics.totalOrders || 0}</div>
              <div class="metric-label">Total Orders</div>
            </div>
            <div class="metric-card">
              <div class="metric-value">${data.metrics.conversionRate || 0}%</div>
              <div class="metric-label">Conversion Rate</div>
            </div>
            <div class="metric-card">
              <div class="metric-value">${formatCurrency(data.metrics.averageOrderValue || 0)}</div>
              <div class="metric-label">Avg Order Value</div>
            </div>
          </div>
        </div>
        ` : ''}

        ${selectedData.includes("orders") && data.orders && data.orders.length > 0 ? `
        <div class="section">
          <h2>Orders Summary</h2>
          <table>
            <thead>
              <tr>
                <th>Order #</th>
                <th>Product</th>
                <th>Customer</th>
                <th>Status</th>
                <th>Total Amount</th>
                <th>Your Earnings</th>
                <th>Order Date</th>
              </tr>
            </thead>
            <tbody>
              ${data.orders.map((order: any) => `
                <tr>
                  <td>${order.orderNumber || 'N/A'}</td>
                  <td>${order.product?.title || 'N/A'}</td>
                  <td>${order.buyer?.name || 'N/A'}</td>
                  <td>${order.orderStatus || 'N/A'}</td>
                  <td>${formatCurrency(order.totalAmount || 0)}</td>
                  <td>${formatCurrency(order.sellerEarnings || 0)}</td>
                  <td>${formatDate(order.orderDate || '')}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
        ` : selectedData.includes("orders") ? `
        <div class="section">
          <h2>Orders Summary</h2>
          <div class="no-data">No orders data available for the selected time period.</div>
        </div>
        ` : ''}

        ${selectedData.includes("offline-sales") && data.offlineSales && data.offlineSales.length > 0 ? `
        <div class="section">
          <h2>Offline Sales</h2>
          <table>
            <thead>
              <tr>
                <th>Client Name</th>
                <th>Product</th>
                <th>Sale Price</th>
                <th>Sale Date</th>
                <th>Payment Status</th>
              </tr>
            </thead>
            <tbody>
              ${data.offlineSales.map((sale: any) => `
                <tr>
                  <td>${sale.clientName || 'N/A'}</td>
                  <td>${sale.product?.title || 'N/A'}</td>
                  <td>${formatCurrency(sale.salePrice || 0)}</td>
                  <td>${formatDate(sale.saleDate || '')}</td>
                  <td>${sale.paymentStatus || 'N/A'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
        ` : selectedData.includes("offline-sales") ? `
        <div class="section">
          <h2>Offline Sales</h2>
          <div class="no-data">No offline sales data available for the selected time period.</div>
        </div>
        ` : ''}

        ${selectedData.includes("invoices") && data.invoices && data.invoices.length > 0 ? `
        <div class="section">
          <h2>Invoices</h2>
          <table>
            <thead>
              <tr>
                <th>Invoice #</th>
                <th>Client</th>
                <th>Amount</th>
                <th>Status</th>
                <th>Issue Date</th>
                <th>Due Date</th>
              </tr>
            </thead>
            <tbody>
              ${data.invoices.map((invoice: any) => `
                <tr>
                  <td>${invoice.invoiceNumber || 'N/A'}</td>
                  <td>${invoice.offlineSale?.clientName || 'N/A'}</td>
                  <td>${formatCurrency(invoice.amount || 0)}</td>
                  <td>${invoice.status || 'N/A'}</td>
                  <td>${formatDate(invoice.issueDate || '')}</td>
                  <td>${formatDate(invoice.dueDate || '')}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
        ` : selectedData.includes("invoices") ? `
        <div class="section">
          <h2>Invoices</h2>
          <div class="no-data">No invoice data available for the selected time period.</div>
        </div>
        ` : ''}
      </body>
      </html>
    `;

    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const newWindow = window.open(url, '_blank');
    if (newWindow) {
      newWindow.onload = () => {
        newWindow.print();
      };
    }
  };

  const handleExport = async () => {
    if (selectedData.length === 0) {
      alert("Please select at least one data type to export");
      return;
    }

    setIsExporting(true);
    
    try {
      if (exportFormat === "csv") {
        downloadCSV();
      } else {
        await generatePDF();
      }
      setIsOpen(false);
    } catch (error) {
      console.error("Export failed:", error);
      alert("Export failed. Please try again.");
    } finally {
      setIsExporting(false);
    }
  };

  // Quick export options
  const quickExportOptions = [
    {
      id: "all",
      name: "Export All",
      description: "Complete sales data",
      icon: <Download className="h-4 w-4" />
    },
    {
      id: "summary",
      name: "Summary Only",
      description: "Key metrics & KPIs",
      icon: <BarChart3 className="h-4 w-4" />
    },
    {
      id: "transactions",
      name: "Transactions",
      description: "Orders & sales data",
      icon: <Table className="h-4 w-4" />
    }
  ];

  const handleQuickExport = (option: string) => {
    if (option === "all") {
      setSelectedData(["orders", "offline-sales", "invoices", "metrics"]);
    } else if (option === "summary") {
      setSelectedData(["metrics"]);
    } else if (option === "transactions") {
      setSelectedData(["orders", "offline-sales", "invoices"]);
    }
    handleExport();
  };

  const dataOptions = [
    {
      id: "orders",
      name: "Online Orders",
      description: "Marketplace orders",
      icon: <Table className="h-4 w-4" />,
      count: data.orders?.length || 0
    },
    {
      id: "offline-sales",
      name: "Offline Sales",
      description: "Direct sales data",
      icon: <Calendar className="h-4 w-4" />,
      count: data.offlineSales?.length || 0
    },
    {
      id: "invoices",
      name: "Invoices",
      description: "Invoice records",
      icon: <FileText className="h-4 w-4" />,
      count: data.invoices?.length || 0
    },
    {
      id: "metrics",
      name: "Performance",
      description: "Revenue & KPIs",
      icon: <CheckCircle className="h-4 w-4" />,
      count: "Summary"
    }
  ];

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        {children}
      </PopoverTrigger>
      <PopoverContent className="min-w-2xl w-full p-0" align="end">
        <div className="p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-foreground">Export Sales Data</h3>
              <p className="text-sm text-muted-foreground">Download your sales information</p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Quick Export Options */}
          <div className="space-y-3">
            <label className="text-sm font-medium text-foreground">Quick Export</label>
            <div className="grid grid-cols-3 gap-2">
              {quickExportOptions.map((option: any) => (
                <Button
                  key={option.id}
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickExport(option.id)}
                  className="font-light h-auto py-3 px-2 flex flex-col items-center gap-2 text-xs"
                  disabled={isExporting}
                >
                  {option.icon}
                  <div className="text-center">
                    <div className="font-medium">{option.name}</div>
                    <div className="text-muted-foreground">{option.description}</div>
                  </div>
                </Button>
              ))}
            </div>
          </div>

          {/* Export Format */}
          <div className="space-y-3">
            <label className="text-sm font-medium text-foreground">Format</label>
            <Select value={exportFormat} onValueChange={(value: "csv" | "pdf") => setExportFormat(value)}>
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="csv">
                  <div className="flex items-center gap-2">
                    <Table className="h-4 w-4" />
                    CSV (Excel compatible)
                  </div>
                </SelectItem>
                <SelectItem value="pdf">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    PDF Report
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Data Selection */}
          <div className="space-y-3">
            <label className="text-sm font-medium text-foreground">Select Data</label>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {dataOptions.map((option: any) => (
                <div key={option.id} className="flex items-start space-x-3">
                  <Checkbox
                    id={option.id}
                    checked={selectedData.includes(option.id)}
                    onCheckedChange={() => handleDataToggle(option.id)}
                  />
                  <div className="flex-1">
                    <label 
                      htmlFor={option.id}
                      className="text-sm font-medium text-foreground cursor-pointer"
                    >
                      <div className="flex items-center gap-2">
                        {option.icon}
                        {option.name}
                        <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                          {option.count}
                        </span>
                      </div>
                    </label>
                    <p className="text-xs text-muted-foreground mt-1">
                      {option.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Export Button */}
          <Button 
            onClick={handleExport}
            disabled={isExporting || selectedData.length === 0}
            className="w-full"
          >
            {isExporting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating {exportFormat.toUpperCase()}...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export as {exportFormat.toUpperCase()}
              </>
            )}
          </Button>

          {/* Tips */}
          <div className="text-xs text-muted-foreground space-y-1">
            <div className="flex items-start gap-2">
              <AlertTriangle className="w-3 h-3 mt-0.5 text-yellow-500" />
              <span>CSV files work with Excel, Google Sheets, and other spreadsheet apps</span>
            </div>
            <div className="flex items-start gap-2">
              <FileText className="w-3 h-3 mt-0.5 text-blue-500" />
              <span>PDF reports open in a new window for printing or saving</span>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
