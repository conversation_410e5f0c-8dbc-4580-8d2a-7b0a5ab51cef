"use client";

import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Clock, CheckCircle, XCircle, AlertCircle, FileText } from "lucide-react";

const statusConfig = {
  pending: {
    label: "Pending Review",
    color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
    icon: Clock,
    description: "Your application is being reviewed by our team."
  },
  under_review: {
    label: "Under Review",
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    icon: FileText,
    description: "We're currently reviewing your application details."
  },
  approved: {
    label: "Approved",
    color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    icon: CheckCircle,
    description: "Congratulations! Your application has been approved."
  },
  rejected: {
    label: "Rejected",
    color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    icon: XCircle,
    description: "Your application was not approved at this time."
  },
  requires_info: {
    label: "More Information Needed",
    color: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
    icon: AlertCircle,
    description: "We need additional information to process your application."
  }
};

export function ApplicationStatus() {
  const { user } = useAuth();
  
  const application = useQuery(
    api.sellerApplicationsSimple.getUserApplication,
    user?.email ? { email: user.email } : "skip"
  );

  if (!user?.email) {
    return null;
  }

  if (application === undefined) {
    return (
      <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
        <CardContent className="pt-6">
          <div className="flex items-center justify-center min-h-[100px]">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!application) {
    return null; // No application found, show the form instead
  }

  const status = statusConfig[application.status as keyof typeof statusConfig];
  const StatusIcon = status.icon;

  return (
    <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
      <CardHeader>
        <CardTitle className="text-xl font-light tracking-wide text-foreground flex items-center gap-3">
          <StatusIcon className="w-5 h-5" />
          Application Status
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0 space-y-4">
        <div className="flex items-center gap-3">
          <Badge className={`${status.color} font-medium`}>
            {status.label}
          </Badge>
          <span className="text-sm text-muted-foreground">
            Submitted on {new Date(application.submittedAt).toLocaleDateString()}
          </span>
        </div>
        
        <p className="text-foreground font-light">
          {status.description}
        </p>

        {application.status === "rejected" && application.rejectionReason && (
          <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">Rejection Reason:</h4>
            <p className="text-red-700 dark:text-red-300 text-sm">{application.rejectionReason}</p>
          </div>
        )}

        {application.status === "requires_info" && application.notes && (
          <div className="bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
            <h4 className="font-medium text-orange-800 dark:text-orange-200 mb-2">Additional Information Needed:</h4>
            <p className="text-orange-700 dark:text-orange-300 text-sm">{application.notes}</p>
          </div>
        )}

        {application.status === "approved" && (
          <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">Next Steps:</h4>
            <p className="text-green-700 dark:text-green-300 text-sm">
              You can now access your seller dashboard and start listing products. 
              Check your email for login credentials and setup instructions.
            </p>
          </div>
        )}

        <div className="pt-4 border-t border-border">
          <h4 className="font-medium text-foreground mb-3">Application Details:</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Business Name:</span>
              <p className="font-medium">{application.businessName}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Business Type:</span>
              <p className="font-medium capitalize">{application.businessType}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Specialties:</span>
              <p className="font-medium">{application.specialties.join(", ")}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Monthly Volume:</span>
              <p className="font-medium">{application.monthlyVolume}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
