"use client";

import { useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { Checkbox } from "@repo/ui/components/checkbox";
import { Badge } from "@repo/ui/components/badge";
import { Progress } from "@repo/ui/components/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, <PERSON><PERSON>Content } from "@repo/ui/components/tabs";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@repo/ui/components/command";
import { 
  Upload, 
  X, 
  Image as ImageIcon,
  Plus,
  Loader2,
  ArrowLeft,
  Save,
  Eye,
  AlertCircle,
  CheckCircle,
  Package,
  Crown,
  ShoppingBag,
  Calendar,
  DollarSign,
  Settings,
  Trash2,
  Check,
  ChevronsUpDown
} from "lucide-react";

// Import designers data
import designersData from "@/lib/designers.json";

interface ProductFormData {
  title: string;
  brand: string;
  sku: string;
  sizes: string[];
  price: string;
  description: string;
  images: File[];
  uploadedImageIds: string[];
  ownershipType: "owned" | "consigned";
  marketplaceType: "moda-watch" | "real-watch-buyers" | "moda-car" | "moda-lifestyle" | "moda-misc";
}

// Categories will be fetched from the database

const CONDITIONS = [
  { value: "new", label: "New", description: "Never worn/used, with tags" },
  { value: "like_new", label: "Like New", description: "Worn once or twice, excellent condition" },
  { value: "excellent", label: "Excellent", description: "Minor signs of wear, very good condition" },
  { value: "very_good", label: "Very Good", description: "Some signs of wear, good condition" },
  { value: "good", label: "Good", description: "Noticeable wear, still in good condition" },
  { value: "fair", label: "Fair", description: "Significant wear, but functional" },
];

// Type for designer data
interface Designer {
  name: string;
  slug: string;
}

// Size options for different regions
const SHOE_SIZES = {
  US: ["3.5M/5W", "4M/5.5W", "4.5M/6W", "5M/6.5W", "5.5M/7W", "6M/7.5W", "6.5M/8W", "7M/8.5W", "7.5M/9W", "8M/9.5W", "8.5M/10W", "9M/10.5W", "9.5M/11W", "10M/11.5W", "10.5M/12W", "11M/12.5W", "11.5M/13W", "12M/13.5W", "12.5M/14W", "13M/14.5W", "13.5M/15W", "14M/15.5W", "14.5M/16W", "15M/16.5W", "16M", "17M", "18M"],
  UK: ["3", "3.5", "4", "4.5", "5", "5.5", "6", "6.5", "7", "7.5", "8", "8.5", "9", "9.5", "10", "10.5", "11", "11.5", "12", "13", "14", "15"],
  EU: ["35", "35.5", "36", "36.5", "37", "37.5", "38", "38.5", "39", "39.5", "40", "40.5", "41", "41.5", "42", "42.5", "43", "43.5", "44", "44.5", "45", "45.5", "46", "47", "48", "49"],
  JP: ["22", "22.5", "23", "23.5", "24", "24.5", "25", "25.5", "26", "26.5", "27", "27.5", "28", "28.5", "29", "29.5", "30", "30.5", "31", "32"],
  KR: ["220", "225", "230", "235", "240", "245", "250", "255", "260", "265", "270", "275", "280", "285", "290", "295", "300", "305", "310", "320"]
};

export function AddProductForm() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imageUploadProgress, setImageUploadProgress] = useState<{ [key: string]: number }>({});
  const [brandSearch, setBrandSearch] = useState("");
  const [brandOpen, setBrandOpen] = useState(false);
  const [sizeType, setSizeType] = useState<"US" | "UK" | "EU" | "JP" | "KR" | "CUSTOM">("US");
  const [showSizeSelector, setShowSizeSelector] = useState(false);

  // Fetch categories and marketplace types
  const categories = useQuery(api.categories.getCategories);
  const marketplaceTypes = useQuery(api.marketplaceQueries.getMarketplaceTypes);
  
  // Process designers data
  const designers = designersData as Designer[];
  const filteredDesigners = designers.filter(designer =>
    designer.name.toLowerCase().includes(brandSearch.toLowerCase())
  );
  
  // Mutations
  const createProduct = useMutation(api.productManagement.createProduct);
  const publishProduct = useMutation(api.productManagement.publishProduct);
  const generateUploadUrl = useMutation(api.productManagement.generateImageUploadUrl);

  const [formData, setFormData] = useState<ProductFormData>({
    title: "",
    brand: "",
    sku: "",
    sizes: [],
    price: "",
    description: "",
    images: [],
    uploadedImageIds: [],
    ownershipType: "owned",
    marketplaceType: "moda-lifestyle",
  });

  const updateFormData = (field: keyof ProductFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const toggleSize = (size: string) => {
    const updatedSizes = formData.sizes.includes(size)
      ? formData.sizes.filter(s => s !== size)
      : [...formData.sizes, size];
    updateFormData("sizes", updatedSizes);
  };

  const getSizesForType = () => {
    if (sizeType === "CUSTOM") return [];
    return SHOE_SIZES[sizeType] || [];
  };

  const getStandardSizes = () => {
    // Get all standard sizes from all size types
    const allStandardSizes = Object.values(SHOE_SIZES).flat();
    return allStandardSizes;
  };

  const handleImageUpload = useCallback(async (files: FileList | null) => {
    if (!files) return;

    const newFiles = Array.from(files);
    const totalImages = formData.images.length + newFiles.length;

    if (totalImages > 10) {
      toast.error("Maximum 10 images allowed per product");
      return;
    }

    // Add files to form data immediately for preview
    updateFormData("images", [...formData.images, ...newFiles]);

    // Upload each file
    for (const file of newFiles) {
      try {
        // Validate file
        if (file.size > 5 * 1024 * 1024) {
          toast.error(`Image ${file.name} is too large. Maximum 5MB per image.`);
          continue;
        }

        if (!file.type.startsWith("image/")) {
          toast.error(`${file.name} is not a valid image file.`);
          continue;
        }

        const fileKey = `${file.name}-${Date.now()}`;
        setImageUploadProgress(prev => ({ ...prev, [fileKey]: 0 }));

        // Generate upload URL
        const uploadUrl = await generateUploadUrl();

        // Upload file
        const response = await fetch(uploadUrl, {
          method: "POST",
          headers: { "Content-Type": file.type },
          body: file,
        });

        if (!response.ok) {
          throw new Error(`Upload failed: ${response.statusText}`);
        }

        const result = await response.json();
        
        // Add storage ID to uploaded images
        updateFormData("uploadedImageIds", [...formData.uploadedImageIds, result.storageId]);
        
        setImageUploadProgress(prev => ({ ...prev, [fileKey]: 100 }));
        
        // Remove progress after delay
        setTimeout(() => {
          setImageUploadProgress(prev => {
            const newProgress = { ...prev };
            delete newProgress[fileKey];
            return newProgress;
          });
        }, 1000);

      } catch (error) {
        console.error("Upload error:", error);
        toast.error(`Failed to upload ${file.name}: ${error}`);
        
        // Remove failed file from images
        updateFormData("images", formData.images.filter(img => img.name !== file.name));
      }
    }
  }, [formData.images, formData.uploadedImageIds, generateUploadUrl]);

  const removeImage = (index: number) => {
    const newImages = formData.images.filter((_, i) => i !== index);
    const newUploadedIds = formData.uploadedImageIds.filter((_, i) => i !== index);
    updateFormData("images", newImages);
    updateFormData("uploadedImageIds", newUploadedIds);
  };

  const validateForm = (): boolean => {
    return !!(
      formData.title.trim() && 
      formData.brand.trim() && 
      formData.sizes.length > 0 && 
      formData.price && 
      parseFloat(formData.price) > 0 &&
      formData.ownershipType
    );
  };

  const handleSubmit = async (shouldPublish: boolean = false) => {
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!formData.title.trim()) throw new Error("Product name is required");
      if (!formData.brand.trim()) throw new Error("Brand is required");
      if (formData.sizes.length === 0) throw new Error("At least one size is required");
      if (!formData.price || parseFloat(formData.price) <= 0) throw new Error("Valid price is required");
      if (!formData.ownershipType) throw new Error("Ownership type is required");

      // Use a default category for now since it's not required
      const defaultCategory = categories?.[0];
      if (!defaultCategory) {
        throw new Error("No categories available");
      }

      const productData = {
        title: formData.title.trim(),
        description: formData.description.trim() || `${formData.brand} ${formData.title} - Luxury item in excellent condition`,
        price: parseFloat(formData.price),
        categoryId: defaultCategory._id,
        condition: "new" as any, // Default condition
        brand: formData.brand.trim(),
        sku: formData.sku.trim(),
        size: formData.sizes.join(", "), // Join sizes as string
        images: formData.uploadedImageIds as Id<"_storage">[],
        isAuthentic: true, // Default to authentic
        ownershipType: formData.ownershipType,
        marketplaceType: formData.marketplaceType,
      };

      console.log("Product data:", productData);

      const result = await createProduct(productData);

      if (shouldPublish && result.productId) {
        // Publish the product immediately to make it appear in dashboard
        await publishProduct({ productId: result.productId });
        toast.success("Product created and published successfully!");
      } else {
        toast.success("Product saved as draft!");
      }

      router.push("/seller/products");

    } catch (error) {
      console.error("Product creation error:", error);
      toast.error(`Failed to create product: ${error}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount: string) => {
    const num = parseFloat(amount);
    if (isNaN(num)) return "";
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(num);
  };

  // Show loading state if categories or marketplace types are not loaded yet
  if (categories === undefined || marketplaceTypes === undefined) {
    return (
      <div className="min-h-screen bg-background">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground font-light">Loading form...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background">
      {/* Header Bar - Full Width */}
      <div className="border-b border-border bg-card">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="rounded-xl font-light"
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-light text-primary tracking-wide">
                  ADD ITEM
                </h1>
                <p className="text-sm text-muted-foreground font-light">
                  Create a new luxury item listing
          </p>
        </div>
        </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="rounded-xl font-light"
                disabled={isSubmitting}
              >
                CANCEL
              </Button>
              <Button
                variant="outline"
                onClick={() => handleSubmit(false)}
                disabled={isSubmitting || !validateForm()}
                className="rounded-xl font-light"
              >
                {isSubmitting ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                SAVE DRAFT
              </Button>
              <Button
                onClick={() => handleSubmit(true)}
                disabled={isSubmitting || !validateForm()}
                className="bg-accent text-accent-foreground hover:bg-accent/90 rounded-xl font-light px-6"
              >
                {isSubmitting ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Eye className="w-4 h-4 mr-2" />
                )}
                PUBLISH
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card className="rounded-xl border-border">
      <CardHeader>
                <CardTitle className="font-light text-primary">Product Information</CardTitle>
      </CardHeader>
              <CardContent className="space-y-4">
                  <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Brand *</Label>
                  <Popover open={brandOpen} onOpenChange={setBrandOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={brandOpen}
                        className="w-full justify-between rounded-xl bg-primary/5 border-border font-light text-left"
                      >
                        {formData.brand || "Enter brand"}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0" align="start">
                      <Command>
                        <CommandInput 
                          placeholder="Search brands..." 
                          value={brandSearch}
                          onValueChange={setBrandSearch}
                          className="font-light"
                        />
                        <CommandList>
                          <CommandEmpty>
                            <div className="p-2">
                              <p className="text-sm text-muted-foreground mb-2">No brand found.</p>
                              <Button
                                size="sm"
                                onClick={() => {
                                  updateFormData("brand", brandSearch);
                                  setBrandOpen(false);
                                  setBrandSearch("");
                                }}
                                className="w-full font-light"
                                disabled={!brandSearch.trim()}
                              >
                                Add "{brandSearch}"
                              </Button>
                  </div>
                          </CommandEmpty>
                          <CommandGroup>
                            {filteredDesigners.slice(0, 50).map((designer) => (
                              <CommandItem
                                key={designer.slug}
                                value={designer.name}
                                onSelect={(currentValue) => {
                                  updateFormData("brand", currentValue);
                                  setBrandOpen(false);
                                  setBrandSearch("");
                                }}
                                className="font-light"
                              >
                                <Check
                                  className={`mr-2 h-4 w-4 ${
                                    formData.brand === designer.name ? "opacity-100" : "opacity-0"
                                  }`}
                                />
                                {designer.name}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
        </div>

                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">SKU</Label>
                  <Input
                    placeholder="Enter SKU number (optional)"
                    value={formData.sku}
                    onChange={(e) => updateFormData("sku", e.target.value)}
                    className="rounded-xl bg-primary/5 border-border font-light"
                  />
                  <p className="text-xs text-muted-foreground mt-1 font-light">
                    Stock Keeping Unit - for your internal inventory tracking
                  </p>
        </div>

                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Ownership Type *</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <Button
                      type="button"
                      variant={formData.ownershipType === "owned" ? "default" : "outline"}
                      onClick={() => updateFormData("ownershipType", "owned")}
                      className="rounded-xl font-light h-12"
                    >
                      <Package className="w-4 h-4 mr-2" />
                      Owner
                    </Button>
                    <Button
                      type="button"
                      variant={formData.ownershipType === "consigned" ? "default" : "outline"}
                      onClick={() => updateFormData("ownershipType", "consigned")}
                      className="rounded-xl font-light h-12"
                    >
                      <Crown className="w-4 h-4 mr-2" />
                      Consignment
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2 font-light">
                    {formData.ownershipType === "owned" 
                      ? "You own this item and will receive full proceeds from the sale."
                      : "This item is on consignment. You'll receive a commission when it sells."
                    }
                  </p>
        </div>

                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Marketplace *</Label>
                  <Select
                    value={formData.marketplaceType}
                    onValueChange={(value: "moda-watch" | "real-watch-buyers" | "moda-car" | "moda-lifestyle" | "moda-misc") => 
                      updateFormData("marketplaceType", value)
                    }
                  >
                    <SelectTrigger className="rounded-xl bg-primary/5 border-border font-light">
                      <SelectValue placeholder="Select marketplace" />
                    </SelectTrigger>
                    <SelectContent>
                      {marketplaceTypes?.filter(marketplace => 
                        marketplace.type !== "general"
                      ).map((marketplace) => (
                        <SelectItem key={marketplace.type} value={marketplace.type}>
                          <div className="flex items-center gap-2">
                            <span className="font-light">{marketplace.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground mt-2 font-light">
                    Choose which marketplace this product will be listed in
                  </p>
        </div>

                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Name *</Label>
              <Input
                    placeholder="Enter product name"
                    value={formData.title}
                    onChange={(e) => updateFormData("title", e.target.value)}
                    className="rounded-xl bg-primary/5 border-border font-light"
                  />
          </div>

                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Sizes *</Label>
                  <Button
                    variant="outline"
                    onClick={() => setShowSizeSelector(!showSizeSelector)}
                    className="w-full justify-between rounded-xl bg-primary/5 border-border font-light text-left"
                  >
                    {formData.sizes.length > 0 ? `${formData.sizes.length} size(s) selected` : "Enter Size(s)"}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>

                  {formData.sizes.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {formData.sizes.map((size) => (
                        <Badge 
                          key={size} 
                          variant="secondary" 
                          className="bg-accent text-accent-foreground font-light"
                        >
                          {size}
                          <X
                            className="w-3 h-3 ml-1 cursor-pointer"
                            onClick={() => toggleSize(size)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}

                  {showSizeSelector && (
                    <Card className="mt-4 rounded-xl border-border">
                      <CardHeader className="pb-4">
                        <CardTitle className="text-lg font-light">Select Size</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {/* Size Type Selector */}
                        <div className="flex flex-wrap gap-2">
                          {(["US", "UK", "EU", "JP", "KR", "CUSTOM"] as const).map((type) => (
                            <Button
                              key={type}
                              variant={sizeType === type ? "default" : "outline"}
                              size="sm"
                              onClick={() => setSizeType(type)}
                              className="rounded-xl font-light"
                            >
                              {type}
                            </Button>
                          ))}
        </div>

                        {/* Size Grid */}
                        {sizeType !== "CUSTOM" && (
                          <div className="grid grid-cols-3 gap-2 max-h-64 overflow-y-auto">
                            {getSizesForType().map((size) => (
                              <Button
                                key={size}
                                variant={formData.sizes.includes(size) ? "default" : "outline"}
                                size="sm"
                                onClick={() => toggleSize(size)}
                                className="rounded-lg font-light h-12"
                              >
                                {size}
                              </Button>
                            ))}
          </div>
                        )}

                                                {sizeType === "CUSTOM" && (
          <div className="space-y-2">
                            <div className="flex gap-2">
            <Input
                                id="custom-size-input"
                                placeholder="Enter custom size (e.g., 42mm, XL, Size 10)"
                                onKeyDown={(e) => {
                                  if (e.key === "Enter") {
                                    e.preventDefault();
                                    const value = (e.target as HTMLInputElement).value;
                                    if (value.trim()) {
                                      toggleSize(value.trim());
                                      (e.target as HTMLInputElement).value = "";
                                    }
                                  }
                                }}
                                className="rounded-xl bg-primary/5 border-border font-light"
                              />
                              <Button
                                type="button"
                                size="sm"
                                onClick={() => {
                                  const input = document.getElementById("custom-size-input") as HTMLInputElement;
                                  const value = input?.value;
                                  if (value?.trim()) {
                                    toggleSize(value.trim());
                                    input.value = "";
                                  }
                                }}
                                className="rounded-xl font-light px-4"
                              >
                                Add
                              </Button>
          </div>
                            <p className="text-xs text-muted-foreground font-light">
                              Press Enter or click Add to include custom size
                            </p>

                            {/* Show custom sizes that have been added */}
          <div className="space-y-2">
                              {formData.sizes.filter(size => !getStandardSizes().includes(size)).length > 0 && (
                                <div>
                                  <p className="text-xs text-muted-foreground font-light mb-2">Custom sizes added:</p>
                                  <div className="flex flex-wrap gap-2">
                                    {formData.sizes
                                      .filter(size => !getStandardSizes().includes(size))
                                      .map((size) => (
                                        <Badge 
                                          key={size} 
                                          variant="secondary" 
                                          className="bg-accent text-accent-foreground font-light"
                                        >
                                          {size}
                                          <X
                                            className="w-3 h-3 ml-1 cursor-pointer"
                                            onClick={() => toggleSize(size)}
                                          />
                                        </Badge>
                                      ))}
                                  </div>
                                </div>
                              )}
          </div>
                          </div>
                        )}

                        <div className="flex gap-2 pt-4">
                          <Button
                            variant="outline"
                            onClick={() => setShowSizeSelector(false)}
                            className="flex-1 rounded-xl font-light"
                          >
                            CANCEL
                          </Button>
                          <Button
                            onClick={() => setShowSizeSelector(false)}
                            className="flex-1 bg-accent text-accent-foreground hover:bg-accent/90 rounded-xl font-light"
                          >
                            APPLY
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  )}
        </div>

                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">Description</Label>
                  <Textarea
                    placeholder="Optional description..."
                    value={formData.description}
                    onChange={(e) => updateFormData("description", e.target.value)}
                    rows={4}
                    className="rounded-xl bg-primary/5 border-border font-light resize-none"
          />
        </div>
      </CardContent>
    </Card>

            {/* Images */}
            <Card className="rounded-xl border-border">
      <CardHeader>
                <CardTitle className="font-light text-primary">Images</CardTitle>
                <CardDescription className="font-light">
                  Upload product images (optional)
        </CardDescription>
      </CardHeader>
              <CardContent>
                <div className="border-2 border-dashed border-border rounded-xl p-8 text-center bg-primary/5">
          <input
            type="file"
            multiple
            accept="image/*"
            onChange={(e) => handleImageUpload(e.target.files)}
            className="hidden"
            id="image-upload"
          />
          <label htmlFor="image-upload" className="cursor-pointer">
                    <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <Upload className="w-8 h-8 text-primary" />
                    </div>
                    <p className="text-lg font-light text-primary mb-2">
                      Drop your images here, or browse
                    </p>
                    <p className="text-sm text-muted-foreground font-light">
                      Supports JPEG, PNG and WEBP (Max 5MB each)
            </p>
          </label>
        </div>

        {formData.images.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4">
            {formData.images.map((file, index) => (
              <div key={index} className="relative group">
                        <div className="aspect-square bg-primary/5 rounded-xl overflow-hidden border border-border">
                  <img
                    src={URL.createObjectURL(file)}
                    alt={`Product image ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
                <button
                  onClick={() => removeImage(index)}
                          className="absolute top-2 right-2 bg-destructive text-destructive-foreground rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        )}
              </CardContent>
            </Card>
          </div>

          {/* Pricing Sidebar */}
    <div className="space-y-6">
            <Card className="rounded-xl border-border">
        <CardHeader>
                <CardTitle className="font-light text-primary">Pricing</CardTitle>
        </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-light text-muted-foreground mb-2 block">List Price *</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground font-light">$</span>
              <Input
                      type="number"
                      placeholder="0.00"
                      value={formData.price}
                      onChange={(e) => updateFormData("price", e.target.value)}
                      className="rounded-xl bg-primary/5 border-border font-light pl-8"
                    />
              </div>
                  {formData.price && (
                    <p className="text-xs text-muted-foreground mt-1 font-light">
                      {formatCurrency(formData.price)}
                    </p>
            )}
          </div>
              </CardContent>
            </Card>

            {/* Form Status */}
            <Card className="rounded-xl border-border">
              <CardHeader>
                <CardTitle className="font-light text-primary">Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2">
                  {formData.brand ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-muted-foreground" />
                  )}
                  <span className="text-sm font-light">Brand selected</span>
              </div>
                <div className="flex items-center gap-2">
                  {formData.sku ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-muted-foreground" />
                  )}
                  <span className="text-sm font-light">SKU entered (optional)</span>
              </div>
                <div className="flex items-center gap-2">
                  {formData.title ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-muted-foreground" />
            )}
                  <span className="text-sm font-light">Name entered</span>
          </div>
                <div className="flex items-center gap-2">
                  {formData.sizes.length > 0 ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-muted-foreground" />
                  )}
                  <span className="text-sm font-light">Size(s) selected</span>
            </div>
                <div className="flex items-center gap-2">
                  {formData.price && parseFloat(formData.price) > 0 ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-muted-foreground" />
                  )}
                  <span className="text-sm font-light">Price set</span>
          </div>
                <div className="flex items-center gap-2">
                  {formData.ownershipType ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-muted-foreground" />
                  )}
                  <span className="text-sm font-light">Ownership type selected</span>
          </div>
                <div className="flex items-center gap-2">
                  {formData.marketplaceType ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-muted-foreground" />
                  )}
                  <span className="text-sm font-light">Marketplace selected</span>
          </div>
        </CardContent>
      </Card>
    </div>
        </div>
      </div>
    </div>
  );
}
