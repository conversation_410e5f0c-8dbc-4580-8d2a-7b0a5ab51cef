"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Checkbox } from "@repo/ui/components/checkbox";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { 
  Award, 
  TrendingUp, 
  ShoppingBag, 
  ArrowRight, 
  ArrowLeft, 
  CheckCircle, 
  AlertCircle,
  Star
} from "lucide-react";

interface ApplicationData {
  yearsExperience: number;
  platformsUsed: string[];
  specialties: string[];
}

interface ExperienceStepProps {
  data: ApplicationData;
  onUpdate: (data: Partial<ApplicationData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

const PLATFORMS = [
  { id: "ebay", name: "eBay", icon: "🛒" },
  { id: "amazon", name: "Amazon", icon: "📦" },
  { id: "etsy", name: "<PERSON><PERSON><PERSON>", icon: "🎨" },
  { id: "poshmark", name: "Poshmark", icon: "👗" },
  { id: "vestiaire", name: "Vestiaire Collective", icon: "👜" },
  { id: "therealreal", name: "The RealReal", icon: "💎" },
  { id: "rebag", name: "Rebag", icon: "👛" },
  { id: "fashionphile", name: "Fashionphile", icon: "✨" },
  { id: "consignment", name: "Consignment Stores", icon: "🏪" },
  { id: "other", name: "Other", icon: "📱" },
];

const SPECIALTIES = [
  { id: "handbags", name: "Luxury Handbags", icon: "👜" },
  { id: "watches", name: "Luxury Watches", icon: "⌚" },
  { id: "jewelry", name: "Fine Jewelry", icon: "💎" },
  { id: "clothing", name: "Designer Clothing", icon: "👔" },
  { id: "shoes", name: "Designer Shoes", icon: "👠" },
  { id: "accessories", name: "Luxury Accessories", icon: "🕶️" },
  { id: "art", name: "Art & Collectibles", icon: "🎨" },
  { id: "vintage", name: "Vintage Items", icon: "🏺" },
];

const VOLUME_OPTIONS = [
  { value: "1-10", label: "1-10 items per month" },
  { value: "11-25", label: "11-25 items per month" },
  { value: "26-50", label: "26-50 items per month" },
  { value: "51-100", label: "51-100 items per month" },
  { value: "100+", label: "100+ items per month" },
];

const VALUE_OPTIONS = [
  { value: "under-500", label: "Under $500" },
  { value: "500-1000", label: "$500 - $1,000" },
  { value: "1000-5000", label: "$1,000 - $5,000" },
  { value: "5000-10000", label: "$5,000 - $10,000" },
  { value: "10000+", label: "$10,000+" },
];

export function ExperienceStep({ 
  data, 
  onUpdate, 
  onNext, 
  onPrev 
}: ExperienceStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handlePlatformToggle = (platformId: string) => {
    const updatedPlatforms = data.platformsUsed.includes(platformId)
      ? data.platformsUsed.filter((id: string) => id !== platformId)
      : [...data.platformsUsed, platformId];
    
    onUpdate({ platformsUsed: updatedPlatforms });
  };

  const handleSpecialtyToggle = (specialtyId: string) => {
    const updatedSpecialties = data.specialties.includes(specialtyId)
      ? data.specialties.filter((id: string) => id !== specialtyId)
      : [...data.specialties, specialtyId];
    
    onUpdate({ specialties: updatedSpecialties });
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (data.yearsExperience < 1) {
      newErrors.yearsExperience = "Please enter your years of experience";
    }
    
    if (data.platformsUsed.length === 0) {
      newErrors.platformsUsed = "Please select at least one platform";
    }
    
    if (data.specialties.length === 0) {
      newErrors.specialties = "Please select at least one specialty";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      onNext();
    }
  };

  return (
    <div className="p-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900 dark:to-purple-800 rounded-2xl flex items-center justify-center mx-auto">
          <Award className="w-8 h-8 text-purple-600 dark:text-purple-400" />
        </div>
        <div>
          <h2 className="text-3xl font-bold text-black dark:text-white mb-2">
            Your Experience
          </h2>
          <p className="text-lg text-neutral-600 dark:text-neutral-400">
            Tell us about your experience selling luxury goods
          </p>
        </div>
      </div>

      {/* Form Fields */}
      <div className="space-y-8 max-w-3xl mx-auto">
        {/* Years of Experience */}
        <div className="space-y-2">
          <Label htmlFor="yearsExperience" className="text-base font-semibold text-black dark:text-white">
            Years of Experience Selling Luxury Goods *
          </Label>
          <div className="relative">
            <Input
              id="yearsExperience"
              type="number"
              min="0"
              max="50"
              value={data.yearsExperience || ""}
              onChange={(e) => onUpdate({ yearsExperience: parseInt(e.target.value) || 0 })}
              placeholder="Enter years of experience"
              className={`pl-12 h-14 text-lg rounded-xl border-2 transition-all duration-200 ${
                errors.yearsExperience
                  ? "border-red-300 focus:border-red-500"
                  : data.yearsExperience > 0
                  ? "border-green-300 focus:border-green-500"
                  : "border-neutral-300 focus:border-black dark:focus:border-white"
              }`}
            />
            <TrendingUp className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
            {data.yearsExperience > 0 && (
              <CheckCircle className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-500" />
            )}
          </div>
          {errors.yearsExperience && (
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.yearsExperience}
            </p>
          )}
        </div>

        {/* Platforms Used */}
        <div className="space-y-4">
          <div>
            <Label className="text-base font-semibold text-black dark:text-white">
              Platforms You've Used *
            </Label>
            <p className="text-sm text-neutral-600 dark:text-neutral-400 mt-1">
              Select all platforms where you've sold luxury items
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {PLATFORMS.map((platform) => (
              <Card
                key={platform.id}
                className={`p-4 cursor-pointer transition-all duration-200 hover:shadow-md ${
                  data.platformsUsed.includes(platform.id)
                    ? "bg-black text-white dark:bg-white dark:text-black border-black dark:border-white"
                    : "bg-white dark:bg-neutral-900 hover:bg-neutral-50 dark:hover:bg-neutral-800"
                }`}
                onClick={() => handlePlatformToggle(platform.id)}
              >
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{platform.icon}</span>
                  <div className="flex-1">
                    <p className="font-medium text-sm">{platform.name}</p>
                  </div>
                  {data.platformsUsed.includes(platform.id) && (
                    <CheckCircle className="w-5 h-5" />
                  )}
                </div>
              </Card>
            ))}
          </div>
          
          {errors.platformsUsed && (
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.platformsUsed}
            </p>
          )}
        </div>

        {/* Specialties */}
        <div className="space-y-4">
          <div>
            <Label className="text-base font-semibold text-black dark:text-white">
              Your Specialties *
            </Label>
            <p className="text-sm text-neutral-600 dark:text-neutral-400 mt-1">
              What types of luxury items do you specialize in?
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {SPECIALTIES.map((specialty) => (
              <Card
                key={specialty.id}
                className={`p-4 cursor-pointer transition-all duration-200 hover:shadow-md ${
                  data.specialties.includes(specialty.id)
                    ? "bg-black text-white dark:bg-white dark:text-black border-black dark:border-white"
                    : "bg-white dark:bg-neutral-900 hover:bg-neutral-50 dark:hover:bg-neutral-800"
                }`}
                onClick={() => handleSpecialtyToggle(specialty.id)}
              >
                <div className="text-center space-y-2">
                  <span className="text-2xl">{specialty.icon}</span>
                  <p className="font-medium text-xs">{specialty.name}</p>
                  {data.specialties.includes(specialty.id) && (
                    <CheckCircle className="w-4 h-4 mx-auto" />
                  )}
                </div>
              </Card>
            ))}
          </div>
          
          {errors.specialties && (
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.specialties}
            </p>
          )}
        </div>

        {/* Experience Summary */}
        {data.yearsExperience > 0 && data.platformsUsed.length > 0 && data.specialties.length > 0 && (
          <Card className="bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800 p-6">
            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-xl flex items-center justify-center flex-shrink-0">
                <Star className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <h3 className="font-semibold text-purple-900 dark:text-purple-300 mb-2">
                  Experience Summary
                </h3>
                <p className="text-sm text-purple-800 dark:text-purple-400">
                  {data.yearsExperience} years of experience • {data.platformsUsed.length} platforms • {data.specialties.length} specialties
                </p>
                <p className="text-sm text-purple-800 dark:text-purple-400 mt-1">
                  Your experience profile looks great for MODA's luxury marketplace!
                </p>
              </div>
            </div>
          </Card>
        )}
      </div>

      {/* Navigation */}
      <div className="flex justify-between pt-6 max-w-3xl mx-auto">
        <Button
          onClick={onPrev}
          variant="outline"
          className="h-14 px-8 text-lg font-semibold rounded-xl border-2"
        >
          <ArrowLeft className="w-5 h-5 mr-2" />
          Previous
        </Button>
        
        <Button
          onClick={handleNext}
          className="bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200 h-14 px-8 text-lg font-semibold rounded-xl"
        >
          Continue
          <ArrowRight className="w-5 h-5 ml-2" />
        </Button>
      </div>
    </div>
  );
}
