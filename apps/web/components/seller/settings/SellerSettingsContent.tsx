"use client";

import { useState } from "react";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Separator } from "@repo/ui/components/separator";
import { ProfileImage } from "@/components/common/ProfileImage";
import { Upload, Building2, Globe, Mail, Phone, Save, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { Id } from "@repo/backend/convex/_generated/dataModel";

export function SellerSettingsContent() {
  const [isLoading, setIsLoading] = useState(false);
  const [isUploadingLogo, setIsUploadingLogo] = useState(false);
  const [tempLogoUrl, setTempLogoUrl] = useState<string>();

  // Get current company information
  const companyInfo = useQuery(api.sellers.getCompanyInfo);
  const updateCompanyInfo = useMutation(api.sellers.updateCompanyInfo);
  const generateUploadUrl = useMutation(api.userManagement.generateProfileImageUploadUrl);

  // Form state
  const [formData, setFormData] = useState({
    website: "",
    companyEmail: "",
    companyPhone: "",
  });

  // Initialize form data when company info loads
  useState(() => {
    if (companyInfo) {
      setFormData({
        website: companyInfo.website || "",
        companyEmail: companyInfo.companyEmail || "",
        companyPhone: companyInfo.companyPhone || "",
      });
    }
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith("image/")) {
      toast.error("Please select an image file");
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("Image must be less than 5MB");
      return;
    }

    setIsUploadingLogo(true);

    try {
      // Create temporary URL for immediate display
      const tempUrl = URL.createObjectURL(file);
      setTempLogoUrl(tempUrl);

      // Get upload URL
      const postUrl = await generateUploadUrl();

      // Upload file
      const result = await fetch(postUrl, {
        method: "POST",
        headers: { "Content-Type": file.type },
        body: file,
      });

      const { storageId } = await result.json();

      // Update company info with new logo
      await updateCompanyInfo({
        companyLogo: storageId as Id<"_storage">,
      });

      toast.success("Company logo updated successfully!");
      
      // Clean up temp URL
      URL.revokeObjectURL(tempUrl);
      setTempLogoUrl(undefined);
    } catch (error) {
      console.error("Logo upload failed:", error);
      toast.error("Failed to upload logo");
      
      // Clean up temp URL on error
      if (tempLogoUrl) {
        URL.revokeObjectURL(tempLogoUrl);
        setTempLogoUrl(undefined);
      }
    } finally {
      setIsUploadingLogo(false);
    }
  };

  const handleSave = async () => {
    setIsLoading(true);

    try {
      await updateCompanyInfo({
        website: formData.website || undefined,
        companyEmail: formData.companyEmail || undefined,
        companyPhone: formData.companyPhone || undefined,
      });

      toast.success("Company information updated successfully!");
    } catch (error) {
      console.error("Failed to update company info:", error);
      toast.error("Failed to update company information");
    } finally {
      setIsLoading(false);
    }
  };

  if (!companyInfo) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6 p-4">
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">Settings</h1>
        <p className="text-gray-600 mt-1">
          Manage your company information and branding for invoices
        </p>
      </div>

      {/* Company Logo Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Company Logo
          </CardTitle>
          <CardDescription>
            Upload your company logo to appear on invoices and branding materials
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <ProfileImage
              storageId={companyInfo.companyLogo}
              name={companyInfo.businessName || "Company"}
              size="xl"
              tempImageUrl={tempLogoUrl}
              showFallback={true}
              variant="company"
            />
            <div className="space-y-2">
              <Label htmlFor="logo-upload" className="cursor-pointer">
                <Button
                  variant="outline"
                  disabled={isUploadingLogo}
                  className="cursor-pointer"
                  asChild
                >
                  <span>
                    {isUploadingLogo ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Uploading...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4 mr-2" />
                        Upload Logo
                      </>
                    )}
                  </span>
                </Button>
              </Label>
              <Input
                id="logo-upload"
                type="file"
                accept="image/*"
                onChange={handleLogoUpload}
                className="hidden"
              />
              <p className="text-xs text-gray-500">
                Recommended: Square image, max 5MB
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Company Information Section */}
      <Card>
        <CardHeader>
          <CardTitle>Company Information</CardTitle>
          <CardDescription>
            This information will appear on your invoices and customer communications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Business Name (Read-only from profile) */}
          <div className="space-y-2">
            <Label>Business Name</Label>
            <Input
              value={companyInfo.businessName || ""}
              disabled
              className="bg-gray-50"
            />
            <p className="text-xs text-gray-500">
              To change your business name, contact support
            </p>
          </div>

          {/* Website */}
          <div className="space-y-2">
            <Label htmlFor="website" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              Website
            </Label>
            <Input
              id="website"
              type="url"
              placeholder="https://your-company.com"
              value={formData.website}
              onChange={(e) => handleInputChange("website", e.target.value)}
            />
          </div>

          {/* Company Email */}
          <div className="space-y-2">
            <Label htmlFor="companyEmail" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Company Email
            </Label>
            <Input
              id="companyEmail"
              type="email"
              placeholder="<EMAIL>"
              value={formData.companyEmail}
              onChange={(e) => handleInputChange("companyEmail", e.target.value)}
            />
            <p className="text-xs text-gray-500">
              Email address to display on invoices (optional)
            </p>
          </div>

          {/* Company Phone */}
          <div className="space-y-2">
            <Label htmlFor="companyPhone" className="flex items-center gap-2">
              <Phone className="h-4 w-4" />
              Company Phone
            </Label>
            <Input
              id="companyPhone"
              type="tel"
              placeholder="(*************"
              value={formData.companyPhone}
              onChange={(e) => handleInputChange("companyPhone", e.target.value)}
            />
            <p className="text-xs text-gray-500">
              Phone number to display on invoices (optional)
            </p>
          </div>

          <Separator />

          {/* Company Address (Read-only from profile) */}
          <div className="space-y-2">
            <Label>Company Address</Label>
            <div className="space-y-1 text-sm text-gray-600">
              <div>{companyInfo.address.street}</div>
              <div>
                {companyInfo.address.city}, {companyInfo.address.state} {companyInfo.address.zipCode}
              </div>
              <div>{companyInfo.address.country}</div>
            </div>
            <p className="text-xs text-gray-500">
              To change your address, contact support
            </p>
          </div>

          {/* Save Button */}
          <div className="flex justify-end pt-4">
            <Button
              onClick={handleSave}
              disabled={isLoading}
              className="min-w-[120px]"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
