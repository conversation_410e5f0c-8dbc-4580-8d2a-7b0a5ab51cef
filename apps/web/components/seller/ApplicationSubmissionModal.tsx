"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@repo/ui/components/dialog";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { 
  CheckCircle, 
  Clock, 
  Mail, 
  FileCheck, 
  Shield,
  ArrowRight,
  Calendar,
  User,
  Building
} from "lucide-react";

interface ApplicationData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  businessName: string;
  taxId: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  yearsExperience: string;
  platformsUsed: string[];
  specialties: string[];
  idDocument?: File;
  businessDocuments: File[];
}

interface ApplicationSubmissionModalProps {
  isOpen: boolean;
  onClose: () => void;
  applicationData: ApplicationData;
}

export function ApplicationSubmissionModal({
  isOpen,
  onClose,
  applicationData,
}: ApplicationSubmissionModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [applicationId, setApplicationId] = useState<string>("");

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      // TODO: Implement actual submission to backend
      // const response = await submitSellerApplication(applicationData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Generate mock application ID
      const mockId = `HV-${Date.now().toString().slice(-6)}`;
      setApplicationId(mockId);
      setIsSubmitted(true);
    } catch (error) {
      console.error("Failed to submit application:", error);
      // Handle error
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setIsSubmitted(false);
    setIsSubmitting(false);
    setApplicationId("");
    onClose();
  };

  if (isSubmitted) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-2xl mx-auto bg-white dark:bg-neutral-900 rounded-3xl border-0 shadow-2xl">
          <div className="p-8 text-center space-y-8">
            {/* Success Icon */}
            <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
              <CheckCircle className="w-10 h-10 text-green-600 dark:text-green-400" />
            </div>
            
            {/* Success Message */}
            <div className="space-y-4">
              <h2 className="text-3xl font-bold text-black dark:text-white">
                Application Submitted Successfully!
              </h2>
              <p className="text-lg text-neutral-600 dark:text-neutral-400">
                Thank you for applying to become a verified seller on MODA.
              </p>
            </div>

            {/* Application ID */}
            <Card className="bg-neutral-50 dark:bg-neutral-800 p-6">
              <div className="space-y-2">
                <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                  Application Reference ID
                </p>
                <p className="text-2xl font-bold text-black dark:text-white font-mono">
                  {applicationId}
                </p>
                <p className="text-sm text-neutral-500 dark:text-neutral-400">
                  Save this ID for your records
                </p>
              </div>
            </Card>

            {/* Next Steps */}
            <div className="text-left space-y-4">
              <h3 className="text-xl font-bold text-black dark:text-white text-center">
                What happens next?
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0">
                    <Mail className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-black dark:text-white">Email Confirmation</h4>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      You'll receive a confirmation email within 5 minutes
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center flex-shrink-0">
                    <FileCheck className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-black dark:text-white">Document Review</h4>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      Our team will review your documents within 2-3 business days
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0">
                    <Shield className="w-4 h-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-black dark:text-white">Verification Complete</h4>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      Once approved, you can start listing luxury items immediately
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-3">
              <Button
                onClick={handleClose}
                className="w-full bg-black text-white hover:bg-neutral-800 dark:bg-white dark:text-black dark:hover:bg-neutral-200 h-12 rounded-xl"
              >
                Continue to Dashboard
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              
              <Button
                variant="outline"
                onClick={() => window.open(`mailto:<EMAIL>?subject=Application ${applicationId}`)}
                className="w-full h-12 rounded-xl"
              >
                Contact Support
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl mx-auto bg-white dark:bg-neutral-900 rounded-3xl border-0 shadow-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="p-8 pb-0">
          <DialogTitle className="text-2xl font-bold text-black dark:text-white text-center">
            Review Your Application
          </DialogTitle>
          <p className="text-neutral-600 dark:text-neutral-400 text-center">
            Please review your information before submitting
          </p>
        </DialogHeader>

        <div className="p-8 space-y-6">
          {/* Personal Information */}
          <Card className="p-6 bg-neutral-50 dark:bg-neutral-800">
            <div className="flex items-center space-x-3 mb-4">
              <User className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              <h3 className="text-lg font-semibold text-black dark:text-white">
                Personal Information
              </h3>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-neutral-600 dark:text-neutral-400">Name</p>
                <p className="font-medium text-black dark:text-white">
                  {applicationData.firstName} {applicationData.lastName}
                </p>
              </div>
              <div>
                <p className="text-neutral-600 dark:text-neutral-400">Email</p>
                <p className="font-medium text-black dark:text-white">
                  {applicationData.email}
                </p>
              </div>
              <div>
                <p className="text-neutral-600 dark:text-neutral-400">Phone</p>
                <p className="font-medium text-black dark:text-white">
                  {applicationData.phone}
                </p>
              </div>
            </div>
          </Card>

          {/* Business Information */}
          <Card className="p-6 bg-neutral-50 dark:bg-neutral-800">
            <div className="flex items-center space-x-3 mb-4">
              <Building className="w-5 h-5 text-green-600 dark:text-green-400" />
              <h3 className="text-lg font-semibold text-black dark:text-white">
                Business Information
              </h3>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-neutral-600 dark:text-neutral-400">Business Name</p>
                <p className="font-medium text-black dark:text-white">
                  {applicationData.businessName}
                </p>
              </div>
              <div>
                <p className="text-neutral-600 dark:text-neutral-400">Tax ID</p>
                <p className="font-medium text-black dark:text-white">
                  {applicationData.taxId}
                </p>
              </div>
              <div className="col-span-2">
                <p className="text-neutral-600 dark:text-neutral-400">Address</p>
                <p className="font-medium text-black dark:text-white">
                  {applicationData.address.street}, {applicationData.address.city}, {applicationData.address.state} {applicationData.address.zipCode}
                </p>
              </div>
            </div>
          </Card>

          {/* Experience Summary */}
          <Card className="p-6 bg-neutral-50 dark:bg-neutral-800">
            <div className="flex items-center space-x-3 mb-4">
              <Shield className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              <h3 className="text-lg font-semibold text-black dark:text-white">
                Experience & Specialties
              </h3>
            </div>
            <div className="space-y-3 text-sm">
              <div>
                <p className="text-neutral-600 dark:text-neutral-400">Years of Experience</p>
                <p className="font-medium text-black dark:text-white">
                  {applicationData.yearsExperience} years
                </p>
              </div>
              <div>
                <p className="text-neutral-600 dark:text-neutral-400 mb-2">Platforms Used</p>
                <div className="flex flex-wrap gap-2">
                  {applicationData.platformsUsed.map((platform: string) => (
                    <Badge key={platform} variant="outline" className="text-xs">
                      {platform}
                    </Badge>
                  ))}
                </div>
              </div>
              <div>
                <p className="text-neutral-600 dark:text-neutral-400 mb-2">Specialties</p>
                <div className="flex flex-wrap gap-2">
                  {applicationData.specialties.map((specialty: string) => (
                    <Badge key={specialty} variant="outline" className="text-xs">
                      {specialty}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </Card>

          {/* Documents */}
          <Card className="p-6 bg-neutral-50 dark:bg-neutral-800">
            <div className="flex items-center space-x-3 mb-4">
              <FileCheck className="w-5 h-5 text-orange-600 dark:text-orange-400" />
              <h3 className="text-lg font-semibold text-black dark:text-white">
                Uploaded Documents
              </h3>
            </div>
            <div className="space-y-3 text-sm">
              <div className="flex items-center justify-between">
                <span className="text-neutral-600 dark:text-neutral-400">ID Document</span>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="font-medium text-black dark:text-white">
                    {applicationData.idDocument?.name}
                  </span>
                </div>
              </div>
              <div>
                <p className="text-neutral-600 dark:text-neutral-400 mb-2">Business Documents</p>
                <div className="space-y-1">
                  {applicationData.businessDocuments.map((doc: any, index: number) => (
                    <div key={index} className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="font-medium text-black dark:text-white text-xs">
                        {doc.name}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Card>

          {/* Processing Time Notice */}
          <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 p-6">
            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center flex-shrink-0">
                <Clock className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h3 className="font-semibold text-blue-900 dark:text-blue-300 mb-2">
                  Application Processing
                </h3>
                <p className="text-sm text-blue-800 dark:text-blue-400">
                  Our verification team typically reviews applications within 2-3 business days. 
                  You'll receive email updates throughout the process.
                </p>
              </div>
            </div>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-center pt-4">
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="w-full max-w-md bg-green-600 text-white hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-700 h-14 text-lg font-semibold rounded-xl"
            >
              {isSubmitting ? (
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Submitting Application...</span>
                </div>
              ) : (
                <>
                  <Shield className="w-6 h-6 mr-3" />
                  Submit Application
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
