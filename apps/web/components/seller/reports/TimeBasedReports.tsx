"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@repo/ui/components/tabs";
import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from "recharts";
import { Calendar, TrendingUp, BarChart3, <PERSON><PERSON><PERSON> as PieChartIcon } from "lucide-react";

interface TimeBasedReportsProps {
  dateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
}

export function TimeBasedReports({ dateRange }: TimeBasedReportsProps) {
  const [timePeriod, setTimePeriod] = useState<"daily" | "weekly" | "monthly" | "yearly">("daily");
  const [chartType, setChartType] = useState<"line" | "bar">("line");
  
  const analytics = useQuery(api.analytics.getSalesAnalytics, {
    startDate: dateRange.from?.getTime(),
    endDate: dateRange.to?.getTime(),
    period: timePeriod,
  });

  if (!analytics) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-48 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    switch (timePeriod) {
      case "daily":
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      case "weekly":
        return `Week of ${date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`;
      case "monthly":
        return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
      case "yearly":
        return date.getFullYear().toString();
      default:
        return dateString;
    }
  };

  const chartData = analytics.timeSeriesData.map((item: any) => ({
    ...item,
    formattedDate: formatDate(item.date),
  }));

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-xl font-semibold">Time-Based Analysis</h2>
          <p className="text-sm text-muted-foreground">
            Sales trends and patterns over time
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Tabs value={timePeriod} onValueChange={(value) => setTimePeriod(value as any)}>
            <TabsList>
              <TabsTrigger value="daily">Daily</TabsTrigger>
              <TabsTrigger value="weekly">Weekly</TabsTrigger>
              <TabsTrigger value="monthly">Monthly</TabsTrigger>
              <TabsTrigger value="yearly">Yearly</TabsTrigger>
            </TabsList>
          </Tabs>
          
          <div className="flex items-center gap-2">
            <Button
              variant={chartType === "line" ? "default" : "outline"}
              size="sm"
              onClick={() => setChartType("line")}
            >
              <TrendingUp className="h-4 w-4" />
            </Button>
            <Button
              variant={chartType === "bar" ? "default" : "outline"}
              size="sm"
              onClick={() => setChartType("bar")}
            >
              <BarChart3 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold">{formatCurrency(analytics.totalRevenue)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Sales</p>
                <p className="text-2xl font-bold">{analytics.totalItems}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg. Sale Price</p>
                <p className="text-2xl font-bold">{formatCurrency(analytics.averageSalePrice)}</p>
              </div>
              <PieChartIcon className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Revenue Trends - {timePeriod.charAt(0).toUpperCase() + timePeriod.slice(1)}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              {chartType === "line" ? (
                <LineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="formattedDate" 
                    tick={{ fontSize: 12 }}
                    angle={-45}
                    textAnchor="end"
                    height={60}
                  />
                  <YAxis 
                    tick={{ fontSize: 12 }}
                    tickFormatter={formatCurrency}
                  />
                  <Tooltip 
                    // @ts-ignore
                    formatter={[formatCurrency, "Revenue"]}
                    labelFormatter={(label) => `Date: ${label}`}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="revenue" 
                    stroke="#0088FE" 
                    strokeWidth={2}
                    dot={{ fill: "#0088FE", strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              ) : (
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="formattedDate" 
                    tick={{ fontSize: 12 }}
                    angle={-45}
                    textAnchor="end"
                    height={60}
                  />
                  <YAxis 
                    tick={{ fontSize: 12 }}
                    tickFormatter={formatCurrency}
                  />
                  <Tooltip 
                    // @ts-ignore
                    formatter={[formatCurrency, "Revenue"]}
                    labelFormatter={(label) => `Date: ${label}`}
                  />
                  <Bar dataKey="revenue" fill="#0088FE" />
                </BarChart>
              )}
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Sales Volume Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Sales Volume - {timePeriod.charAt(0).toUpperCase() + timePeriod.slice(1)}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="formattedDate" 
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip 
                  // @ts-ignore
                  formatter={[(value: number) => [value, "Sales Count"]]}
                  labelFormatter={(label) => `Date: ${label}`}
                />
                <Bar dataKey="sales" fill="#00C49F" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Period Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Period Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Best Day</p>
              <p className="text-lg font-semibold">
                {chartData.length > 0 ? 
                  formatDate(chartData.reduce((max: any, item: any) => 
                    item.revenue > max.revenue ? item : max
                  ).date) : "N/A"
                }
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Peak Revenue</p>
              <p className="text-lg font-semibold">
                {chartData.length > 0 ? 
                  formatCurrency(Math.max(...chartData.map((item: any) => item.revenue))) : 
                  "$0"
                }
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Avg. Daily Revenue</p>
              <p className="text-lg font-semibold">
                {chartData.length > 0 ? 
                  formatCurrency(chartData.reduce((sum: any, item: any) => sum + item.revenue, 0) / chartData.length) : 
                  "$0"
                }
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Growth Trend</p>
              <Badge variant={
                chartData.length >= 2
                  ? (Number(chartData[chartData.length - 1]?.revenue) > Number(chartData[0]?.revenue) ? "default" : "secondary")
                  : "secondary"
              }>
                {chartData.length >= 2
                  ? (Number(chartData[chartData.length - 1]?.revenue) > Number(chartData[0]?.revenue) ? "Increasing" : "Decreasing")
                  : "Insufficient Data"
                }
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
