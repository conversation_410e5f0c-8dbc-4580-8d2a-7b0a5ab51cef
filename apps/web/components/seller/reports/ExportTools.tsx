"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Checkbox } from "@repo/ui/components/checkbox";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { 
  Download, 
  FileText, 
  Table, 
  Calendar,
  Filter,
  CheckCircle
} from "lucide-react";

interface ExportToolsProps {
  dateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
}

export function ExportTools({ dateRange }: ExportToolsProps) {
  const [exportFormat, setExportFormat] = useState<"csv" | "pdf">("csv");
  const [selectedReports, setSelectedReports] = useState<string[]>([
    "sales-summary",
    "time-analysis",
    "top-performers"
  ]);
  const [isExporting, setIsExporting] = useState(false);
  
  const analytics = useQuery(api.analytics.getSalesAnalytics, {
    startDate: dateRange.from?.getTime(),
    endDate: dateRange.to?.getTime(),
  });

  const topPerformers = useQuery(api.analytics.getTopPerformers, {
    startDate: dateRange.from?.getTime(),
    endDate: dateRange.to?.getTime(),
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const handleReportToggle = (reportId: string) => {
    setSelectedReports(prev => 
      prev.includes(reportId) 
        ? prev.filter(id => id !== reportId)
        : [...prev, reportId]
    );
  };

  const generateCSV = () => {
    if (!analytics || !topPerformers) return;

    let csvContent = "";
    
    // Sales Summary
    if (selectedReports.includes("sales-summary")) {
      csvContent += "SALES SUMMARY\n";
      csvContent += `Period,${dateRange.from?.toLocaleDateString()} - ${dateRange.to?.toLocaleDateString()}\n`;
      csvContent += `Total Revenue,${analytics.totalRevenue}\n`;
      csvContent += `Total Items Sold,${analytics.totalItems}\n`;
      csvContent += `Average Sale Price,${analytics.averageSalePrice}\n\n`;
    }

    // Time Analysis
    if (selectedReports.includes("time-analysis") && analytics.timeSeriesData.length > 0) {
      csvContent += "TIME ANALYSIS\n";
      csvContent += "Date,Revenue,Sales Count\n";
      analytics.timeSeriesData.forEach((item: any) => {
        csvContent += `${item.date},${item.revenue},${item.sales}\n`;
      });
      csvContent += "\n";
    }

    // Top Customers
    if (selectedReports.includes("top-performers") && topPerformers.topCustomers.length > 0) {
      csvContent += "TOP CUSTOMERS\n";
      csvContent += "Name,Email,Total Spent,Purchases,Average Order\n";
      topPerformers.topCustomers.forEach((customer: any) => {
        csvContent += `${customer.name},${customer.email},${customer.totalSpent},${customer.purchases},${customer.totalSpent / customer.purchases}\n`;
      });
      csvContent += "\n";
    }

    // Top Categories
    if (selectedReports.includes("top-performers") && topPerformers.topCategories.length > 0) {
      csvContent += "TOP CATEGORIES\n";
      csvContent += "Category,Sales Count,Revenue,Average Sale\n";
      topPerformers.topCategories.forEach((category: any) => {
        csvContent += `${category.name},${category.sales},${category.revenue},${category.revenue / category.sales}\n`;
      });
      csvContent += "\n";
    }

    // Top Products
    if (selectedReports.includes("top-performers") && topPerformers.topProducts.length > 0) {
      csvContent += "TOP PRODUCTS\n";
      csvContent += "Product,Brand,Sales Count,Revenue,Average Price,Profit\n";
      topPerformers.topProducts.forEach((product: any) => {
        csvContent += `${product.title},${product.brand},${product.sales},${product.revenue},${product.averagePrice},${product.profit || 'N/A'}\n`;
      });
    }

    return csvContent;
  };

  const downloadCSV = () => {
    const csvContent = generateCSV();
    if (!csvContent) return;

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `sales-report-${Date.now()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const generatePDF = async () => {
    // This would typically use a library like jsPDF or call a backend service
    // For now, we'll create a simple HTML version that can be printed as PDF
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Sales Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1, h2 { color: #333; }
          table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
          .summary { background-color: #f9f9f9; padding: 15px; margin-bottom: 20px; }
        </style>
      </head>
      <body>
        <h1>Sales Report</h1>
        <div class="summary">
          <h2>Summary</h2>
          <p><strong>Period:</strong> ${dateRange.from?.toLocaleDateString()} - ${dateRange.to?.toLocaleDateString()}</p>
          <p><strong>Total Revenue:</strong> ${formatCurrency(analytics?.totalRevenue || 0)}</p>
          <p><strong>Items Sold:</strong> ${analytics?.totalItems || 0}</p>
          <p><strong>Average Sale Price:</strong> ${formatCurrency(analytics?.averageSalePrice || 0)}</p>
        </div>
        
        ${topPerformers?.topCustomers.length ? `
        <h2>Top Customers</h2>
        <table>
          <tr><th>Name</th><th>Email</th><th>Total Spent</th><th>Purchases</th></tr>
          ${topPerformers.topCustomers.map((customer: any) => `
            <tr>
              <td>${customer.name}</td>
              <td>${customer.email}</td>
              <td>${formatCurrency(customer.totalSpent)}</td>
              <td>${customer.purchases}</td>
            </tr>
          `).join('')}
        </table>
        ` : ''}
        
        ${topPerformers?.topCategories.length ? `
        <h2>Top Categories</h2>
        <table>
          <tr><th>Category</th><th>Sales</th><th>Revenue</th></tr>
          ${topPerformers.topCategories.map((category: any) => `
            <tr>
              <td>${category.name}</td>
              <td>${category.sales}</td>
              <td>${formatCurrency(category.revenue)}</td>
            </tr>
          `).join('')}
        </table>
        ` : ''}
      </body>
      </html>
    `;

    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const newWindow = window.open(url, '_blank');
    if (newWindow) {
      newWindow.onload = () => {
        newWindow.print();
      };
    }
  };

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      if (exportFormat === "csv") {
        downloadCSV();
      } else {
        await generatePDF();
      }
    } catch (error) {
      console.error("Export failed:", error);
    } finally {
      setIsExporting(false);
    }
  };

  const reportOptions = [
    {
      id: "sales-summary",
      name: "Sales Summary",
      description: "Total revenue, items sold, and average prices",
      icon: <Table className="h-4 w-4" />
    },
    {
      id: "time-analysis",
      name: "Time Analysis",
      description: "Daily/weekly/monthly sales trends",
      icon: <Calendar className="h-4 w-4" />
    },
    {
      id: "top-performers",
      name: "Top Performers",
      description: "Best customers, categories, and products",
      icon: <CheckCircle className="h-4 w-4" />
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold">Export Data</h2>
        <p className="text-sm text-muted-foreground">
          Download your sales reports and analytics data
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Export Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Export Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Format Selection */}
            <div>
              <label className="text-sm font-medium mb-2 block">Export Format</label>
              <Select value={exportFormat} onValueChange={(value: "csv" | "pdf") => setExportFormat(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="csv">
                    <div className="flex items-center gap-2">
                      <Table className="h-4 w-4" />
                      CSV (Spreadsheet)
                    </div>
                  </SelectItem>
                  <SelectItem value="pdf">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      PDF (Document)
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Report Selection */}
            <div>
              <label className="text-sm font-medium mb-3 block">Include Reports</label>
              <div className="space-y-3">
                {reportOptions.map((report) => (
                  <div key={report.id} className="flex items-start space-x-3">
                    <Checkbox
                      id={report.id}
                      checked={selectedReports.includes(report.id)}
                      onCheckedChange={() => handleReportToggle(report.id)}
                    />
                    <div className="flex-1">
                      <label 
                        htmlFor={report.id}
                        className="text-sm font-medium cursor-pointer flex items-center gap-2"
                      >
                        {report.icon}
                        {report.name}
                      </label>
                      <p className="text-xs text-muted-foreground mt-1">
                        {report.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Date Range Info */}
            <div className="p-3 bg-muted rounded-lg">
              <p className="text-sm font-medium mb-1">Date Range</p>
              <p className="text-sm text-muted-foreground">
                {dateRange.from?.toLocaleDateString()} - {dateRange.to?.toLocaleDateString()}
              </p>
            </div>

            {/* Export Button */}
            <Button 
              onClick={handleExport} 
              disabled={isExporting || selectedReports.length === 0}
              className="w-full"
            >
              <Download className="h-4 w-4 mr-2" />
              {isExporting ? "Exporting..." : `Export as ${exportFormat.toUpperCase()}`}
            </Button>
          </CardContent>
        </Card>

        {/* Preview */}
        <Card>
          <CardHeader>
            <CardTitle>Export Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Format:</span>
                <Badge variant="outline">
                  {exportFormat.toUpperCase()}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Reports:</span>
                <Badge variant="outline">
                  {selectedReports.length} selected
                </Badge>
              </div>

              <div className="border rounded-lg p-4 bg-muted/50">
                <h4 className="font-medium mb-2">Will include:</h4>
                <ul className="text-sm space-y-1">
                  {selectedReports.map(reportId => {
                    const report = reportOptions.find(r => r.id === reportId);
                    return (
                      <li key={reportId} className="flex items-center gap-2">
                        <CheckCircle className="h-3 w-3 text-green-600" />
                        {report?.name}
                      </li>
                    );
                  })}
                </ul>
              </div>

              {analytics && (
                <div className="border rounded-lg p-4">
                  <h4 className="font-medium mb-2">Data Summary:</h4>
                  <div className="text-sm space-y-1">
                    <p>• {analytics.totalItems} sales transactions</p>
                    <p>• {formatCurrency(analytics.totalRevenue)} total revenue</p>
                    <p>• {topPerformers?.topCustomers.length || 0} unique customers</p>
                    <p>• {topPerformers?.topCategories.length || 0} product categories</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Export History */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Exports</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Download className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No recent exports</p>
            <p className="text-sm">Your export history will appear here</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
