"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Alert, AlertDescription } from "@repo/ui/components/alert";
import { 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  ExternalLink, 
  RefreshCw,
  CreditCard,
  DollarSign
} from "lucide-react";
import { toast } from "sonner";

export function StripeConnectStatus() {
  const [isLoading, setIsLoading] = useState(false);
  const connectStatus = useQuery(api.stripeConnect.getConnectStatus, {});

  const handleOnboarding = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/stripe/connect/onboard?userId=${connectStatus?.accountId || ''}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success && result.url) {
        // Redirect to Stripe onboarding
        window.location.href = result.url;
      } else {
        throw new Error(result.error || 'Failed to generate onboarding link');
      }
    } catch (error) {
      console.error('Onboarding error:', error);
      toast.error('Failed to start onboarding process');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status?: string) => {
    switch (status) {
      case 'enabled':
        return <Badge variant="default" className="bg-green-100 text-green-800">Enabled</Badge>;
      case 'restricted':
        return <Badge variant="destructive">Restricted</Badge>;
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
      default:
        return <Badge variant="outline">Not Set Up</Badge>;
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'enabled':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'restricted':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-400" />;
    }
  };

  if (!connectStatus) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Payment Account Status
          </CardTitle>
          <CardDescription>Loading your payment account information...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Payment Account Status
          </CardTitle>
          <CardDescription>
            Manage your Stripe Connect account for receiving payments
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getStatusIcon(connectStatus.status)}
              <div>
                <p className="font-medium">Account Status</p>
                <p className="text-sm text-muted-foreground">
                  {connectStatus.hasStripeAccount ? 'Connected to Stripe' : 'Not connected'}
                </p>
              </div>
            </div>
            {getStatusBadge(connectStatus.status)}
          </div>

          {connectStatus.hasStripeAccount && (
            <div className="grid grid-cols-2 gap-4 pt-4 border-t">
              <div className="flex items-center gap-2">
                <CheckCircle className={`h-4 w-4 ${connectStatus.chargesEnabled ? 'text-green-600' : 'text-gray-400'}`} />
                <span className="text-sm">Charges Enabled</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className={`h-4 w-4 ${connectStatus.payoutsEnabled ? 'text-green-600' : 'text-gray-400'}`} />
                <span className="text-sm">Payouts Enabled</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className={`h-4 w-4 ${connectStatus.detailsSubmitted ? 'text-green-600' : 'text-gray-400'}`} />
                <span className="text-sm">Details Submitted</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className={`h-4 w-4 ${connectStatus.onboardingComplete ? 'text-green-600' : 'text-gray-400'}`} />
                <span className="text-sm">Setup Complete</span>
              </div>
            </div>
          )}

          {connectStatus.requirements && connectStatus.requirements.length > 0 && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Action Required:</strong> Please complete the following requirements:
                <ul className="mt-2 list-disc list-inside text-sm">
                  {connectStatus.requirements.map((req: any, index: number) => (
                    <li key={index}>{req.replace(/_/g, ' ')}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          <div className="flex gap-2 pt-4">
            {!connectStatus.hasStripeAccount ? (
              <Button 
                onClick={handleOnboarding}
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <ExternalLink className="h-4 w-4" />
                )}
                Set Up Payment Account
              </Button>
            ) : !connectStatus.onboardingComplete ? (
              <Button 
                onClick={handleOnboarding}
                disabled={isLoading}
                variant="outline"
                className="flex items-center gap-2"
              >
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <ExternalLink className="h-4 w-4" />
                )}
                Complete Setup
              </Button>
            ) : (
              <Button 
                onClick={handleOnboarding}
                disabled={isLoading}
                variant="outline"
                className="flex items-center gap-2"
              >
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <ExternalLink className="h-4 w-4" />
                )}
                Manage Account
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {connectStatus.hasStripeAccount && connectStatus.onboardingComplete && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Earnings Overview
            </CardTitle>
            <CardDescription>
              Your earnings and payout information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <p className="text-2xl font-bold text-green-600">$0.00</p>
                <p className="text-sm text-muted-foreground">Total Earnings</p>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <p className="text-2xl font-bold text-blue-600">$0.00</p>
                <p className="text-sm text-muted-foreground">Pending Payouts</p>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <p className="text-2xl font-bold text-gray-600">$0.00</p>
                <p className="text-sm text-muted-foreground">Available Balance</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
