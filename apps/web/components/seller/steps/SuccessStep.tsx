"use client";

import { CheckCir<PERSON>, Mail, Clock, FileText } from "lucide-react";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Card, CardContent } from "@repo/ui/components/card";
import Link from "next/link";

export function SuccessStep() {
  const applicationId = `HV-${Date.now().toString().slice(-8)}`;

  return (
    <div className="max-w-2xl mx-auto text-center space-y-8">
      {/* Success Icon */}
      <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
        <CheckCircle className="w-12 h-12 text-green-600 dark:text-green-400" />
      </div>

      {/* Success Message */}
      <div className="space-y-4">
        <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100">
          Application Submitted Successfully!
        </h2>
        <p className="text-lg text-neutral-600 dark:text-neutral-400">
          Thank you for applying to become a MODA seller. Your application is now under review.
        </p>
      </div>

      {/* Application Details */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Application ID:</span>
              <span className="font-mono font-medium text-neutral-900 dark:text-neutral-100">
                {applicationId}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Submitted:</span>
              <span className="font-medium text-neutral-900 dark:text-neutral-100">
                {new Date().toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Status:</span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                Under Review
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Next Steps */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="text-center space-y-3">
          <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto">
            <Mail className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 className="font-semibold text-neutral-900 dark:text-neutral-100">
            Email Confirmation
          </h3>
          <p className="text-sm text-neutral-600 dark:text-neutral-400">
            Check your email for a confirmation message with your application details.
          </p>
        </div>

        <div className="text-center space-y-3">
          <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mx-auto">
            <Clock className="w-6 h-6 text-orange-600 dark:text-orange-400" />
          </div>
          <h3 className="font-semibold text-neutral-900 dark:text-neutral-100">
            Review Process
          </h3>
          <p className="text-sm text-neutral-600 dark:text-neutral-400">
            Our team will review your application within 2-3 business days.
          </p>
        </div>

        <div className="text-center space-y-3">
          <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
            <FileText className="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <h3 className="font-semibold text-neutral-900 dark:text-neutral-100">
            Decision Notification
          </h3>
          <p className="text-sm text-neutral-600 dark:text-neutral-400">
            You'll receive an email with our decision and next steps.
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Button asChild>
          <Link href="/seller/dashboard">
            Check Application Status
          </Link>
        </Button>
        <Button variant="outline" asChild>
          <Link href="/marketplace">
            Browse Marketplace
          </Link>
        </Button>
      </div>

      {/* Contact Info */}
      <div className="bg-neutral-50 dark:bg-neutral-900 rounded-lg p-6">
        <h4 className="font-medium text-neutral-900 dark:text-neutral-100 mb-2">
          Questions about your application?
        </h4>
        <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-3">
          Our seller support team is here to help.
        </p>
        <div className="flex flex-col sm:flex-row gap-2 text-sm">
          <span className="text-neutral-600 dark:text-neutral-400">Email:</span>
          <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400 hover:underline">
            <EMAIL>
          </a>
        </div>
      </div>
    </div>
  );
}