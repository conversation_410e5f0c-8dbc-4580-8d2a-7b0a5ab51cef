"use client";

import { useState, useMemo } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { formatDistanceToNow } from "date-fns";
import { motion, AnimatePresence } from "framer-motion";

import { Button } from "@repo/ui/components/button";
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar";
import { Badge } from "@repo/ui/components/badge";
import { Textarea } from "@repo/ui/components/textarea";
import { 
  MessageCircle, 
  Send, 
  User,
  Package,
  Clock,
  CheckCheck,
  MoreHorizontal,
  X,
  ArrowLeft
} from "lucide-react";
import { toast } from "sonner";

interface ConversationViewProps {
  conversation: {
    _id: string;
    conversationId: string;
    otherParticipant: {
      _id: string;
      name: string;
      email: string;
      profileImage?: string;
      userType?: string;
    } | null;
    product?: {
      _id: string;
      title: string;
      images: string[];
    } | null;
    unreadCount: number;
    lastMessageAt: number;
  } | null;
  onBack: () => void;
  isSupportMode?: boolean;
}

export function ConversationView({ 
  conversation, 
  onBack, 
  isSupportMode = false 
}: ConversationViewProps) {
  const { user } = useAuth();
  const [newMessage, setNewMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [expandedMessages, setExpandedMessages] = useState<Set<string>>(new Set());

  // Get messages for this conversation
  const messages = useQuery(
    api.messages.getMessages,
    conversation?.otherParticipant?._id 
      ? { recipientId: conversation.otherParticipant._id as any }
      : "skip"
  );

  // Send message mutation
  const sendMessage = useMutation(api.messages.sendMessage);

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleSendMessage = async () => {
    if (!conversation?.otherParticipant?._id || !newMessage.trim()) return;

    setIsLoading(true);
    try {
      await sendMessage({
        recipientId: conversation.otherParticipant._id as any,
        content: newMessage.trim(),
        messageType: "text",
      });
      setNewMessage("");
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error("Failed to send message");
    } finally {
      setIsLoading(false);
    }
  };

  const toggleMessageExpansion = (messageId: string) => {
    setExpandedMessages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(messageId)) {
        newSet.delete(messageId);
      } else {
        newSet.add(messageId);
      }
      return newSet;
    });
  };

  if (!conversation) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <MessageCircle className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-light text-foreground mb-2">
            Select a conversation
          </h3>
          <p className="text-muted-foreground font-light">
            Choose a conversation to start messaging
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-background">
      {/* Chat Header */}
      <div className="border-b border-border bg-card p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onBack}
              className="p-1 h-8 w-8 rounded-lg"
            >
              <ArrowLeft className="w-4 h-4" />
            </Button>
            
            <Avatar className="w-10 h-10">
              <AvatarImage 
                src={conversation.otherParticipant?.profileImage} 
                alt="User" 
              />
              <AvatarFallback>
                {getInitials(conversation.otherParticipant?.name || 'User')}
              </AvatarFallback>
            </Avatar>
            
            <div>
              <h3 className="font-medium text-foreground">
                {conversation.otherParticipant?.name || 'Unknown User'}
              </h3>
              <p className="text-sm text-muted-foreground font-light">
                {conversation.otherParticipant?.userType === 'consumer' ? 'Buyer' : 'Seller'}
                {conversation.unreadCount > 0 && (
                  <span className="ml-2">
                    • <Badge variant="secondary" className="text-xs">
                      {conversation.unreadCount} unread
                    </Badge>
                  </span>
                )}
              </p>
            </div>
          </div>
          
          <Button variant="outline" size="sm" className="rounded-xl">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>

        {/* Product info if available */}
        {conversation.product && (
          <div className="mt-3 pt-3 border-t border-border">
            <div className="flex items-center gap-2">
              <Package className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground font-light">
                About: {conversation.product.title}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3 bg-gradient-to-b from-background to-muted/10">
        {messages && messages.length > 0 ? (
          messages.map((message: any) => {
            // Determine if this is our own message by checking senderId
            const isOwnMessage = message.senderId === user?.userId;
            const isExpanded = expandedMessages.has(message._id);
            
            return (
              <motion.div
                key={message._id}
                className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} group`}
                layout
              >
                <div className={`flex flex-col max-w-[75%] ${isOwnMessage ? 'items-end' : 'items-start'}`}>
                  <div className={`flex items-end gap-2 ${isOwnMessage ? 'flex-row-reverse' : 'flex-row'}`}>
                    {/* Avatar for received messages */}
                    {!isOwnMessage && (
                      <Avatar className="flex-shrink-0">
                        <AvatarImage 
                          src={conversation.otherParticipant?.profileImage} 
                          alt="User" 
                        />
                        <AvatarFallback className="text-xs">
                          {getInitials(conversation.otherParticipant?.name || 'User')}
                        </AvatarFallback>
                      </Avatar>
                    )}
                    
                    {/* Message bubble */}
                    <motion.div 
                      className={`relative rounded-2xl px-4 py-2.5 shadow-sm transition-all duration-200 cursor-pointer ${
                        isOwnMessage
                          ? 'bg-primary text-primary-foreground ml-2 rounded-br-md'
                          : 'bg-card text-foreground border border-border/50 mr-2 rounded-bl-md hover:border-border'
                      }`}
                      onClick={() => toggleMessageExpansion(message._id)}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <p
                        className={`text-sm leading-relaxed break-words ${isOwnMessage ? 'text-white text-right' : 'text-foreground text-left'}`}
                        style={isOwnMessage ? { marginLeft: 'auto' } : {}}
                      >
                        {message.content}
                      </p>
                    </motion.div>
                  </div>
                  
                  {/* Expanded metadata below bubble */}
                  <AnimatePresence>
                    {isExpanded && (
                      <motion.div
                        initial={{ opacity: 0, height: 0, y: -5 }}
                        animate={{ opacity: 1, height: "auto", y: 0 }}
                        exit={{ opacity: 0, height: 0, y: -5 }}
                        transition={{ duration: 0.2, ease: "easeInOut" }}
                        className={`mt-1 px-2 py-1.5 rounded-lg backdrop-blur-sm ${
                          isOwnMessage ? 'text-right' : 'text-left'
                        }`}
                      >
                        <div className={`flex flex-col gap-1 text-xs text-muted-foreground ${
                          isOwnMessage ? 'justify-end' : 'justify-start'
                        }`}>
                          <div className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            <span className="font-light">
                              {formatDistanceToNow(message._creationTime, { addSuffix: true })}
                            </span>
                          </div>
                          
                          {isOwnMessage && (
                            <div className={`flex items-center ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
                              {message.isRead ? (
                                <div className="flex items-center gap-1">
                                  <CheckCheck className="w-3 h-3" />
                                  <span>Read</span>
                                </div>
                              ) : (
                                <div className="flex items-center gap-1 font-light">
                                  <span>Delivered</span>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </motion.div>
            );
          })
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <MessageCircle className="w-12 h-12 text-muted-foreground/50 mx-auto mb-3" />
              <p className="text-muted-foreground font-light">
                No messages yet. Start the conversation!
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Message Input */}
      <div className="border-t border-border bg-card">
        <div className="flex items-center space-x-2 p-3">
          <Textarea
            placeholder="Type your message..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            className="resize-none rounded-xl bg-primary/5 border-border font-light placeholder:text-muted-foreground transition-all duration-300 min-h-[44px] max-h-[120px]"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || isLoading}
            size="sm"
            className="rounded-xl bg-primary hover:bg-primary/90 transition-colors duration-300 h-[44px] px-4"
          >
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
