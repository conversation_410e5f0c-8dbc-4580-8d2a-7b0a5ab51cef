"use client";

import { ReactNode } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@repo/ui/lib/utils";
import { useAuth } from "@/hooks/useBetterAuth";
import { 
  LayoutDashboard, 
  Package, 
  Plus, 
  TrendingUp, 
  FileText, 
  BarChart3,
  Settings,
  LogOut,
  User,
  Crown
} from "lucide-react";

interface SellerLayoutProps {
  children: ReactNode;
}

const navigation = [
  { name: "Dashboard", href: "/seller/dashboard", icon: LayoutDashboard },
  { name: "Inventory", href: "/seller/products", icon: Package },
  { name: "Sales", href: "/seller/sales", icon: TrendingUp },
  // { name: "Invoices", href: "/seller/invoices", icon: FileText },
  { name: "Reports", href: "/seller/reports", icon: BarChart3 },
];

export function SellerLayout({ children }: SellerLayoutProps) {
  const pathname = usePathname();
  const { user, isLoading, isAuthenticated } = useAuth();

  // Show loading state
  if (isLoading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-16 w-16 border-b-1 border-black dark:border-white"></div>
      </div>
    );
  }

  // Redirect if not authenticated or not a seller
  if (!isAuthenticated || !user || (user.userType !== "seller" && user.userType !== "admin")) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Access Restricted</h2>
          <p className="text-neutral-600 mb-6">
            You need to be logged in as a seller to access this page.
          </p>
          <Link
            href="/login"
            className="bg-black text-white px-6 py-3 rounded-lg hover:bg-neutral-800"
          >
            Sign In
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <div className="flex">
        {/* Sidebar */}
        <div className="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0">
          <div className="flex flex-col flex-grow bg-white dark:bg-neutral-800 border-r border-neutral-200 dark:border-neutral-700">
            {/* Logo */}
            <div className="flex items-center h-16 px-6 border-b border-neutral-200 dark:border-neutral-700">
              <Link href="/seller/dashboard" className="flex items-center gap-2">
                <div className="bg-black dark:bg-white text-white dark:text-black flex size-8 items-center justify-center rounded-md">
                  <Package className="size-4" />
                </div>
                <span className="font-semibold text-lg">MODA</span>
              </Link>
            </div>

            {/* User Info */}
            <div className="px-6 py-4 border-b border-neutral-200 dark:border-neutral-700">
              <div className="flex items-center gap-3">
                <div className="bg-neutral-100 dark:bg-neutral-700 flex size-10 items-center justify-center rounded-full">
                  {user.userType === "admin" ? (
                    <Crown className="size-5 text-yellow-600" />
                  ) : (
                    <User className="size-5 text-neutral-600 dark:text-neutral-400" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-neutral-900 dark:text-neutral-100 truncate">
                    {user.name || user.email}
                  </p>
                  <div className="flex items-center gap-2">
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 capitalize">
                      {user.userType}
                    </p>
                    {user.subscriptionStatus && (
                      <span className={cn(
                        "inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium",
                        user.subscriptionStatus === "active" 
                          ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                          : user.subscriptionStatus === "trial"
                          ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                          : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                      )}>
                        {user.subscriptionStatus}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation */}
            <nav className="flex-1 px-4 py-6 space-y-1">
              {navigation.map((item) => {
                const isActive = pathname === item.href;
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={cn(
                      "flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-md transition-colors",
                      isActive
                        ? "bg-neutral-100 dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
                        : "text-neutral-600 dark:text-neutral-400 hover:bg-neutral-50 dark:hover:bg-neutral-700/50 hover:text-neutral-900 dark:hover:text-neutral-100"
                    )}
                  >
                    <item.icon className="h-5 w-5" />
                    {item.name}
                  </Link>
                );
              })}
            </nav>

            {/* Bottom actions */}
            <div className="px-4 py-4 border-t border-neutral-200 dark:border-neutral-700">
              <div className="space-y-1">
                <Link
                  href="/seller/settings"
                  className="flex items-center gap-3 px-3 py-2 text-sm font-medium text-neutral-600 dark:text-neutral-400 hover:bg-neutral-50 dark:hover:bg-neutral-700/50 hover:text-neutral-900 dark:hover:text-neutral-100 rounded-md transition-colors"
                >
                  <Settings className="h-5 w-5" />
                  Settings
                </Link>
                <button className="flex items-center gap-3 px-3 py-2 text-sm font-medium text-neutral-600 dark:text-neutral-400 hover:bg-neutral-50 dark:hover:bg-neutral-700/50 hover:text-neutral-900 dark:hover:text-neutral-100 rounded-md transition-colors w-full">
                  <LogOut className="h-5 w-5" />
                  Sign Out
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="lg:pl-64 flex flex-col flex-1">
          <main className="">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
}