"use client";

import React from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Package, User, Calendar, DollarSign, MessageSquare } from "lucide-react";
import { ReviewForm } from "./ReviewForm";
import { useAuth } from "@/hooks/useBetterAuth";

interface ReviewableOrdersProps {
  sellerId?: Id<"users">;
  productId?: Id<"products">;
}

export function ReviewableOrders({ sellerId, productId }: ReviewableOrdersProps) {
  const { user } = useAuth();
  
  const reviewableOrders = useQuery(api.sellerReviews.getReviewableOrders, {
    sellerId,
    productId,
  });

  if (!user) {
    return null;
  }

  if (reviewableOrders === undefined) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Reviewable Orders</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            <div className="h-20 bg-muted rounded"></div>
            <div className="h-20 bg-muted rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (reviewableOrders.orders.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Reviewable Orders</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <MessageSquare className="w-12 h-12 mx-auto mb-4" />
            <p className="text-lg font-medium mb-2">No orders to review</p>
            <p className="text-sm">
              {sellerId 
                ? "You don't have any delivered orders from this seller that need reviews."
                : "You don't have any delivered orders that need reviews."
              }
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Orders Ready for Review</span>
          <div className="flex space-x-2 text-sm">
            <Badge variant="secondary">
              {reviewableOrders.productReviews} Product Reviews
            </Badge>
            <Badge variant="secondary">
              {reviewableOrders.sellerReviews} Seller Reviews
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {reviewableOrders.orders.map((order: any) => (
          <Card key={order._id} className="border-l-4 border-l-primary">
            <CardContent className="pt-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <Package className="w-5 h-5 text-muted-foreground" />
                    <div>
                      <h4 className="font-medium">Order #{order.orderNumber}</h4>
                      <p className="text-sm text-muted-foreground">
                        Delivered {formatDate(order.deliveredDate || 0)}
                      </p>
                    </div>
                  </div>
                  
                  {order.product && (
                    <div className="ml-8 mb-3">
                      <p className="font-medium">{order.product.title}</p>
                      <p className="text-sm text-muted-foreground">{order.product.brand}</p>
                    </div>
                  )}
                  
                  {order.seller && (
                    <div className="ml-8 mb-3">
                      <p className="font-medium">{order.seller.businessName || order.seller.name}</p>
                      <p className="text-sm text-muted-foreground">Seller</p>
                    </div>
                  )}
                  
                  <div className="ml-8 flex items-center space-x-4 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>Ordered {formatDate(order.orderDate)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <DollarSign className="w-4 h-4" />
                      <span>{formatCurrency(order.totalAmount)}</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex flex-col items-end space-y-2">
                  <div className="flex space-x-2">
                    {order.hasProductReview && (
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        ✓ Product Reviewed
                      </Badge>
                    )}
                    {order.hasSellerReview && (
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        ✓ Seller Reviewed
                      </Badge>
                    )}
                  </div>
                  
                  <ReviewForm 
                    order={order} 
                    onReviewSubmitted={() => {
                      // The query will automatically update due to Convex reactivity
                    }} 
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </CardContent>
    </Card>
  );
}
