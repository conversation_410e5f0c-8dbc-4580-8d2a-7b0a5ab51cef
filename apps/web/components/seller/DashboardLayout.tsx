"use client"

import { ReactNode } from "react"
import { usePathname } from "next/navigation"
import { useAuth } from "@/hooks/useBetterAuth"
import Link from "next/link"
import Image from "next/image"
import { LoadingSpinner } from "@/components/common"

import { AppSidebar } from "@/components/seller/AppSidebar"
import { SellerApplicationModal } from "@/components/seller/SellerApplicationModal"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@repo/ui/components/breadcrumb"
import { Separator } from "@repo/ui/components/separator"
import { Button } from "@repo/ui/components/button"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@repo/ui/components/sidebar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar"
import { Badge } from "@repo/ui/components/badge"
import { 
  User, 
  Settings, 
  LogOut, 
  Store, 
  Bell, 
  HelpCircle,
  ChevronDown,
  Package,
  BarChart3
} from "lucide-react"

interface DashboardLayoutProps {
  children: ReactNode
}

const breadcrumbMap: Record<string, { label: string; href?: string }[]> = {
  '/seller/dashboard': [{ label: 'Dashboard' }],
  '/seller/products': [{ label: 'Products' }],
  '/seller/products/new': [
    { label: 'Products', href: '/seller/products' },
    { label: 'Add Product' }
  ],
  '/seller/sales': [{ label: 'Sales' }],
  '/seller/messages': [{ label: 'Messages' }],
  '/seller/reports': [{ label: 'Reports' }],
  '/seller/invoices': [{ label: 'Invoices' }],
  '/seller/settings': [{ label: 'Settings' }],
  '/seller/suppliers': [{ label: 'Suppliers' }],
  '/seller/suppliers/list': [
    { label: 'Suppliers', href: '/seller/suppliers' },
    { label: 'All Suppliers' }
  ],
  '/seller/suppliers/[id]': [
    { label: 'Suppliers', href: '/seller/suppliers' },
    { label: 'Supplier Details' }
  ],
  '/seller/suppliers/[id]/edit': [
    { label: 'Suppliers', href: '/seller/suppliers' },
    { label: 'Edit Supplier' }
  ],
  '/seller/suppliers/new': [
    { label: 'Suppliers', href: '/seller/suppliers' },
    { label: 'Add Supplier' }
  ],
  '/seller/offers': [{ label: 'Offers' }],
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const pathname = usePathname()
  const { user, isLoading, isAuthenticated, sellerStatus } = useAuth()

  // Show loading state
  if (isLoading || !user || (user.userType === "seller" && !sellerStatus)) {
    return (
      <LoadingSpinner 
        message={user?.userType === "seller" ? "Loading seller status..." : "Loading..."} 
        size="lg" 
        theme="primary" 
        fullScreen 
      />
    )
  }

  // Redirect if not authenticated
  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Access Restricted</h2>
          <p className="text-muted-foreground mb-6">
            You need to be logged in to access this page.
          </p>
          <Link
            href="/login"
            className="bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary/90"
          >
            Sign In
          </Link>
        </div>
      </div>
    )
  }

  // Redirect if not a seller (but allow admins)
  if (user.userType !== "seller") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="mb-6">
            <div className="mx-auto w-16 h-16 bg-amber-100 dark:bg-amber-900/20 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">Seller Access Required</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              You need to apply and be approved as a seller to access the seller portal.
            </p>
          </div>
          <div className="space-y-3">
            <SellerApplicationModal>
              <Button className="w-full bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors block text-center">
                Apply to Become a Seller
              </Button>
            </SellerApplicationModal>
            <Link
              href="/dashboard"
              className="w-full bg-secondary text-secondary-foreground px-6 py-3 rounded-lg hover:bg-secondary/80 transition-colors block text-center"
            >
              Go to Consumer Dashboard
            </Link>
          </div>
        </div>
      </div>
    )
  }

  // Check if seller has approved profile (but allow admins)
  if (user.userType === "seller" && sellerStatus && !sellerStatus.canSell) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="mb-6">
            <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">Seller Application Pending</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {sellerStatus.verificationStatus === "pending" 
                ? "Your seller application is currently under review. We'll notify you once it's approved."
                : sellerStatus.verificationStatus === "rejected"
                ? "Your seller application was not approved. Please contact support for more information."
                : "Your seller profile needs to be verified before you can access the seller portal."
              }
            </p>
          </div>
          <div className="space-y-3">
            {sellerStatus.verificationStatus === "rejected" && (
              <SellerApplicationModal>
                <Button className="w-full bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors block text-center">
                  Reapply as Seller
                </Button>
              </SellerApplicationModal>
            )}
            <Link
              href="/dashboard"
              className="w-full bg-secondary text-secondary-foreground px-6 py-3 rounded-lg hover:bg-secondary/80 transition-colors block text-center"
            >
              Go to Consumer Dashboard
            </Link>
          </div>
        </div>
      </div>
    )
  }

  // Handle dynamic breadcrumbs for products with status filters
  const getBreadcrumbs = () => {
    // For now, use static breadcrumbs to avoid useSearchParams issues
    // TODO: Implement dynamic breadcrumbs with proper Suspense boundary if needed
    return breadcrumbMap[pathname] || [{ label: 'Dashboard' }]
  }
  
  const breadcrumbs = getBreadcrumbs()

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset className="max-h-[calc(100vh-16px)] flex flex-col overflow-hidden">
        <header className="flex h-16 shrink-0 items-center gap-2 border-b border-border">
          <div className="flex items-center gap-2 px-4 flex-1">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/seller/dashboard">
                    Seller Portal
                  </BreadcrumbLink>
                </BreadcrumbItem>
                {breadcrumbs.length > 1 && (
                  <BreadcrumbSeparator className="hidden md:block" />
                )}
                {breadcrumbs.map((crumb, index) => (
                  <div key={index} className="flex items-center">
                    {index > 0 && <BreadcrumbSeparator className="hidden md:block" />}
                    <BreadcrumbItem>
                      {crumb.href ? (
                        <BreadcrumbLink href={crumb.href}>
                          {crumb.label}
                        </BreadcrumbLink>
                      ) : (
                        <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
                      )}
                    </BreadcrumbItem>
                  </div>
                ))}
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div className="flex-1 overflow-auto">
          {children}
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
