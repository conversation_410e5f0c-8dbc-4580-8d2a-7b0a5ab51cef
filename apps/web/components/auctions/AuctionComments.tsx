"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { toast } from "sonner";
import { But<PERSON> } from "@repo/ui/components/button";
import { Textarea } from "@repo/ui/components/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar";
import { Badge } from "@repo/ui/components/badge";
import { 
  MessageCircle, 
  Heart, 
  Reply, 
  MoreHorizontal, 
  Edit, 
  Trash, 
  Flag,
  Send,
  User
} from "lucide-react";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { useAuth } from "@/hooks/useBetterAuth";
import { format } from "date-fns";
import { cn } from "@repo/ui/lib/utils";

interface AuctionCommentsProps {
  auctionId: string;
}

interface CommentComponentProps {
  comment: any;
  onReply: (parentId: string) => void;
  onEdit: (commentId: string, content: string) => void;
  onDelete: (commentId: string) => void;
  onLike: (commentId: string) => void;
  onReport: (commentId: string) => void;
  currentUserId?: string;
}

function CommentComponent({ 
  comment, 
  onReply, 
  onEdit, 
  onDelete, 
  onLike, 
  onReport, 
  currentUserId 
}: CommentComponentProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(comment.content);
  const [isReplying, setIsReplying] = useState(false);
  const [replyContent, setReplyContent] = useState("");

  const handleSaveEdit = () => {
    if (editContent.trim()) {
      onEdit(comment._id, editContent.trim());
      setIsEditing(false);
    }
  };

  const handleSubmitReply = () => {
    if (replyContent.trim()) {
      onReply(comment._id);
      setReplyContent("");
      setIsReplying(false);
    }
  };

  const isOwnComment = currentUserId === comment.user._id;

  return (
    <div className="space-y-4">
      <div className="flex space-x-3">
        <Avatar className="w-8 h-8 flex-shrink-0">
          <AvatarImage src={comment.user.avatar} />
          <AvatarFallback>
            <User className="w-4 h-4" />
          </AvatarFallback>
        </Avatar>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <span className="font-medium text-sm">{comment.user.name}</span>
            {comment.user.isVerified && (
              <Badge variant="secondary" className="text-xs px-1 py-0">
                Verified
              </Badge>
            )}
            <span className="text-xs text-muted-foreground">
              {format(new Date(comment._creationTime), "MMM dd, h:mm a")}
            </span>
            {comment.isEdited && (
              <span className="text-xs text-muted-foreground">(edited)</span>
            )}
          </div>
          
          {isEditing ? (
            <div className="space-y-2">
              <Textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                className="min-h-[60px] text-sm"
                placeholder="Edit your comment..."
                maxLength={1000}
              />
              <div className="flex space-x-2">
                <Button size="sm" onClick={handleSaveEdit}>
                  Save
                </Button>
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => {
                    setIsEditing(false);
                    setEditContent(comment.content);
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <>
              <p className="text-sm text-foreground mb-2 whitespace-pre-wrap">
                {comment.content}
              </p>
              
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 text-muted-foreground hover:text-red-500"
                  onClick={() => onLike(comment._id)}
                >
                  <Heart className="w-4 h-4 mr-1" />
                  {comment.likes > 0 && <span className="text-xs">{comment.likes}</span>}
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 text-muted-foreground hover:text-foreground"
                  onClick={() => setIsReplying(!isReplying)}
                >
                  <Reply className="w-4 h-4 mr-1" />
                  <span className="text-xs">Reply</span>
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 text-muted-foreground hover:text-foreground"
                    >
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {isOwnComment && (
                      <>
                        <DropdownMenuItem onClick={() => setIsEditing(true)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => onDelete(comment._id)}
                          className="text-destructive"
                        >
                          <Trash className="w-4 h-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                      </>
                    )}
                    <DropdownMenuItem onClick={() => onReport(comment._id)}>
                      <Flag className="w-4 h-4 mr-2" />
                      Report
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </>
          )}

          {isReplying && (
            <div className="mt-3 space-y-2">
              <Textarea
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                className="min-h-[60px] text-sm"
                placeholder="Write a reply..."
                maxLength={1000}
              />
              <div className="flex space-x-2">
                <Button size="sm" onClick={handleSubmitReply}>
                  <Send className="w-4 h-4 mr-1" />
                  Reply
                </Button>
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => {
                    setIsReplying(false);
                    setReplyContent("");
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}

          {/* Replies */}
          {comment.replies && comment.replies.length > 0 && (
            <div className="mt-4 space-y-3 border-l-2 border-muted pl-4">
              {comment.replies.map((reply: any) => (
                <CommentComponent
                  key={reply._id}
                  comment={reply}
                  onReply={onReply}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onLike={onLike}
                  onReport={onReport}
                  currentUserId={currentUserId}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export function AuctionComments({ auctionId }: AuctionCommentsProps) {
  const { user } = useAuth();
  const [newComment, setNewComment] = useState("");
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState("");

  // Fetch comments
  const comments = useQuery(api.auctionComments.getAuctionComments, {
    auctionId: auctionId as Id<"auctions">,
  });

  // Mutations
  const addComment = useMutation(api.auctionComments.addAuctionComment);
  const editComment = useMutation(api.auctionComments.editAuctionComment);
  const deleteComment = useMutation(api.auctionComments.deleteAuctionComment);
  const toggleLike = useMutation(api.auctionComments.toggleCommentLike);
  const reportComment = useMutation(api.auctionComments.reportAuctionComment);

  const handleSubmitComment = async () => {
    if (!user) {
      toast.error("Please log in to comment");
      return;
    }

    if (!newComment.trim()) {
      toast.error("Comment cannot be empty");
      return;
    }

    try {
      await addComment({
        auctionId: auctionId as Id<"auctions">,
        content: newComment.trim(),
      });
      setNewComment("");
      toast.success("Comment added successfully");
    } catch (error: any) {
      toast.error(error.message || "Failed to add comment");
    }
  };

  const handleReply = async (parentId: string) => {
    if (!user) {
      toast.error("Please log in to reply");
      return;
    }

    if (!replyContent.trim()) {
      toast.error("Reply cannot be empty");
      return;
    }

    try {
      await addComment({
        auctionId: auctionId as Id<"auctions">,
        content: replyContent.trim(),
        parentId: parentId as Id<"auctionComments">,
      });
      setReplyContent("");
      setReplyingTo(null);
      toast.success("Reply added successfully");
    } catch (error: any) {
      toast.error(error.message || "Failed to add reply");
    }
  };

  const handleEdit = async (commentId: string, content: string) => {
    try {
      await editComment({
        commentId: commentId as Id<"auctionComments">,
        content,
      });
      toast.success("Comment updated successfully");
    } catch (error: any) {
      toast.error(error.message || "Failed to update comment");
    }
  };

  const handleDelete = async (commentId: string) => {
    if (!confirm("Are you sure you want to delete this comment?")) return;

    try {
      await deleteComment({
        commentId: commentId as Id<"auctionComments">,
      });
      toast.success("Comment deleted successfully");
    } catch (error: any) {
      toast.error(error.message || "Failed to delete comment");
    }
  };

  const handleLike = async (commentId: string) => {
    if (!user) {
      toast.error("Please log in to like comments");
      return;
    }

    try {
      await toggleLike({
        commentId: commentId as Id<"auctionComments">,
      });
    } catch (error: any) {
      toast.error(error.message || "Failed to update like");
    }
  };

  const handleReport = async (commentId: string) => {
    if (!user) {
      toast.error("Please log in to report comments");
      return;
    }

    const reason = prompt("Please provide a reason for reporting this comment:");
    if (!reason) return;

    try {
      await reportComment({
        commentId: commentId as Id<"auctionComments">,
        reason,
      });
      toast.success("Comment reported successfully");
    } catch (error: any) {
      toast.error(error.message || "Failed to report comment");
    }
  };

  const commentCount = comments?.length || 0;
  const totalReplies = comments?.reduce((sum, comment) => sum + (comment.replies?.length || 0), 0) || 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <MessageCircle className="w-5 h-5 text-muted-foreground" />
        <h3 className="text-lg font-medium">
          Comments ({commentCount + totalReplies})
        </h3>
      </div>

      {/* Add new comment */}
      {user ? (
        <div className="space-y-3">
          <div className="flex space-x-3">
            <Avatar className="w-8 h-8 flex-shrink-0">
              <AvatarImage src={user.profileImage} />
              <AvatarFallback>
                <User className="w-4 h-4" />
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <Textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Share your thoughts about this auction..."
                className="min-h-[80px]"
                maxLength={1000}
              />
              <div className="flex justify-between items-center mt-2">
                <span className="text-xs text-muted-foreground">
                  {newComment.length}/1000 characters
                </span>
                <Button
                  onClick={handleSubmitComment}
                  disabled={!newComment.trim()}
                  size="sm"
                >
                  <Send className="w-4 h-4 mr-1" />
                  Comment
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-8 bg-muted/30 rounded-lg">
          <MessageCircle className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
          <p className="text-muted-foreground mb-4">
            Sign in to join the conversation about this auction
          </p>
          <Button variant="outline" size="sm">
            Sign In
          </Button>
        </div>
      )}

      {/* Comments list */}
      <div className="space-y-6">
        {comments && comments.length > 0 ? (
          comments.map((comment) => (
            <CommentComponent
              key={comment._id}
              comment={comment}
              onReply={(parentId) => {
                setReplyingTo(parentId);
                // Focus on the reply input for that comment
              }}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onLike={handleLike}
              onReport={handleReport}
              currentUserId={user?._id}
            />
          ))
        ) : (
          <div className="text-center py-12">
            <MessageCircle className="w-16 h-16 text-muted-foreground mx-auto mb-4 opacity-50" />
            <h4 className="text-lg font-medium text-muted-foreground mb-2">
              No comments yet
            </h4>
            <p className="text-muted-foreground">
              Be the first to share your thoughts about this auction!
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
