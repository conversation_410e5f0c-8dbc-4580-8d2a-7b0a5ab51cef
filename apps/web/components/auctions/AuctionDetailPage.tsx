"use client";

import { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { toast } from "sonner";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import { Separator } from "@repo/ui/components/separator";
import { Checkbox } from "@repo/ui/components/checkbox";
import { 
  Gavel, 
  Heart, 
  Eye, 
  Clock, 
  Users, 
  ArrowLeft,
  TrendingUp,
  Shield,
  AlertCircle,
  CheckCircle,
  DollarSign,
  Share2,
  Star
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useBetterAuth";
import Image from "next/image";
import { format } from "date-fns";
import Link from "next/link";
import { ProductLayout } from "@/components/layouts/ProductLayout";
import { ProductImageGallery } from "@/components/product/ProductImageGallery";
import { AuctionComments } from "./AuctionComments";

interface AuctionDetailPageProps {
  auctionId: string;
}

export function AuctionDetailPage({ auctionId }: AuctionDetailPageProps) {
  const router = useRouter();
  const { user } = useAuth();
  const [bidAmount, setBidAmount] = useState("");
  const [maxBidAmount, setMaxBidAmount] = useState("");
  const [isProxyBidding, setIsProxyBidding] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [timeLeft, setTimeLeft] = useState(0);

  // Fetch auction details - this will update in real-time
  const auctionData = useQuery(api.auctions.getAuctionDetails, {
    auctionId: auctionId as Id<"auctions">,
  });

  // Mutations
  const placeBid = useMutation(api.auctions.placeBid);
  const toggleWatch = useMutation(api.auctions.toggleAuctionWatch);

  const auction = auctionData?.auction;
  const bids = auctionData?.bids || [];
  const userBids = auctionData?.userBids || [];
  const isWatching = auctionData?.isWatching || false;
  const canBid = auctionData?.canBid || false;

  // Debug logging (remove in production)
  if (process.env.NODE_ENV === 'development') {
    console.log("Auction Debug:", {
      status: auction?.status,
      timeRemaining: auction?.timeRemaining,
      canBid,
      isActive: auction?.status === "active",
      userSubscription: user?.subscriptionStatus,
      hasUser: !!user
    });
  }

  // Set default bid amount to next minimum bid
  useEffect(() => {
    if (auction && !bidAmount) {
      setBidAmount(auction.nextMinBid.toString());
    }
  }, [auction, bidAmount]);

  // Enhanced real-time countdown with high-precision updates
  useEffect(() => {
    if (!auction || auction.status === "ended" || auction.status === "sold") {
      setTimeLeft(0);
      return;
    }

    const updateTimeLeft = () => {
      const now = Date.now();
      let remaining: number;
      
      if (auction.status === "scheduled") {
        remaining = Math.max(0, auction.startTime - now);
      } else if (auction.status === "active") {
        remaining = Math.max(0, auction.endTime - now);
      } else {
        remaining = 0;
      }
      
      setTimeLeft(remaining);
    };

    // Update immediately
    updateTimeLeft();

    // Set up interval with more frequent updates for ending soon auctions
    const getUpdateInterval = () => {
      const now = Date.now();
      let remaining: number;
      
      if (auction.status === "scheduled") {
        remaining = Math.max(0, auction.startTime - now);
      } else if (auction.status === "active") {
        remaining = Math.max(0, auction.endTime - now);
      } else {
        remaining = 0;
      }
      
      // Update every 100ms if less than 1 hour remaining, otherwise every second
      return remaining < 60 * 60 * 1000 ? 100 : 1000;
    };

    const timer = setInterval(updateTimeLeft, getUpdateInterval());

    return () => clearInterval(timer);
  }, [auction?.startTime, auction?.endTime, auction?.status]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatTimeRemaining = (ms: number) => {
    if (ms <= 0) return "Auction Ended";
    
    const days = Math.floor(ms / (1000 * 60 * 60 * 24));
    const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((ms % (1000 * 60)) / 1000);

    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const getTimeColor = (timeRemaining: number, status: string) => {
    if (timeRemaining <= 0 || status === "ended" || status === "sold") return "text-muted-foreground";
    if (status === "scheduled") return "text-blue-600"; // Scheduled auctions
    if (status === "active") {
      if (timeRemaining < 60 * 60 * 1000) return "text-destructive"; // Less than 1 hour
      if (timeRemaining < 24 * 60 * 60 * 1000) return "text-orange-600"; // Less than 1 day
      return "text-green-600";
    }
    return "text-muted-foreground";
  };

  const getTimeLabel = (status: string) => {
    if (status === "scheduled") return "Starts in";
    if (status === "active") return "Time remaining";
    if (status === "ended") return "Auction ended";
    if (status === "sold") return "Sold";
    return "Time remaining";
  };

  const handlePlaceBid = async () => {
    if (!auction || !user) return;
    
    const amount = parseFloat(bidAmount);
    const maxAmount = isProxyBidding ? parseFloat(maxBidAmount) : undefined;
    
    if (amount < auction.nextMinBid) {
      toast.error(`Minimum bid is ${formatCurrency(auction.nextMinBid)}`);
      return;
    }

    if (isProxyBidding && maxAmount && maxAmount < amount) {
      toast.error("Maximum bid must be greater than or equal to bid amount");
      return;
    }

    try {
      const result = await placeBid({
        auctionId: auctionId as Id<"auctions">,
        amount,
        maxAmount,
      });

      if (result.success) {
        toast.success(result.message);
        setBidAmount(result.newCurrentBid + auction.bidIncrement + "");
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to place bid");
    }
  };

  const handleToggleWatch = async () => {
    try {
      await toggleWatch({
        auctionId: auctionId as Id<"auctions">,
        watch: !isWatching,
      });
      toast.success(isWatching ? "Removed from watchlist" : "Added to watchlist");
    } catch (error) {
      toast.error("Failed to update watchlist");
    }
  };

  const handleShare = () => {
    // TODO: Implement share functionality
    toast.success("Share link copied to clipboard!");
  };

  const handleFavorite = async () => {
    await handleToggleWatch();
  };

  // Helper function to capitalize and format tags/categories
  const formatTag = (tag: string) => {
    // List of inappropriate/unwanted tags to filter out
    const inappropriateTags = ['nsfw', 'adult', 'explicit', 'inappropriate', 'banned'];
    
    if (inappropriateTags.includes(tag.toLowerCase())) {
      return null; // Filter out inappropriate tags
    }
    
    // Capitalize first letter of each word
    return tag
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  if (!auction) {
    return (
      <ProductLayout>
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <Gavel className="w-8 h-8 text-muted-foreground mx-auto mb-2 animate-pulse" />
            <p className="text-muted-foreground text-sm">Loading auction details...</p>
          </div>
        </div>
      </ProductLayout>
    );
  }

  return (
    <ProductLayout>
      <div className="container mx-auto px-4 py-6">
        {/* Breadcrumb */}
        <div className="flex items-center space-x-2 text-xs text-muted-foreground mb-4">
          <Link 
            href="/auctions" 
            className="hover:text-foreground transition-colors font-light"
          >
            Auctions
          </Link>
          <span className="text-muted-foreground/50">/</span>
          <span className="capitalize hover:text-foreground transition-colors cursor-pointer font-light">
            {formatTag(auction.category)}
          </span>
          <span className="text-muted-foreground/50">/</span>
          <span className="text-foreground font-medium truncate text-xs">
            {auction.title}
          </span>
        </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 xl:grid-cols-12 gap-6">
        {/* Left Column - Image Gallery */}
        <div className="xl:col-span-7">
          <ProductImageGallery 
            images={auction.images} 
            title={auction.title} 
          />
        </div>

        {/* Right Column - Auction Info and Bidding */}
        <div className="xl:col-span-5">
          {/* Auction Information - Non-sticky */}
          <div className="bg-card border-0 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-300 mb-4">
            {/* Header */}
            <div className="mb-3">
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1">
                  <h1 className="text-xl font-light text-primary tracking-tight mb-1">
                    {auction.title}
                  </h1>
                  <p className="text-sm text-muted-foreground font-light mb-2">
                    {auction.brand}
                  </p>
                </div>
                <div className="flex items-center space-x-1 ml-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleFavorite}
                    className={`h-8 w-8 rounded-full transition-colors duration-300 ${
                      isWatching 
                        ? 'text-red-500 bg-red-50 dark:bg-red-950' 
                        : 'text-muted-foreground hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-950'
                    }`}
                  >
                    <Heart className={`w-4 h-4 ${isWatching ? 'fill-current' : ''}`} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={handleShare}
                    className="h-8 w-8 rounded-full text-muted-foreground hover:text-foreground hover:bg-muted transition-colors duration-300"
                  >
                    <Share2 className="w-5 h-5" />
                  </Button>
                </div>
              </div>

              {/* Current Bid / Final Price */}
              <div className="mb-3">
                <p className="text-xs text-muted-foreground font-light mb-1">
                  {auction.status === "scheduled" 
                    ? "Starting Bid" 
                    : auction.status === "sold" 
                      ? "Sold for" 
                      : auction.status === "ended"
                        ? "Final Bid"
                        : "Current Bid"
                  }
                </p>
                <div className="flex items-baseline space-x-2">
                  <span className={`text-2xl font-light ${
                    auction.status === "sold" 
                      ? "text-green-600" 
                      : auction.status === "ended"
                        ? "text-orange-600"
                        : "text-foreground"
                  }`}>
                    {formatCurrency(auction.winningBid || auction.currentBid)}
                  </span>
                  {auction.status === "sold" && (
                    <div className="flex items-center gap-1 text-green-600">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-xs font-medium">SOLD</span>
                    </div>
                  )}
                  {auction.status === "ended" && (
                    <div className="flex items-center gap-1 text-orange-600">
                      <Clock className="w-4 h-4" />
                      <span className="text-xs font-medium">ENDED</span>
                    </div>
                  )}
                  {auction.currentBid > auction.startingBid && auction.status !== "scheduled" && auction.status !== "sold" && auction.status !== "ended" && (
                    <span className="text-sm text-muted-foreground line-through font-light">
                      {formatCurrency(auction.startingBid)}
                    </span>
                  )}
                </div>
              </div>

              {/* Status and Time */}
              <div className="flex items-center space-x-2 mb-3">
                {auction.status === "active" ? (
                  <Badge className="bg-green-100 text-green-800 border-green-200 font-medium text-xs">
                    Live Auction
                  </Badge>
                ) : auction.status === "sold" ? (
                  <Badge className="bg-purple-100 text-purple-800 border-purple-200 font-medium text-xs">
                    Sold
                  </Badge>
                ) : auction.status === "scheduled" ? (
                  <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 font-medium text-xs">
                    Scheduled
                  </Badge>
                ) : (
                  <Badge className="bg-gray-100 text-gray-800 border-gray-200 font-medium text-xs">
                    Auction Ended
                  </Badge>
                )}
                
                <Badge variant="outline" className="capitalize text-xs">
                  {formatTag(auction.condition) || auction.condition.replace('_', ' ')}
                </Badge>
              </div>
            </div>

            {/* Time Remaining */}
            <div className="space-y-2 mb-3">
              <div className="flex justify-between py-1 border-b border-border">
                <span className="text-xs text-muted-foreground font-light">{getTimeLabel(auction.status)}</span>
                <span className={`text-xs font-medium ${getTimeColor(timeLeft || auction.timeRemaining, auction.status)} ${
                  timeLeft < 60 * 60 * 1000 && auction.status === "active" ? "animate-pulse" : ""
                }`}>
                  {formatTimeRemaining(timeLeft || auction.timeRemaining)}
                </span>
              </div>
              <div className="flex justify-between py-1 border-b border-border">
                <span className="text-xs text-muted-foreground font-light">Total Bids</span>
                <span className="text-xs font-medium text-foreground">{auction.totalBids}</span>
              </div>
              <div className="flex justify-between py-1 border-b border-border">
                <span className="text-xs text-muted-foreground font-light">Unique Bidders</span>
                <span className="text-xs font-medium text-foreground">{auction.uniqueBidders}</span>
              </div>
              <div className="flex justify-between py-1 border-b border-border">
                <span className="text-xs text-muted-foreground font-light">Watchers</span>
                <span className="text-xs font-medium text-foreground">{auction.watchers}</span>
              </div>
            </div>

            {/* Description */}
            <div className="mb-3">
              <h3 className="font-medium text-foreground text-sm mb-1">Description</h3>
              <p className="text-muted-foreground font-light leading-relaxed text-xs">
                {auction.description}
              </p>
            </div>

            {/* Reserve Price Warning */}
            {auction.reservePrice && (
              <div className="mb-3 bg-amber-50 border border-amber-200 rounded-lg p-2">
                <div className="flex items-center gap-1">
                  <AlertCircle className="w-3 h-3 text-amber-600" />
                  <span className="font-medium text-amber-800 text-xs">Reserve Price</span>
                </div>
                <p className="text-amber-700 text-xs mt-1">
                  This item has a reserve price of {formatCurrency(auction.reservePrice)}.
                  {auction.reserveMet ? (
                    <span className="text-green-700 font-medium"> Reserve met!</span>
                  ) : (
                    " The reserve has not been met yet."
                  )}
                </p>
              </div>
            )}

            {/* Stats */}
            <div className="flex items-center space-x-4 text-xs text-muted-foreground font-light">
              <div className="flex items-center space-x-1">
                <Eye className="w-3 h-3" />
                <span>{auction.views} views</span>
              </div>
              <div className="flex items-center space-x-1">
                <Heart className="w-3 h-3" />
                <span>{auction.watchers} watching</span>
              </div>
            </div>
          </div>

            {/* Bidding Actions */}
            {auction.status === "active" && canBid && (
              <div className="bg-card border-0 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-300 mb-4">
                <div className="space-y-3">
                  <div>
                    <h3 className="text-sm font-medium text-foreground mb-2">Place Your Bid</h3>
                    <div className="space-y-2">
                      <div>
                        <label className="text-xs font-medium text-muted-foreground">
                          Bid Amount (min: {formatCurrency(auction.nextMinBid)})
                        </label>
                        <Input
                          type="number"
                          step="0.01"
                          value={bidAmount}
                          onChange={(e) => setBidAmount(e.target.value)}
                          placeholder={auction.nextMinBid.toString()}
                          className="mt-1 h-8 text-sm"
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="proxy-bid"
                          checked={isProxyBidding}
                          onCheckedChange={(checked) => setIsProxyBidding(checked === true)}
                        />
                        <label 
                          htmlFor="proxy-bid" 
                          className="text-xs text-muted-foreground cursor-pointer"
                        >
                          Enable proxy bidding
                        </label>
                      </div>

                      {isProxyBidding && (
                        <div>
                          <label className="text-xs font-medium text-muted-foreground">
                            Maximum Bid Amount
                          </label>
                          <Input
                            type="number"
                            step="0.01"
                            value={maxBidAmount}
                            onChange={(e) => setMaxBidAmount(e.target.value)}
                            placeholder="Enter your maximum bid"
                            className="mt-1 h-8 text-xs"
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            We'll automatically bid up to this amount for you
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  <Button
                    onClick={handlePlaceBid}
                    className="w-full h-8 text-sm font-medium"
                    disabled={!bidAmount || parseFloat(bidAmount) < auction.nextMinBid}
                  >
                    <Gavel className="w-4 h-4 mr-1" />
                    Place Bid - {formatCurrency(parseFloat(bidAmount) || 0)}
                  </Button>

                  <p className="text-xs text-muted-foreground text-center">
                    By bidding, you agree to our terms and conditions
                  </p>
                </div>
              </div>
            )}

            {/* Your Bids */}
            {userBids.length > 0 && (
              <div className="bg-card border-0 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-300 mb-4">
                <h3 className="text-sm font-medium text-foreground mb-2">Your Bids</h3>
                <div className="space-y-2">
                  {userBids.slice(0, 3).map((bid) => (
                    <div
                      key={bid._id}
                      className={`flex justify-between items-center p-2 rounded-lg text-xs ${
                        bid.isWinning ? "bg-green-50 text-green-800" : 
                        bid.isOutbid ? "bg-red-50 text-red-800" : "bg-muted/50"
                      }`}
                    >
                      <span className="font-medium">
                        {formatCurrency(bid.amount)}
                      </span>
                      <span className="text-xs font-medium">
                        {bid.isWinning ? "Winning" : bid.isOutbid ? "Outbid" : "Active"}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Bid History Section - Below right column */}
            <div className="bg-card border-0 rounded-lg p-4 shadow-sm">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-foreground">Bid History ({auction.totalBids})</h3>
                <Badge variant="outline" className="text-xs">
                  {bids.length > 0 ? `${bids.length} bids` : "No bids yet"}
                </Badge>
              </div>
              
              {bids.length > 0 ? (
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {bids.slice(0, 8).map((bid, index) => (
                    <div
                      key={bid._id}
                      className={`flex items-center justify-between p-2 rounded-lg text-xs transition-colors ${
                        bid.isWinning ? "bg-green-50 border border-green-200" : "bg-muted/30 hover:bg-muted/50"
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                          bid.isWinning ? "bg-green-100 text-green-700" : "bg-muted text-muted-foreground"
                        }`}>
                          #{index + 1}
                        </div>
                        <div>
                          <div className="font-medium text-foreground text-xs">
                            {formatCurrency(bid.amount)}
                          </div>
                          <div className="text-xs text-muted-foreground font-light">
                            by {bid.bidder.name}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-muted-foreground font-light">
                          {format(new Date(bid.timestamp), "MMM dd, h:mm a")}
                        </div>
                        {bid.isWinning && (
                          <Badge className="bg-green-100 text-green-800 text-xs mt-1">
                            Winning
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <div className="w-8 h-8 bg-muted rounded-lg mx-auto flex items-center justify-center mb-2">
                    <Gavel className="w-4 h-4 opacity-50" />
                  </div>
                  <p className="text-sm font-light">No bids yet</p>
                  <p className="text-xs">Be the first to place a bid on this auction!</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Comments Section - Compact */}
        <div className="xl:col-span-12 mt-4">
          <div className="bg-card border-0 rounded-lg p-4 shadow-sm">
            <AuctionComments auctionId={auctionId} />
          </div>
        </div>
      </div>
    </ProductLayout>
  );
}
