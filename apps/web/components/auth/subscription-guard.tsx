"use client";

import React, { useEffect, useState } from "react";
import { authClient } from "@repo/backend/better-auth/client";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Lock, CreditCard, Check, Clock, AlertTriangle } from "lucide-react";
import { toast } from "sonner";

interface SubscriptionGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

type SubscriptionStatus = "loading" | "active" | "trial" | "expired" | "inactive";

export function SubscriptionGuard({ children, fallback }: SubscriptionGuardProps) {
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus>("loading");
  const [user, setUser] = useState<any>(null);
  const [trialDaysLeft, setTrialDaysLeft] = useState<number>(0);

  useEffect(() => {
    const checkSubscription = async () => {
      try {
        const session = await authClient.getSession();
        if (session.data?.user) {
          setUser(session.data.user);

          // For now, simulate subscription checking since the user object structure
          // might not have all the fields we need yet
          const userData = session.data.user as any;
          const now = Date.now();

          // Check if user has subscription fields
          if (userData.subscriptionStatus && userData.subscriptionExpiresAt) {
            const expiresAt = userData.subscriptionExpiresAt;

            if (userData.subscriptionStatus === "active" && expiresAt > now) {
              setSubscriptionStatus("active");
            } else if (userData.subscriptionStatus === "trial" && expiresAt > now) {
              setSubscriptionStatus("trial");
              const daysLeft = Math.ceil((expiresAt - now) / (1000 * 60 * 60 * 24));
              setTrialDaysLeft(daysLeft);
            } else if (expiresAt <= now) {
              setSubscriptionStatus("expired");
            } else {
              setSubscriptionStatus("inactive");
            }
          } else {
            // Default to trial for new users (simulate 30-day trial)
            setSubscriptionStatus("trial");
            setTrialDaysLeft(30);
          }
        } else {
          setSubscriptionStatus("inactive");
        }
      } catch {
        setSubscriptionStatus("inactive");
      }
    };

    checkSubscription();
  }, []);

  if (subscriptionStatus === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
      </div>
    );
  }

  // Allow access for active subscriptions and trials
  if (subscriptionStatus === "active" || subscriptionStatus === "trial") {
    return (
      <>
        {subscriptionStatus === "trial" && trialDaysLeft <= 7 && (
          <TrialWarning daysLeft={trialDaysLeft} />
        )}
        {children}
      </>
    );
  }

  // Block access for inactive or expired subscriptions
  return fallback || <SubscriptionRequired user={user} subscriptionStatus={subscriptionStatus} />;
}

function TrialWarning({ daysLeft }: { daysLeft: number }) {
  return (
    <div className="bg-yellow-900/20 border-b border-yellow-800 p-3">
      <div className="container mx-auto flex items-center justify-between">
        <div className="flex items-center gap-2 text-yellow-300">
          <Clock className="w-4 h-4" />
          <span className="text-sm">
            Trial expires in {daysLeft} day{daysLeft !== 1 ? 's' : ''}
          </span>
        </div>
        <Button size="sm" variant="outline" className="border-yellow-600 text-yellow-300 hover:bg-yellow-900/30">
          Upgrade Now
        </Button>
      </div>
    </div>
  );
}

function SubscriptionRequired({ user, subscriptionStatus }: { user: any; subscriptionStatus: SubscriptionStatus }) {
  const handleSubscribe = async () => {
    // Placeholder for payment integration
    toast.success("Redirecting to payment...", {
      description: "Payment integration coming soon!",
    });
  };

  const isExpired = subscriptionStatus === "expired";
  const icon = isExpired ? AlertTriangle : Lock;
  const title = isExpired ? "Subscription Expired" : "Subscription Required";
  const description = isExpired
    ? "Your subscription has expired. Renew now to continue accessing MODA's premium marketplace."
    : "Access to MODA's premium marketplace requires an active subscription.";

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-zinc-900 border-zinc-800">
        <CardHeader className="text-center">
          <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
            isExpired ? "bg-red-900/30" : "bg-zinc-800"
          }`}>
            {React.createElement(icon, {
              className: `w-8 h-8 ${isExpired ? "text-red-400" : "text-zinc-300"}`
            })}
          </div>
          <CardTitle className="text-xl text-white">{title}</CardTitle>
          <CardDescription className="text-zinc-400">
            {description}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center">
            <div className="mb-4">
              <span className="text-4xl font-light text-white">$20</span>
              <span className="text-zinc-400">/month</span>
            </div>
            <Badge className="bg-zinc-800 text-zinc-300">Premium Access</Badge>
          </div>

          <ul className="space-y-3">
            {[
              "Unlimited access to luxury listings",
              "Authenticated goods only",
              "Verified seller network",
              "Buyer protection guarantee",
              "White-glove customer service"
            ].map((feature, index) => (
              <li key={index} className="flex items-center text-sm text-zinc-300">
                <Check className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                {feature}
              </li>
            ))}
          </ul>

          <Button 
            onClick={handleSubscribe}
            className="w-full bg-white text-black hover:bg-zinc-200 font-medium"
          >
            <CreditCard className="w-4 h-4 mr-2" />
            Subscribe Now
          </Button>

          <p className="text-xs text-zinc-500 text-center">
            Cancel anytime. No hidden fees.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}