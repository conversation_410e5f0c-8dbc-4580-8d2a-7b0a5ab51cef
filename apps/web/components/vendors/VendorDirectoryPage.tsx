"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@repo/ui/components/dialog";
import { 
  Star,
  MapPin,
  Calendar,
  Users,
  Globe,
  Mail,
  Phone,
  Award,
  Briefcase,
  Search,
  ExternalLink,
  Building,
  Filter,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const categoryLabels = {
  legal: "Legal Services",
  marketing: "Marketing & PR",
  design: "Design & Creative",
  automotive: "Automotive",
  cleaning: "Cleaning Services",
  consulting: "Consulting",
  technology: "Technology",
  other: "Other",
};

export function VendorDirectoryPage() {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedVendor, setSelectedVendor] = useState<any>(null);
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);

  // Queries
  const vendors = useQuery(api.vendors.getApprovedVendors, {
    category: selectedCategory === "all" ? undefined : selectedCategory as any,
    featured: showFeaturedOnly ? true : undefined,
  });

  const featuredVendors = useQuery(api.vendors.getApprovedVendors, {
    featured: true,
  });

  const filteredVendors = vendors?.filter(vendor => 
    vendor.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.services.some(service => 
      service.toLowerCase().includes(searchTerm.toLowerCase())
    )
  ) || [];

  const handleContactVendor = (vendor: any) => {
    if (vendor.contactEmail) {
      window.open(`mailto:${vendor.contactEmail}`, '_blank');
    } else if (vendor.website) {
      window.open(vendor.website, '_blank');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-light to-white">
      {/* Hero Section */}
      <div className="bg-primary-dark text-primary-light">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-light mb-6">
              Vendor Directory
            </h1>
            <p className="text-xl text-primary-light/80 mb-8">
              Discover trusted service providers and business partners in the luxury marketplace ecosystem
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search vendors, services, or specialties..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-white"
                />
              </div>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-full sm:w-[200px] bg-white">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {Object.entries(categoryLabels).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Featured Vendors Section */}
        {featuredVendors && featuredVendors.length > 0 && (
          <section className="mb-12">
            <div className="flex items-center gap-2 mb-6">
              <Star className="h-5 w-5 text-yellow-500 fill-current" />
              <h2 className="text-2xl font-semibold">Featured Vendors</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredVendors.slice(0, 3).map((vendor) => (
                <VendorCard 
                  key={vendor._id} 
                  vendor={vendor} 
                  isFeatured={true}
                  onSelect={setSelectedVendor}
                  onContact={handleContactVendor}
                />
              ))}
            </div>
          </section>
        )}

        {/* Filters */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div>
            <h2 className="text-2xl font-semibold">
              All Vendors
              {filteredVendors.length > 0 && (
                <span className="text-muted-foreground ml-2">
                  ({filteredVendors.length})
                </span>
              )}
            </h2>
          </div>
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <Button
              variant={showFeaturedOnly ? "default" : "outline"}
              size="sm"
              onClick={() => setShowFeaturedOnly(!showFeaturedOnly)}
            >
              Featured Only
            </Button>
          </div>
        </div>

        {/* Vendors Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredVendors.map((vendor) => (
            <VendorCard 
              key={vendor._id} 
              vendor={vendor} 
              isFeatured={vendor.isFeatured}
              onSelect={setSelectedVendor}
              onContact={handleContactVendor}
            />
          ))}
        </div>

        {filteredVendors.length === 0 && (
          <div className="text-center py-16">
            <Building className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">No vendors found</h3>
            <p className="text-muted-foreground">
              Try adjusting your search criteria or browse all categories
            </p>
          </div>
        )}

        {/* Vendor Detail Modal */}
        {selectedVendor && (
          <Dialog open={!!selectedVendor} onOpenChange={() => setSelectedVendor(null)}>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <div className="flex items-start gap-4">
                  {selectedVendor.logoUrl && (
                    <div className="w-16 h-16 relative flex-shrink-0">
                      <Image
                        src={selectedVendor.logoUrl}
                        alt={selectedVendor.companyName}
                        fill
                        className="rounded-lg object-cover"
                      />
                    </div>
                  )}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <DialogTitle className="text-2xl">
                        {selectedVendor.companyName}
                      </DialogTitle>
                      {selectedVendor.isFeatured && (
                        <Star className="h-5 w-5 text-yellow-500 fill-current" />
                      )}
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                      <Badge variant="outline">
                        {categoryLabels[selectedVendor.category as keyof typeof categoryLabels]}
                      </Badge>
                      {selectedVendor.location && (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          {selectedVendor.location}
                        </div>
                      )}
                      {selectedVendor.establishedYear && (
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          Est. {selectedVendor.establishedYear}
                        </div>
                      )}
                    </div>
                    {selectedVendor.rating && (
                      <div className="flex items-center gap-1">
                        <div className="flex">
                          {Array.from({ length: 5 }).map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${
                                i < Math.floor(selectedVendor.rating)
                                  ? "text-yellow-500 fill-current"
                                  : "text-gray-300"
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {selectedVendor.rating} ({selectedVendor.reviewCount} reviews)
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </DialogHeader>

              <div className="space-y-6">
                {selectedVendor.bannerUrl && (
                  <div className="w-full h-48 relative rounded-lg overflow-hidden">
                    <Image
                      src={selectedVendor.bannerUrl}
                      alt={`${selectedVendor.companyName} banner`}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}

                <DialogDescription className="text-base">
                  {selectedVendor.description}
                </DialogDescription>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Services */}
                  <div>
                    <h3 className="font-semibold mb-3 flex items-center gap-2">
                      <Briefcase className="h-4 w-4" />
                      Services
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {selectedVendor.services.map((service: string, index: number) => (
                        <Badge key={index} variant="secondary">
                          {service}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Specialties */}
                  {selectedVendor.specialties && selectedVendor.specialties.length > 0 && (
                    <div>
                      <h3 className="font-semibold mb-3 flex items-center gap-2">
                        <Award className="h-4 w-4" />
                        Specialties
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {selectedVendor.specialties.map((specialty: string, index: number) => (
                          <Badge key={index} variant="outline">
                            {specialty}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Company Info */}
                  <div>
                    <h3 className="font-semibold mb-3 flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      Company Info
                    </h3>
                    <div className="space-y-2 text-sm">
                      {selectedVendor.employeeCount && (
                        <div className="flex items-center gap-2">
                          <Users className="h-3 w-3 text-muted-foreground" />
                          {selectedVendor.employeeCount} employees
                        </div>
                      )}
                      {selectedVendor.establishedYear && (
                        <div className="flex items-center gap-2">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          Established {selectedVendor.establishedYear}
                        </div>
                      )}
                      {selectedVendor.location && (
                        <div className="flex items-center gap-2">
                          <MapPin className="h-3 w-3 text-muted-foreground" />
                          {selectedVendor.location}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Certifications */}
                  {selectedVendor.certifications && selectedVendor.certifications.length > 0 && (
                    <div>
                      <h3 className="font-semibold mb-3 flex items-center gap-2">
                        <Award className="h-4 w-4" />
                        Certifications
                      </h3>
                      <div className="space-y-1 text-sm">
                        {selectedVendor.certifications.map((cert: string, index: number) => (
                          <div key={index} className="flex items-center gap-2">
                            <div className="w-2 h-2 rounded-full bg-green-500" />
                            {cert}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Contact Actions */}
                <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
                  {selectedVendor.website && (
                    <Button asChild className="flex-1">
                      <Link href={selectedVendor.website} target="_blank">
                        <Globe className="h-4 w-4 mr-2" />
                        Visit Website
                        <ExternalLink className="h-3 w-3 ml-2" />
                      </Link>
                    </Button>
                  )}
                  {selectedVendor.contactEmail && (
                    <Button variant="outline" asChild className="flex-1">
                      <Link href={`mailto:${selectedVendor.contactEmail}`}>
                        <Mail className="h-4 w-4 mr-2" />
                        Send Email
                      </Link>
                    </Button>
                  )}
                  {selectedVendor.contactPhone && (
                    <Button variant="outline" asChild className="flex-1">
                      <Link href={`tel:${selectedVendor.contactPhone}`}>
                        <Phone className="h-4 w-4 mr-2" />
                        Call Now
                      </Link>
                    </Button>
                  )}
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </div>
  );
}

interface VendorCardProps {
  vendor: any;
  isFeatured?: boolean;
  onSelect: (vendor: any) => void;
  onContact: (vendor: any) => void;
}

function VendorCard({ vendor, isFeatured, onSelect, onContact }: VendorCardProps) {
  return (
    <Card className={`h-full hover:shadow-lg transition-shadow cursor-pointer ${
      isFeatured ? 'ring-2 ring-yellow-200' : ''
    }`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3 flex-1">
            {vendor.logoUrl && (
              <div className="w-12 h-12 relative flex-shrink-0">
                <Image
                  src={vendor.logoUrl}
                  alt={vendor.companyName}
                  fill
                  className="rounded-md object-cover"
                />
              </div>
            )}
            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2 mb-1">
                <CardTitle className="text-lg truncate">
                  {vendor.companyName}
                </CardTitle>
                {isFeatured && (
                  <Star className="h-4 w-4 text-yellow-500 fill-current flex-shrink-0" />
                )}
              </div>
                                    <Badge variant="outline" className="text-xs">
                        {categoryLabels[vendor.category as keyof typeof categoryLabels]}
                      </Badge>
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground line-clamp-3">
          {vendor.description}
        </p>
        
        <div className="space-y-3">
          {/* Services */}
          <div>
            <div className="flex flex-wrap gap-1">
              {vendor.services.slice(0, 3).map((service: string, index: number) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {service}
                </Badge>
              ))}
              {vendor.services.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{vendor.services.length - 3} more
                </Badge>
              )}
            </div>
          </div>

          {/* Info */}
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              {vendor.location && (
                <div className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  <span className="truncate">{vendor.location}</span>
                </div>
              )}
              {vendor.rating && (
                <div className="flex items-center gap-1">
                  <Star className="h-3 w-3 text-yellow-500 fill-current" />
                  <span>{vendor.rating}</span>
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2 pt-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => onSelect(vendor)}
              className="flex-1"
            >
              View Details
            </Button>
            <Button 
              size="sm" 
              onClick={() => onContact(vendor)}
              className="flex-1"
            >
              Contact
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
