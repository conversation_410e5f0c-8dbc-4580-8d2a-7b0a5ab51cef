"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import { CheckCircle2, HandHeart, Upload, X } from "lucide-react";

const middlemanSchema = z.object({
  // Product Details
  productName: z.string().min(1, "Product name is required"),
  productBrand: z.string().min(1, "Product brand is required"),
  productModel: z.string().optional(),
  productDescription: z.string().min(10, "Product description must be at least 10 characters"),
  productCondition: z.enum(["new", "like_new", "excellent", "good", "fair"], {
    required_error: "Please select product condition",
  }),
  
  // Product Value
  productValue: z.string().min(1, "Product value is required"),
  currency: z.string().min(1, "Currency is required").default("USD"),
  
  // Product Inclusions
  productInclusions: z.string().min(1, "Please specify what's included (box, papers, accessories, etc.)"),
  
  // Seller Information
  sellerName: z.string().min(1, "Seller name is required"),
  sellerEmail: z.string().email("Valid seller email is required"),
  sellerPhone: z.string().optional(),
  sellerAddress: z.string().min(1, "Seller address is required"),
  
  // Buyer Information
  buyerName: z.string().min(1, "Buyer name is required"),
  buyerEmail: z.string().email("Valid buyer email is required"),
  buyerPhone: z.string().optional(),
  buyerAddress: z.string().min(1, "Buyer address is required"),
  
  // Shipment Details
  approximateShipmentDate: z.string().min(1, "Approximate shipment date is required"),
  shippingMethod: z.string().optional(),
  
  // Additional Information
  specialInstructions: z.string().optional(),
  agreementTerms: z.boolean().refine((val) => val === true, {
    message: "You must agree to the terms and conditions",
  }),
});

type MiddlemanFormData = z.infer<typeof middlemanSchema>;

const conditionOptions = [
  { value: "new", label: "New - Never used, with all original packaging" },
  { value: "like_new", label: "Like New - Used once or twice, excellent condition" },
  { value: "excellent", label: "Excellent - Minor signs of wear, great condition" },
  { value: "good", label: "Good - Normal wear, all functions work perfectly" },
  { value: "fair", label: "Fair - Visible wear but fully functional" },
];

export function MiddlemanForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  
  const { user, isLoading } = useAuth();
  // const submitMiddlemanRequest = useMutation(api.middleman.submitMiddlemanRequest);

  const form = useForm<MiddlemanFormData>({
    resolver: zodResolver(middlemanSchema) as any,
    defaultValues: {
      productName: "",
      productBrand: "",
      productModel: "",
      productDescription: "",
      productValue: "",
      currency: "USD",
      productInclusions: "",
      sellerName: "",
      sellerEmail: "",
      sellerPhone: "",
      sellerAddress: "",
      buyerName: user?.name || "",
      buyerEmail: user?.email || "",
      buyerPhone: "",
      buyerAddress: "",
      approximateShipmentDate: "",
      shippingMethod: "",
      specialInstructions: "",
      agreementTerms: false,
    },
  });

  const { handleSubmit, formState: { errors }, reset, watch, setValue } = form;

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      // Handle image upload logic here
      // For now, just show placeholder
      toast.info("Image upload functionality will be implemented");
    }
  };

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (data: MiddlemanFormData) => {
    if (!user) {
      toast.error("Please sign in to submit a middleman service request");
      return;
    }

    setIsSubmitting(true);
    
    try {
      // TODO: Implement API call when backend is ready
      // await submitMiddlemanRequest({
      //   productName: data.productName,
      //   productBrand: data.productBrand,
      //   productModel: data.productModel,
      //   productDescription: data.productDescription,
      //   productCondition: data.productCondition,
      //   productValue: data.productValue,
      //   currency: data.currency,
      //   productInclusions: data.productInclusions,
      //   sellerName: data.sellerName,
      //   sellerEmail: data.sellerEmail,
      //   sellerPhone: data.sellerPhone,
      //   sellerAddress: data.sellerAddress,
      //   buyerName: data.buyerName,
      //   buyerEmail: data.buyerEmail,
      //   buyerPhone: data.buyerPhone,
      //   buyerAddress: data.buyerAddress,
      //   approximateShipmentDate: data.approximateShipmentDate,
      //   shippingMethod: data.shippingMethod,
      //   specialInstructions: data.specialInstructions,
      //   productImages: uploadedImages,
      // });
      
      // Simulate API call for now
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success("Middleman service request submitted successfully!", {
        description: "Our team will review your request and contact you within 24 hours.",
      });
      
      setIsSubmitted(true);
      reset();
      setUploadedImages([]);
    } catch (error: any) {
      console.error("Submission error:", error);
      toast.error("Failed to submit middleman service request", {
        description: error?.message || "Please try again or contact support.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
          <CardContent className="p-8 text-center">
            <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading form...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
          <CardContent className="p-8 text-center">
            <HandHeart className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">Authentication Required</h3>
            <p className="text-muted-foreground mb-6">
              Please sign in to submit a middleman service request.
            </p>
            <Button onClick={() => window.location.href = "/login"}>
              Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isSubmitted) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
          <CardContent className="p-8 text-center">
            <CheckCircle2 className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">Request Submitted Successfully!</h3>
            <p className="text-muted-foreground mb-6">
              Thank you for submitting your middleman service request. Our team will review the details 
              and contact you within 24 hours to discuss the next steps.
            </p>
            <Button onClick={() => setIsSubmitted(false)}>
              Submit Another Request
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
        <CardHeader>
          <CardTitle className="text-2xl font-light tracking-wide text-foreground">
            Middleman Service Request
          </CardTitle>
          <p className="text-muted-foreground font-light">
            Fill out the form below to request our secure transaction mediation service. 
            All fields marked with * are required.
          </p>
        </CardHeader>

        <CardContent className="pt-0">
          <form onSubmit={handleSubmit(onSubmit as any)} className="space-y-8">
            
            {/* Product Details Section */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-foreground border-b border-border pb-2">
                Product Details
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="productName" className="text-sm font-medium text-foreground">
                    Product Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="productName"
                    {...form.register("productName")}
                    placeholder="e.g., Rolex Submariner"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                  {errors.productName && (
                    <p className="text-sm text-red-600 font-medium">{errors.productName.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="productBrand" className="text-sm font-medium text-foreground">
                    Brand <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="productBrand"
                    {...form.register("productBrand")}
                    placeholder="e.g., Rolex"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                  {errors.productBrand && (
                    <p className="text-sm text-red-600 font-medium">{errors.productBrand.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="productModel" className="text-sm font-medium text-foreground">
                    Model/Reference Number
                  </Label>
                  <Input
                    id="productModel"
                    {...form.register("productModel")}
                    placeholder="e.g., 116610LN"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="productCondition" className="text-sm font-medium text-foreground">
                    Condition <span className="text-red-500">*</span>
                  </Label>
                  <select
                    {...form.register("productCondition")}
                    className="w-full bg-primary/5 border border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  >
                    <option value="">Select condition</option>
                    {conditionOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  {errors.productCondition && (
                    <p className="text-sm text-red-600 font-medium">{errors.productCondition.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="productDescription" className="text-sm font-medium text-foreground">
                  Product Description <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="productDescription"
                  {...form.register("productDescription")}
                  placeholder="Provide detailed description of the product including any notable features, flaws, or history..."
                  rows={4}
                  className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 p-4 resize-none"
                />
                {errors.productDescription && (
                  <p className="text-sm text-red-600 font-medium">{errors.productDescription.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="productInclusions" className="text-sm font-medium text-foreground">
                  Product Inclusions <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="productInclusions"
                  {...form.register("productInclusions")}
                  placeholder="List everything included: original box, papers, warranty cards, certificates, additional straps, manuals, etc. Be as specific as possible."
                  rows={3}
                  className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 p-4 resize-none"
                />
                {errors.productInclusions && (
                  <p className="text-sm text-red-600 font-medium">{errors.productInclusions.message}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-2 space-y-2">
                  <Label htmlFor="productValue" className="text-sm font-medium text-foreground">
                    Product Value <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="productValue"
                    {...form.register("productValue")}
                    placeholder="e.g., 15000"
                    type="number"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                  {errors.productValue && (
                    <p className="text-sm text-red-600 font-medium">{errors.productValue.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="currency" className="text-sm font-medium text-foreground">
                    Currency
                  </Label>
                  <select
                    {...form.register("currency")}
                    className="w-full bg-primary/5 border border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  >
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                    <option value="CAD">CAD</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Product Images Section */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-foreground border-b border-border pb-2">
                Product Images
              </h3>
              
              <div className="space-y-4">
                <div className="border-2 border-dashed border-border rounded-xl p-8 text-center">
                  <Upload className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground mb-4">
                    Upload clear photos of the product from multiple angles
                  </p>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="image-upload"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => document.getElementById('image-upload')?.click()}
                  >
                    Choose Images
                  </Button>
                </div>

                {uploadedImages.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {uploadedImages.map((image, index) => (
                      <div key={index} className="relative">
                        <img
                          src={image}
                          alt={`Product ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg"
                        />
                        <button
                          type="button"
                          onClick={() => removeImage(index)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Seller Information Section */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-foreground border-b border-border pb-2">
                Seller Information
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="sellerName" className="text-sm font-medium text-foreground">
                    Seller Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="sellerName"
                    {...form.register("sellerName")}
                    placeholder="Full name of the seller"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                  {errors.sellerName && (
                    <p className="text-sm text-red-600 font-medium">{errors.sellerName.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sellerEmail" className="text-sm font-medium text-foreground">
                    Seller Email <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="sellerEmail"
                    type="email"
                    {...form.register("sellerEmail")}
                    placeholder="<EMAIL>"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                  {errors.sellerEmail && (
                    <p className="text-sm text-red-600 font-medium">{errors.sellerEmail.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="sellerPhone" className="text-sm font-medium text-foreground">
                    Seller Phone
                  </Label>
                  <Input
                    id="sellerPhone"
                    {...form.register("sellerPhone")}
                    placeholder="+****************"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sellerAddress" className="text-sm font-medium text-foreground">
                    Seller Address <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="sellerAddress"
                    {...form.register("sellerAddress")}
                    placeholder="Full address for shipping"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                  {errors.sellerAddress && (
                    <p className="text-sm text-red-600 font-medium">{errors.sellerAddress.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Buyer Information Section */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-foreground border-b border-border pb-2">
                Buyer Information
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="buyerName" className="text-sm font-medium text-foreground">
                    Buyer Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="buyerName"
                    {...form.register("buyerName")}
                    placeholder="Your full name"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                  {errors.buyerName && (
                    <p className="text-sm text-red-600 font-medium">{errors.buyerName.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="buyerEmail" className="text-sm font-medium text-foreground">
                    Buyer Email <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="buyerEmail"
                    type="email"
                    {...form.register("buyerEmail")}
                    placeholder="<EMAIL>"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                  {errors.buyerEmail && (
                    <p className="text-sm text-red-600 font-medium">{errors.buyerEmail.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="buyerPhone" className="text-sm font-medium text-foreground">
                    Buyer Phone
                  </Label>
                  <Input
                    id="buyerPhone"
                    {...form.register("buyerPhone")}
                    placeholder="+****************"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="buyerAddress" className="text-sm font-medium text-foreground">
                    Buyer Address <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="buyerAddress"
                    {...form.register("buyerAddress")}
                    placeholder="Full address for delivery"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                  {errors.buyerAddress && (
                    <p className="text-sm text-red-600 font-medium">{errors.buyerAddress.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Shipment Information Section */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-foreground border-b border-border pb-2">
                Shipment Information
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="approximateShipmentDate" className="text-sm font-medium text-foreground">
                    Approximate Shipment Date <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="approximateShipmentDate"
                    type="date"
                    {...form.register("approximateShipmentDate")}
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                  {errors.approximateShipmentDate && (
                    <p className="text-sm text-red-600 font-medium">{errors.approximateShipmentDate.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="shippingMethod" className="text-sm font-medium text-foreground">
                    Preferred Shipping Method
                  </Label>
                  <select
                    {...form.register("shippingMethod")}
                    className="w-full bg-primary/5 border border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  >
                    <option value="">Select shipping method</option>
                    <option value="fedex_overnight">FedEx Overnight</option>
                    <option value="fedex_2day">FedEx 2-Day</option>
                    <option value="ups_overnight">UPS Next Day</option>
                    <option value="ups_2day">UPS 2-Day</option>
                    <option value="dhl_express">DHL Express</option>
                    <option value="usps_priority">USPS Priority</option>
                    <option value="other">Other (specify in special instructions)</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Special Instructions Section */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-foreground border-b border-border pb-2">
                Additional Information
              </h3>
              
              <div className="space-y-2">
                <Label htmlFor="specialInstructions" className="text-sm font-medium text-foreground">
                  Special Instructions or Notes
                </Label>
                <Textarea
                  id="specialInstructions"
                  {...form.register("specialInstructions")}
                  placeholder="Any special handling instructions, timeline requirements, or other important information..."
                  rows={4}
                  className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 p-4 resize-none"
                />
              </div>
            </div>

            {/* Agreement Section */}
            <div className="space-y-6">
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  id="agreementTerms"
                  {...form.register("agreementTerms")}
                  className="mt-1 h-4 w-4 text-primary focus:ring-primary border-border rounded"
                />
                <Label htmlFor="agreementTerms" className="text-sm text-foreground leading-relaxed">
                  I agree to the middleman service terms and conditions. I understand that MODA will 
                  charge a service fee for facilitating this transaction and that all parties must 
                  comply with the agreed-upon terms. <span className="text-red-500">*</span>
                </Label>
              </div>
              {errors.agreementTerms && (
                <p className="text-sm text-red-600 font-medium">{errors.agreementTerms.message}</p>
              )}
            </div>

            {/* Submit Button */}
            <div className="pt-6">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-primary hover:bg-primary/90 text-primary-foreground font-medium py-3 px-6 rounded-xl transition-all duration-300 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed h-12"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full"></div>
                    <span>Submitting Request...</span>
                  </div>
                ) : (
                  "Submit Middleman Service Request"
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
