"use client";

import { useState, useMemo, useEffect, useCallback } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { toast } from "sonner";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { But<PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@repo/ui/components/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@repo/ui/components/alert-dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/ui/components/popover";
import { Checkbox } from "@repo/ui/components/checkbox";
import { 
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Archive,
  Star,
  TrendingUp,
  Calendar,
  Package,
  DollarSign,
  BarChart3,
  ShoppingCart,
  Filter,
  Upload,
  Grid,
  List,
  Settings,
  Crown,
  ShoppingBag,
  Heart,
  Users,
  UserCheck,
  UserX,
  Shield,
  Mail,
  Phone,
  MapPin,
  CreditCard,
  Activity,
  Clock,
  Ban,
  CheckCircle,
  XCircle
} from "lucide-react";

interface FilterState {
  search: string;
  userType: "all" | "consumer" | "seller" | "admin";
  subscriptionStatus: "all" | "active" | "inactive" | "trial";
  sortBy: "newest" | "oldest" | "name" | "email" | "lastActive";
  viewMode: "table" | "card";
  limit: number;
  offset: number;
  joinDateRange: { start: string; end: string };
  lastActiveRange: { start: string; end: string };
}

const USER_TYPE_COLORS = {
  consumer: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  seller: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
  admin: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
};

const SUBSCRIPTION_COLORS = {
  active: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  inactive: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
  trial: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  expired: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
};

export function AdminUserManagement() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  // Initialize filters from URL params
  const initializeFiltersFromURL = useCallback((): FilterState => {
    const userType = searchParams.get('userType') || 'all';
    const subscriptionStatus = searchParams.get('subscriptionStatus') || 'all';
    const search = searchParams.get('search') || '';
    const sortBy = searchParams.get('sortBy') || 'newest';
    const viewMode = searchParams.get('viewMode') || 'table';
    
    return {
      search,
      userType: userType as FilterState["userType"],
      subscriptionStatus: subscriptionStatus as FilterState["subscriptionStatus"],
      sortBy: sortBy as FilterState["sortBy"],
      viewMode: viewMode as FilterState["viewMode"],
      limit: 50,
      offset: 0,
      joinDateRange: { start: "", end: "" },
      lastActiveRange: { start: "", end: "" },
    };
  }, [searchParams]);

  const [filters, setFilters] = useState<FilterState>(initializeFiltersFromURL);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // Update URL when filters change
  const updateURL = useCallback((newFilters: FilterState) => {
    const params = new URLSearchParams();
    if (newFilters.userType !== 'all') params.set('userType', newFilters.userType);
    if (newFilters.subscriptionStatus !== 'all') params.set('subscriptionStatus', newFilters.subscriptionStatus);
    if (newFilters.search) params.set('search', newFilters.search);
    if (newFilters.sortBy !== 'newest') params.set('sortBy', newFilters.sortBy);
    if (newFilters.viewMode !== 'table') params.set('viewMode', newFilters.viewMode);
    
    const newURL = `${pathname}?${params.toString()}`;
    router.replace(newURL);
  }, [pathname, router]);

  // Initialize filters from URL on mount
  useEffect(() => {
    setFilters(initializeFiltersFromURL);
  }, [initializeFiltersFromURL]);

  // Fetch user data
  const usersData = useQuery(api.userManagement.getAllUsers, {
    userType: filters.userType !== "all" ? filters.userType : undefined,
    subscriptionStatus: filters.subscriptionStatus !== "all" ? filters.subscriptionStatus : undefined,
    limit: filters.limit,
    offset: filters.offset
  });

  // Mutations
  const suspendUser = useMutation(api.userManagement.updateVerificationStatus);
  const activateUser = useMutation(api.userManagement.updateVerificationStatus);
  const deleteUser = useMutation(api.userManagement.deleteUser);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleFilterChange = (key: keyof FilterState, value: any) => {
    const newFilters = {
      ...filters,
      [key]: value,
      offset: key !== 'offset' ? 0 : value, // Reset offset when changing other filters
    };
    setFilters(newFilters);
    updateURL(newFilters);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked && usersData?.users) {
      setSelectedItems(usersData.users.map((user: any) => user._id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (itemId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, itemId]);
    } else {
      setSelectedItems(prev => prev.filter(id => id !== itemId));
    }
  };

  const handleBulkAction = async (action: string) => {
    try {
      switch (action) {
        case "suspend":
          for (const userId of selectedItems) {
            await suspendUser({ 
              userId: userId as any,
              isVerified: false,
              reason: "Bulk suspension operation"
            });
          }
          toast.success(`Successfully suspended ${selectedItems.length} users`);
          break;
        case "activate":
          for (const userId of selectedItems) {
            await activateUser({ 
              userId: userId as any,
              isVerified: true,
              reason: "Bulk activation operation"
            });
          }
          toast.success(`Successfully activated ${selectedItems.length} users`);
          break;
        case "delete":
          for (const userId of selectedItems) {
            await deleteUser({ 
              userId: userId as any,
              reason: "Bulk deletion operation"
            });
          }
          toast.success(`Successfully deleted ${selectedItems.length} users`);
          break;
      }
      setSelectedItems([]);
    } catch (error) {
      toast.error(`Failed to perform bulk action: ${error}`);
    }
  };

  const handleUserAction = async (userId: string, action: string) => {
    try {
      switch (action) {
        case "edit":
          router.push(`/admin/users/${userId}/edit`);
          break;
        case "view":
          router.push(`/admin/users/${userId}`);
          break;
        case "suspend":
          await suspendUser({ 
            userId: userId as any,
            isVerified: false,
            reason: "Manual suspension"
          });
          toast.success("User suspended successfully");
          break;
        case "activate":
          await activateUser({ 
            userId: userId as any,
            isVerified: true,
            reason: "Manual activation"
          });
          toast.success("User activated successfully");
          break;
        case "delete":
          await deleteUser({ 
            userId: userId as any,
            reason: "Manual deletion"
          });
          toast.success("User deleted successfully");
          break;
      }
    } catch (error) {
      toast.error(`Failed to perform action: ${error}`);
    }
  };

  // Calculate summary stats
  const summaryStats = useMemo(() => {
    if (!usersData?.users) return null;
    
    const users = usersData.users;
    return {
      totalUsers: users.length,
      activeUsers: users.filter((u: any) => u.isVerified).length,
      totalValue: 0, // totalSpent doesn't exist in the current schema
      avgSpent: 0, // totalSpent doesn't exist in the current schema
    };
  }, [usersData]);

  if (usersData === undefined) {
    return (
      <div className="container mx-auto px-6 py-6 space-y-6">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground font-light">Loading users...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background">
      {/* Full Width Header with Search and Actions */}
      <div className="border-b border-border bg-card">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between gap-4">
            {/* Search and Filters */}
            <div className="flex items-center gap-4 flex-1">
              <div className="relative max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search users..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                  className="pl-10 rounded-xl bg-primary/5 border-border font-light placeholder:text-primary transition-all duration-300 focus:ring-2 focus:ring-primary/20"
                />
              </div>
              
              {/* Status Dropdown */}
              <Select value={filters.userType} onValueChange={(value) => handleFilterChange("userType", value as FilterState["userType"])}>
                <SelectTrigger className="w-40 rounded-xl bg-primary/5 border-border font-light">
                  <SelectValue placeholder="User Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4" />
                      ALL TYPES
                    </div>
                  </SelectItem>
                  <SelectItem value="consumer" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <ShoppingCart className="w-4 h-4" />
                      CONSUMERS
                    </div>
                  </SelectItem>
                  <SelectItem value="seller" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <Crown className="w-4 h-4" />
                      SELLERS
                    </div>
                  </SelectItem>
                  <SelectItem value="admin" className="rounded-xl font-light">
                    <div className="flex items-center gap-2">
                      <Shield className="w-4 h-4" />
                      ADMINS
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>

              {/* Filter Popover */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button 
                    variant="outline" 
                    className="rounded-xl font-light"
                  >
                    <Filter className="w-4 h-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-6" align="start">
                  <div className="space-y-4">
                    <h4 className="font-medium text-sm uppercase tracking-wide">Filters</h4>
                    
                    {/* Subscription Status Filter */}
                    <div>
                      <label className="text-sm font-light text-muted-foreground mb-2 block">Subscription Status</label>
                      <Select value={filters.subscriptionStatus} onValueChange={(value) => handleFilterChange("subscriptionStatus", value)}>
                        <SelectTrigger className="rounded-xl">
                          <SelectValue placeholder="All Statuses" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Statuses</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="inactive">Inactive</SelectItem>
                          <SelectItem value="trial">Trial</SelectItem>
                          <SelectItem value="expired">Expired</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Join Date Range */}
                    <div>
                      <label className="text-sm font-light text-muted-foreground mb-2 block">Join Date Range</label>
                      <div className="flex gap-2">
                        <Input
                          type="date"
                          placeholder="Start"
                          value={filters.joinDateRange.start}
                          onChange={(e) => handleFilterChange("joinDateRange", { ...filters.joinDateRange, start: e.target.value })}
                          className="rounded-xl"
                        />
                        <Input
                          type="date"
                          placeholder="End"
                          value={filters.joinDateRange.end}
                          onChange={(e) => handleFilterChange("joinDateRange", { ...filters.joinDateRange, end: e.target.value })}
                          className="rounded-xl"
                        />
                      </div>
                    </div>

                    {/* Last Active Range */}
                    <div>
                      <label className="text-sm font-light text-muted-foreground mb-2 block">Last Active Range</label>
                      <div className="flex gap-2">
                        <Input
                          type="date"
                          placeholder="Start"
                          value={filters.lastActiveRange.start}
                          onChange={(e) => handleFilterChange("lastActiveRange", { ...filters.lastActiveRange, start: e.target.value })}
                          className="rounded-xl"
                        />
                        <Input
                          type="date"
                          placeholder="End"
                          value={filters.lastActiveRange.end}
                          onChange={(e) => handleFilterChange("lastActiveRange", { ...filters.lastActiveRange, end: e.target.value })}
                          className="rounded-xl"
                        />
                      </div>
                    </div>

                    <Button 
                      onClick={() => {
                        const newFilters = { 
                          ...filters, 
                          subscriptionStatus: "all" as const, 
                          joinDateRange: { start: "", end: "" }, 
                          lastActiveRange: { start: "", end: "" }
                        };
                        setFilters(newFilters);
                        updateURL(newFilters);
                      }}
                      variant="outline" 
                      className="w-full rounded-xl font-light"
                    >
                      Clear Filters
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-4">
              <Button 
                variant="outline" 
                className="rounded-xl font-light"
              >
                <Upload className="w-4 h-4 mr-2" />
                EXPORT
              </Button>
              
              <Button 
                variant="ghost" 
                className="text-accent hover:text-accent/80 font-light rounded-xl px-4 py-2 transition-all duration-300"
              >
                <Activity className="w-4 h-4 mr-2" />
                ANALYTICS
              </Button>
              
              <Button 
                onClick={() => router.push("/admin/users/new")}
                className="bg-accent text-accent-foreground hover:bg-accent/90 font-light rounded-xl px-6 py-2 transition-all duration-300"
              >
                <Plus className="w-4 h-4 mr-2" />
                ADD USER
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Full Width Table */}
      <div className="flex-1">
        <Table>
          <TableHeader>
            <TableRow className="border-b border-border">
              <TableHead className="w-12">
                <Checkbox 
                  checked={selectedItems.length === usersData?.users?.length}
                  onCheckedChange={handleSelectAll}
                  className="rounded border-border"
                />
              </TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">User</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">User ID</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Type</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Subscription</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Total Spent</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide">Join Date</TableHead>
              <TableHead className="font-light text-muted-foreground uppercase tracking-wide text-center">Last Active</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {usersData?.users && usersData.users.length > 0 ? (
              usersData.users.map((user: any) => (
                <TableRow key={user._id} className="hover:bg-primary/5 transition-colors duration-300">
                  <TableCell>
                    <Checkbox 
                      checked={selectedItems.includes(user._id)}
                      onCheckedChange={(checked) => handleSelectItem(user._id, checked as boolean)}
                      className="rounded border-border"
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-primary/5 rounded-xl overflow-hidden border border-border flex items-center justify-center">
                        <div className="w-full h-full bg-primary/5 flex items-center justify-center">
                          <Users className="w-4 h-4 text-muted-foreground" />
                        </div>
                      </div>
                      <div>
                        <p className="font-light text-foreground text-sm">{user.name}</p>
                        <p className="text-muted-foreground text-xs font-light">{user.email}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="font-light font-mono text-muted-foreground">
                    <span className="text-xs">{user._id}</span>
                    </TableCell>
                  <TableCell>
                    <Badge 
                      variant="outline" 
                      className={`text-xs font-light rounded-xl ${USER_TYPE_COLORS[user.userType as keyof typeof USER_TYPE_COLORS] || 'bg-gray-100 text-gray-800'}`}
                    >
                      {user.userType === 'consumer' ? 'Consumer' : 
                       user.userType === 'seller' ? 'Seller' :
                       user.userType === 'admin' ? 'Admin' :
                       user.userType}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant="outline" 
                      className={`text-xs font-light rounded-xl ${SUBSCRIPTION_COLORS[user.subscriptionStatus as keyof typeof SUBSCRIPTION_COLORS] || 'bg-gray-100 text-gray-800'}`}
                    >
                      {user.subscriptionStatus === 'active' ? 'Active' : 
                       user.subscriptionStatus === 'inactive' ? 'Inactive' :
                       user.subscriptionStatus === 'trial' ? 'Trial' :
                       user.subscriptionStatus === 'expired' ? 'Expired' :
                       user.subscriptionStatus}
                    </Badge>
                  </TableCell>
                  <TableCell className="font-light text-primary">{formatCurrency(0)}</TableCell>
                  <TableCell className="font-light text-muted-foreground">{formatDate(user._creationTime)}</TableCell>
                  <TableCell className="text-center">
                    <div className="flex items-center justify-center gap-1 text-muted-foreground">
                      <Clock className="w-4 h-4" />
                      <span className="font-light">{formatDate(user._creationTime)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="p-1 h-8 w-8">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleUserAction(user._id, "edit")}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleUserAction(user._id, "view")}>
                          <Eye className="w-4 h-4 mr-2" />
                          View
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {user.isVerified && (
                          <DropdownMenuItem onClick={() => handleUserAction(user._id, "suspend")}>
                            <Ban className="w-4 h-4 mr-2" />
                            Suspend
                          </DropdownMenuItem>
                        )}
                        {!user.isVerified && (
                          <DropdownMenuItem onClick={() => handleUserAction(user._id, "activate")}>
                            <CheckCircle className="w-4 h-4 mr-2" />
                            Activate
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete User</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete this user? This will permanently remove their account and all associated data.
                                This action cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleUserAction(user._id, "delete")}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              >
                                Delete User
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={11} className="text-center py-16">
                  <div className="flex flex-col items-center justify-center">
                    <div className="w-16 h-16 bg-primary/5 rounded-xl flex items-center justify-center mb-4 border border-border">
                      <Users className="w-8 h-8 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-light text-primary mb-2">
                      We could not find any users matching your criteria
                    </h3>
                    <p className="text-muted-foreground mb-6 text-center max-w-md font-light">
                      Select the "Add User" button below to start managing users on the platform.
                    </p>
                    <Button 
                      onClick={() => router.push("/admin/users/new")}
                      className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-xl font-light transition-all duration-300"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      ADD USER
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Footer Stats */}
      <div className="border-t border-border bg-card">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between text-sm font-light">
            <div className="text-primary">
              Total Users: <span className="font-medium">{summaryStats?.totalUsers || 0}</span>
            </div>
            <div className="text-primary">
              Active Users: <span className="font-medium">{summaryStats?.activeUsers || 0}</span>
            </div>
            <div className="text-muted-foreground">
              0–0 of {usersData?.users?.length || 0}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
