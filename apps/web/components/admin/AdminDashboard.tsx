"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { AdminOverview } from "./AdminOverview";
import { DashboardLoader } from "@/components/common";

import { SellerManagement } from "./SellerManagement";
import { EnhancedUserManagement } from "./EnhancedUserManagement";
import { EnhancedSellerManagement } from "./EnhancedSellerManagement";
import { SubscriptionManagement } from "./SubscriptionManagement";
import { UserActivityManagement } from "./UserActivityManagement";
import { PlatformSettings } from "./PlatformSettings";
import { SupportManagement } from "./SupportManagement";
import { useRouter } from "next/navigation";

export interface AdminUser {
  id: string;
  name: string;
  email: string;
  role: "super_admin" | "admin" | "moderator";
  permissions: string[];
}

export interface User {
  id: string;
  name: string;
  email: string;
  type: "consumer" | "seller";
  status: "active" | "suspended" | "pending";
  subscriptionStatus: "active" | "inactive" | "trial" | "expired";
  subscriptionPlan: "basic" | "premium" | "enterprise" | null;
  joinDate: string;
  lastActive: string;
  totalSpent?: number;
  totalEarned?: number;
}

export interface SellerApplication {
  id: string;
  applicantName: string;
  applicantEmail: string;
  businessName: string;
  businessType: string;
  applicationDate: string;
  status: "pending" | "approved" | "rejected";
  documents: string[];
  reviewNotes?: string;
}

const MOCK_ADMIN_USER: AdminUser = {
  id: "admin-1",
  name: "Admin User",
  email: "<EMAIL>",
  role: "super_admin",
  permissions: ["users", "sellers", "settings", "analytics", "billing", "support"]
};

export function AdminDashboard() {
  const [activeSection, setActiveSection] = useState("overview");
  const [adminUser] = useState<AdminUser>(MOCK_ADMIN_USER);
  const router = useRouter();
  
  // Fetch real platform metrics
  const platformMetrics = useQuery(api.adminMetrics.getPlatformMetrics);

  const hasPermission = (permission: string): boolean => {
    return adminUser.permissions.includes(permission) || adminUser.role === "super_admin";
  };

  const renderContent = () => {
    switch (activeSection) {
      case "overview":
        // Show loading state while fetching data
        if (platformMetrics === undefined) {
          return <DashboardLoader />;
        }
        
        return (
          <AdminOverview 
            metrics={platformMetrics}
            adminUser={adminUser}
          />
        );
        
      // User Management Sections
      case "users":
        if (!hasPermission("users")) {
          return <UnauthorizedAccess />;
        }
        // Redirect to the dedicated users page
        router.push("/admin/users");
        return null;
      
      case "all-users":
        if (!hasPermission("users")) {
          return <UnauthorizedAccess />;
        }
        return <EnhancedUserManagement activeTab="users" />;
      
      case "subscriptions":
        if (!hasPermission("users")) {
          return <UnauthorizedAccess />;
        }
        return <SubscriptionManagement activeTab="overview" />;
      
      case "user-activity":
        if (!hasPermission("users")) {
          return <UnauthorizedAccess />;
        }
        return <UserActivityManagement activeTab="overview" />;
        
      // Support Management Section
      case "support-tickets":
        if (!hasPermission("support")) {
          return <UnauthorizedAccess />;
        }
        return <SupportManagement />;
        
      // Seller Management Sections
      case "sellers":
      case "pending-applications":
        if (!hasPermission("sellers")) {
          return <UnauthorizedAccess />;
        }
        return <EnhancedSellerManagement activeTab="pending" />;
      
      case "approved-sellers":
        if (!hasPermission("sellers")) {
          return <UnauthorizedAccess />;
        }
        return <EnhancedSellerManagement activeTab="approved" />;
      
      case "seller-performance":
        if (!hasPermission("sellers")) {
          return <UnauthorizedAccess />;
        }
        return <EnhancedSellerManagement activeTab="performance" />;
      
      case "commission-tracking":
        if (!hasPermission("sellers")) {
          return <UnauthorizedAccess />;
        }
        return <EnhancedSellerManagement activeTab="commissions" />;
        
      case "settings":
        if (!hasPermission("settings")) {
          return <UnauthorizedAccess />;
        }
        return <PlatformSettings />;
        
      default:
        // Show loading state while fetching data
        if (platformMetrics === undefined) {
          return <DashboardLoader />;
        }
        
        return (
          <AdminOverview 
            metrics={platformMetrics}
            adminUser={adminUser}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <main className="flex-1">
        <div className="container mx-auto px-6 py-8 space-y-8">
          {renderContent()}
        </div>
      </main>
    </div>
  );
}

// Unauthorized access component
function UnauthorizedAccess() {
  return (
    <div className="flex items-center justify-center min-h-[60vh]">
      <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow duration-300 p-8 text-center max-w-md">
        <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H10m2-5V9m0 0V7m0 2h2m-2 0H10" />
          </svg>
        </div>
        <h3 className="text-xl font-bold text-foreground mb-2">
          Access Denied
        </h3>
        <p className="text-muted-foreground font-light mb-4">
          You don't have permission to access this section.
        </p>
        <Badge variant="destructive" className="rounded-lg">
          Insufficient Permissions
        </Badge>
      </Card>
    </div>
  );
}
