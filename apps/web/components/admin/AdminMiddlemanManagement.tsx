"use client";

import { useState } from "react";
import { useQ<PERSON>y, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from "@repo/ui/components/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@repo/ui/components/dialog";
import { 
  HandHeart,
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Eye,
  Search,
  DollarSign,
  Package,
  User,
  Calendar
} from "lucide-react";
import { Doc, Id } from "@repo/backend/convex/_generated/dataModel";
import { format } from "date-fns";

const statusConfig = {
  pending: {
    label: "Pending",
    color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
    icon: Clock,
  },
  under_review: {
    label: "Under Review",
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    icon: Eye,
  },
  approved: {
    label: "Approved",
    color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    icon: CheckCircle,
  },
  in_progress: {
    label: "In Progress",
    color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
    icon: AlertTriangle,
  },
  completed: {
    label: "Completed",
    color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    icon: CheckCircle,
  },
  rejected: {
    label: "Rejected",
    color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    icon: XCircle,
  },
  cancelled: {
    label: "Cancelled",
    color: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
    icon: XCircle,
  },
};

const priorityConfig = {
  low: { label: "Low", color: "bg-gray-100 text-gray-800" },
  medium: { label: "Medium", color: "bg-yellow-100 text-yellow-800" },
  high: { label: "High", color: "bg-red-100 text-red-800" },
};

interface MiddlemanRequest extends Doc<"middlemanRequests"> {}

export function AdminMiddlemanManagement() {
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedRequest, setSelectedRequest] = useState<MiddlemanRequest | null>(null);
  const [updateData, setUpdateData] = useState({
    status: "",
    priority: "",
    adminNotes: "",
    resolutionNotes: "",
    serviceFee: "",
  });

  const { user } = useAuth();

  // Queries
  const requests = useQuery(api.middleman.getAllMiddlemanRequests, {
    status: selectedStatus !== "all" ? selectedStatus as any : undefined,
    limit: 50,
  });

  const stats = useQuery(api.middleman.getMiddlemanStats);

  // Mutations
  const updateRequestStatus = useMutation(api.middleman.updateMiddlemanRequestStatus);

  const handleStatusUpdate = async (requestId: Id<"middlemanRequests">) => {
    if (!updateData.status) {
      toast.error("Please select a status");
      return;
    }

    try {
      await updateRequestStatus({
        requestId,
        status: updateData.status as any,
        priority: updateData.priority as any || undefined,
        adminNotes: updateData.adminNotes || undefined,
        resolutionNotes: updateData.resolutionNotes || undefined,
        serviceFee: updateData.serviceFee ? parseFloat(updateData.serviceFee) : undefined,
      });

      toast.success("Request updated successfully");
      setUpdateData({ status: "", priority: "", adminNotes: "", resolutionNotes: "", serviceFee: "" });
      setSelectedRequest(null);
    } catch (error: any) {
      toast.error("Failed to update request: " + error.message);
    }
  };

  const filteredRequests = requests?.filter((request: MiddlemanRequest) => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        request.requestNumber.toLowerCase().includes(query) ||
        request.userName?.toLowerCase().includes(query) ||
        request.userEmail?.toLowerCase().includes(query) ||
        request.productName.toLowerCase().includes(query) ||
        request.productBrand.toLowerCase().includes(query) ||
        request.sellerName.toLowerCase().includes(query) ||
        request.buyerName.toLowerCase().includes(query)
      );
    }
    return true;
  }) || [];

  if (!user || user.userType !== "admin") {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
        <p className="text-muted-foreground">You need admin privileges to access this page.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{stats.total}</div>
              <p className="text-xs text-muted-foreground">Total Requests</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
              <p className="text-xs text-muted-foreground">Pending</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{stats.underReview}</div>
              <p className="text-xs text-muted-foreground">Under Review</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
              <p className="text-xs text-muted-foreground">Approved</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-600">{stats.inProgress}</div>
              <p className="text-xs text-muted-foreground">In Progress</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
              <p className="text-xs text-muted-foreground">Completed</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
              <p className="text-xs text-muted-foreground">Rejected</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search requests..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          
          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="under_review">Under Review</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Requests Table */}
      <Card>
        <CardHeader>
          <CardTitle>Middleman Service Requests</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Request #</TableHead>
                  <TableHead>Product</TableHead>
                  <TableHead>Value</TableHead>
                  <TableHead>Buyer</TableHead>
                  <TableHead>Seller</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Submitted</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRequests.map((request) => {
                  const StatusIcon = statusConfig[request.status as keyof typeof statusConfig]?.icon || Clock;
                  return (
                    <TableRow key={request._id}>
                      <TableCell className="font-medium">
                        {request.requestNumber}
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{request.productName}</div>
                          <div className="text-sm text-muted-foreground">{request.productBrand}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <DollarSign className="w-4 h-4 mr-1" />
                          {request.productValue} {request.currency}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{request.buyerName}</div>
                          <div className="text-sm text-muted-foreground">{request.buyerEmail}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{request.sellerName}</div>
                          <div className="text-sm text-muted-foreground">{request.sellerEmail}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={statusConfig[request.status as keyof typeof statusConfig]?.color}>
                          <StatusIcon className="w-3 h-3 mr-1" />
                          {statusConfig[request.status as keyof typeof statusConfig]?.label}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className={priorityConfig[request.priority as keyof typeof priorityConfig]?.color}>
                          {priorityConfig[request.priority as keyof typeof priorityConfig]?.label}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Calendar className="w-4 h-4 mr-1" />
                          {format(new Date(request.submittedAt), "MMM dd, yyyy")}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => setSelectedRequest(request)}
                            >
                              <Eye className="w-4 h-4 mr-1" />
                              View
                            </Button>
                          </DialogTrigger>
                          <RequestDetailModal
                            request={selectedRequest}
                            updateData={updateData}
                            setUpdateData={setUpdateData}
                            onUpdateStatus={handleStatusUpdate}
                          />
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
          
          {filteredRequests.length === 0 && (
            <div className="text-center py-8">
              <HandHeart className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No requests found</h3>
              <p className="text-muted-foreground">
                {searchQuery ? "No requests match your search criteria." : "No middleman service requests have been submitted yet."}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Request Detail Modal Component
interface RequestDetailModalProps {
  request: MiddlemanRequest | null;
  updateData: {
    status: string;
    priority: string;
    adminNotes: string;
    resolutionNotes: string;
    serviceFee: string;
  };
  setUpdateData: (data: any) => void;
  onUpdateStatus: (requestId: Id<"middlemanRequests">) => void;
}

function RequestDetailModal({
  request,
  updateData,
  setUpdateData,
  onUpdateStatus,
}: RequestDetailModalProps) {
  if (!request) return null;

  return (
    <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>Middleman Service Request - {request.requestNumber}</DialogTitle>
      </DialogHeader>
      
      <Tabs defaultValue="details" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="details">Request Details</TabsTrigger>
          <TabsTrigger value="manage">Manage Request</TabsTrigger>
        </TabsList>
        
        <TabsContent value="details" className="space-y-6">
          {/* Product Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Product Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Product Name</Label>
                <p className="text-sm text-muted-foreground">{request.productName}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Brand</Label>
                <p className="text-sm text-muted-foreground">{request.productBrand}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Model</Label>
                <p className="text-sm text-muted-foreground">{request.productModel || "N/A"}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Condition</Label>
                <p className="text-sm text-muted-foreground">{request.productCondition}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Value</Label>
                <p className="text-sm text-muted-foreground">{request.productValue} {request.currency}</p>
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium">Description</Label>
              <p className="text-sm text-muted-foreground">{request.productDescription}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Inclusions</Label>
              <p className="text-sm text-muted-foreground">{request.productInclusions}</p>
            </div>
          </div>

          {/* Seller Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Seller Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Name</Label>
                <p className="text-sm text-muted-foreground">{request.sellerName}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Email</Label>
                <p className="text-sm text-muted-foreground">{request.sellerEmail}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Phone</Label>
                <p className="text-sm text-muted-foreground">{request.sellerPhone || "N/A"}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Address</Label>
                <p className="text-sm text-muted-foreground">{request.sellerAddress}</p>
              </div>
            </div>
          </div>

          {/* Buyer Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Buyer Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Name</Label>
                <p className="text-sm text-muted-foreground">{request.buyerName}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Email</Label>
                <p className="text-sm text-muted-foreground">{request.buyerEmail}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Phone</Label>
                <p className="text-sm text-muted-foreground">{request.buyerPhone || "N/A"}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Address</Label>
                <p className="text-sm text-muted-foreground">{request.buyerAddress}</p>
              </div>
            </div>
          </div>

          {/* Shipment Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Shipment Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Approximate Shipment Date</Label>
                <p className="text-sm text-muted-foreground">{request.approximateShipmentDate}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Shipping Method</Label>
                <p className="text-sm text-muted-foreground">{request.shippingMethod || "N/A"}</p>
              </div>
            </div>
            {request.specialInstructions && (
              <div>
                <Label className="text-sm font-medium">Special Instructions</Label>
                <p className="text-sm text-muted-foreground">{request.specialInstructions}</p>
              </div>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="manage" className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="status">Status</Label>
              <Select 
                value={updateData.status} 
                onValueChange={(value) => setUpdateData({...updateData, status: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="under_review">Under Review</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="priority">Priority</Label>
              <Select 
                value={updateData.priority} 
                onValueChange={(value) => setUpdateData({...updateData, priority: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="serviceFee">Service Fee (USD)</Label>
              <Input
                id="serviceFee"
                type="number"
                placeholder="Enter service fee"
                value={updateData.serviceFee}
                onChange={(e) => setUpdateData({...updateData, serviceFee: e.target.value})}
              />
            </div>
            
            <div>
              <Label htmlFor="admin-notes">Admin Notes</Label>
              <Textarea
                id="admin-notes"
                placeholder="Internal notes about this request..."
                value={updateData.adminNotes}
                onChange={(e) => setUpdateData({...updateData, adminNotes: e.target.value})}
                rows={3}
              />
            </div>
            
            <div>
              <Label htmlFor="resolution-notes">Resolution Notes</Label>
              <Textarea
                id="resolution-notes"
                placeholder="Notes about the resolution (visible to user)..."
                value={updateData.resolutionNotes}
                onChange={(e) => setUpdateData({...updateData, resolutionNotes: e.target.value})}
                rows={3}
              />
            </div>
            
            <Button 
              onClick={() => onUpdateStatus(request._id)} 
              disabled={!updateData.status}
              className="w-full"
            >
              Update Request
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </DialogContent>
  );
}
