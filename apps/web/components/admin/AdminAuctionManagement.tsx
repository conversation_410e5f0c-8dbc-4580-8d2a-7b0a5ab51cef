"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@repo/ui/components/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@repo/ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@repo/ui/components/form";
import { Input } from "@repo/ui/components/input";
import { Textarea } from "@repo/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@repo/ui/components/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@repo/ui/components/alert-dialog";
import { 
  Gavel, 
  Plus, 
  Trash2, 
  Clock, 
  Users, 
  DollarSign,
  Search,
  Package,
  Edit,
  StopCircle,
  MoreHorizontal,
  AlertTriangle,
  CheckCircle,
  Truck
} from "lucide-react";
import { format, addHours } from "date-fns";
import Image from "next/image";

const createAuctionSchema = z.object({
  // Product selection
  auctionType: z.enum(["product", "custom"]),
  productId: z.string().optional(),
  
  // Custom auction fields (required only if auctionType is "custom")
  title: z.string().optional(),
  description: z.string().optional(),
  category: z.enum([
    "clothing",
    "sneakers", 
    "collectibles",
    "accessories",
    "handbags",
    "jewelry",
    "watches",
    "sunglasses",
    "cars",
    "art"
  ]).optional(),
  brand: z.string().optional(),
  condition: z.enum([
    "new",
    "like_new", 
    "excellent",
    "very_good",
    "good",
    "fair"
  ]).optional(),
  
  // Auction settings (always required)
  startingBid: z.number().min(0.01, "Starting bid must be at least $0.01"),
  reservePrice: z.number().optional(),
  bidIncrement: z.number().min(0.01, "Bid increment must be at least $0.01"),
  startTime: z.string().min(1, "Start time is required"),
  duration: z.number().min(1, "Duration must be at least 1 hour"),
  extendOnBid: z.boolean().optional(),
  extensionTime: z.number().optional(),
});

type CreateAuctionForm = z.infer<typeof createAuctionSchema>;

const CATEGORIES = [
  { value: "clothing", label: "Clothing" },
  { value: "sneakers", label: "Sneakers" },
  { value: "collectibles", label: "Collectibles" },
  { value: "accessories", label: "Accessories" },
  { value: "handbags", label: "Handbags" },
  { value: "jewelry", label: "Jewelry" },
  { value: "watches", label: "Watches" },
  { value: "sunglasses", label: "Sunglasses" },
  { value: "cars", label: "Cars" },
  { value: "art", label: "Art" },
];

const CONDITIONS = [
  { value: "new", label: "New" },
  { value: "like_new", label: "Like New" },
  { value: "excellent", label: "Excellent" },
  { value: "very_good", label: "Very Good" },
  { value: "good", label: "Good" },
  { value: "fair", label: "Fair" },
];

export function AdminAuctionManagement() {
  const [activeTab, setActiveTab] = useState("active");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingAuction, setEditingAuction] = useState<any>(null);
  const [productSearchQuery, setProductSearchQuery] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [stopDialogOpen, setStopDialogOpen] = useState(false);
  const [selectedAuction, setSelectedAuction] = useState<any>(null);

  // Fetch auctions
  const auctionsData = useQuery(api.auctions.getAdminAuctions, {
    status: activeTab === "all" ? undefined : activeTab,
    limit: 50,
  });

  // Fetch products for selection
  const productsData = useQuery(api.auctions.getProductsForAuction, {
    searchQuery: productSearchQuery || undefined,
    limit: 20,
  });

  // Mutations
  const createAuction = useMutation(api.auctions.createAuction);
  const updateAuction = useMutation(api.auctions.updateAuction);
  const deleteAuction = useMutation(api.auctions.deleteAuction);
  const stopAuction = useMutation(api.auctions.stopAuction);
  const markAuctionSold = useMutation(api.auctions.markAuctionSold);

  const form = useForm<CreateAuctionForm>({
    resolver: zodResolver(createAuctionSchema),
    defaultValues: {
      auctionType: "product",
      productId: "",
      title: "",
      description: "",
      startingBid: 10,
      reservePrice: undefined,
      bidIncrement: 5,
      startTime: format(addHours(new Date(), 1), "yyyy-MM-dd'T'HH:mm"),
      duration: 24,
      extendOnBid: true,
      extensionTime: 5,
      category: "clothing",
      brand: "",
      condition: "excellent",
    },
  });

  const onSubmit = async (data: CreateAuctionForm) => {
    try {
      // Calculate end time
      const startTime = new Date(data.startTime).getTime();
      const endTime = startTime + (data.duration * 60 * 60 * 1000); // Convert hours to milliseconds

      if (data.auctionType === "product") {
        // Create auction from existing product
        if (!data.productId || data.productId === "") {
          toast.error("Please select a product");
          return;
        }

        await createAuction({
          productId: data.productId as Id<"products">,
          startingBid: data.startingBid,
          reservePrice: data.reservePrice,
          bidIncrement: data.bidIncrement,
          startTime,
          endTime,
          extendOnBid: data.extendOnBid,
          extensionTime: data.extensionTime,
        });
      } else {
        // Create custom auction
        if (!data.title || !data.description || !data.category || !data.brand || !data.condition) {
          toast.error("Please fill in all required fields for custom auction");
          return;
        }

        await createAuction({
          title: data.title,
          description: data.description,
          images: [], // Empty for now - can be added later
          category: data.category,
          brand: data.brand,
          condition: data.condition,
          startingBid: data.startingBid,
          reservePrice: data.reservePrice,
          bidIncrement: data.bidIncrement,
          startTime,
          endTime,
          extendOnBid: data.extendOnBid,
          extensionTime: data.extensionTime,
        });
      }

      toast.success("Auction created successfully");
      setIsCreateModalOpen(false);
      form.reset({
        auctionType: "product",
        productId: "",
        title: "",
        description: "",
        startingBid: 10,
        reservePrice: undefined,
        bidIncrement: 5,
        startTime: format(addHours(new Date(), 1), "yyyy-MM-dd'T'HH:mm"),
        duration: 24,
        extendOnBid: true,
        extensionTime: 5,
        category: "clothing",
        brand: "",
        condition: "excellent",
      });
      setProductSearchQuery("");
    } catch (error) {
      console.error("Error creating auction:", error);
      toast.error("Failed to create auction");
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDateTime = (timestamp: number) => {
    return format(new Date(timestamp), "MMM dd, yyyy 'at' h:mm a");
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { color: "bg-gray-100 text-gray-800", label: "Draft" },
      scheduled: { color: "bg-blue-100 text-blue-800", label: "Scheduled" },
      active: { color: "bg-green-100 text-green-800", label: "Active" },
      ended: { color: "bg-yellow-100 text-yellow-800", label: "Ended" },
      sold: { color: "bg-purple-100 text-purple-800", label: "Sold" },
      cancelled: { color: "bg-red-100 text-red-800", label: "Cancelled" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const handleEdit = (auction: any) => {
    setEditingAuction(auction);
    // Pre-populate form with auction data
    form.reset({
      auctionType: auction.productId ? "product" : "custom",
      productId: auction.productId || "",
      title: auction.title || "",
      description: auction.description || "",
      startingBid: auction.startingBid,
      reservePrice: auction.reservePrice,
      bidIncrement: auction.bidIncrement,
      startTime: format(new Date(auction.startTime), "yyyy-MM-dd'T'HH:mm"),
      duration: Math.round((auction.endTime - auction.startTime) / (60 * 60 * 1000)),
      extendOnBid: auction.extendOnBid,
      extensionTime: auction.extensionTime,
      category: auction.category,
      brand: auction.brand,
      condition: auction.condition,
    });
    setIsEditModalOpen(true);
  };

  const handleUpdate = async (data: CreateAuctionForm) => {
    if (!editingAuction) return;
    
    try {
      const startTime = new Date(data.startTime).getTime();
      const endTime = startTime + (data.duration * 60 * 60 * 1000);

      await updateAuction({
        auctionId: editingAuction._id,
        title: data.title,
        description: data.description,
        startingBid: data.startingBid,
        reservePrice: data.reservePrice,
        bidIncrement: data.bidIncrement,
        startTime,
        endTime,
        extendOnBid: data.extendOnBid,
        extensionTime: data.extensionTime,
      });

      toast.success("Auction updated successfully");
      setIsEditModalOpen(false);
      setEditingAuction(null);
    } catch (error) {
      console.error("Error updating auction:", error);
      toast.error("Failed to update auction");
    }
  };

  const handleDelete = async () => {
    if (!selectedAuction) return;
    
    try {
      await deleteAuction({ auctionId: selectedAuction._id });
      toast.success("Auction deleted successfully");
      setDeleteDialogOpen(false);
      setSelectedAuction(null);
    } catch (error: any) {
      console.error("Error deleting auction:", error);
      toast.error(error.message || "Failed to delete auction");
    }
  };

  const handleStop = async () => {
    if (!selectedAuction) return;
    
    try {
      await stopAuction({ 
        auctionId: selectedAuction._id,
        reason: "Stopped by admin"
      });
      toast.success("Auction stopped successfully");
      setStopDialogOpen(false);
      setSelectedAuction(null);
    } catch (error: any) {
      console.error("Error stopping auction:", error);
      toast.error(error.message || "Failed to stop auction");
    }
  };

  const canEdit = (auction: any) => {
    return auction.status === "draft" || auction.status === "scheduled" || 
           (auction.status === "active" && auction.totalBids === 0);
  };

  const canDelete = (auction: any) => {
    return auction.status === "draft" || auction.status === "scheduled" || 
           (auction.status === "active" && auction.totalBids === 0);
  };

  const canStop = (auction: any) => {
    return auction.status === "active" || auction.status === "scheduled";
  };

  const canMarkSold = (auction: any) => {
    return auction.status === "ended";
  };

  const handleMarkSold = async () => {
    if (!selectedAuction) return;
    
    try {
      await markAuctionSold({ 
        auctionId: selectedAuction._id,
      });
      toast.success("Auction marked as sold successfully");
      setSelectedAuction(null);
    } catch (error: any) {
      console.error("Error marking auction as sold:", error);
      toast.error(error.message || "Failed to mark auction as sold");
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Auction Management</h1>
          <p className="text-muted-foreground">
            Create and manage luxury item auctions
          </p>
        </div>
        
        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Auction
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Create New Auction</DialogTitle>
              <DialogDescription>
                Set up a new luxury item auction with detailed information and timing.
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="auctionType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Auction Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select auction type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="product">From Existing Product</SelectItem>
                          <SelectItem value="custom">Custom Auction</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {form.watch("auctionType") === "product" && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <FormLabel>Select Product</FormLabel>
                      <div className="relative">
                        <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search products..."
                          value={productSearchQuery}
                          onChange={(e) => setProductSearchQuery(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    
                    <FormField
                      control={form.control}
                      name="productId"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <div className="max-h-60 overflow-y-auto space-y-2 border rounded-md p-2">
                              {productsData?.map((product) => (
                                <div
                                  key={product._id}
                                  className={`flex items-center space-x-3 p-3 rounded-md cursor-pointer transition-colors ${
                                    field.value === product._id 
                                      ? "bg-primary/10 border border-primary" 
                                      : "hover:bg-muted"
                                  }`}
                                  onClick={() => field.onChange(product._id)}
                                >
                                  <div className="relative w-12 h-12 rounded-md overflow-hidden bg-muted">
                                    {product.images[0] ? (
                                      <Image
                                        src={product.images[0]}
                                        alt={product.title}
                                        fill
                                        className="object-cover"
                                      />
                                    ) : (
                                      <Package className="w-6 h-6 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-muted-foreground" />
                                    )}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium truncate">{product.title}</p>
                                    <p className="text-xs text-muted-foreground">
                                      {product.brand} • {formatCurrency(product.price)}
                                    </p>
                                    <p className="text-xs text-muted-foreground">
                                      by {product.seller.name}
                                    </p>
                                  </div>
                                </div>
                              ))}
                              {productsData?.length === 0 && (
                                <p className="text-center text-muted-foreground py-4">
                                  No products found
                                </p>
                              )}
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                )}

                {form.watch("auctionType") === "custom" && (
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Title</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter auction title..." {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Enter auction description..."
                              className="min-h-[120px]"
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="category"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Category</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select category" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {CATEGORIES.map((category) => (
                                  <SelectItem key={category.value} value={category.value}>
                                    {category.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="brand"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Brand</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter brand..." {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <FormField
                      control={form.control}
                      name="condition"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Condition</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select condition" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {CONDITIONS.map((condition) => (
                                <SelectItem key={condition.value} value={condition.value}>
                                  {condition.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                )}

                <div className="border-t pt-4">
                  <h3 className="font-medium mb-4">Auction Settings</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="startingBid"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Starting Bid ($)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              step="0.01"
                              placeholder="10.00"
                              {...field}
                              value={field.value || ""}
                              onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="bidIncrement"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bid Increment ($)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              step="0.01"
                              placeholder="5.00"
                              {...field}
                              value={field.value || ""}
                              onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <FormField
                      control={form.control}
                      name="reservePrice"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Reserve Price ($)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              step="0.01"
                              placeholder="Optional minimum price"
                              {...field}
                              value={field.value || ""}
                              onChange={e => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="duration"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Duration (Hours)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              min="1"
                              placeholder="24"
                              {...field}
                              value={field.value || ""}
                              onChange={e => field.onChange(parseInt(e.target.value) || 1)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <FormField
                    control={form.control}
                    name="startTime"
                    render={({ field }) => (
                      <FormItem className="mt-4">
                        <FormLabel>Start Time</FormLabel>
                        <FormControl>
                          <Input 
                            type="datetime-local"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsCreateModalOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">Create Auction</Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Auctions</CardTitle>
            <Gavel className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{auctionsData?.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              Across all categories
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Auctions</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {auctionsData?.auctions?.filter(a => a.status === "active").length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Currently live
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Bids</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {auctionsData?.auctions?.reduce((sum, a) => sum + a.totalBids, 0) || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              All time bids
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(auctionsData?.auctions?.reduce((sum, a) => sum + a.currentBid, 0) || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Current bid total
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Auctions</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
          <TabsTrigger value="ended">Ended</TabsTrigger>
          <TabsTrigger value="sold">Sold</TabsTrigger>
          <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Auctions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Current Bid</TableHead>
                      <TableHead>Bids</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>End Time</TableHead>
                      <TableHead>Created By</TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {auctionsData?.auctions?.map((auction) => (
                      <TableRow key={auction._id}>
                        <TableCell className="font-medium">
                          <div className="max-w-[300px] truncate">
                            <div className="font-semibold">{auction.title}</div>
                            <div className="text-sm text-muted-foreground">{auction.brand}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="capitalize">
                            {auction.category}
                          </Badge>
                        </TableCell>
                        <TableCell>{formatCurrency(auction.currentBid)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <span>{auction.totalBids}</span>
                            <Badge variant="secondary" className="text-xs">
                              {auction.uniqueBidders} bidders
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(auction.status)}</TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {formatDateTime(auction.endTime)}
                        </TableCell>
                        <TableCell>{auction.createdBy}</TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              {canEdit(auction) && (
                                <DropdownMenuItem onClick={() => handleEdit(auction)}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit
                                </DropdownMenuItem>
                              )}
                              {canMarkSold(auction) && (
                                <DropdownMenuItem 
                                  onClick={() => {
                                    setSelectedAuction(auction);
                                    handleMarkSold();
                                  }}
                                  className="text-green-600"
                                >
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  Mark as Sold
                                </DropdownMenuItem>
                              )}
                              {canStop(auction) && (
                                <DropdownMenuItem 
                                  onClick={() => {
                                    setSelectedAuction(auction);
                                    setStopDialogOpen(true);
                                  }}
                                  className="text-orange-600"
                                >
                                  <StopCircle className="h-4 w-4 mr-2" />
                                  Stop Auction
                                </DropdownMenuItem>
                              )}
                              {canEdit(auction) && canDelete(auction) && (
                                <>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem 
                                    onClick={() => {
                                      setSelectedAuction(auction);
                                      setDeleteDialogOpen(true);
                                    }}
                                    className="text-red-600"
                                  >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete
                                  </DropdownMenuItem>
                                </>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {!auctionsData?.auctions?.length && (
                <div className="text-center py-8 text-muted-foreground">
                  No auctions found. Create your first auction to get started.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Edit Auction Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Auction</DialogTitle>
            <DialogDescription>
              Update auction details. Note: Some fields may be restricted based on auction status and existing bids.
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleUpdate)} className="space-y-4">
              {/* Same form fields as create, but some may be disabled */}
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter auction title..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Enter auction description..."
                          className="min-h-[120px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="border-t pt-4">
                <h3 className="font-medium mb-4">Auction Settings</h3>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="startingBid"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Starting Bid ($)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            placeholder="10.00"
                            {...field}
                            value={field.value || ""}
                            onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                            disabled={editingAuction?.totalBids > 0}
                          />
                        </FormControl>
                        <FormMessage />
                        {editingAuction?.totalBids > 0 && (
                          <p className="text-xs text-muted-foreground">
                            Cannot change starting bid after bids have been placed
                          </p>
                        )}
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="bidIncrement"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bid Increment ($)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            placeholder="5.00"
                            {...field}
                            value={field.value || ""}
                            onChange={e => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4 mt-4">
                  <FormField
                    control={form.control}
                    name="reservePrice"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reserve Price ($)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            placeholder="Optional minimum price"
                            {...field}
                            value={field.value || ""}
                            onChange={e => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="duration"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Duration (Hours)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            min="1"
                            placeholder="24"
                            {...field}
                            value={field.value || ""}
                            onChange={e => field.onChange(parseInt(e.target.value) || 1)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={form.control}
                  name="startTime"
                  render={({ field }) => (
                    <FormItem className="mt-4">
                      <FormLabel>Start Time</FormLabel>
                      <FormControl>
                        <Input 
                          type="datetime-local"
                          {...field}
                          disabled={editingAuction?.status === "active"}
                        />
                      </FormControl>
                      <FormMessage />
                      {editingAuction?.status === "active" && (
                        <p className="text-xs text-muted-foreground">
                          Cannot change start time for active auctions
                        </p>
                      )}
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditModalOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Update Auction</Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Delete Auction
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{selectedAuction?.title}"? This action cannot be undone.
              {selectedAuction?.totalBids > 0 && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-700">
                  Warning: This auction has {selectedAuction.totalBids} bid(s). Consider stopping the auction instead.
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete Auction
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Stop Auction Confirmation Dialog */}
      <AlertDialog open={stopDialogOpen} onOpenChange={setStopDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <StopCircle className="h-5 w-5 text-orange-500" />
              Stop Auction
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to stop "{selectedAuction?.title}"? This will:
              <ul className="mt-2 list-disc list-inside space-y-1 text-sm">
                <li>Cancel the auction and mark it as "cancelled"</li>
                <li>Remove winning status from all bids</li>
                <li>Prevent any new bids from being placed</li>
              </ul>
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleStop}
              className="bg-orange-600 hover:bg-orange-700"
            >
              Stop Auction
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
