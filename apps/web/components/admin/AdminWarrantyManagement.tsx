"use client";

import { useState } from "react";
import { useQ<PERSON>y, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from "@repo/ui/components/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@repo/ui/components/dialog";
import { 
  ShieldCheck,
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Eye,
  Search,
  DollarSign,
  Calendar,
  TrendingUp,
  TrendingDown
} from "lucide-react";
import { Doc, Id } from "@repo/backend/convex/_generated/dataModel";
import { format } from "date-fns";

const statusConfig = {
  pending: {
    label: "Pending",
    color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
    icon: Clock,
  },
  under_review: {
    label: "Under Review",
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    icon: Eye,
  },
  needs_payment: {
    label: "Needs Payment",
    color: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
    icon: DollarSign,
  },
  in_process: {
    label: "In Process",
    color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
    icon: AlertTriangle,
  },
  completed_approved: {
    label: "Completed - Approved",
    color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    icon: CheckCircle,
  },
  completed_rejected: {
    label: "Completed - Rejected",
    color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    icon: XCircle,
  },
};

const urgencyConfig = {
  low: { label: "Low", color: "bg-gray-100 text-gray-800" },
  medium: { label: "Medium", color: "bg-yellow-100 text-yellow-800" },
  high: { label: "High", color: "bg-red-100 text-red-800" },
};

const verificationTypeConfig = {
  authenticity: { label: "Authenticity Only", color: "bg-blue-100 text-blue-800" },
  warranty_status: { label: "Warranty Status Only", color: "bg-green-100 text-green-800" },
  both: { label: "Both", color: "bg-purple-100 text-purple-800" },
};

interface WarrantyCheck extends Doc<"warrantyChecks"> {}

export function AdminWarrantyManagement() {
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedVerificationType, setSelectedVerificationType] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCheck, setSelectedCheck] = useState<WarrantyCheck | null>(null);
  const [updateData, setUpdateData] = useState({
    status: "",
    authenticationResult: "",
    warrantyStatus: "",
    adminNotes: "",
    authenticationReport: "",
    serviceFee: "",
  });

  const { user } = useAuth();

  // Queries
  const checks = useQuery(api.warranty.getAllWarrantyChecks, {
    status: selectedStatus !== "all" ? selectedStatus as any : undefined,
    verificationType: selectedVerificationType !== "all" ? selectedVerificationType as any : undefined,
    limit: 50,
  });

  const stats = useQuery(api.warranty.getWarrantyCheckStats);

  // Mutations
  const updateCheckStatus = useMutation(api.warranty.updateWarrantyCheckStatus);

  const handleStatusUpdate = async (checkId: Id<"warrantyChecks">) => {
    if (!updateData.status) {
      toast.error("Please select a status");
      return;
    }

    try {
      await updateCheckStatus({
        checkId,
        status: updateData.status as any,
        authenticationResult: updateData.authenticationResult as any || undefined,
        warrantyStatus: updateData.warrantyStatus as any || undefined,
        adminNotes: updateData.adminNotes || undefined,
        authenticationReport: updateData.authenticationReport || undefined,
        serviceFee: updateData.serviceFee ? parseFloat(updateData.serviceFee) : undefined,
      });

      toast.success("Warranty check updated successfully");
      setUpdateData({ 
        status: "", 
        authenticationResult: "", 
        warrantyStatus: "", 
        adminNotes: "", 
        authenticationReport: "", 
        serviceFee: "" 
      });
      setSelectedCheck(null);
    } catch (error: any) {
      toast.error("Failed to update warranty check: " + error.message);
    }
  };

  const filteredChecks = checks?.filter((check: WarrantyCheck) => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        check.requestNumber.toLowerCase().includes(query) ||
        check.userName?.toLowerCase().includes(query) ||
        check.userEmail?.toLowerCase().includes(query) ||
        check.productName.toLowerCase().includes(query) ||
        check.productBrand.toLowerCase().includes(query) ||
        check.contactName.toLowerCase().includes(query) ||
        check.contactEmail.toLowerCase().includes(query)
      );
    }
    return true;
  }) || [];

  if (!user || user.userType !== "admin") {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
        <p className="text-muted-foreground">You need admin privileges to access this page.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{stats.total}</div>
              <p className="text-xs text-muted-foreground">Total Checks</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
              <p className="text-xs text-muted-foreground">Pending</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{stats.underReview}</div>
              <p className="text-xs text-muted-foreground">Under Review</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-orange-600">{stats.needsPayment}</div>
              <p className="text-xs text-muted-foreground">Needs Payment</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-600">{stats.inProcess}</div>
              <p className="text-xs text-muted-foreground">In Process</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{stats.completedApproved}</div>
              <p className="text-xs text-muted-foreground">Approved</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-red-600">{stats.completedRejected}</div>
              <p className="text-xs text-muted-foreground">Rejected</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold text-green-600">{stats.approvalRate}%</div>
                  <p className="text-xs text-muted-foreground">Approval Rate</p>
                </div>
                <TrendingUp className="w-4 h-4 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Verification Type Breakdown */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{stats.authenticityOnly}</div>
              <p className="text-xs text-muted-foreground">Authenticity Only</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{stats.warrantyOnly}</div>
              <p className="text-xs text-muted-foreground">Warranty Status Only</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-600">{stats.both}</div>
              <p className="text-xs text-muted-foreground">Both Services</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search warranty checks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          
          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="under_review">Under Review</SelectItem>
              <SelectItem value="needs_payment">Needs Payment</SelectItem>
              <SelectItem value="in_process">In Process</SelectItem>
              <SelectItem value="completed_approved">Completed - Approved</SelectItem>
              <SelectItem value="completed_rejected">Completed - Rejected</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedVerificationType} onValueChange={setSelectedVerificationType}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="authenticity">Authenticity</SelectItem>
              <SelectItem value="warranty_status">Warranty Status</SelectItem>
              <SelectItem value="both">Both</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Warranty Checks Table */}
      <Card>
        <CardHeader>
          <CardTitle>Warranty Check Requests</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Request #</TableHead>
                  <TableHead>Product</TableHead>
                  <TableHead>Verification Type</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Urgency</TableHead>
                  <TableHead>Result</TableHead>
                  <TableHead>Submitted</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredChecks.map((check) => {
                  const StatusIcon = statusConfig[check.status as keyof typeof statusConfig]?.icon || Clock;
                  return (
                    <TableRow key={check._id}>
                      <TableCell className="font-medium">
                        {check.requestNumber}
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{check.productName}</div>
                          <div className="text-sm text-muted-foreground">{check.productBrand}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={verificationTypeConfig[check.verificationType as keyof typeof verificationTypeConfig]?.color}>
                          {verificationTypeConfig[check.verificationType as keyof typeof verificationTypeConfig]?.label}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{check.contactName}</div>
                          <div className="text-sm text-muted-foreground">{check.contactEmail}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={statusConfig[check.status as keyof typeof statusConfig]?.color}>
                          <StatusIcon className="w-3 h-3 mr-1" />
                          {statusConfig[check.status as keyof typeof statusConfig]?.label}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className={urgencyConfig[check.urgencyLevel as keyof typeof urgencyConfig]?.color}>
                          {urgencyConfig[check.urgencyLevel as keyof typeof urgencyConfig]?.label}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {check.authenticationResult ? (
                          <Badge variant={check.authenticationResult === "authentic" ? "default" : "destructive"}>
                            {check.authenticationResult === "authentic" ? "Authentic" : 
                             check.authenticationResult === "not_authentic" ? "Not Authentic" : "Inconclusive"}
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Calendar className="w-4 h-4 mr-1" />
                          {format(new Date(check.submittedAt), "MMM dd, yyyy")}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => setSelectedCheck(check)}
                            >
                              <Eye className="w-4 h-4 mr-1" />
                              View
                            </Button>
                          </DialogTrigger>
                          <CheckDetailModal
                            check={selectedCheck}
                            updateData={updateData}
                            setUpdateData={setUpdateData}
                            onUpdateStatus={handleStatusUpdate}
                          />
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
          
          {filteredChecks.length === 0 && (
            <div className="text-center py-8">
              <ShieldCheck className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No warranty checks found</h3>
              <p className="text-muted-foreground">
                {searchQuery ? "No warranty checks match your search criteria." : "No warranty check requests have been submitted yet."}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Check Detail Modal Component
interface CheckDetailModalProps {
  check: WarrantyCheck | null;
  updateData: {
    status: string;
    authenticationResult: string;
    warrantyStatus: string;
    adminNotes: string;
    authenticationReport: string;
    serviceFee: string;
  };
  setUpdateData: (data: any) => void;
  onUpdateStatus: (checkId: Id<"warrantyChecks">) => void;
}

function CheckDetailModal({
  check,
  updateData,
  setUpdateData,
  onUpdateStatus,
}: CheckDetailModalProps) {
  if (!check) return null;

  return (
    <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>Warranty Check Request - {check.requestNumber}</DialogTitle>
      </DialogHeader>
      
      <Tabs defaultValue="details" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="details">Request Details</TabsTrigger>
          <TabsTrigger value="manage">Manage Request</TabsTrigger>
        </TabsList>
        
        <TabsContent value="details" className="space-y-6">
          {/* Product Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Product Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Product Name</Label>
                <p className="text-sm text-muted-foreground">{check.productName}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Brand</Label>
                <p className="text-sm text-muted-foreground">{check.productBrand}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Model</Label>
                <p className="text-sm text-muted-foreground">{check.productModel || "N/A"}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Serial Number</Label>
                <p className="text-sm text-muted-foreground">{check.serialNumber || "N/A"}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Purchase Date</Label>
                <p className="text-sm text-muted-foreground">{check.purchaseDate || "N/A"}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Purchase Price</Label>
                <p className="text-sm text-muted-foreground">{check.purchasePrice ? `$${check.purchasePrice}` : "N/A"}</p>
              </div>
            </div>
          </div>

          {/* Verification Type */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Verification Request</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Verification Type</Label>
                <Badge className={verificationTypeConfig[check.verificationType as keyof typeof verificationTypeConfig]?.color}>
                  {verificationTypeConfig[check.verificationType as keyof typeof verificationTypeConfig]?.label}
                </Badge>
              </div>
              <div>
                <Label className="text-sm font-medium">Urgency Level</Label>
                <Badge className={urgencyConfig[check.urgencyLevel as keyof typeof urgencyConfig]?.color}>
                  {urgencyConfig[check.urgencyLevel as keyof typeof urgencyConfig]?.label}
                </Badge>
              </div>
            </div>
          </div>

          {/* Documentation */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Available Documentation</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <input type="checkbox" checked={check.hasOriginalReceipt} disabled />
                <span className="text-sm">Original Receipt</span>
              </div>
              <div className="flex items-center space-x-2">
                <input type="checkbox" checked={check.hasWarrantyCard} disabled />
                <span className="text-sm">Warranty Card</span>
              </div>
              <div className="flex items-center space-x-2">
                <input type="checkbox" checked={check.hasOriginalBox} disabled />
                <span className="text-sm">Original Box</span>
              </div>
              <div className="flex items-center space-x-2">
                <input type="checkbox" checked={check.hasCertificates} disabled />
                <span className="text-sm">Certificates</span>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Contact Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Name</Label>
                <p className="text-sm text-muted-foreground">{check.contactName}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Email</Label>
                <p className="text-sm text-muted-foreground">{check.contactEmail}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Phone</Label>
                <p className="text-sm text-muted-foreground">{check.contactPhone || "N/A"}</p>
              </div>
            </div>
            {check.concernsDescription && (
              <div>
                <Label className="text-sm font-medium">Concerns Description</Label>
                <p className="text-sm text-muted-foreground">{check.concernsDescription}</p>
              </div>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="manage" className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="status">Status</Label>
              <Select 
                value={updateData.status} 
                onValueChange={(value) => setUpdateData({...updateData, status: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="under_review">Under Review</SelectItem>
                  <SelectItem value="needs_payment">Needs Payment</SelectItem>
                  <SelectItem value="in_process">In Process</SelectItem>
                  <SelectItem value="completed_approved">Completed - Approved</SelectItem>
                  <SelectItem value="completed_rejected">Completed - Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="authenticationResult">Authentication Result</Label>
              <Select 
                value={updateData.authenticationResult} 
                onValueChange={(value) => setUpdateData({...updateData, authenticationResult: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select result" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="authentic">Authentic</SelectItem>
                  <SelectItem value="not_authentic">Not Authentic</SelectItem>
                  <SelectItem value="inconclusive">Inconclusive</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="warrantyStatus">Warranty Status</Label>
              <Select 
                value={updateData.warrantyStatus} 
                onValueChange={(value) => setUpdateData({...updateData, warrantyStatus: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select warranty status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="valid">Valid</SelectItem>
                  <SelectItem value="expired">Expired</SelectItem>
                  <SelectItem value="void">Void</SelectItem>
                  <SelectItem value="unknown">Unknown</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="serviceFee">Service Fee (USD)</Label>
              <Input
                id="serviceFee"
                type="number"
                placeholder="Enter service fee"
                value={updateData.serviceFee}
                onChange={(e) => setUpdateData({...updateData, serviceFee: e.target.value})}
              />
            </div>
            
            <div>
              <Label htmlFor="admin-notes">Admin Notes</Label>
              <Textarea
                id="admin-notes"
                placeholder="Internal notes about this check..."
                value={updateData.adminNotes}
                onChange={(e) => setUpdateData({...updateData, adminNotes: e.target.value})}
                rows={3}
              />
            </div>
            
            <div>
              <Label htmlFor="authentication-report">Authentication Report</Label>
              <Textarea
                id="authentication-report"
                placeholder="Detailed authentication report (visible to user)..."
                value={updateData.authenticationReport}
                onChange={(e) => setUpdateData({...updateData, authenticationReport: e.target.value})}
                rows={4}
              />
            </div>
            
            <Button 
              onClick={() => onUpdateStatus(check._id)} 
              disabled={!updateData.status}
              className="w-full"
            >
              Update Warranty Check
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </DialogContent>
  );
}
