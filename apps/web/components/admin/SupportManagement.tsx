"use client";

import { useState } from "react";
import { 
  MessageSquare, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Tag, 
  User, 
  Filter,
  Search,
  MoreHorizontal,
  AtSign,
  Edit
} from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Input } from "@repo/ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { formatDistanceToNow } from "date-fns";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";

export function SupportManagement() {
  const [filters, setFilters] = useState({
    status: "",
    priority: "",
    category: "",
    search: "",
  });

  const tickets = useQuery(api.supportTickets.getAllSupportTickets, {
    status: filters.status as any || undefined,
    priority: filters.priority as any || undefined,
    category: filters.category as any || undefined,
  });

  const adminUsers = useQuery(api.supportTickets.getAdminUsers);
  const assignTicket = useMutation(api.supportTickets.assignTicketToAdmin);
  const updateStatus = useMutation(api.supportTickets.updateTicketStatus);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-blue-100 text-blue-800";
      case "in_progress":
        return "bg-yellow-100 text-yellow-800";
      case "waiting_for_customer":
        return "bg-orange-100 text-orange-800";
      case "resolved":
        return "bg-green-100 text-green-800";
      case "closed":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800";
      case "high":
        return "bg-orange-100 text-orange-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "open":
        return <AlertCircle className="w-4 h-4" />;
      case "in_progress":
        return <Clock className="w-4 h-4" />;
      case "waiting_for_customer":
        return <MessageSquare className="w-4 h-4" />;
      case "resolved":
      case "closed":
        return <CheckCircle className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  const handleAssignTicket = async (ticketId: string, adminId: string) => {
    try {
      await assignTicket({ ticketId: ticketId as any, adminId: adminId as any });
    } catch (error) {
      console.error("Failed to assign ticket:", error);
    }
  };

  const handleUpdateStatus = async (ticketId: string, status: string) => {
    try {
      await updateStatus({ 
        ticketId: ticketId as any, 
        status: status as any 
      });
    } catch (error) {
      console.error("Failed to update status:", error);
    }
  };

  const filteredTickets = tickets?.filter((ticket: any) => {
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      return (
        ticket.subject.toLowerCase().includes(searchLower) ||
        ticket.description.toLowerCase().includes(searchLower) ||
        ticket.ticketNumber.toLowerCase().includes(searchLower) ||
        ticket.user?.name.toLowerCase().includes(searchLower) ||
        ticket.user?.email.toLowerCase().includes(searchLower)
      );
    }
    return true;
  });

  if (!tickets || !adminUsers) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground font-light">Loading support tickets...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-light text-foreground">Support Management</h2>
          <p className="text-muted-foreground font-light mt-1">
            Manage and assign support tickets to team members
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="bg-muted text-muted-foreground">
            {tickets.length} Total Tickets
          </Badge>
          <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
            {tickets.filter((t: any) => t.status === "open").length} Open
          </Badge>
        </div>
      </div>

      {/* Filters */}
      <Card className="rounded-xl border border-border bg-card shadow-sm hover:shadow-md transition-shadow duration-300">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-lg font-light text-foreground">
            <Filter className="w-5 h-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tickets..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="pl-10"
              />
            </div>
            
            <Select
              value={filters.status}
              onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Status</SelectItem>
                <SelectItem value="open">Open</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="waiting_for_customer">Waiting for Customer</SelectItem>
                <SelectItem value="resolved">Resolved</SelectItem>
                <SelectItem value="closed">Closed</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.priority}
              onValueChange={(value) => setFilters(prev => ({ ...prev, priority: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Priorities</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.category}
              onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Categories</SelectItem>
                <SelectItem value="general">General</SelectItem>
                <SelectItem value="technical">Technical</SelectItem>
                <SelectItem value="billing">Billing</SelectItem>
                <SelectItem value="order_issue">Order Issue</SelectItem>
                <SelectItem value="product_issue">Product Issue</SelectItem>
                <SelectItem value="seller_issue">Seller Issue</SelectItem>
                <SelectItem value="refund">Refund</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              onClick={() => setFilters({ status: "", priority: "", category: "", search: "" })}
            >
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Tickets List */}
      <div className="space-y-4">
        {filteredTickets?.map((ticket: any) => (
          <motion.div
            key={ticket._id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <CardTitle className="text-lg">{ticket.subject}</CardTitle>
                      <Badge variant="outline">#{ticket.ticketNumber}</Badge>
                    </div>
                    
                    <div className="flex items-center space-x-2 mb-3">
                      <Badge className={getStatusColor(ticket.status)}>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(ticket.status)}
                          <span className="capitalize">{ticket.status.replace('_', ' ')}</span>
                        </div>
                      </Badge>
                      <Badge className={getPriorityColor(ticket.priority)}>
                        <span className="capitalize">{ticket.priority}</span>
                      </Badge>
                      <Badge variant="outline">
                        <Tag className="w-3 h-3 mr-1" />
                        {ticket.category.replace('_', ' ')}
                      </Badge>
                    </div>

                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <User className="w-4 h-4" />
                        <span>{ticket.user?.name || 'Unknown User'}</span>
                      </div>
                      <span>•</span>
                      <span>{ticket.user?.email}</span>
                      <span>•</span>
                      <span>
                        {ticket._creationTime ? 
                          formatDistanceToNow(ticket._creationTime, { addSuffix: true }) : 
                          'Recently'
                        }
                      </span>
                    </div>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      
                      <DropdownMenuItem>
                        <AtSign className="w-4 h-4 mr-2" />
                        Assign to...
                      </DropdownMenuItem>
                      
                      <DropdownMenuItem>
                        <Edit className="w-4 h-4 mr-2" />
                        Update Status
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>

              <CardContent>
                <p className="text-muted-foreground mb-4 line-clamp-2">
                  {ticket.description}
                </p>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {ticket.assignedAdminId ? (
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground">Assigned to:</span>
                        <Badge variant="secondary">
                          {adminUsers.find((admin: any) => admin.user?._id === ticket.assignedAdminId)?.user?.name || 'Unknown'}
                        </Badge>
                      </div>
                    ) : (
                      <Badge variant="outline" className="text-orange-600">
                        Unassigned
                      </Badge>
                    )}
                  </div>

                  <div className="flex space-x-2">
                    <Select
                      value={ticket.assignedAdminId || "unassigned"}
                      onValueChange={(value) => handleAssignTicket(ticket._id, value === "unassigned" ? "" : value)}
                    >
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="Assign to..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="unassigned">Unassigned</SelectItem>
                        {adminUsers?.map((admin: any) => (
                          <SelectItem key={admin.user?._id} value={admin.user?._id || "unknown"}>
                            {admin.user?.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Select
                      value={ticket.status}
                      onValueChange={(value) => handleUpdateStatus(ticket._id, value)}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="open">Open</SelectItem>
                        <SelectItem value="in_progress">In Progress</SelectItem>
                        <SelectItem value="waiting_for_customer">Waiting</SelectItem>
                        <SelectItem value="resolved">Resolved</SelectItem>
                        <SelectItem value="closed">Closed</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {filteredTickets?.length === 0 && (
        <Card>
          <CardContent className="flex items-center justify-center h-32">
            <div className="text-center">
              <MessageSquare className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">No tickets found matching your filters</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
