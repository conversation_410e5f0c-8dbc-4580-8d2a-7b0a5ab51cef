"use client"

import * as React from "react"
import {
  LayoutDashboard,
  Users,
  Shield,
  MessageCircle,
  Trash2,
  FileText,
  LifeBuoy,
  Send,
  MessagesSquare,
  AlertTriangle,
  UserCheck,
  Building,
  Gavel,
  HandHeart,
  ShieldCheck,
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useAuth } from "@/hooks/useBetterAuth"
import { useQuery } from "convex/react"
import { api } from "@repo/backend/convex/_generated/api"

import { NavFooter } from "@/components/seller/NavFooter"
import { NavUser } from "@/components/seller/NavUser"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuBadge,
} from "@repo/ui/components/sidebar"

const data = {
  navMain: [
    {
      title: "Dashboard",
      url: "/admin",
      icon: LayoutDashboard,
    },
    {
      title: "User Management",
      url: "/admin/users",
      icon: Users,
    },
    {
      title: "Support Tickets",
      url: "/admin/support-tickets",
      icon: MessageCircle,
    },
    {
      title: "Disputes",
      url: "/admin/disputes",
      icon: AlertTriangle,
    },
    {
      title: "Middleman Service",
      url: "/admin/middleman",
      icon: HandHeart,
    },
    {
      title: "Warranty Check",
      url: "/admin/warranty",
      icon: ShieldCheck,
    },
    {
      title: "Auctions",
      url: "/admin/auctions",
      icon: Gavel,
    },
    {
      title: "Seller Applications",
      url: "/admin/seller-applications",
      icon: FileText,
    },
    {
      title: "Forum Management",
      url: "/admin/forum-management",
      icon: MessagesSquare,
    },
    {
      title: "Vendor Directory",
      url: "/admin/vendors",
      icon: Building,
    },
    {
      title: "Staff Directory",
      url: "/admin/staff",
      icon: UserCheck,
    },
    {
      title: "Cleanup",
      url: "/admin/cleanup",
      icon: Trash2,
    },
  ],
  navFooter: [
    {
      title: "Support",
      url: "/help",
      icon: LifeBuoy,
    },
    {
      title: "Feedback",
      url: "/feedback",
      icon: Send,
    },
  ],
}

export function AdminSidebar() {
  const { user } = useAuth()
  const pathname = usePathname()

  // Get real-time counts for badges
  const pendingApplications = useQuery(api.sellerApplicationsSimple.getAllApplications, {
    status: "pending"
  })

  const underReviewApplications = useQuery(api.sellerApplicationsSimple.getAllApplications, {
    status: "under_review"
  })

  const openTickets = useQuery(api.supportTickets.getAllSupportTickets, {
    status: "open"
  })

  return (
    <Sidebar variant="inset">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link href="/admin">
                <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                  <Shield className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">MODA</span>
                  <span className="truncate text-xs">Admin Portal</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      
      <SidebarContent>
        <SidebarMenu>
          {data.navMain.map((item, index) => (
            <SidebarMenuItem key={index}>
              <SidebarMenuButton
                asChild
                isActive={pathname === item.url}
                tooltip={item.title}
              >
                <Link href={item.url}>
                  <item.icon className="size-4" />
                  <span>{item.title}</span>
                  {item.title === "Seller Applications" && (pendingApplications || underReviewApplications) && (
                    (() => {
                      const pendingCount = pendingApplications?.length || 0;
                      const underReviewCount = underReviewApplications?.length || 0;
                      const totalCount = pendingCount + underReviewCount;
                      return totalCount > 0 ? (
                        <SidebarMenuBadge>
                          {totalCount}
                        </SidebarMenuBadge>
                      ) : null;
                    })()
                  )}
                  {item.title === "Support Tickets" && openTickets && openTickets?.length > 0 && (
                    <SidebarMenuBadge>
                      {openTickets?.length}
                    </SidebarMenuBadge>
                  )}
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
        <NavFooter items={data.navFooter} className="mt-auto" />
      </SidebarContent>

      <SidebarFooter>
        <NavUser user={user} />
      </SidebarFooter>
    </Sidebar>
  )
}


