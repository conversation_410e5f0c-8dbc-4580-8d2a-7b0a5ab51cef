"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { But<PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Badge } from "@repo/ui/components/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/dialog";
import { 
  Search, 
  Activity,
  TrendingUp,
  Users,
  Eye,
  Calendar,
  Clock,
  MousePointer,
  ShoppingCart,
  CreditCard,
  UserPlus,
  LogIn,
  LogOut,
  Download,
  RefreshCw,
  Filter,
  BarChart3,
  AlertCircle
} from "lucide-react";
import { toast } from "sonner";

interface UserActivityManagementProps {
  activeTab?: "overview" | "events" | "analytics";
}

export function UserActivityManagement({ activeTab = "overview" }: UserActivityManagementProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [eventTypeFilter, setEventTypeFilter] = useState<string>("all");
  const [timeRange, setTimeRange] = useState<"7d" | "30d" | "90d" | "1y">("7d");
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [showActivityDialog, setShowActivityDialog] = useState(false);

  // Get all users for activity tracking
  const allUsersData = useQuery(api.userManagement.getAllUsers, {
    limit: 100
  });

  // Get user activity for selected user
  const userActivity = useQuery(
    api.userManagement.getUserActivity,
    selectedUser ? {
      userId: selectedUser._id,
      limit: 50
    } : "skip"
  );

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEventTypeColor = (eventType: string) => {
    switch (eventType) {
      case "login": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "logout": return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
      case "product_view": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "product_purchase": return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
      case "subscription_upgrade": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "subscription_cancelled": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      case "profile_updated": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "seller_application": return "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  const getEventTypeIcon = (eventType: string) => {
    switch (eventType) {
      case "login": return LogIn;
      case "logout": return LogOut;
      case "product_view": return Eye;
      case "product_purchase": return ShoppingCart;
      case "subscription_upgrade": return CreditCard;
      case "subscription_cancelled": return CreditCard;
      case "profile_updated": return UserPlus;
      case "seller_application": return UserPlus;
      default: return Activity;
    }
  };

  const formatEventDescription = (eventType: string, metadata?: any) => {
    switch (eventType) {
      case "login": return "User signed in";
      case "logout": return "User signed out";
      case "product_view": return `Viewed product: ${metadata?.productTitle || 'Unknown'}`;
      case "product_purchase": return `Purchased product for ${metadata?.amount ? `$${metadata.amount}` : 'Unknown amount'}`;
      case "subscription_upgrade": return `Upgraded to ${metadata?.newPlan || 'Unknown'} plan`;
      case "subscription_cancelled": return "Cancelled subscription";
      case "profile_updated": return "Updated profile information";
      case "seller_application": return "Submitted seller application";
      default: return eventType.replace(/_/g, ' ');
    }
  };

  const handleViewUserActivity = (user: any) => {
    setSelectedUser(user);
    setShowActivityDialog(true);
  };

  const users = allUsersData?.users || [];

  // Filter users based on search
  const filteredUsers = users.filter((user: any) => 
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-black dark:text-white">
            User Activity
          </h1>
          <p className="text-lg text-neutral-600 dark:text-neutral-400 mt-1">
            Monitor user behavior and platform engagement
          </p>
        </div>
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 days</SelectItem>
              <SelectItem value="30d">30 days</SelectItem>
              <SelectItem value="90d">90 days</SelectItem>
              <SelectItem value="1y">1 year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400 flex items-center gap-2">
              <Users className="w-4 h-4" />
              Active Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {users.filter((u: any) => !u.isDeleted).length}
            </div>
            <Badge className="mt-1">
              Total users
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400 flex items-center gap-2">
              <Activity className="w-4 h-4" />
              Daily Active
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {Math.floor(users.length * 0.3)} {/* Simulated DAU */}
            </div>
            <Badge variant="secondary" className="mt-1">
              Last 24h
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400 flex items-center gap-2">
              <MousePointer className="w-4 h-4" />
              Engagement Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              73% {/* Simulated engagement rate */}
            </div>
            <Badge variant="secondary" className="mt-1">
              Weekly avg
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-neutral-600 dark:text-neutral-400 flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              Growth Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              +12% {/* Simulated growth rate */}
            </div>
            <Badge variant="secondary" className="mt-1">
              Month over month
            </Badge>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue={activeTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="events">User Events</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Search and Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="w-5 h-5" />
                User Activity Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4 mb-6">
                <div className="flex-1">
                  <Input
                    placeholder="Search users by name or email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <Select value={eventTypeFilter} onValueChange={setEventTypeFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Event Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Events</SelectItem>
                    <SelectItem value="login">Logins</SelectItem>
                    <SelectItem value="product_view">Product Views</SelectItem>
                    <SelectItem value="product_purchase">Purchases</SelectItem>
                    <SelectItem value="subscription_upgrade">Upgrades</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Users Table */}
              {filteredUsers.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Last Active</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.map((user: any) => (
                      <TableRow key={user._id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{user.name}</div>
                            <div className="text-sm text-gray-500">{user.email}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {user.userType}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge 
                            className={user.subscriptionStatus === 'active' 
                              ? "bg-green-100 text-green-800" 
                              : "bg-gray-100 text-gray-800"
                            }
                          >
                            {user.subscriptionStatus}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {user.updatedAt ? formatDate(user.updatedAt) : 'Never'}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleViewUserActivity(user)}
                          >
                            <Activity className="w-4 h-4 mr-2" />
                            View Activity
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8">
                  <AlertCircle className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    No users found matching your search
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="events" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Events</CardTitle>
              <CardDescription>
                Latest user activities across the platform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Activity className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600 dark:text-gray-400">
                  Select a user to view their activity events
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  Use the Overview tab to search and select users
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>User Engagement</CardTitle>
                <CardDescription>
                  Activity patterns and engagement metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Average Session Duration</span>
                    <span className="font-medium">12m 34s</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Pages per Session</span>
                    <span className="font-medium">4.2</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Bounce Rate</span>
                    <span className="font-medium">23%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Return Visitors</span>
                    <span className="font-medium">67%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Popular Actions</CardTitle>
                <CardDescription>
                  Most common user activities
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Eye className="w-4 h-4" />
                      <span>Product Views</span>
                    </div>
                    <span className="font-medium">1,234</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <LogIn className="w-4 h-4" />
                      <span>User Logins</span>
                    </div>
                    <span className="font-medium">856</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <ShoppingCart className="w-4 h-4" />
                      <span>Purchases</span>
                    </div>
                    <span className="font-medium">123</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <CreditCard className="w-4 h-4" />
                      <span>Subscription Changes</span>
                    </div>
                    <span className="font-medium">45</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* User Activity Dialog */}
      <Dialog open={showActivityDialog} onOpenChange={setShowActivityDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>User Activity Details</DialogTitle>
            <DialogDescription>
              Activity history for {selectedUser?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="max-h-96 overflow-y-auto">
            {userActivity && userActivity.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Event</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Timestamp</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {userActivity.map((event: any) => {
                    const EventIcon = getEventTypeIcon(event.eventType);
                    return (
                      <TableRow key={event._id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <EventIcon className="w-4 h-4" />
                            <Badge className={getEventTypeColor(event.eventType)}>
                              {event.eventType.replace(/_/g, ' ')}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          {formatEventDescription(event.eventType, event.metadata)}
                        </TableCell>
                        <TableCell>
                          {formatDate(event.timestamp)}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8">
                <Activity className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600 dark:text-gray-400">
                  No activity found for this user
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowActivityDialog(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

