"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { But<PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import { Switch } from "@repo/ui/components/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@repo/ui/components/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@repo/ui/components/alert-dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { 
  Plus, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Users,
  User,
  Eye,
  EyeOff,
  Building,
  Calendar,
  Mail,
  Phone,
  Linkedin,
  ArrowUpDown
} from "lucide-react";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import Image from "next/image";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { format } from "date-fns";

interface StaffFormData {
  name: string;
  role: string;
  imageUrl: string;
  bio: string;
  email: string;
  phone: string;
  linkedinUrl: string;
  department: string;
  startDate: number | undefined;
  isPublic: boolean;
  sortOrder: number | undefined;
}

const initialFormData: StaffFormData = {
  name: "",
  role: "",
  imageUrl: "",
  bio: "",
  email: "",
  phone: "",
  linkedinUrl: "",
  department: "",
  startDate: undefined,
  isPublic: true,
  sortOrder: undefined,
};

const departmentOptions = [
  "Executive",
  "Engineering",
  "Marketing",
  "Sales",
  "Operations",
  "Customer Success",
  "Finance",
  "HR",
  "Legal",
  "Other",
];

export function AdminStaffManagement() {
  const { user } = useAuth();
  const [selectedDepartment, setSelectedDepartment] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [includeInactive, setIncludeInactive] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingStaff, setEditingStaff] = useState<Id<"staff"> | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [staffToDelete, setStaffToDelete] = useState<{
    id: Id<"staff">;
    name: string;
  } | null>(null);
  const [formData, setFormData] = useState<StaffFormData>(initialFormData);

  // Queries
  const staff = useQuery(api.staff.getAllStaff, {
    department: selectedDepartment === "all" ? undefined : selectedDepartment,
    includeInactive,
  });

  const staffStats = useQuery(api.staff.getStaffStats);

  // Mutations
  const createStaff = useMutation(api.staff.createStaff);
  const updateStaff = useMutation(api.staff.updateStaff);
  const deleteStaff = useMutation(api.staff.deleteStaff);
  const seedStaff = useMutation(api.seedStaff.seedSampleStaff);

  const handleCreateStaff = async () => {
    if (!user?._id) return;

    try {
      await createStaff({
        ...formData,
        createdById: user._id,
      });

      toast.success("Staff member created successfully");
      setIsCreateDialogOpen(false);
      resetForm();
    } catch (error) {
      toast.error("Failed to create staff member");
      console.error("Create staff error:", error);
    }
  };

  const handleUpdateStaff = async () => {
    if (!editingStaff) return;

    try {
      await updateStaff({
        staffId: editingStaff,
        ...formData,
      });

      toast.success("Staff member updated successfully");
      setEditingStaff(null);
      resetForm();
    } catch (error) {
      toast.error("Failed to update staff member");
      console.error(error);
    }
  };

  const handleDeleteClick = (staffMember: { _id: Id<"staff">; name: string }) => {
    setStaffToDelete({
      id: staffMember._id,
      name: staffMember.name,
    });
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!staffToDelete) return;

    try {
      await deleteStaff({ staffId: staffToDelete.id });
      toast.success("Staff member deleted successfully");
    } catch (error) {
      toast.error("Failed to delete staff member");
      console.error(error);
    } finally {
      setDeleteDialogOpen(false);
      setStaffToDelete(null);
    }
  };

  const handleToggleActive = async (staffId: Id<"staff">, isActive: boolean) => {
    try {
      await updateStaff({
        staffId,
        isActive: !isActive,
      });
      toast.success(`Staff member ${!isActive ? "activated" : "deactivated"} successfully`);
    } catch (error) {
      toast.error("Failed to update staff member");
      console.error(error);
    }
  };

  const handleTogglePublic = async (staffId: Id<"staff">, isPublic: boolean) => {
    try {
      await updateStaff({
        staffId,
        isPublic: !isPublic,
      });
      toast.success(`Staff member ${!isPublic ? "made public" : "made private"} successfully`);
    } catch (error) {
      toast.error("Failed to update staff member");
      console.error(error);
    }
  };

  const handleSeedStaff = async () => {
    if (!user?._id) return;
    
    if (!confirm("This will create 8 sample staff members. Continue?")) return;

    try {
      await seedStaff({ createdById: user._id });
      toast.success("Sample staff members created successfully");
    } catch (error) {
      toast.error("Failed to create sample staff members");
      console.error("Seed staff error:", error);
    }
  };

  const resetForm = () => {
    setFormData(initialFormData);
  };

  const openEditDialog = (staffMember: any) => {
    setFormData({
      name: staffMember.name,
      role: staffMember.role,
      imageUrl: staffMember.imageUrl || "",
      bio: staffMember.bio || "",
      email: staffMember.email || "",
      phone: staffMember.phone || "",
      linkedinUrl: staffMember.linkedinUrl || "",
      department: staffMember.department || "",
      startDate: staffMember.startDate,
      isPublic: staffMember.isPublic,
      sortOrder: staffMember.sortOrder,
    });
    setEditingStaff(staffMember._id);
  };

  const filteredStaff = staff?.filter(member => 
    member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (member.department && member.department.toLowerCase().includes(searchTerm.toLowerCase()))
  ) || [];

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Staff Directory Management</h1>
          <p className="text-muted-foreground">
            Manage company team members and staff directory
          </p>
        </div>
        <div className="flex gap-2">
          {/* <Button variant="outline" onClick={handleSeedStaff}>
            Seed Sample Data
          </Button> */}
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => resetForm()}>
                <Plus className="h-4 w-4 mr-2" />
                Add Staff Member
              </Button>
            </DialogTrigger>
            <StaffDialog 
              isEdit={false}
              formData={formData}
              setFormData={setFormData}
              onSubmit={handleCreateStaff}
              onClose={() => setIsCreateDialogOpen(false)}
            />
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      {staffStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Staff</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{staffStats.totalStaff}</div>
              <p className="text-xs text-muted-foreground">
                All team members
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Staff</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{staffStats.activeStaff}</div>
              <p className="text-xs text-muted-foreground">
                Currently employed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Public Profiles</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{staffStats.publicStaff}</div>
              <p className="text-xs text-muted-foreground">
                Visible in directory
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Departments</CardTitle>
              <Building className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Object.keys(staffStats.staffByDepartment).length}
              </div>
              <p className="text-xs text-muted-foreground">
                Active departments
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search staff members..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Filter by department" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Departments</SelectItem>
            {departmentOptions.map((dept) => (
              <SelectItem key={dept} value={dept}>
                {dept}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <div className="flex items-center space-x-2">
          <Switch
            id="include-inactive"
            checked={includeInactive}
            onCheckedChange={setIncludeInactive}
          />
          <Label htmlFor="include-inactive">Include inactive</Label>
        </div>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>Staff Members</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Staff Member</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Start Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Visibility</TableHead>
                  <TableHead>Sort Order</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredStaff.map((member) => (
                  <TableRow key={member._id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-3">
                        {member.imageUrl && (
                          <div className="w-8 h-8 relative">
                            <Image
                              src={member.imageUrl}
                              alt={member.name}
                              fill
                              className="rounded-full object-cover"
                            />
                          </div>
                        )}
                        <div>
                          <div className="font-medium">{member.name}</div>
                          {member.email && (
                            <div className="text-xs text-muted-foreground">
                              {member.email}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{member.role}</TableCell>
                    <TableCell>
                      {member.department ? (
                        <Badge variant="outline">{member.department}</Badge>
                      ) : (
                        <span className="text-muted-foreground text-sm">Not set</span>
                      )}
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {member.startDate 
                        ? format(new Date(member.startDate), "MMM dd, yyyy")
                        : "Not set"
                      }
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Badge 
                          variant={member.isActive ? "default" : "secondary"}
                          className={member.isActive ? "bg-green-100 text-green-800" : ""}
                        >
                          {member.isActive ? "Active" : "Inactive"}
                        </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleActive(member._id, member.isActive)}
                        >
                          <ArrowUpDown className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleTogglePublic(member._id, member.isPublic)}
                        >
                          {member.isPublic ? (
                            <Eye className="h-4 w-4 text-green-600" />
                          ) : (
                            <EyeOff className="h-4 w-4 text-muted-foreground" />
                          )}
                        </Button>
                        <span className="text-xs text-muted-foreground">
                          {member.isPublic ? "Public" : "Private"}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {member.sortOrder ?? "Auto"}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => openEditDialog(member)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleDeleteClick(member)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {filteredStaff.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                No staff members found
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      {editingStaff && (
        <Dialog open={!!editingStaff} onOpenChange={() => setEditingStaff(null)}>
          <StaffDialog 
            isEdit={true}
            formData={formData}
            setFormData={setFormData}
            onSubmit={handleUpdateStaff}
            onClose={() => setEditingStaff(null)}
          />
        </Dialog>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete "{staffToDelete?.name}" from the staff directory. 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

interface StaffDialogProps {
  isEdit: boolean;
  formData: StaffFormData;
  setFormData: React.Dispatch<React.SetStateAction<StaffFormData>>;
  onSubmit: () => void;
  onClose: () => void;
}

function StaffDialog({
  isEdit,
  formData,
  setFormData,
  onSubmit,
  onClose,
}: StaffDialogProps) {
  return (
    <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>{isEdit ? "Edit Staff Member" : "Add New Staff Member"}</DialogTitle>
        <DialogDescription>
          {isEdit ? "Update staff member information" : "Add a new team member to the staff directory"}
        </DialogDescription>
      </DialogHeader>
      
      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Enter full name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="role">Role *</Label>
            <Input
              id="role"
              value={formData.role}
              onChange={(e) => setFormData({ ...formData, role: e.target.value })}
              placeholder="e.g., CEO, CTO, Marketing Director"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="department">Department</Label>
            <Select
              value={formData.department}
              onValueChange={(value) => setFormData({ ...formData, department: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select department" />
              </SelectTrigger>
              <SelectContent>
                {departmentOptions.map((dept) => (
                  <SelectItem key={dept} value={dept}>
                    {dept}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="startDate">Start Date</Label>
            <Input
              id="startDate"
              type="date"
              value={formData.startDate ? new Date(formData.startDate).toISOString().split('T')[0] : ""}
              onChange={(e) => setFormData({ 
                ...formData, 
                startDate: e.target.value ? new Date(e.target.value).getTime() : undefined 
              })}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="imageUrl">Profile Image URL</Label>
          <Input
            id="imageUrl"
            value={formData.imageUrl}
            onChange={(e) => setFormData({ ...formData, imageUrl: e.target.value })}
            placeholder="https://example.com/profile.jpg"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="bio">Bio</Label>
          <Textarea
            id="bio"
            value={formData.bio}
            onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
            placeholder="Brief description about the staff member"
            rows={3}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              placeholder="<EMAIL>"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              placeholder="+****************"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="linkedinUrl">LinkedIn URL</Label>
            <Input
              id="linkedinUrl"
              value={formData.linkedinUrl}
              onChange={(e) => setFormData({ ...formData, linkedinUrl: e.target.value })}
              placeholder="https://linkedin.com/in/username"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="sortOrder">Sort Order</Label>
            <Input
              id="sortOrder"
              type="number"
              value={formData.sortOrder || ""}
              onChange={(e) => setFormData({ 
                ...formData, 
                sortOrder: e.target.value ? parseInt(e.target.value) : undefined 
              })}
              placeholder="Optional ordering number"
            />
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="isPublic"
            checked={formData.isPublic}
            onCheckedChange={(checked) => setFormData({ ...formData, isPublic: checked })}
          />
          <Label htmlFor="isPublic">Show in public directory</Label>
        </div>
      </div>

      <DialogFooter>
        <Button type="button" variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button type="button" onClick={onSubmit}>
          {isEdit ? "Update Staff Member" : "Add Staff Member"}
        </Button>
      </DialogFooter>
    </DialogContent>
  );
}
