"use client";

import { useState } from "react";
import { useQ<PERSON>y, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from "@repo/ui/components/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@repo/ui/components/dialog";
import { 
  AlertTriangle, 
  Clock, 
  CheckCircle, 
  XCircle, 
  MessageSquare, 
  User, 
  Calendar,
  Search,
  Eye
} from "lucide-react";
import { Doc, Id } from "@repo/backend/convex/_generated/dataModel";
import { format } from "date-fns";

const topicLabels = {
  condition: "Product Condition",
  warranty: "Warranty Claim", 
  not_delivered: "Not Delivered",
  unresponsive_seller: "Unresponsive Seller",
  forum_issue: "Forum Issue",
  misc: "Miscellaneous"
};

const statusConfig = {
  open: {
    label: "Open",
    color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    icon: AlertTriangle,
  },
  in_progress: {
    label: "In Progress",
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    icon: Clock,
  },
  resolved: {
    label: "Resolved",
    color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    icon: CheckCircle,
  },
  closed: {
    label: "Closed",
    color: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
    icon: XCircle,
  },
};

const priorityConfig = {
  low: { label: "Low", color: "bg-gray-100 text-gray-800" },
  medium: { label: "Medium", color: "bg-yellow-100 text-yellow-800" },
  high: { label: "High", color: "bg-orange-100 text-orange-800" },
  urgent: { label: "Urgent", color: "bg-red-100 text-red-800" },
};

interface DisputeWithMessages extends Doc<"supportTickets"> {
  messageCount?: number;
  lastMessageAt?: number;
}

export function AdminDisputesPage() {
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedTopic, setSelectedTopic] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedDispute, setSelectedDispute] = useState<DisputeWithMessages | null>(null);
  const [newMessage, setNewMessage] = useState("");
  const [isInternal, setIsInternal] = useState(false);
  const [updateData, setUpdateData] = useState({
    status: "",
    priority: "",
    adminNotes: "",
    resolutionNotes: "",
  });

  const { user } = useAuth();

  // Queries
  const disputes = useQuery(api.disputes.getAllDisputes, {
    status: selectedStatus !== "all" ? selectedStatus as any : undefined,
    category: selectedTopic !== "all" ? selectedTopic as any : undefined,
    limit: 50,
  });

  const disputeMessages = useQuery(
    api.disputes.getDisputeMessages,
    selectedDispute ? {
      ticketId: selectedDispute._id,
      includeInternal: true,
    } : "skip"
  );

  // Mutations
  const updateDisputeStatus = useMutation(api.disputes.updateDisputeStatus);
  const sendMessage = useMutation(api.disputes.sendDisputeMessage);

  const handleStatusUpdate = async (ticketId: Id<"supportTickets">) => {
    if (!updateData.status) {
      toast.error("Please select a status");
      return;
    }

    try {
      await updateDisputeStatus({
        ticketId,
        status: updateData.status as any,
        priority: updateData.priority as any || undefined,
        adminNotes: updateData.adminNotes || undefined,
        resolutionNotes: updateData.resolutionNotes || undefined,
      });

      toast.success("Dispute updated successfully");
      setUpdateData({ status: "", priority: "", adminNotes: "", resolutionNotes: "" });
      setSelectedDispute(null);
    } catch (error: any) {
      toast.error("Failed to update dispute: " + error.message);
    }
  };

  const handleSendMessage = async () => {
    if (!selectedDispute || !newMessage.trim()) {
      return;
    }

    try {
      await sendMessage({
        ticketId: selectedDispute._id,
        message: newMessage.trim(),
        isInternal,
      });

      toast.success("Message sent successfully");
      setNewMessage("");
      setIsInternal(false);
    } catch (error: any) {
      toast.error("Failed to send message: " + error.message);
    }
  };

  const filteredDisputes = disputes?.filter((dispute: DisputeWithMessages) => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        dispute.userName?.toLowerCase().includes(query) ||
        dispute.userEmail?.toLowerCase().includes(query) ||
        dispute.description.toLowerCase().includes(query) ||
        (dispute.memberVendorInQuestion && dispute.memberVendorInQuestion.toLowerCase().includes(query))
      );
    }
    return true;
  }) || [];

  const getDisputeStats = () => {
    if (!disputes) return { open: 0, inProgress: 0, resolved: 0, total: 0 };
    
    return {
      open: disputes.filter((d: DisputeWithMessages) => d.status === "open").length,
      inProgress: disputes.filter((d: DisputeWithMessages) => d.status === "in_progress").length,
      resolved: disputes.filter((d: DisputeWithMessages) => d.status === "resolved").length,
      total: disputes.length,
    };
  };

  const stats = getDisputeStats();

  if (!user || user.userType !== "admin") {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
        <p className="text-muted-foreground">You need admin privileges to access this page.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dispute Management</h1>
          <p className="text-muted-foreground">
            Manage and resolve user disputes across the platform
          </p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Disputes</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              Across all categories
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Open Disputes</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.open}</div>
            <p className="text-xs text-muted-foreground">
              Requiring attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.inProgress}</div>
            <p className="text-xs text-muted-foreground">
              Being reviewed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Resolved</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.resolved}</div>
            <p className="text-xs text-muted-foreground">
              Successfully handled
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-wrap gap-4 items-end">
            <div className="flex-1 min-w-[200px]">
              <Label htmlFor="search">Search Disputes</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by user, email, or description..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="status-filter">Status</Label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="topic-filter">Topic</Label>
              <Select value={selectedTopic} onValueChange={setSelectedTopic}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Topics</SelectItem>
                  {Object.entries(topicLabels).map(([value, label]) => (
                    <SelectItem key={value} value={value}>{label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <Tabs value="disputes" className="space-y-4">
        <TabsList>
          <TabsTrigger value="disputes">Disputes ({filteredDisputes.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="disputes" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>All Disputes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredDisputes.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8">
                          <p className="text-muted-foreground">No disputes found matching your criteria.</p>
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredDisputes.map((dispute: DisputeWithMessages) => {
                        const status = statusConfig[dispute.status as keyof typeof statusConfig];
                        const priority = priorityConfig[dispute.priority as keyof typeof priorityConfig];

                        return (
                          <TableRow key={dispute._id}>
                            <TableCell>
                              <div className="text-sm">
                                <div className="font-medium">{dispute.userName}</div>
                                <div className="text-muted-foreground text-xs">
                                  {dispute.userEmail}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">
                                {topicLabels[dispute.category as keyof typeof topicLabels] || dispute.category}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge className={status.color}>{status.label}</Badge>
                            </TableCell>
                            <TableCell>
                              <Badge className={priority.color}>{priority.label}</Badge>
                            </TableCell>
                            <TableCell className="font-medium">
                              <div className="max-w-[300px] truncate">
                                {dispute.description}
                              </div>
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {format(new Date(dispute.updatedAt), "MMM dd, yyyy")}
                            </TableCell>
                            <TableCell>
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setSelectedDispute(dispute)}
                                  >
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                </DialogTrigger>
                                <DisputeDetailModal
                                  dispute={selectedDispute}
                                  messages={disputeMessages}
                                  newMessage={newMessage}
                                  setNewMessage={setNewMessage}
                                  isInternal={isInternal}
                                  setIsInternal={setIsInternal}
                                  updateData={updateData}
                                  setUpdateData={setUpdateData}
                                  onSendMessage={handleSendMessage}
                                  onUpdateStatus={() => handleStatusUpdate(dispute._id)}
                                />
                              </Dialog>
                            </TableCell>
                          </TableRow>
                        );
                      })
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

interface DisputeDetailModalProps {
  dispute: DisputeWithMessages | null;
  messages: any[] | undefined;
  newMessage: string;
  setNewMessage: (message: string) => void;
  isInternal: boolean;
  setIsInternal: (internal: boolean) => void;
  updateData: any;
  setUpdateData: (data: any) => void;
  onSendMessage: () => void;
  onUpdateStatus: () => void;
}

function DisputeDetailModal({
  dispute,
  messages,
  newMessage,
  setNewMessage,
  isInternal,
  setIsInternal,
  updateData,
  setUpdateData,
  onSendMessage,
  onUpdateStatus,
}: DisputeDetailModalProps) {
  if (!dispute) return null;

  const status = statusConfig[dispute.status as keyof typeof statusConfig];
  const priority = priorityConfig[dispute.priority as keyof typeof priorityConfig];

  return (
    <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>Dispute Details - {dispute.userName}</DialogTitle>
      </DialogHeader>
      
      <Tabs defaultValue="details" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="messages">Messages ({messages?.length || 0})</TabsTrigger>
          <TabsTrigger value="actions">Actions</TabsTrigger>
        </TabsList>
        
        <TabsContent value="details" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Status</Label>
              <div className="flex items-center gap-2 mt-1">
                <Badge className={status.color}>{status.label}</Badge>
                <Badge className={priority.color}>{priority.label}</Badge>
              </div>
            </div>
            <div>
              <Label>Topic</Label>
              <p className="mt-1">{topicLabels[dispute.category as keyof typeof topicLabels] || dispute.category}</p>
            </div>
            <div>
              <Label>Date of Issue</Label>
              <p className="mt-1">{dispute.dateOfIssue}</p>
            </div>
            <div>
              <Label>Submitted</Label>
              <p className="mt-1">{new Date(dispute.updatedAt).toLocaleString()}</p>
            </div>
          </div>
          
          {dispute.memberVendorInQuestion && (
            <div>
              <Label>Member/Vendor in Question</Label>
              <p className="mt-1">{dispute.memberVendorInQuestion}</p>
            </div>
          )}
          
          <div>
            <Label>Description</Label>
            <div className="mt-1 p-3 bg-muted rounded-lg">
              <p className="whitespace-pre-wrap">{dispute.description}</p>
            </div>
          </div>
          
          {dispute.adminNotes && (
            <div>
              <Label>Admin Notes</Label>
              <div className="mt-1 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                <p className="whitespace-pre-wrap">{dispute.adminNotes}</p>
              </div>
            </div>
          )}
          
          {dispute.resolutionNotes && (
            <div>
              <Label>Resolution Notes</Label>
              <div className="mt-1 p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                <p className="whitespace-pre-wrap">{dispute.resolutionNotes}</p>
              </div>
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="messages" className="space-y-4">
          <div className="max-h-96 overflow-y-auto space-y-3 border rounded-lg p-4">
            {messages?.length === 0 ? (
              <p className="text-center text-muted-foreground py-4">No messages yet</p>
            ) : (
              messages?.map((message) => (
                <div
                  key={message._id}
                  className={`p-3 rounded-lg ${
                    message.senderType === "admin"
                      ? "bg-blue-50 dark:bg-blue-950/20 ml-4"
                      : "bg-gray-50 dark:bg-gray-950/20 mr-4"
                  } ${message.isInternal ? "border-2 border-orange-200 dark:border-orange-800" : ""}`}
                >
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium">{message.senderName}</span>
                    <Badge variant={message.senderType === "admin" ? "default" : "secondary"}>
                      {message.senderType}
                    </Badge>
                    {message.isInternal && (
                      <Badge variant="outline" className="text-orange-600">Internal</Badge>
                    )}
                    <span className="text-xs text-muted-foreground ml-auto">
                      {new Date(message.sentAt).toLocaleString()}
                    </span>
                  </div>
                  <p className="whitespace-pre-wrap">{message.message}</p>
                </div>
              ))
            )}
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="internal"
                checked={isInternal}
                onChange={(e) => setIsInternal(e.target.checked)}
              />
              <Label htmlFor="internal">Internal message (not visible to user)</Label>
            </div>
            
            <Textarea
              placeholder="Type your message..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              rows={3}
            />
            
            <Button onClick={onSendMessage} disabled={!newMessage.trim()}>
              Send Message
            </Button>
          </div>
        </TabsContent>
        
        <TabsContent value="actions" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="status-update">Update Status</Label>
              <Select
                value={updateData.status}
                onValueChange={(value) => setUpdateData({...updateData, status: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="priority-update">Update Priority</Label>
              <Select
                value={updateData.priority}
                onValueChange={(value) => setUpdateData({...updateData, priority: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div>
            <Label htmlFor="admin-notes">Admin Notes</Label>
            <Textarea
              id="admin-notes"
              placeholder="Internal notes about this dispute..."
              value={updateData.adminNotes}
              onChange={(e) => setUpdateData({...updateData, adminNotes: e.target.value})}
              rows={3}
            />
          </div>
          
          <div>
            <Label htmlFor="resolution-notes">Resolution Notes</Label>
            <Textarea
              id="resolution-notes"
              placeholder="Notes about the resolution (visible to user)..."
              value={updateData.resolutionNotes}
              onChange={(e) => setUpdateData({...updateData, resolutionNotes: e.target.value})}
              rows={3}
            />
          </div>
          
          <Button onClick={onUpdateStatus} disabled={!updateData.status}>
            Update Dispute
          </Button>
        </TabsContent>
      </Tabs>
    </DialogContent>
  );
}
