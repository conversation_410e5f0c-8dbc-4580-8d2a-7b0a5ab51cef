"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { But<PERSON> } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@repo/ui/components/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@repo/ui/components/alert-dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@repo/ui/components/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import { 
  Plus, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Star, 
  StarOff,
  Users,
  Building,
  Phone,
  Mail,
  Globe,
  MapPin,
  Calendar,
  Award,
  Briefcase,
  Eye
} from "lucide-react";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import Image from "next/image";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { format } from "date-fns";

interface VendorFormData {
  companyName: string;
  website: string;
  description: string;
  services: string[];
  contactEmail: string;
  contactPhone: string;
  logoUrl: string;
  bannerUrl: string;
  category: "legal" | "marketing" | "design" | "automotive" | "cleaning" | "consulting" | "technology" | "other";
  location: string;
  establishedYear: number | undefined;
  employeeCount: string;
  specialties: string[];
  certifications: string[];
}

const initialFormData: VendorFormData = {
  companyName: "",
  website: "",
  description: "",
  services: [],
  contactEmail: "",
  contactPhone: "",
  logoUrl: "",
  bannerUrl: "",
  category: "other",
  location: "",
  establishedYear: undefined,
  employeeCount: "",
  specialties: [],
  certifications: [],
};

const categoryLabels = {
  legal: "Legal Services",
  marketing: "Marketing & PR",
  design: "Design & Creative",
  automotive: "Automotive",
  cleaning: "Cleaning Services",
  consulting: "Consulting",
  technology: "Technology",
  other: "Other",
};

const employeeCountOptions = [
  "1-10",
  "11-50",
  "51-100",
  "101-500",
  "500+",
];

export function AdminVendorManagement() {
  const { user } = useAuth();
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingVendor, setEditingVendor] = useState<Id<"vendors"> | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [vendorToDelete, setVendorToDelete] = useState<{
    id: Id<"vendors">;
    name: string;
  } | null>(null);
  const [formData, setFormData] = useState<VendorFormData>(initialFormData);
  const [servicesInput, setServicesInput] = useState("");
  const [specialtiesInput, setSpecialtiesInput] = useState("");
  const [certificationsInput, setCertificationsInput] = useState("");

  // Queries
  const vendors = useQuery(api.vendors.getAllVendors, {
    category: selectedCategory === "all" ? undefined : selectedCategory as any,
  });

  const vendorStats = useQuery(api.vendors.getVendorStats);

  // Mutations
  const createVendor = useMutation(api.vendors.createVendor);
  const updateVendor = useMutation(api.vendors.updateVendor);
  const deleteVendor = useMutation(api.vendors.deleteVendor);
  const seedVendors = useMutation(api.seedVendors.seedSampleVendors);

  const handleCreateVendor = async () => {
    if (!user?._id) return;

    try {
      // Debug the user ID to ensure we're using the correct Convex user ID
      console.log("Creating vendor with user ID:", user._id);
      
      await createVendor({
        ...formData,
        services: servicesInput.split(",").map(s => s.trim()).filter(Boolean),
        specialties: specialtiesInput.split(",").map(s => s.trim()).filter(Boolean),
        certifications: certificationsInput.split(",").map(s => s.trim()).filter(Boolean),
        createdById: user._id,
      });

      toast.success("Vendor created successfully");
      setIsCreateDialogOpen(false);
      resetForm();
    } catch (error) {
      toast.error("Failed to create vendor");
      console.error("Create vendor error:", error);
    }
  };

  const handleUpdateVendor = async () => {
    if (!editingVendor) return;

    try {
      await updateVendor({
        vendorId: editingVendor,
        ...formData,
        services: servicesInput.split(",").map(s => s.trim()).filter(Boolean),
        specialties: specialtiesInput.split(",").map(s => s.trim()).filter(Boolean),
        certifications: certificationsInput.split(",").map(s => s.trim()).filter(Boolean),
      });

      toast.success("Vendor updated successfully");
      setEditingVendor(null);
      resetForm();
    } catch (error) {
      toast.error("Failed to update vendor");
      console.error(error);
    }
  };

  const handleDeleteClick = (vendor: { _id: Id<"vendors">; companyName: string }) => {
    setVendorToDelete({
      id: vendor._id,
      name: vendor.companyName,
    });
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!vendorToDelete) return;

    try {
      await deleteVendor({ vendorId: vendorToDelete.id });
      toast.success("Vendor deleted successfully");
    } catch (error) {
      toast.error("Failed to delete vendor");
      console.error(error);
    } finally {
      setDeleteDialogOpen(false);
      setVendorToDelete(null);
    }
  };

  const handleToggleFeatured = async (vendorId: Id<"vendors">, isFeatured: boolean) => {
    try {
      await updateVendor({
        vendorId,
        isFeatured: !isFeatured,
      });
      toast.success(`Vendor ${!isFeatured ? "featured" : "unfeatured"} successfully`);
    } catch (error) {
      toast.error("Failed to update vendor");
      console.error(error);
    }
  };

  const handleSeedVendors = async () => {
    if (!user?._id) return;
    
    if (!confirm("This will create 5 sample vendors. Continue?")) return;

    try {
      // Debug the user ID to understand the issue
      console.log("User object:", user);
      console.log("User._id:", user._id);
      console.log("User.userId:", user.userId);
      
      // Use the Convex user ID, not the Better Auth user ID
      const convexUserId = user._id;
      await seedVendors({ createdById: convexUserId });
      toast.success("Sample vendors created successfully");
    } catch (error) {
      toast.error("Failed to create sample vendors");
      console.error("Seed vendors error:", error);
    }
  };

  const resetForm = () => {
    setFormData(initialFormData);
    setServicesInput("");
    setSpecialtiesInput("");
    setCertificationsInput("");
  };

  const openEditDialog = (vendor: any) => {
    setFormData({
      companyName: vendor.companyName,
      website: vendor.website || "",
      description: vendor.description,
      services: vendor.services || [],
      contactEmail: vendor.contactEmail || "",
      contactPhone: vendor.contactPhone || "",
      logoUrl: vendor.logoUrl || "",
      bannerUrl: vendor.bannerUrl || "",
      category: vendor.category,
      location: vendor.location || "",
      establishedYear: vendor.establishedYear,
      employeeCount: vendor.employeeCount || "",
      specialties: vendor.specialties || [],
      certifications: vendor.certifications || [],
    });
    setServicesInput((vendor.services || []).join(", "));
    setSpecialtiesInput((vendor.specialties || []).join(", "));
    setCertificationsInput((vendor.certifications || []).join(", "));
    setEditingVendor(vendor._id);
  };

  const filteredVendors = vendors?.filter(vendor => 
    vendor.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.description.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Vendor Directory Management</h1>
          <p className="text-muted-foreground">
            Manage service providers and business partners across the platform
          </p>
        </div>
        <div className="flex gap-2">
          {/* <Button variant="outline" onClick={handleSeedVendors}>
            Seed Sample Data
          </Button> */}
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => resetForm()}>
                <Plus className="h-4 w-4 mr-2" />
                Add Vendor
              </Button>
            </DialogTrigger>
            <VendorDialog 
              isEdit={false}
              formData={formData}
              setFormData={setFormData}
              servicesInput={servicesInput}
              setServicesInput={setServicesInput}
              specialtiesInput={specialtiesInput}
              setSpecialtiesInput={setSpecialtiesInput}
              certificationsInput={certificationsInput}
              setCertificationsInput={setCertificationsInput}
              onSubmit={handleCreateVendor}
              onClose={() => setIsCreateDialogOpen(false)}
            />
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      {vendorStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Vendors</CardTitle>
              <Building className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{vendorStats.totalVendors}</div>
              <p className="text-xs text-muted-foreground">
                Across all categories
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Approved Vendors</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{vendorStats.approvedVendors}</div>
              <p className="text-xs text-muted-foreground">
                Ready for directory
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Featured Partners</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{vendorStats.featuredVendors}</div>
              <p className="text-xs text-muted-foreground">
                Premium listings
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Categories</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Object.values(vendorStats.vendorsByCategory).filter(count => count > 0).length}
              </div>
              <p className="text-xs text-muted-foreground">
                With vendors listed
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search vendors..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {Object.entries(categoryLabels).map(([value, label]) => (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>Vendors</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Company</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Services</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Featured</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredVendors.map((vendor) => (
                  <TableRow key={vendor._id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-3">
                        {vendor.logoUrl && (
                          <div className="w-8 h-8 relative">
                            <Image
                              src={vendor.logoUrl}
                              alt={vendor.companyName}
                              fill
                              className="rounded-md object-cover"
                            />
                          </div>
                        )}
                        <div className="max-w-[200px]">
                          <div className="font-medium truncate">{vendor.companyName}</div>
                          {vendor.website && (
                            <div className="text-xs text-muted-foreground truncate">
                              {vendor.website.replace(/^https?:\/\//, '')}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{categoryLabels[vendor.category]}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <MapPin className="h-3 w-3 mr-1" />
                        {vendor.location || "Not specified"}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-[200px] truncate text-sm">
                        {vendor.services.slice(0, 2).join(", ")}
                        {vendor.services.length > 2 && ` +${vendor.services.length - 2} more`}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={vendor.isApproved ? "default" : "secondary"}
                        className={vendor.isApproved ? "bg-green-100 text-green-800" : ""}
                      >
                        {vendor.isApproved ? "Approved" : "Pending"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleToggleFeatured(vendor._id, vendor.isFeatured || false)}
                      >
                        {vendor.isFeatured ? (
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        ) : (
                          <StarOff className="h-4 w-4 text-muted-foreground" />
                        )}
                      </Button>
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {format(new Date(vendor._creationTime), "MMM dd, yyyy")}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => openEditDialog(vendor)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleDeleteClick(vendor)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {filteredVendors.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                No vendors found
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      {editingVendor && (
        <Dialog open={!!editingVendor} onOpenChange={() => setEditingVendor(null)}>
          <VendorDialog 
            isEdit={true}
            formData={formData}
            setFormData={setFormData}
            servicesInput={servicesInput}
            setServicesInput={setServicesInput}
            specialtiesInput={specialtiesInput}
            setSpecialtiesInput={setSpecialtiesInput}
            certificationsInput={certificationsInput}
            setCertificationsInput={setCertificationsInput}
            onSubmit={handleUpdateVendor}
            onClose={() => setEditingVendor(null)}
          />
        </Dialog>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the vendor "{vendorToDelete?.name}" and all associated data. 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

interface VendorDialogProps {
  isEdit: boolean;
  formData: VendorFormData;
  setFormData: React.Dispatch<React.SetStateAction<VendorFormData>>;
  servicesInput: string;
  setServicesInput: React.Dispatch<React.SetStateAction<string>>;
  specialtiesInput: string;
  setSpecialtiesInput: React.Dispatch<React.SetStateAction<string>>;
  certificationsInput: string;
  setCertificationsInput: React.Dispatch<React.SetStateAction<string>>;
  onSubmit: () => void;
  onClose: () => void;
}

function VendorDialog({
  isEdit,
  formData,
  setFormData,
  servicesInput,
  setServicesInput,
  specialtiesInput,
  setSpecialtiesInput,
  certificationsInput,
  setCertificationsInput,
  onSubmit,
  onClose,
}: VendorDialogProps) {
  return (
    <DialogContent className="sm:max-w-[600px]">
      <DialogHeader>
        <DialogTitle>{isEdit ? "Edit Vendor" : "Create New Vendor"}</DialogTitle>
        <DialogDescription>
          {isEdit ? "Update vendor information and services" : "Add a new vendor to the directory. This will be available to all platform users."}
        </DialogDescription>
      </DialogHeader>
      
      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="companyName">Company Name *</Label>
            <Input
              id="companyName"
              value={formData.companyName}
              onChange={(e) => setFormData({ ...formData, companyName: e.target.value })}
              placeholder="Enter company name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="category">Category *</Label>
            <Select
              value={formData.category}
              onValueChange={(value) => setFormData({ ...formData, category: value as any })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(categoryLabels).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description *</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            placeholder="Describe the vendor's services and expertise"
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="services">Services (comma-separated) *</Label>
          <Input
            id="services"
            value={servicesInput}
            onChange={(e) => setServicesInput(e.target.value)}
            placeholder="Web Design, SEO, Marketing, etc."
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="website">Website</Label>
            <Input
              id="website"
              value={formData.website}
              onChange={(e) => setFormData({ ...formData, website: e.target.value })}
              placeholder="https://example.com"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="location">Location</Label>
            <Input
              id="location"
              value={formData.location}
              onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              placeholder="City, State"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="contactEmail">Contact Email</Label>
            <Input
              id="contactEmail"
              type="email"
              value={formData.contactEmail}
              onChange={(e) => setFormData({ ...formData, contactEmail: e.target.value })}
              placeholder="<EMAIL>"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="contactPhone">Contact Phone</Label>
            <Input
              id="contactPhone"
              value={formData.contactPhone}
              onChange={(e) => setFormData({ ...formData, contactPhone: e.target.value })}
              placeholder="+****************"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="logoUrl">Logo URL</Label>
            <Input
              id="logoUrl"
              value={formData.logoUrl}
              onChange={(e) => setFormData({ ...formData, logoUrl: e.target.value })}
              placeholder="https://example.com/logo.png"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="bannerUrl">Banner URL</Label>
            <Input
              id="bannerUrl"
              value={formData.bannerUrl}
              onChange={(e) => setFormData({ ...formData, bannerUrl: e.target.value })}
              placeholder="https://example.com/banner.png"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="establishedYear">Established Year</Label>
            <Input
              id="establishedYear"
              type="number"
              value={formData.establishedYear || ""}
              onChange={(e) => setFormData({ 
                ...formData, 
                establishedYear: e.target.value ? parseInt(e.target.value) : undefined 
              })}
              placeholder="2020"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="employeeCount">Employee Count</Label>
            <Select
              value={formData.employeeCount}
              onValueChange={(value) => setFormData({ ...formData, employeeCount: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select range" />
              </SelectTrigger>
              <SelectContent>
                {employeeCountOptions.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="specialties">Specialties (comma-separated)</Label>
          <Input
            id="specialties"
            value={specialtiesInput}
            onChange={(e) => setSpecialtiesInput(e.target.value)}
            placeholder="Luxury Brands, E-commerce, Mobile Apps, etc."
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="certifications">Certifications (comma-separated)</Label>
          <Input
            id="certifications"
            value={certificationsInput}
            onChange={(e) => setCertificationsInput(e.target.value)}
            placeholder="Google Partner, Adobe Certified, etc."
          />
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={onClose}
        >
          Cancel
        </Button>
        <Button type="button" onClick={onSubmit}>
          {isEdit ? "Update Vendor" : "Create Vendor"}
        </Button>
      </div>
    </DialogContent>
  );
}
