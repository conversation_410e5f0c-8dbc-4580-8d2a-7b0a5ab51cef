"use client";

import { <PERSON><PERSON> } from "@repo/ui/components/button";
import { Lock, Search } from "lucide-react";
import { Input } from "@repo/ui/components/input";
import { ModeToggle } from "@repo/ui/components/mode-toggle";
import Image from "next/image";

interface LandingHeaderProps {
  onGetAccess: () => void;
  onSignIn: () => void;
  onSellerApplication: () => void;
}

export function LandingHeader({ onGetAccess, onSignIn, onSellerApplication }: LandingHeaderProps) {
  return (
    <header className="bg-background border-b border-border relative md:sticky top-0 z-50">
      <div className="container mx-auto px-6 py-4">
        {/* Navigation Bar */}
        <div className="flex items-center justify-between flex-col md:flex-row">
          {/* Logo - Left */}
            <div className="flex items-center space-x-2">
                <Image 
                  src="/logo.png" 
                  alt="Moda Logo" 
                  width={100} 
                  height={100} 
                />
            </div>

            {/* Search Bar - Center */}
            <div className="flex-1 max-w-md mx-8 w-full md:w-auto mt-4 md:mt-0 cursor-not-allowed group">
              <div className="relative">
                <Search className="text-primary absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5" />
                <Input 
                  placeholder="Subscribe to gain access"
                  className="w-full pl-10 pr-12 py-2 placeholder:text-primary group-hover:border-primary"
                  disabled
                />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <Lock className="w-4 h-4 text-primary" />
                </div>
              </div>
            </div>

          {/* Buttons - Right */}
          <div className="flex items-center space-x-3 w-full md:w-auto mt-4 md:mt-0">
            <Button 
              variant="outline" 
              className="w-full md:w-auto"
              onClick={onGetAccess}
            >
              Get Access
            </Button>
            <Button 
              variant="outline"
              className="w-full md:w-auto"
              onClick={onSignIn}
            >
              Sign in
            </Button>
            <ModeToggle />
          </div>
        </div>
      </div>
    </header>
  );
}
