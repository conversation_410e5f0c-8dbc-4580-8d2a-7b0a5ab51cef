"use client";

import { useState } from "react";
import { LandingHeader } from "./LandingHeader";
import { HeroBanner } from "./HeroBanner";
import { BrandCarousel } from "./BrandCarousel";
import { SampleItems } from "./SampleItems";
import { WhyBezelSection } from "./WhyBezelSection";
import { CallToAction } from "./CallToAction";
import { AuthModal } from "@/components/auth/auth-modal";
import { useAuth } from "@/hooks/useBetterAuth";
import { useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";

export function LandingPage() {
  const { user } = useAuth();
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<"login" | "signup">("login");
  const [defaultUserType, setDefaultUserType] = useState<"consumer" | "seller">("consumer");
  const [showApplicationDetails, setShowApplicationDetails] = useState(false);
  
  // Check if user has an application
  const userApplication = useQuery(
    api.sellerApplicationsSimple.getUserApplication,
    user?.email ? { email: user.email } : "skip"
  );

  const openAuthModal = (mode: "login" | "signup") => {
    setAuthMode(mode);
    setDefaultUserType("consumer");
    setAuthModalOpen(true);
  };

  const handleGetAccess = () => {
    openAuthModal("signup");
  };

  const handleSignIn = () => {
    openAuthModal("login");
  };

  const handleSellerApplication = () => {
    // Check if user already has an application
    if (user && userApplication) {
      // User has an application, toggle application details view
      setShowApplicationDetails(!showApplicationDetails);
    } else {
      // Open auth modal in signup mode with seller pre-selected
      setAuthMode("signup");
      setDefaultUserType("seller");
      setAuthModalOpen(true);
    }
  };

  const handleSubscribe = () => {
    openAuthModal("signup");
  };

  return (
    <div className="min-h-screen bg-background">
      <LandingHeader 
        onGetAccess={handleGetAccess}
        onSignIn={handleSignIn}
        onSellerApplication={handleSellerApplication}
      />
      
      <HeroBanner />
      
      <BrandCarousel />
      
      <SampleItems />
      
      <WhyBezelSection />
      
      <CallToAction 
        onSubscribe={handleSubscribe}
        onSellerApplication={handleSellerApplication}
        userApplication={userApplication}
        showDetails={showApplicationDetails}
      />



      {/* Footer */}
      <footer className="border-t border-border py-12 px-6 bg-primary">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-lg font-light uppercase tracking-wide text-primary-foreground">Moda</span>
              </div>
              <p className="text-primary-foreground/70 text-sm font-light">
                The premier destination for authenticated luxury goods.
              </p>
            </div>
            <div>
              <h4 className="font-light text-primary-foreground mb-4">Marketplace</h4>
              <ul className="space-y-2 text-sm text-primary-foreground/70">
                <li><a href="#" className="hover:text-primary-foreground font-light">Browse Listings</a></li>
                <li><a href="#" className="hover:text-primary-foreground font-light">Categories</a></li>
                <li><a href="/seller/directory" className="hover:text-primary-foreground font-light">Sellers</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-light text-primary-foreground mb-4">Support</h4>
              <ul className="space-y-2 text-sm text-primary-foreground/70">
                <li><a href="#" className="hover:text-primary-foreground font-light">Help Center</a></li>
                <li><a href="#" className="hover:text-primary-foreground font-light">Authentication</a></li>
                <li><a href="#" className="hover:text-primary-foreground font-light">Contact</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-light text-primary-foreground mb-4">Company</h4>
              <ul className="space-y-2 text-sm text-primary-foreground/70">
                <li><a href="#" className="hover:text-primary-foreground font-light">About</a></li>
                <li><a href="#" className="hover:text-primary-foreground font-light">Privacy</a></li>
                <li><a href="#" className="hover:text-primary-foreground font-light">Terms</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center text-sm text-primary-foreground/70 font-light">
            © 2025 MODA. All rights reserved.
          </div>
        </div>
      </footer>

      <AuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        defaultMode={authMode}
        defaultUserType={defaultUserType}
        social={false}
      />
    </div>
  );
}


