"use client";

import { Shield, CheckCircle, Users } from "lucide-react";
import Image from "next/image";

export function WhyBezelSection() {
  return (
    <section className="bg-accent">
      {/* 
        Use flex-col on mobile, flex-row (grid-cols-2) on lg+.
        This ensures the image is on the bottom on mobile, right on desktop.
      */}
      <div className="flex flex-col lg:grid lg:grid-cols-2 min-h-[600px]">
        {/* Left Content */}
        <div className="space-y-8 py-20 px-6 flex flex-col justify-center order-1 lg:order-none">
          <div>
            <h3 className="text-lg font-light text-accent-foreground mb-2">Why MODA?</h3>
            <h2 className="text-3xl font-light text-accent-foreground mb-6">
              We're luxury buyers like you that wanted something{" "}
              <span className="font-normal italic text-accent-foreground">better</span>
            </h2>
            <p className="text-accent-foreground/80 leading-relaxed font-light">
              We started MODA because we believe that everyone, first-time buyers to seasoned collectors, should be able to find what they want with complete confidence.
            </p>
          </div>

          {/* Feature Cards */}
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/80 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-accent-foreground" />
              </div>
              <h4 className="font-light text-accent-foreground mb-2">The best inventory</h4>
              <p className="text-sm text-accent-foreground/80 font-light">
                Shop thousands of the most collectible luxury goods on the planet from all of the top brands, all in one place.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary/80 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-accent-foreground" />
              </div>
              <h4 className="font-light text-accent-foreground mb-2">In-house authentication</h4>
              <p className="text-sm text-accent-foreground/80 font-light">
                Everything is sent to our in-house experts for multi-point MODA certification before it gets to you.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary/80 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-accent-foreground" />
              </div>
              <h4 className="font-light text-accent-foreground mb-2">Private Concierge</h4>
              <p className="text-sm text-accent-foreground/80 font-light">
                Whether you want to source a hard to find item or need recommendations, your private client advisor is here to help.
              </p>
            </div>
          </div>
        </div>

        {/* Right Image */}
        <div className="relative min-h-[320px] lg:min-h-0 order-2 lg:order-none">
          <Image
            src="/watch.png"
            alt="Luxury collector"
            className="w-full h-full object-cover"
            fill={true}
            priority
          />
          {/* Fade overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-accent via-accent/50 to-transparent pointer-events-none" />
          
          {/* Mobile fade overlay line at top */}
          <div className="absolute top-0 left-0 right-0 h-8 bg-gradient-to-b from-accent to-transparent pointer-events-none lg:hidden" />
        </div>
      </div>
    </section>
  );
}
