"use client";

import { cn } from "@repo/ui/lib/utils";

interface LoadingSpinnerProps {
  /** The loading message to display */
  message?: string;
  /** Size of the spinner */
  size?: "sm" | "md" | "lg";
  /** Whether to center the spinner in the full screen */
  fullScreen?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Color theme for the spinner */
  theme?: "light" | "dark" | "primary";
}

const sizeMap = {
  sm: "h-4 w-4",
  md: "h-8 w-8",
  lg: "h-12 w-12",
};

const themeMap = {
  light: "border-white",
  dark: "border-black dark:border-white",
  primary: "border-primary",
};

export function LoadingSpinner({
  message = "Loading...",
  size = "md",
  fullScreen = false,
  className,
  theme = "light",
}: LoadingSpinnerProps) {
  const containerClasses = cn(
    "flex items-center justify-center",
    fullScreen && "min-h-screen",
    className
  );

  const spinnerClasses = cn(
    "animate-spin rounded-full border-b-2 mx-auto",
    sizeMap[size],
    themeMap[theme]
  );

  const textClasses = cn(
    "mt-2",
    theme === "light" && "text-gray-400",
    theme === "dark" && "text-neutral-600 dark:text-neutral-400",
    theme === "primary" && "text-muted-foreground",
    size === "sm" && "text-sm",
    size === "lg" && "text-base font-light"
  );

  return (
    <div className={containerClasses}>
      <div className="text-center">
        <div className={spinnerClasses} />
        {message && <p className={textClasses}>{message}</p>}
      </div>
    </div>
  );
}

// Convenience components for common use cases
export function FullScreenLoader({ message, theme = "light" }: Pick<LoadingSpinnerProps, "message" | "theme">) {
  return <LoadingSpinner message={message} fullScreen theme={theme} />;
}

export function InlineLoader({ message = "Loading...", size = "sm" }: Pick<LoadingSpinnerProps, "message" | "size">) {
  return <LoadingSpinner message={message} size={size} theme="primary" />;
}

export function DashboardLoader({ message = "Loading dashboard..." }: Pick<LoadingSpinnerProps, "message">) {
  return (
    <LoadingSpinner 
      message={message} 
      size="lg" 
      theme="primary" 
      className="min-h-[60vh]" 
    />
  );
}
