"use client";

import { useState } from "react";
import { useUserFavorites, useFavoriteActions } from "@/hooks/useFavorites";
import { useWatchedAuctions, useAuctionWatchingActions } from "@/hooks/useAuctionWatching";
import { useAuth } from "@/hooks/useBetterAuth";
import { But<PERSON> } from "@repo/ui/components/button";
import { Card, CardContent } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Skeleton } from "@repo/ui/components/skeleton";
import { Input } from "@repo/ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import { 
  Heart, 
  Search, 
  SortAsc, 
  Star, 
  Package, 
  Eye, 
  ShoppingBag,
  X,
  Grid3X3,
  List,
  Filter,
  CheckCircle,
  Clock,
  AlertCircle,
  <PERSON><PERSON><PERSON>
} from "lucide-react";
import Link from "next/link";
import { MarketplaceHeader } from "../marketplace/MarketplaceHeader";
import { formatDistanceToNow } from "date-fns";

interface FavoritesPageProps {
  className?: string;
}

type ViewMode = "grid" | "list";
type SortBy = "newest" | "oldest" | "price_high" | "price_low" | "brand" | "status";
type StatusFilter = "all" | "available" | "sold" | "reserved";
type AuctionSortBy = "newest" | "oldest" | "ending_soon" | "bid_high" | "bid_low" | "brand";
type AuctionStatusFilter = "all" | "active" | "ended" | "sold";
type TabType = "products" | "auctions";

const CONDITION_COLORS = {
  new: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
  like_new: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
  good: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300",
  fair: "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300",
};

const CONDITION_LABELS = {
  new: "New",
  like_new: "Like New",
  good: "Good",
  fair: "Fair",
};

const STATUS_COLORS = {
  active: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
  sold: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300",
  reserved: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300",
  draft: "bg-neutral-100 text-neutral-800 dark:bg-neutral-900/20 dark:text-neutral-300",
};

const STATUS_LABELS = {
  active: "Available",
  sold: "Sold",
  reserved: "Reserved",
  draft: "Draft",
};

const STATUS_ICONS = {
  active: CheckCircle,
  sold: CheckCircle,
  reserved: Clock,
  draft: AlertCircle,
};

const AUCTION_STATUS_COLORS = {
  active: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
  ended: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300",
  sold: "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300",
  scheduled: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300",
};

const AUCTION_STATUS_LABELS = {
  active: "Live Auction",
  ended: "Ended",
  sold: "Sold",
  scheduled: "Scheduled",
};

const AUCTION_STATUS_ICONS = {
  active: Gavel,
  ended: Clock,
  sold: CheckCircle,
  scheduled: Clock,
};

export function FavoritesPage({ className }: FavoritesPageProps) {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>("products");
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<SortBy>("newest");
  const [auctionSortBy, setAuctionSortBy] = useState<AuctionSortBy>("newest");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [statusFilter, setStatusFilter] = useState<StatusFilter>("all");
  const [auctionStatusFilter, setAuctionStatusFilter] = useState<AuctionStatusFilter>("all");
  const [currentPage, setCurrentPage] = useState(0);
  
  const limit = 12;
  const offset = currentPage * limit;

  const { favorites, total, hasMore, isLoading } = useUserFavorites({
    limit,
    offset,
    includeSold: true,
  });

  const { watchedAuctions, total: auctionTotal, hasMore: auctionHasMore, isLoading: auctionIsLoading } = useWatchedAuctions({
    limit,
    offset,
    status: auctionStatusFilter,
  });

  const { removeFromFavorites } = useFavoriteActions();
  const { removeFromWatchlist } = useAuctionWatchingActions();

  // Filter favorites based on search and status
  const filteredFavorites = favorites.filter((product: any) => {
    // Status filter
    if (statusFilter !== "all" && product.status !== statusFilter) {
      return false;
    }
    
    // Search filter
    if (!searchQuery.trim()) return true;
    
    const searchTerm = searchQuery.toLowerCase();
    return (
      product.title?.toLowerCase().includes(searchTerm) ||
      product.brand?.toLowerCase().includes(searchTerm) ||
      product.category?.toLowerCase().includes(searchTerm)
    );
  });

  // Filter watched auctions based on search
  const filteredWatchedAuctions = watchedAuctions.filter((auction: any) => {
    if (!searchQuery.trim()) return true;
    
    const searchTerm = searchQuery.toLowerCase();
    return (
      auction.title?.toLowerCase().includes(searchTerm) ||
      auction.brand?.toLowerCase().includes(searchTerm) ||
      auction.category?.toLowerCase().includes(searchTerm)
    );
  });

  // Sort favorites
  const sortedFavorites = [...filteredFavorites].sort((a: any, b: any) => {
    switch (sortBy) {
      case "newest":
        return (b._creationTime || 0) - (a._creationTime || 0);
      case "oldest":
        return (a._creationTime || 0) - (b._creationTime || 0);
      case "price_high":
        return (b.price || 0) - (a.price || 0);
      case "price_low":
        return (a.price || 0) - (b.price || 0);
      case "brand":
        return (a.brand || "").localeCompare(b.brand || "");
      case "status":
        return (a.status || "").localeCompare(b.status || "");
      default:
        return 0;
    }
  });

  // Sort watched auctions
  const sortedWatchedAuctions = [...filteredWatchedAuctions].sort((a: any, b: any) => {
    switch (auctionSortBy) {
      case "newest":
        return (b.watchedAt || 0) - (a.watchedAt || 0);
      case "oldest":
        return (a.watchedAt || 0) - (b.watchedAt || 0);
      case "ending_soon":
        return (a.timeRemaining || 0) - (b.timeRemaining || 0);
      case "bid_high":
        return (b.currentBid || 0) - (a.currentBid || 0);
      case "bid_low":
        return (a.currentBid || 0) - (b.currentBid || 0);
      case "brand":
        return (a.brand || "").localeCompare(b.brand || "");
      default:
        return 0;
    }
  });

  const handleRemoveFavorite = async (productId: string) => {
    await removeFromFavorites(productId as any);
  };

  const handleRemoveWatchedAuction = async (auctionId: string) => {
    await removeFromWatchlist(auctionId as any);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatTimeRemaining = (ms: number) => {
    if (ms <= 0) return "Ended";
    
    const days = Math.floor(ms / (1000 * 60 * 60 * 24));
    const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days}d ${hours}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  const getStatusBadge = (product: any) => {
    const status = product.status || "active";
    const IconComponent = STATUS_ICONS[status as keyof typeof STATUS_ICONS] || CheckCircle;
    
    return (
      <Badge
        className={`absolute top-3 left-3 text-xs flex items-center gap-1 ${
          STATUS_COLORS[status as keyof typeof STATUS_COLORS]
                        }`}
      >
        <IconComponent className="w-3 h-3" />
        {STATUS_LABELS[status as keyof typeof STATUS_LABELS]}
      </Badge>
    );
  };

  const getAuctionStatusBadge = (auction: any) => {
    const status = auction.status || "active";
    const IconComponent = AUCTION_STATUS_ICONS[status as keyof typeof AUCTION_STATUS_ICONS] || Gavel;
    
    return (
      <Badge
        className={`absolute top-3 left-3 text-xs flex items-center gap-1 ${
          AUCTION_STATUS_COLORS[status as keyof typeof AUCTION_STATUS_COLORS]
        }`}
      >
        <IconComponent className="w-3 h-3" />
        {AUCTION_STATUS_LABELS[status as keyof typeof AUCTION_STATUS_LABELS]}
      </Badge>
    );
  };

  const getProductActions = (product: any) => {
    if (product.status === "sold") {
      return (
        <div className="p-4 space-y-3">
          <div className="text-center">
            <p className="text-sm text-neutral-500 dark:text-neutral-400 mb-2">
              This item was sold
            </p>
            {product.orderInfo && (
              <p className="text-xs text-neutral-400 dark:text-neutral-500">
                Sold {formatDistanceToNow(product.orderInfo.soldDate)} ago
              </p>
            )}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="flex-1">
              <Eye className="w-4 h-4 mr-2" />
              View Details
            </Button>
          </div>
        </div>
      );
    }

    if (product.status === "reserved") {
      return (
        <div className="p-4 space-y-3">
          <div className="text-center">
            <p className="text-sm text-neutral-500 dark:text-neutral-400">
              This item is reserved
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="flex-1">
              <Eye className="w-4 h-4 mr-2" />
              View Details
            </Button>
          </div>
        </div>
      );
    }

    // Available products - show normal actions
    return (
      <div className="p-4 space-y-3">
        <div className="flex gap-2">
          <Button asChild className="flex-1">
            <Link href={`/marketplace/product/${product._id}`}>
              <Eye className="w-4 h-4 mr-2" />
              View Details
            </Link>
          </Button>
        </div>
      </div>
    );
  };

  if (!user) {
    return (
      <div className="bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900 min-h-screen">
        <MarketplaceHeader />
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="p-8 text-center max-w-md">
            <CardContent>
              <Heart className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                Sign in to view favorites
              </h3>
              <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                Create an account to save your favorite luxury items.
              </p>
              <Button asChild>
                <Link href="/login">Sign In</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const getAuctionActions = (auction: any) => {
    if (auction.status === "sold") {
      return (
        <div className="p-4 space-y-3">
          <div className="text-center">
            <p className="text-sm text-neutral-500 dark:text-neutral-400 mb-2">
              This auction was sold
            </p>
            {auction.winningBid && (
              <p className="text-xs text-neutral-400 dark:text-neutral-500">
                Final bid: {formatCurrency(auction.winningBid)}
              </p>
            )}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="flex-1">
              <Eye className="w-4 h-4 mr-2" />
              View Details
            </Button>
          </div>
        </div>
      );
    }

    if (auction.status === "ended") {
      return (
        <div className="p-4 space-y-3">
          <div className="text-center">
            <p className="text-sm text-neutral-500 dark:text-neutral-400">
              This auction has ended
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="flex-1">
              <Eye className="w-4 h-4 mr-2" />
              View Results
            </Button>
          </div>
        </div>
      );
    }

    // Active auctions - show normal actions
    return (
      <div className="p-4 space-y-3">
        <div className="flex gap-2">
          <Button asChild className="flex-1">
            <Link href={`/auctions/${auction._id}`}>
              <Gavel className="w-4 h-4 mr-2" />
              View Auction
            </Link>
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900 min-h-screen">
      <MarketplaceHeader />
      <div className={`max-w-7xl mx-auto p-6 space-y-6 ${className}`}>
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black dark:text-white">My Collection</h1>
            <p className="text-neutral-600 dark:text-neutral-400 mt-1">
              {activeTab === "products" 
                ? (total > 0 ? `${total} saved ${total === 1 ? 'product' : 'products'}` : 'No saved products yet')
                : (auctionTotal > 0 ? `${auctionTotal} watched ${auctionTotal === 1 ? 'auction' : 'auctions'}` : 'No watched auctions yet')
              }
            </p>
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as TabType)}>
          <TabsList className="grid w-full max-w-md grid-cols-2">
            <TabsTrigger value="products" className="flex items-center gap-2">
              <Heart className="w-4 h-4" />
              Products ({total})
            </TabsTrigger>
            <TabsTrigger value="auctions" className="flex items-center gap-2">
              <Gavel className="w-4 h-4" />
              Auctions ({auctionTotal})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="products" className="space-y-6">
            {/* Controls */}
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                  {/* Search */}
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
                    <Input
                      placeholder="Search your favorites..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  <div className="flex items-center gap-3">
                    {/* Sort */}
                    <Select value={sortBy} onValueChange={(value) => setSortBy(value as SortBy)}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Sort by" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="newest">Newest First</SelectItem>
                        <SelectItem value="oldest">Oldest First</SelectItem>
                        <SelectItem value="price_high">Price: High to Low</SelectItem>
                        <SelectItem value="price_low">Price: Low to High</SelectItem>
                        <SelectItem value="brand">Brand A-Z</SelectItem>
                        <SelectItem value="status">Status</SelectItem>
                      </SelectContent>
                    </Select>

                    {/* Status Filter */}
                    <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as StatusFilter)}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Filter by status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="available">Available</SelectItem>
                        <SelectItem value="sold">Sold</SelectItem>
                        <SelectItem value="reserved">Reserved</SelectItem>
                      </SelectContent>
                    </Select>

                    {/* View Mode */}
                    <div className="flex border rounded-lg overflow-hidden">
                      <Button
                        variant={viewMode === "grid" ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setViewMode("grid")}
                        className="rounded-none"
                      >
                        <Grid3X3 className="w-4 h-4" />
                      </Button>
                      <Button
                        variant={viewMode === "list" ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setViewMode("list")}
                        className="rounded-none"
                      >
                        <List className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

        {/* Content */}
        {isLoading ? (
          // Loading skeleton
          <div className={viewMode === "grid" 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" 
            : "space-y-4"
          }>
            {Array.from({ length: limit }).map((_, i) => (
              <Card key={i}>
                <CardContent className={viewMode === "grid" ? "p-0" : "p-6"}>
                  {viewMode === "grid" ? (
                    <div className="space-y-4">
                      <Skeleton className="w-full h-48 rounded-t-lg" />
                      <div className="p-4 space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                        <Skeleton className="h-4 w-1/4" />
                      </div>
                    </div>
                  ) : (
                    <div className="flex gap-4">
                      <Skeleton className="w-24 h-24 rounded" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                        <Skeleton className="h-4 w-1/4" />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        ) : sortedFavorites.length === 0 ? (
          // Empty state
          <Card>
            <CardContent className="p-12 text-center">
              {searchQuery ? (
                <>
                  <Search className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                    No matching favorites
                  </h3>
                  <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                    Try adjusting your search or browse the marketplace for new items.
                  </p>
                  <Button onClick={() => setSearchQuery("")} variant="outline">
                    Clear Search
                  </Button>
                </>
              ) : (
                <>
                  <Heart className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                    No favorites yet
                  </h3>
                  <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                    Start browsing and heart items you love to save them here.
                  </p>
                  <Button asChild>
                    <Link href="/marketplace">Browse Marketplace</Link>
                  </Button>
                </>
              )}
            </CardContent>
          </Card>
        ) : (
          // Favorites grid/list
          <div className={viewMode === "grid" 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" 
            : "space-y-4"
          }>
            {sortedFavorites.map((product: any) => (
              <Card key={product._id} className="group hover:shadow-lg transition-shadow duration-200">
                {viewMode === "grid" ? (
                  // Grid view
                  <CardContent className="p-0">
                    <div className="relative overflow-hidden rounded-t-lg">
                      <Link href={`/marketplace/product/${product._id}`}>
                        <div className="relative aspect-square bg-neutral-100 dark:bg-neutral-800">
                          {product.images?.[0] ? (
                            <>
                              <img
                                src={product.images[0]}
                                alt={product.title}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                                onError={(e) => {
                                  const img = e.target as HTMLImageElement;
                                  const fallback = img.parentElement?.querySelector('.fallback-icon') as HTMLElement;
                                  img.style.display = 'none';
                                  if (fallback) {
                                    fallback.classList.remove('hidden');
                                    fallback.classList.add('flex');
                                  }
                                }}
                              />
                              <div className="absolute inset-0 items-center justify-center fallback-icon hidden">
                                <Package className="w-16 h-16 text-neutral-400" />
                              </div>
                            </>
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <Package className="w-16 h-16 text-neutral-400" />
                            </div>
                          )}
                        </div>
                      </Link>

                      {/* Remove from favorites button */}
                      <Button
                        size="sm"
                        variant="secondary"
                        className="absolute top-3 right-3 w-8 h-8 p-0 bg-white/80 dark:bg-neutral-900/80 hover:bg-white dark:hover:bg-neutral-900 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                        onClick={() => handleRemoveFavorite(product._id)}
                      >
                        <X className="w-4 h-4 text-red-500" />
                      </Button>

                      {/* Condition badge */}
                      <Badge
                        className={`absolute top-3 left-3 text-xs ${
                          CONDITION_COLORS[product.condition as keyof typeof CONDITION_COLORS]
                        }`}
                      >
                        {CONDITION_LABELS[product.condition as keyof typeof CONDITION_LABELS]}
                      </Badge>

                      {/* Status badge */}
                      {getStatusBadge(product)}
                    </div>

                    <div className="p-4 space-y-2">
                      <div className="space-y-1">
                        <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wide">
                          {product.brand}
                        </p>
                        <Link href={`/marketplace/product/${product._id}`}>
                          <h3 className="font-medium text-neutral-900 dark:text-neutral-100 line-clamp-2 leading-tight hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors">
                            {product.title}
                          </h3>
                        </Link>
                      </div>

                      <div className="flex items-center justify-between">
                        <p className="text-lg font-bold text-black dark:text-white">
                          {formatCurrency(product.price)}
                        </p>
                        
                        {product.seller && (
                          <div className="flex items-center gap-1">
                            <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                            <span className="text-xs text-neutral-600 dark:text-neutral-400">
                              {product.seller.rating?.toFixed(1) || "0.0"}
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="flex gap-2 pt-2">
                        {getProductActions(product)}
                      </div>
                    </div>
                  </CardContent>
                ) : (
                  // List view
                  <CardContent className="p-4">
                    <div className="flex gap-4">
                      <Link href={`/marketplace/product/${product._id}`} className="flex-shrink-0">
                        <div className="relative w-24 h-24 bg-neutral-100 dark:bg-neutral-800 rounded-lg overflow-hidden">
                          {product.images?.[0] ? (
                            <>
                              <img
                                src={product.images[0]}
                                alt={product.title}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  const img = e.target as HTMLImageElement;
                                  const fallback = img.parentElement?.querySelector('.fallback-icon') as HTMLElement;
                                  img.style.display = 'none';
                                  if (fallback) {
                                    fallback.classList.remove('hidden');
                                    fallback.classList.add('flex');
                                  }
                                }}
                              />
                              <div className="absolute inset-0 items-center justify-center fallback-icon hidden">
                                <Package className="w-8 h-8 text-neutral-400" />
                              </div>
                            </>
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <Package className="w-8 h-8 text-neutral-400" />
                            </div>
                          )}
                        </div>
                      </Link>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wide">
                              {product.brand}
                            </p>
                            <Link href={`/marketplace/product/${product._id}`}>
                              <h3 className="font-medium text-neutral-900 dark:text-neutral-100 line-clamp-2 hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors">
                                {product.title}
                              </h3>
                            </Link>
                            <p className="text-lg font-bold text-black dark:text-white mt-1">
                              {formatCurrency(product.price)}
                            </p>
                          </div>

                          <div className="flex items-center gap-2 ml-4">
                            <Badge
                              className={`text-xs ${
                                CONDITION_COLORS[product.condition as keyof typeof CONDITION_COLORS]
                              }`}
                            >
                              {CONDITION_LABELS[product.condition as keyof typeof CONDITION_LABELS]}
                            </Badge>
                            
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleRemoveFavorite(product._id)}
                              className="text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>

                        <div className="flex items-center justify-between mt-3">
                          <div className="flex items-center gap-4 text-sm text-neutral-500 dark:text-neutral-400">
                            {product.seller && (
                              <div className="flex items-center gap-1">
                                <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                                <span>{product.seller.rating?.toFixed(1) || "0.0"}</span>
                                <span>({product.seller.reviewCount || 0} reviews)</span>
                              </div>
                            )}
                            <span>
                              Added {formatDistanceToNow(new Date(product._creationTime || 0), { addSuffix: true })}
                            </span>
                          </div>

                          <div className="flex gap-2">
                            {getProductActions(product)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {total > limit && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              Showing {offset + 1} to {Math.min(offset + limit, total)} of {total} favorites
            </p>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                disabled={currentPage === 0}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={!hasMore}
              >
                Next
              </Button>
            </div>
          </div>
        )}
          </TabsContent>

          {/* Auctions Tab */}
          <TabsContent value="auctions" className="space-y-6">
            {/* Controls */}
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                  {/* Search */}
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
                    <Input
                      placeholder="Search watched auctions..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  <div className="flex items-center gap-3">
                    {/* Sort */}
                    <Select value={auctionSortBy} onValueChange={(value) => setAuctionSortBy(value as AuctionSortBy)}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Sort by" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="newest">Recently Watched</SelectItem>
                        <SelectItem value="ending_soon">Ending Soon</SelectItem>
                        <SelectItem value="bid_high">Highest Bid</SelectItem>
                        <SelectItem value="bid_low">Lowest Bid</SelectItem>
                        <SelectItem value="brand">Brand A-Z</SelectItem>
                      </SelectContent>
                    </Select>

                    {/* Status Filter */}
                    <Select value={auctionStatusFilter} onValueChange={(value) => setAuctionStatusFilter(value as AuctionStatusFilter)}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Filter by status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Auctions</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="ended">Ended</SelectItem>
                        <SelectItem value="sold">Sold</SelectItem>
                      </SelectContent>
                    </Select>

                    {/* View Mode */}
                    <div className="flex border rounded-lg overflow-hidden">
                      <Button
                        variant={viewMode === "grid" ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setViewMode("grid")}
                        className="rounded-none"
                      >
                        <Grid3X3 className="w-4 h-4" />
                      </Button>
                      <Button
                        variant={viewMode === "list" ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setViewMode("list")}
                        className="rounded-none"
                      >
                        <List className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Auction Content */}
            {auctionIsLoading ? (
              // Loading skeleton
              <div className={viewMode === "grid" 
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" 
                : "space-y-4"
              }>
                {Array.from({ length: limit }).map((_, i) => (
                  <Card key={i}>
                    <CardContent className={viewMode === "grid" ? "p-0" : "p-6"}>
                      <div className="space-y-4">
                        <Skeleton className="w-full h-48 rounded-t-lg" />
                        <div className="p-4 space-y-2">
                          <Skeleton className="h-4 w-3/4" />
                          <Skeleton className="h-3 w-1/2" />
                          <Skeleton className="h-4 w-1/4" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : sortedWatchedAuctions.length === 0 ? (
              // Empty state
              <Card>
                <CardContent className="p-12 text-center">
                  {searchQuery ? (
                    <>
                      <Search className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                        No matching auctions
                      </h3>
                      <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                        Try adjusting your search or browse auctions to watch new ones.
                      </p>
                      <Button onClick={() => setSearchQuery("")} variant="outline">
                        Clear Search
                      </Button>
                    </>
                  ) : (
                    <>
                      <Gavel className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                        No watched auctions yet
                      </h3>
                      <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                        Start browsing auctions and click the heart to watch them here.
                      </p>
                      <Button asChild>
                        <Link href="/auctions">Browse Auctions</Link>
                      </Button>
                    </>
                  )}
                </CardContent>
              </Card>
            ) : (
              // Auctions grid
              <div className={viewMode === "grid" 
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" 
                : "space-y-4"
              }>
                {sortedWatchedAuctions.map((auction: any) => (
                  <Card key={auction._id} className="group hover:shadow-lg transition-shadow duration-200">
                    <CardContent className="p-0">
                      <div className="relative overflow-hidden rounded-t-lg">
                        <Link href={`/auctions/${auction._id}`}>
                          <div className="relative aspect-square bg-neutral-100 dark:bg-neutral-800">
                            {auction.images?.[0] ? (
                              <img
                                src={auction.images[0]}
                                alt={auction.title}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <Gavel className="w-16 h-16 text-neutral-400" />
                              </div>
                            )}
                          </div>
                        </Link>

                        {/* Remove from watchlist button */}
                        <Button
                          size="sm"
                          variant="secondary"
                          className="absolute top-3 right-3 w-8 h-8 p-0 bg-white/80 dark:bg-neutral-900/80 hover:bg-white dark:hover:bg-neutral-900 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                          onClick={() => handleRemoveWatchedAuction(auction._id)}
                        >
                          <X className="w-4 h-4 text-red-500" />
                        </Button>

                        {/* Status badge */}
                        {getAuctionStatusBadge(auction)}
                      </div>

                      <div className="p-4 space-y-2">
                        <div className="space-y-1">
                          <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wide">
                            {auction.brand}
                          </p>
                          <Link href={`/auctions/${auction._id}`}>
                            <h3 className="font-medium text-neutral-900 dark:text-neutral-100 line-clamp-2 leading-tight hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors">
                              {auction.title}
                            </h3>
                          </Link>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-1">
                            <p className="text-sm text-neutral-600 dark:text-neutral-400">
                              Current Bid
                            </p>
                            <p className="text-lg font-bold text-black dark:text-white">
                              {formatCurrency(auction.currentBid)}
                            </p>
                          </div>
                          
                          {auction.timeRemaining > 0 && (
                            <div className="text-right">
                              <p className="text-xs text-neutral-500 dark:text-neutral-400">
                                Time Left
                              </p>
                              <p className="text-sm font-medium text-orange-600">
                                {formatTimeRemaining(auction.timeRemaining)}
                              </p>
                            </div>
                          )}
                        </div>

                        <div className="flex items-center justify-between text-xs text-neutral-500 dark:text-neutral-400">
                          <span>{auction.totalBids} bids</span>
                          <span>Watched {formatDistanceToNow(new Date(auction.watchedAt), { addSuffix: true })}</span>
                        </div>

                        <div className="pt-2">
                          {getAuctionActions(auction)}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Pagination for auctions */}
            {auctionTotal > limit && (
              <div className="flex items-center justify-between">
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Showing {offset + 1} to {Math.min(offset + limit, auctionTotal)} of {auctionTotal} watched auctions
                </p>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                    disabled={currentPage === 0}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={!auctionHasMore}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
