"use client";

import { useState } from "react";
import { useUserFavorites, useFavoriteActions } from "@/hooks/useFavorites";
import { useWatchedAuctions, useAuctionWatchingActions } from "@/hooks/useAuctionWatching";
import { useAuth } from "@/hooks/useBetterAuth";
import { Button } from "@repo/ui/components/button";
import { Card, CardContent } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Skeleton } from "@repo/ui/components/skeleton";
import { Input } from "@repo/ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import { 
  Heart, 
  Search, 
  Star, 
  Package, 
  Eye, 
  X,
  Grid3X3,
  List,
  CheckCircle,
  Clock,
  AlertCircle,
  Gavel
} from "lucide-react";
import Link from "next/link";
import { MarketplaceHeader } from "../marketplace/MarketplaceHeader";
import { formatDistanceToNow } from "date-fns";

interface FavoritesPageProps {
  className?: string;
}

type ViewMode = "grid" | "list";
type SortBy = "newest" | "oldest" | "price_high" | "price_low" | "brand" | "status";
type StatusFilter = "all" | "available" | "sold" | "reserved";
type AuctionSortBy = "newest" | "oldest" | "ending_soon" | "bid_high" | "bid_low" | "brand";
type AuctionStatusFilter = "all" | "active" | "ended" | "sold";
type TabType = "products" | "auctions";

const CONDITION_COLORS = {
  new: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
  like_new: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
  excellent: "bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-300",
  very_good: "bg-cyan-100 text-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-300",
  good: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300",
  fair: "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300",
};

const CONDITION_LABELS = {
  new: "New",
  like_new: "Like New",
  excellent: "Excellent",
  very_good: "Very Good",
  good: "Good",
  fair: "Fair",
};

const STATUS_COLORS = {
  active: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
  sold: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300",
  reserved: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300",
  draft: "bg-neutral-100 text-neutral-800 dark:bg-neutral-900/20 dark:text-neutral-300",
};

const STATUS_LABELS = {
  active: "Available",
  sold: "Sold",
  reserved: "Reserved",
  draft: "Draft",
};

const AUCTION_STATUS_COLORS = {
  active: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
  ended: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300",
  sold: "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300",
  scheduled: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300",
};

const AUCTION_STATUS_LABELS = {
  active: "Live Auction",
  ended: "Ended",
  sold: "Sold",
  scheduled: "Scheduled",
};

export function FavoritesPageWithAuctions({ className }: FavoritesPageProps) {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>("products");
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<SortBy>("newest");
  const [auctionSortBy, setAuctionSortBy] = useState<AuctionSortBy>("newest");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [statusFilter, setStatusFilter] = useState<StatusFilter>("all");
  const [auctionStatusFilter, setAuctionStatusFilter] = useState<AuctionStatusFilter>("all");
  const [currentPage, setCurrentPage] = useState(0);
  
  const limit = 12;
  const offset = currentPage * limit;

  // Product favorites
  const { favorites, total, hasMore, isLoading } = useUserFavorites({
    limit,
    offset,
    includeSold: true,
  });

  // Auction watchlist
  const { watchedAuctions, total: auctionTotal, hasMore: auctionHasMore, isLoading: auctionIsLoading } = useWatchedAuctions({
    limit,
    offset,
    status: auctionStatusFilter,
  });

  const { removeFromFavorites } = useFavoriteActions();
  const { removeFromWatchlist } = useAuctionWatchingActions();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatTimeRemaining = (ms: number) => {
    if (ms <= 0) return "Ended";
    
    const days = Math.floor(ms / (1000 * 60 * 60 * 24));
    const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days}d ${hours}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  const handleRemoveFavorite = async (productId: string) => {
    await removeFromFavorites(productId as any);
  };

  const handleRemoveWatchedAuction = async (auctionId: string) => {
    await removeFromWatchlist(auctionId as any);
  };

  if (!user) {
    return (
      <div className="bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900 min-h-screen">
        <MarketplaceHeader />
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="p-8 text-center max-w-md">
            <CardContent>
              <Heart className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                Sign in to view collection
              </h3>
              <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                Create an account to save your favorite items and watch auctions.
              </p>
              <Button asChild>
                <Link href="/login">Sign In</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-950 dark:to-neutral-900 min-h-screen">
      <MarketplaceHeader />
      <div className={`max-w-7xl mx-auto p-6 space-y-6 ${className}`}>
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black dark:text-white">My Collection</h1>
            <p className="text-neutral-600 dark:text-neutral-400 mt-1">
              {activeTab === "products" 
                ? (total > 0 ? `${total} saved ${total === 1 ? 'product' : 'products'}` : 'No saved products yet')
                : (auctionTotal > 0 ? `${auctionTotal} watched ${auctionTotal === 1 ? 'auction' : 'auctions'}` : 'No watched auctions yet')
              }
            </p>
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as TabType)}>
          <TabsList className="grid w-full max-w-md grid-cols-2">
            <TabsTrigger value="products" className="flex items-center gap-2">
              <Heart className="w-4 h-4" />
              Products ({total})
            </TabsTrigger>
            <TabsTrigger value="auctions" className="flex items-center gap-2">
              <Gavel className="w-4 h-4" />
              Auctions ({auctionTotal})
            </TabsTrigger>
          </TabsList>

          {/* Products Tab */}
          <TabsContent value="products" className="space-y-6">
            {/* Controls */}
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                  {/* Search */}
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
                    <Input
                      placeholder="Search your favorites..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  <div className="flex items-center gap-3">
                    {/* Sort */}
                    <Select value={sortBy} onValueChange={(value) => setSortBy(value as SortBy)}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Sort by" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="newest">Newest First</SelectItem>
                        <SelectItem value="oldest">Oldest First</SelectItem>
                        <SelectItem value="price_high">Price: High to Low</SelectItem>
                        <SelectItem value="price_low">Price: Low to High</SelectItem>
                        <SelectItem value="brand">Brand A-Z</SelectItem>
                        <SelectItem value="status">Status</SelectItem>
                      </SelectContent>
                    </Select>

                    {/* Status Filter */}
                    <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as StatusFilter)}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Filter by status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="available">Available</SelectItem>
                        <SelectItem value="sold">Sold</SelectItem>
                        <SelectItem value="reserved">Reserved</SelectItem>
                      </SelectContent>
                    </Select>

                    {/* View Mode */}
                    <div className="flex border rounded-lg overflow-hidden">
                      <Button
                        variant={viewMode === "grid" ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setViewMode("grid")}
                        className="rounded-none"
                      >
                        <Grid3X3 className="w-4 h-4" />
                      </Button>
                      <Button
                        variant={viewMode === "list" ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setViewMode("list")}
                        className="rounded-none"
                      >
                        <List className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Products Content - Simplified for now */}
            <div className="text-center py-12">
              <Heart className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                Product Favorites
              </h3>
              <p className="text-neutral-500 dark:text-neutral-400">
                Your favorite products will appear here
              </p>
            </div>
          </TabsContent>

          {/* Auctions Tab */}
          <TabsContent value="auctions" className="space-y-6">
            {/* Controls */}
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                  {/* Search */}
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4" />
                    <Input
                      placeholder="Search watched auctions..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  <div className="flex items-center gap-3">
                    {/* Sort */}
                    <Select value={auctionSortBy} onValueChange={(value) => setAuctionSortBy(value as AuctionSortBy)}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Sort by" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="newest">Recently Watched</SelectItem>
                        <SelectItem value="ending_soon">Ending Soon</SelectItem>
                        <SelectItem value="bid_high">Highest Bid</SelectItem>
                        <SelectItem value="bid_low">Lowest Bid</SelectItem>
                        <SelectItem value="brand">Brand A-Z</SelectItem>
                      </SelectContent>
                    </Select>

                    {/* Status Filter */}
                    <Select value={auctionStatusFilter} onValueChange={(value) => setAuctionStatusFilter(value as AuctionStatusFilter)}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Filter by status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Auctions</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="ended">Ended</SelectItem>
                        <SelectItem value="sold">Sold</SelectItem>
                      </SelectContent>
                    </Select>

                    {/* View Mode */}
                    <div className="flex border rounded-lg overflow-hidden">
                      <Button
                        variant={viewMode === "grid" ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setViewMode("grid")}
                        className="rounded-none"
                      >
                        <Grid3X3 className="w-4 h-4" />
                      </Button>
                      <Button
                        variant={viewMode === "list" ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setViewMode("list")}
                        className="rounded-none"
                      >
                        <List className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Auction Content */}
            {auctionIsLoading ? (
              // Loading skeleton
              <div className={viewMode === "grid" 
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" 
                : "space-y-4"
              }>
                {Array.from({ length: limit }).map((_, i) => (
                  <Card key={i}>
                    <CardContent className={viewMode === "grid" ? "p-0" : "p-6"}>
                      <div className="space-y-4">
                        <Skeleton className="w-full h-48 rounded-t-lg" />
                        <div className="p-4 space-y-2">
                          <Skeleton className="h-4 w-3/4" />
                          <Skeleton className="h-3 w-1/2" />
                          <Skeleton className="h-4 w-1/4" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : watchedAuctions.length === 0 ? (
              // Empty state
              <Card>
                <CardContent className="p-12 text-center">
                  {searchQuery ? (
                    <>
                      <Search className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                        No matching auctions
                      </h3>
                      <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                        Try adjusting your search or browse auctions to watch new ones.
                      </p>
                      <Button onClick={() => setSearchQuery("")} variant="outline">
                        Clear Search
                      </Button>
                    </>
                  ) : (
                    <>
                      <Gavel className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-400 mb-2">
                        No watched auctions yet
                      </h3>
                      <p className="text-neutral-500 dark:text-neutral-400 mb-6">
                        Start browsing auctions and click the heart to watch them here.
                      </p>
                      <Button asChild>
                        <Link href="/auctions">Browse Auctions</Link>
                      </Button>
                    </>
                  )}
                </CardContent>
              </Card>
            ) : (
              // Auctions grid
              <div className={viewMode === "grid" 
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" 
                : "space-y-4"
              }>
                {watchedAuctions.map((auction: any) => (
                  <Card key={auction._id} className="group hover:shadow-lg transition-shadow duration-200">
                    <CardContent className="p-0">
                      <div className="relative overflow-hidden rounded-t-lg">
                        <Link href={`/auctions/${auction._id}`}>
                          <div className="relative aspect-square bg-neutral-100 dark:bg-neutral-800">
                            {auction.images?.[0] ? (
                              <img
                                src={auction.images[0]}
                                alt={auction.title}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <Gavel className="w-16 h-16 text-neutral-400" />
                              </div>
                            )}
                          </div>
                        </Link>

                        {/* Remove from watchlist button */}
                        <Button
                          size="sm"
                          variant="secondary"
                          className="absolute top-3 right-3 w-8 h-8 p-0 bg-white/80 dark:bg-neutral-900/80 hover:bg-white dark:hover:bg-neutral-900 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                          onClick={() => handleRemoveWatchedAuction(auction._id)}
                        >
                          <X className="w-4 h-4 text-red-500" />
                        </Button>

                        {/* Status badge */}
                        <Badge
                          className={`absolute top-3 left-3 text-xs ${
                            AUCTION_STATUS_COLORS[auction.status as keyof typeof AUCTION_STATUS_COLORS]
                          }`}
                        >
                          {AUCTION_STATUS_LABELS[auction.status as keyof typeof AUCTION_STATUS_LABELS]}
                        </Badge>
                      </div>

                      <div className="p-4 space-y-2">
                        <div className="space-y-1">
                          <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wide">
                            {auction.brand}
                          </p>
                          <Link href={`/auctions/${auction._id}`}>
                            <h3 className="font-medium text-neutral-900 dark:text-neutral-100 line-clamp-2 leading-tight hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors">
                              {auction.title}
                            </h3>
                          </Link>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-1">
                            <p className="text-sm text-neutral-600 dark:text-neutral-400">
                              Current Bid
                            </p>
                            <p className="text-lg font-bold text-black dark:text-white">
                              {formatCurrency(auction.currentBid)}
                            </p>
                          </div>
                          
                          {auction.timeRemaining > 0 && (
                            <div className="text-right">
                              <p className="text-xs text-neutral-500 dark:text-neutral-400">
                                Time Left
                              </p>
                              <p className="text-sm font-medium text-orange-600">
                                {formatTimeRemaining(auction.timeRemaining)}
                              </p>
                            </div>
                          )}
                        </div>

                        <div className="flex items-center justify-between text-xs text-neutral-500 dark:text-neutral-400">
                          <span>{auction.totalBids} bids</span>
                          <span>Watched {formatDistanceToNow(new Date(auction.watchedAt), { addSuffix: true })}</span>
                        </div>

                        <div className="pt-2">
                          <Button asChild className="w-full" size="sm">
                            <Link href={`/auctions/${auction._id}`}>
                              <Gavel className="w-4 h-4 mr-2" />
                              View Auction
                            </Link>
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Pagination for auctions */}
            {auctionTotal > limit && (
              <div className="flex items-center justify-between">
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Showing {offset + 1} to {Math.min(offset + limit, auctionTotal)} of {auctionTotal} watched auctions
                </p>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                    disabled={currentPage === 0}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={!auctionHasMore}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
