"use client";

import { Badge } from "@repo/ui/components/badge";
import { Separator } from "@repo/ui/components/separator";
import { Truck, Shield, RotateCcw, Star } from "lucide-react";

interface Product {
  _id: string;
  title: string;
  brand: string;
  price: number;
  description: string;
  categoryId: string;
  condition: string;
  estimatedDeliveryDays?: number;
  views?: number;
  _creationTime: number;
}

interface ProductInfoPanelProps {
  product: Product;
}

const CONDITION_COLORS = {
  new: "bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300",
  like_new: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  excellent: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  good: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  fair: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
};

const CONDITION_LABELS = {
  new: "New",
  like_new: "Like New",
  excellent: "Excellent",
  good: "Good",
  fair: "Fair",
};

const CATEGORY_LABELS = {
  handbags: "Handbags",
  watches: "Watches",
  sneakers: "Sneakers",
  jewelry: "Jewelry",
  clothing: "Clothing",
  accessories: "Accessories",
  collectibles: "Collectibles",
};

export function ProductInfoPanel({ product }: ProductInfoPanelProps) {
  const estimatedDelivery = new Date();
  estimatedDelivery.setDate(estimatedDelivery.getDate() + (product.estimatedDeliveryDays || 3));

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="space-y-6">
        <div className="space-y-3">
          <p className="text-sm font-bold text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
            {product.brand}
          </p>
          <h1 className="text-4xl font-bold text-black dark:text-white leading-tight">
            {product.title}
          </h1>
        </div>

        {/* Price and Condition */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <p className="text-5xl font-bold text-black dark:text-white">
              ${product.price.toLocaleString()}
            </p>
            <div className="flex items-center space-x-3">
              <Badge
                className={`${CONDITION_COLORS[product.condition as keyof typeof CONDITION_COLORS]} font-medium px-3 py-1`}
              >
                {CONDITION_LABELS[product.condition as keyof typeof CONDITION_LABELS]}
              </Badge>
              <Badge variant="outline" className="font-medium px-3 py-1">
                {CATEGORY_LABELS[product.categoryId as keyof typeof CATEGORY_LABELS] || product.categoryId}
              </Badge>
            </div>
          </div>
        </div>

        {/* Delivery Info */}
        <div className="bg-neutral-50 dark:bg-neutral-900 rounded-2xl p-6 border border-neutral-200/50 dark:border-neutral-800/50">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">
              <Truck className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p className="font-semibold text-black dark:text-white">
                Estimated delivery: {formatDate(estimatedDelivery)}
              </p>
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                Free shipping on orders over $500
              </p>
            </div>
          </div>
        </div>
      </div>

      <Separator />

      {/* Description */}
      <div className="space-y-4">
        <h3 className="text-2xl font-bold text-black dark:text-white">
          Description
        </h3>
        <p className="text-lg text-neutral-600 dark:text-neutral-400 leading-relaxed">
          {product.description}
        </p>
      </div>

      <Separator />

      {/* Product Details */}
      <div className="space-y-4">
        <h3 className="text-2xl font-bold text-black dark:text-white">
          Product Details
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Brand</span>
              <span className="font-medium text-black dark:text-white">{product.brand}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Category</span>
              <span className="font-medium text-black dark:text-white">
                {CATEGORY_LABELS[product.categoryId as keyof typeof CATEGORY_LABELS] || product.categoryId}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Condition</span>
              <span className="font-medium text-black dark:text-white">
                {CONDITION_LABELS[product.condition as keyof typeof CONDITION_LABELS]}
              </span>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Views</span>
              <span className="font-medium text-black dark:text-white">{product.views || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Listed</span>
              <span className="font-medium text-black dark:text-white">
                {new Date(product._creationTime).toLocaleDateString()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-600 dark:text-neutral-400">Item ID</span>
              <span className="font-medium text-black dark:text-white font-mono text-sm">
                {product._id.slice(-8).toUpperCase()}
              </span>
            </div>
          </div>
        </div>
      </div>

      <Separator />

      {/* Trust Badges */}
      <div className="space-y-4">
        <h3 className="text-2xl font-bold text-black dark:text-white">
          Why Buy from MODA
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-y-3 p-4 bg-white dark:bg-neutral-900 rounded-2xl border border-neutral-200/50 dark:border-neutral-800/50 shadow-sm">
            <div className="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900 dark:to-green-800 rounded-2xl flex items-center justify-center mr-3">
              <Shield className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p className="text-sm font-bold text-black dark:text-white">
                Authenticity Guaranteed
              </p>
              <p className="text-xs text-neutral-500 dark:text-neutral-400">
                Every item verified
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-y-3 p-4 bg-white dark:bg-neutral-900 rounded-2xl border border-neutral-200/50 dark:border-neutral-800/50 shadow-sm">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 rounded-2xl flex items-center justify-center mr-3">
              <Truck className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p className="text-sm font-bold text-black dark:text-white">
                Fast & Secure Shipping
              </p>
              <p className="text-xs text-neutral-500 dark:text-neutral-400">
                Insured delivery
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-y-3 p-4 bg-white dark:bg-neutral-900 rounded-2xl border border-neutral-200/50 dark:border-neutral-800/50 shadow-sm">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900 dark:to-purple-800 rounded-2xl flex items-center justify-center mr-3">
              <RotateCcw className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <p className="text-sm font-bold text-black dark:text-white">
                Easy Returns
              </p>
              <p className="text-xs text-neutral-500 dark:text-neutral-400">
                7-day return policy
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
