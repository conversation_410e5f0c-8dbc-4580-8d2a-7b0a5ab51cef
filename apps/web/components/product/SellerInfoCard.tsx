"use client";

import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import { Avatar, AvatarFallback, AvatarImage } from "@repo/ui/components/avatar";
import { Star, Shield, MessageCircle, Store, Calendar } from "lucide-react";
import { ContactSellerModal } from "@/components/marketplace/ContactSellerModal";
import Link from "next/link";

interface Seller {
  _id: string;
  name: string;
  userType: string;
  avatar?: string;
  rating?: number;
  reviewCount?: number;
  totalSales?: number;
  memberSince?: number;
  isVerified?: boolean;
  responseTime?: string;
  location?: string;
  businessName?: string;
  verificationStatus?: string;
}

interface Product {
  _id: string;
  title: string;
  price: number;
  images: string[];
  brand: string;
}

interface SellerInfoCardProps {
  seller: Seller;
  product?: Product;
}

export function SellerInfoCard({ seller, product }: SellerInfoCardProps) {
  const rating = seller.rating || 0;
  const totalSales = seller.totalSales || 0;
  const memberSince = seller.memberSince || Date.now();
  const responseTime = seller.responseTime || "Response time not specified";
  const location = seller.location || "Location not specified";

  const formatMemberSince = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric'
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <div key="half" className="relative">
          <Star className="w-3 h-3 text-muted-foreground/50" />
          <div className="absolute inset-0 overflow-hidden w-1/2">
            <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
          </div>
        </div>
      );
    }

    const remainingStars = 5 - Math.ceil(rating);
    for (let i = 0; i < remainingStars; i++) {
      stars.push(
        <Star key={`empty-${i}`} className="w-3 h-3 text-muted-foreground/50" />
      );
    }

    return stars;
  };

  return (
    <div className="bg-card border-0 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-300">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="w-10 h-10 ring-1 ring-border">
              <AvatarImage src={seller.avatar} alt={seller.name} />
              <AvatarFallback className="bg-gradient-to-br from-muted/50 to-muted text-sm font-medium">
                {getInitials(seller.name)}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <div>
                  <h3 className="text-sm font-medium text-foreground">
                    {seller.businessName || seller.name}
                  </h3>
                  {seller.businessName && (
                    <p className="text-xs text-muted-foreground font-light">
                      {seller.name}
                    </p>
                  )}
                </div>
                {seller.isVerified && (
                  <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-1.5 py-0.5 text-xs">
                    <Shield className="w-2.5 h-2.5 mr-1" />
                    Verified
                  </Badge>
                )}
                {seller.verificationStatus === "pending" && (
                  <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 px-1.5 py-0.5 text-xs">
                    Pending Verification
                  </Badge>
                )}
              </div>
              <div className="flex items-center space-x-1">
                {rating > 0 ? (
                  <Link
                    href={`/seller/${seller._id}/reviews`}
                    className="flex items-center space-x-1 hover:opacity-80 transition-opacity cursor-pointer"
                  >
                    {renderStars(rating)}
                    <span className="text-xs font-light text-muted-foreground ml-1">
                      {rating.toFixed(1)} ({seller.reviewCount || 0} reviews)
                    </span>
                  </Link>
                ) : (
                  <span className="text-xs text-muted-foreground font-light">
                    No ratings yet
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-muted rounded-lg p-2 text-center">
            <div className="flex items-center justify-center mb-1">
              <Store className="w-4 h-4 text-muted-foreground" />
            </div>
            <p className="text-lg font-light text-foreground">{totalSales}</p>
            <p className="text-xs text-muted-foreground font-light">Total Sales</p>
          </div>
          
          <div className="bg-muted rounded-lg p-2 text-center">
            <div className="flex items-center justify-center mb-1">
              <Calendar className="w-4 h-4 text-muted-foreground" />
            </div>
            <p className="text-sm font-light text-foreground">
              {formatMemberSince(memberSince)}
            </p>
            <p className="text-xs text-muted-foreground font-light">Member Since</p>
          </div>
        </div>

        {/* Additional Info */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground font-light">Response time</span>
            <span className="text-xs font-medium text-foreground">{responseTime}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground font-light">Location</span>
            <span className="text-xs font-medium text-foreground">{location}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground font-light">Seller type</span>
            <Badge variant="outline" className="font-light text-xs">
              {seller.userType === 'seller' ? 'Professional' : 'Individual'}
            </Badge>
          </div>
        </div>

        {/* Actions */}
        <div className="space-y-2 pt-2 border-t border-border">
          <ContactSellerModal seller={seller} product={product}>
            <Button className="w-full bg-primary text-primary-foreground hover:bg-primary/90 transition-colors duration-300 rounded-lg h-8 font-light text-xs">
              <MessageCircle className="w-3 h-3 mr-1" />
              Contact Seller
            </Button>
          </ContactSellerModal>
          <Link href={`/seller/${seller._id}`} className="w-full" passHref>
            <Button
              variant="outline"
              className="w-full rounded-lg h-8 font-light transition-colors duration-300 text-xs"
            >
              <Store className="w-3 h-3 mr-1" />
              View All Items
            </Button>
          </Link>
        </div>

        {/* Trust Indicators */}
        {seller.isVerified && (
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-2">
            <div className="flex items-center space-x-1 mb-1">
              <Shield className="w-4 h-4 text-green-600 dark:text-green-400" />
              <span className="font-semibold text-green-800 dark:text-green-300 text-sm">Verified Seller</span>
            </div>
            <p className="text-xs text-green-700 dark:text-green-400">
              This seller has been verified by MODA and meets our quality standards.
            </p>
          </div>
        )}
        {seller.verificationStatus === "pending" && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-2">
            <div className="flex items-center space-x-1 mb-1">
              <Shield className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
              <span className="font-semibold text-yellow-800 dark:text-yellow-300 text-sm">Verification Pending</span>
            </div>
            <p className="text-xs text-yellow-700 dark:text-yellow-400">
              This seller's verification is currently under review by MODA.
            </p>
          </div>
        )}
        {!seller.isVerified && seller.verificationStatus !== "pending" && (
          <div className="bg-neutral-50 dark:bg-neutral-800 rounded-lg p-2">
            <div className="flex items-center space-x-1 mb-1">
              <Shield className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
              <span className="font-semibold text-neutral-800 dark:text-neutral-300 text-sm">New Seller</span>
            </div>
            <p className="text-xs text-neutral-700 dark:text-neutral-400">
              This seller is new to MODA. Exercise standard caution when purchasing.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
