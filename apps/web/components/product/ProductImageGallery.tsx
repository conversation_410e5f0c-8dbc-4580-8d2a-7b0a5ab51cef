"use client";

import { useState } from "react";
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, Maximize2 } from "lucide-react";
import { Button } from "@repo/ui/components/button";
import { cn } from "@repo/ui/lib/utils";
import Image from "next/image";

interface ProductImageGalleryProps {
  images: string[];
  title: string;
}

export function ProductImageGallery({ images, title }: ProductImageGalleryProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isZoomed, setIsZoomed] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const nextImage = () => {
    setCurrentImageIndex((prev) => 
      prev === images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? images.length - 1 : prev + 1
    );
  };

  const selectImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  if (!images || images.length === 0) {
    return (
      <div className="aspect-[4/3] bg-gradient-to-br from-neutral-100 to-neutral-200 dark:from-neutral-800 dark:to-neutral-900 rounded-lg flex items-center justify-center">
        <div className="text-center space-y-1">
          <div className="w-8 h-8 bg-neutral-200 dark:bg-neutral-700 rounded-md mx-auto flex items-center justify-center">
            <svg className="w-4 h-4 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400">No images</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {/* Main Image */}
      <div className="relative aspect-[4/3] bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-900 dark:to-neutral-800 rounded-lg overflow-hidden group shadow-md ring-1 ring-neutral-200/50 dark:ring-neutral-800/50">
        <Image
          src={images[currentImageIndex] || ""}
          alt={`${title} - Image ${currentImageIndex + 1}`}
          fill
          className={cn(
            "object-cover transition-all duration-500 ease-out",
            isZoomed ? "scale-150 cursor-zoom-out" : "cursor-zoom-in hover:scale-105"
          )}
          sizes="(max-width: 768px) 100vw, 40vw"
          priority
          onClick={() => setIsZoomed(!isZoomed)}
        />

        {/* Navigation Arrows */}
        {images.length > 1 && (
          <>
            <Button
              variant="secondary"
              size="sm"
              onClick={prevImage}
              className="absolute left-2 top-1/2 -translate-y-1/2 w-6 h-6 p-0 bg-white/95 dark:bg-black/95 hover:bg-white dark:hover:bg-black opacity-0 group-hover:opacity-100 transition-all duration-300 shadow-sm backdrop-blur-sm rounded-full border border-neutral-200/50 dark:border-neutral-800/50"
            >
              <ChevronLeft className="w-3 h-3" />
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={nextImage}
              className="absolute right-2 top-1/2 -translate-y-1/2 w-6 h-6 p-0 bg-white/95 dark:bg-black/95 hover:bg-white dark:hover:bg-black opacity-0 group-hover:opacity-100 transition-all duration-300 shadow-sm backdrop-blur-sm rounded-full border border-neutral-200/50 dark:border-neutral-800/50"
            >
              <ChevronRight className="w-3 h-3" />
            </Button>
          </>
        )}

        {/* Zoom Controls */}
        <div className="absolute top-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-300">
          <Button
            variant="secondary"
            size="sm"
            onClick={() => setIsZoomed(!isZoomed)}
            className="w-6 h-6 p-0 bg-white/95 dark:bg-black/95 hover:bg-white dark:hover:bg-black shadow-sm backdrop-blur-sm rounded-full border border-neutral-200/50 dark:border-neutral-800/50"
          >
            {isZoomed ? <ZoomOut className="w-2.5 h-2.5" /> : <ZoomIn className="w-2.5 h-2.5" />}
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => setIsFullscreen(true)}
            className="w-6 h-6 p-0 bg-white/95 dark:bg-black/95 hover:bg-white dark:hover:bg-black shadow-sm backdrop-blur-sm rounded-full border border-neutral-200/50 dark:border-neutral-800/50"
          >
            <Maximize2 className="w-2.5 h-2.5" />
          </Button>
        </div>

        {/* Image Counter */}
        {images.length > 1 && (
          <div className="absolute bottom-2 left-2 bg-black/80 text-white rounded-md px-1.5 py-0.5 backdrop-blur-sm">
            <span className="text-xs font-bold">
              {currentImageIndex + 1} / {images.length}
            </span>
          </div>
        )}
      </div>

      {/* Thumbnail Grid */}
      {images.length > 1 && (
        <div className="grid grid-cols-4 gap-1.5">
          {images.map((image, index) => (
            <button
              key={index}
              onClick={() => selectImage(index)}
              className={cn(
                "relative aspect-[4/3] rounded-md overflow-hidden border transition-all duration-300 hover:scale-105",
                index === currentImageIndex
                  ? "border-black dark:border-white ring-1 ring-black/20 dark:ring-white/20 shadow-md"
                  : "border-neutral-200 dark:border-neutral-700 hover:border-neutral-400 dark:hover:border-neutral-500 shadow-sm hover:shadow-md"
              )}
            >
              <Image
                src={image}
                alt={`${title} - Thumbnail ${index + 1}`}
                fill
                className="object-cover transition-transform duration-300 hover:scale-110"
                sizes="(max-width: 768px) 25vw, 10vw"
              />
              {index === currentImageIndex && (
                <div className="absolute inset-0 bg-black/10 dark:bg-white/10" />
              )}
            </button>
          ))}
        </div>
      )}

      {/* Fullscreen Modal */}
      {isFullscreen && (
        <div className="fixed inset-0 z-50 bg-black/95 flex items-center justify-center p-4">
          <div className="relative max-w-7xl max-h-full">
            <Image
              src={images[currentImageIndex] || ""}
              alt={`${title} - Fullscreen`}
              width={1200}
              height={1200}
              className="object-contain max-h-[90vh]"
            />
            <Button
              variant="secondary"
              onClick={() => setIsFullscreen(false)}
              className="absolute top-4 right-4 w-10 h-10 p-0 bg-white/20 hover:bg-white/30 text-white border-white/30 rounded-full"
            >
              ×
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
