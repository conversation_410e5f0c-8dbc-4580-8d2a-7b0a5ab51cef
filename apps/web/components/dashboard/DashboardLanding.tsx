"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Card } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { 
  ShoppingBag, 
  MessageSquare, 
  Users, 
  UserCheck, 
  Scale, 
  Building, 
  Crown, 
  Gavel, 
  FileCheck, 
  ShieldCheck,
  HandHeart
} from "lucide-react";

interface ThumbnailItem {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  category: "marketplace" | "forum" | "directory" | "services";
  badge?: string;
  comingSoon?: boolean;
  image?: string;
}

const thumbnailData: ThumbnailItem[] = [
  // Default Marketplace
  {
    id: "marketplace-default",
    title: "Marketplace",
    description: "Browse and buy luxury items",
    icon: <ShoppingBag className="w-8 h-8" />,
    href: "/marketplace",
    category: "marketplace",
    image: "/purse2.png"
  },
  // Moda Watch Club - Marketplace & Forum
  {
    id: "marketplace-moda-watch",
    title: "Moda Watch Club (Marketplace)",
    description: "Premium timepieces and luxury watches",
    icon: <ShoppingBag className="w-8 h-8" />,
    href: "/marketplace/moda-watch",
    category: "marketplace",
    badge: "Popular",
    image: "/watch.png"
  },
  {
    id: "forum-moda-watch",
    title: "Moda Watch Club (Forum)",
    description: "Discuss luxury watches and timepieces",
    icon: <MessageSquare className="w-8 h-8" />,
    href: "/marketplace/moda-watch/forum",
    category: "forum"
  },
  
  // Real Watch Buyers - Marketplace & Forum
  {
    id: "marketplace-real-watch-buyers",
    title: "Real Watch Buyers (Marketplace)",
    description: "Authentic watch trading and sales",
    icon: <ShoppingBag className="w-8 h-8" />,
    href: "/marketplace/real-watch-buyers",
    category: "marketplace",
    image: "/watch2.png"
  },
  {
    id: "forum-real-watch-buyers",
    title: "Real Watch Buyers (Forum)",
    description: "Community for serious watch collectors",
    icon: <MessageSquare className="w-8 h-8" />,
    href: "/marketplace/real-watch-buyers/forum",
    category: "forum"
  },
  
  // Moda Car Club - Marketplace & Forum
  {
    id: "marketplace-moda-car",
    title: "Moda Car Club (Marketplace)",
    description: "Luxury cars and collectible vehicles",
    icon: <ShoppingBag className="w-8 h-8" />,
    href: "/marketplace/moda-car",
    category: "marketplace",
    badge: "New",
    image: "/pants.png"
  },
  {
    id: "forum-moda-car",
    title: "Moda Car Club (Forum)",
    description: "Automotive enthusiasts community",
    icon: <MessageSquare className="w-8 h-8" />,
    href: "/marketplace/moda-car/forum",
    category: "forum"
  },
  {
    id: "references",
    title: "References",
    description: "Seller & buyer reviews",
    icon: <FileCheck className="w-8 h-8" />,
    href: "/references",
    category: "forum"
  },
  
  // Moda Lifestyle Club - Marketplace & Forum
  {
    id: "marketplace-moda-lifestyle",
    title: "Moda Lifestyle Club (Marketplace)",
    description: "Fashion, jewelry, and luxury lifestyle items",
    icon: <ShoppingBag className="w-8 h-8" />,
    href: "/marketplace/moda-lifestyle",
    category: "marketplace",
    image: "/purse.png"
  },
  {
    id: "forum-moda-lifestyle",
    title: "Moda Lifestyle Club (Forum)",
    description: "Luxury lifestyle and fashion discussions",
    icon: <MessageSquare className="w-8 h-8" />,
    href: "/marketplace/moda-lifestyle/forum",
    category: "forum"
  },
  
  // Moda Misc Club - Marketplace & Forum
  {
    id: "marketplace-moda-misc",
    title: "Moda Misc Club (Marketplace)",
    description: "Art, collectibles, and miscellaneous luxury items",
    icon: <ShoppingBag className="w-8 h-8" />,
    href: "/marketplace/moda-misc",
    category: "marketplace",
    image: "/card.png"
  },
  {
    id: "forum-moda-misc",
    title: "Moda Misc Club (Forum)",
    description: "General discussions and miscellaneous topics",
    icon: <MessageSquare className="w-8 h-8" />,
    href: "/marketplace/moda-misc/forum",
    category: "forum"
  },
  {
    id: "seller-directory",
    title: "Seller Directory",
    description: "Browse verified sellers",
    icon: <UserCheck className="w-8 h-8" />,
    href: "/seller/directory",
    category: "directory"
  },
  {
    id: "member-directory", 
    title: "Member Directory",
    description: "Connect with members",
    icon: <Users className="w-8 h-8" />,
    href: "/members/directory",
    category: "directory"
  },
  {
    id: "dispute-center",
    title: "Dispute Center",
    description: "Resolution & support",
    icon: <Scale className="w-8 h-8" />,
    href: "/disputes",
    category: "services"
  },
  {
    id: "vendor-directory",
    title: "Vendor Directory", 
    description: "Business partnerships",
    icon: <Building className="w-8 h-8" />,
    href: "/vendors",
    category: "directory"
  },
  {
    id: "staff-directory",
    title: "Staff Directory",
    description: "Platform team & support",
    icon: <Crown className="w-8 h-8" />,
    href: "/staff",
    category: "directory"
  },
  {
    id: "auctions",
    title: "Auctions",
    description: "Live bidding events",
    icon: <Gavel className="w-8 h-8" />,
    href: "/auctions",
    category: "services",
    badge: "Live"
  },
  {
    id: "warranty-check",
    title: "Warranty Check",
    description: "Product authenticity verification",
    icon: <ShieldCheck className="w-8 h-8" />,
    href: "/warranty",
    category: "services"
  },
  {
    id: "middleman-service",
    title: "Middleman Service",
    description: "Secure transaction mediation",
    icon: <HandHeart className="w-8 h-8" />,
    href: "/middleman",
    category: "services",
    badge: "New"
  }
];

const categoryColors = {
  marketplace: "bg-green-800/30 text-green-200 border-green-700/40",
  forum: "bg-green-800/30 text-green-200 border-green-700/40", 
  directory: "bg-green-800/30 text-green-200 border-green-700/40",
  services: "bg-green-800/30 text-green-200 border-green-700/40"
};

export function DashboardLanding() {
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  const filteredThumbnails = selectedCategory === "all" 
    ? thumbnailData 
    : thumbnailData.filter(item => item.category === selectedCategory);

  const handleThumbnailClick = (item: ThumbnailItem) => {
    router.push(item.href);
  };

  return (
    <div className="bg-card min-h-screen">
      {/* Dashboard Hero Section */}
      <div className="w-full relative bg-primary px-4 sm:px-6 lg:px-8 h-[180px] sm:h-[220px] md:h-[280px]">
        <div className="container py-4 sm:py-6 h-full flex flex-col justify-center max-w-7xl mx-auto">
          {/* Text content */}
          <div className="h-full flex flex-col justify-center text-center sm:text-left">
            <div className="text-xs sm:text-sm leading-6 opacity-60 tracking-widest font-medium text-accent uppercase mb-2">
              Welcome Back
            </div>
            <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl leading-tight font-bold text-white mb-3 sm:mb-4">
              Your MODA Dashboard
            </h1>
            <div className="text-xs sm:text-sm md:text-base leading-relaxed">
              <p className="text-secondary max-w-2xl mx-auto sm:mx-0">
                Your gateway to luxury commerce and community. Explore exclusive marketplaces, connect with fellow enthusiasts, and manage your luxury lifestyle.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Category Filters */}
      <div className="border-b bg-card sticky top-0 z-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 py-3 sm:py-4">
          {/* Mobile: Horizontal scroll */}
          <div className="overflow-x-auto scrollbar-hide">
            <div className="flex gap-2 sm:gap-3 min-w-max sm:justify-center lg:justify-start pb-1">
              {[
                { key: "all", label: "Home" },
                { key: "marketplace", label: "Marketplaces" },
                { key: "services", label: "Services" },
                { key: "forum", label: "Forums" },
                { key: "directory", label: "Directory" }
              ].map((category) => (
                <button
                  key={category.key}
                  onClick={() => setSelectedCategory(category.key)}
                  className={`px-3 sm:px-4 py-2 sm:py-2.5 rounded-full text-xs sm:text-sm font-medium transition-all whitespace-nowrap touch-manipulation ${
                    selectedCategory === category.key
                      ? "bg-primary text-primary-foreground shadow-sm"
                      : "bg-muted text-muted-foreground hover:bg-muted/80 active:bg-muted/90 border border-border"
                  }`}
                >
                  {category.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Thumbnail Grid */}
      <div className="px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        {/* Mobile: Single column grid, Tablet: 2 columns, Desktop: Horizontal scroll */}
        <div className="block sm:hidden">
          {/* Mobile: Vertical stack */}
          <div className="space-y-4">
            {filteredThumbnails.map((item) => (
              <Card
                key={item.id}
                className="relative group cursor-pointer transition-all duration-300 active:scale-95 bg-card border border-border touch-manipulation p-0"
                onClick={() => handleThumbnailClick(item)}
              >
                <div className="flex">
                  {/* Mobile: Compact horizontal layout */}
                  <div className="relative w-24 h-24 bg-gradient-to-br from-zinc-100 to-zinc-200 rounded-l-lg overflow-hidden flex-shrink-0">
                    {item.image ? (
                      <div className="absolute inset-0 bg-white">
                        <Image
                          src={item.image}
                          alt={item.title}
                          fill
                          className="object-cover opacity-80"
                        />
                      </div>
                    ) : (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className={`p-3 rounded-full ${categoryColors[item.category]}`}>
                          <div className="w-8 h-8">{item.icon}</div>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1 px-4 py-1">
                    <div className="flex justify-between items-start mb-1">
                      <h3 className="text-base font-semibold text-foreground line-clamp-1">
                        {item.title}
                      </h3>
                      {item.badge && (
                        <Badge variant="secondary" className="bg-white/90 text-zinc-700 text-xs ml-2 flex-shrink-0">
                          {item.badge}
                        </Badge>
                      )}
                    </div>
                    <p className="text-muted-foreground text-sm leading-relaxed line-clamp-2">
                      {item.description}
                    </p>
                    <Badge 
                      variant="outline" 
                      className={`${categoryColors[item.category]} text-xs mt-2`}
                    >
                      {item.category.charAt(0).toUpperCase() + item.category.slice(1)}
                    </Badge>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Tablet: 2-column grid */}
        <div className="hidden sm:block md:hidden">
          <div className="grid grid-cols-2 gap-4">
            {filteredThumbnails.map((item) => (
              <Card
                key={item.id}
                className="relative group cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-1 bg-card border border-border p-0"
                onClick={() => handleThumbnailClick(item)}
              >
                <div className="relative h-32 bg-gradient-to-br from-zinc-100 to-zinc-200 rounded-t-lg overflow-hidden">
                  {item.image ? (
                    <div className="absolute inset-0 bg-white">
                      <Image
                        src={item.image}
                        alt={item.title}
                        fill
                        className="object-cover opacity-80 group-hover:opacity-100 group-hover:scale-105 transition-all duration-300"
                      />
                    </div>
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className={`p-4 rounded-full ${categoryColors[item.category]}`}>
                        <div className="w-10 h-10">{item.icon}</div>
                      </div>
                    </div>
                  )}
                  
                  <div className="absolute top-2 left-2 flex gap-1 z-10">
                    {item.badge && (
                      <Badge variant="secondary" className="bg-white/90 text-zinc-700 text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </div>

                  <div className="absolute top-2 right-2 z-10">
                    <Badge 
                      variant="outline" 
                      className={`${categoryColors[item.category]} text-xs`}
                    >
                      {item.category.charAt(0).toUpperCase() + item.category.slice(1)}
                    </Badge>
                  </div>
                </div>

                <div className="p-4">
                  <h3 className="text-sm font-semibold text-foreground mb-1 line-clamp-2">
                    {item.title}
                  </h3>
                  <p className="text-muted-foreground text-xs leading-relaxed line-clamp-2">
                    {item.description}
                  </p>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Desktop: Horizontal scroll */}
        <div className="hidden md:block">
          <div className="overflow-x-auto scrollbar-hide">
            <div className="flex gap-6 pb-4" style={{ width: "max-content" }}>
              {filteredThumbnails.map((item) => (
                <Card
                  key={item.id}
                  className="relative group cursor-pointer transition-all duration-300 hover:shadow-2xl hover:-translate-y-1 w-80 flex-shrink-0 bg-card border border-border p-0"
                  onClick={() => handleThumbnailClick(item)}
                >
                  <div className="relative h-48 bg-gradient-to-br from-zinc-100 to-zinc-200 rounded-t-lg overflow-hidden">
                    {item.image ? (
                      <div className="absolute inset-0 bg-white">
                        <Image
                          src={item.image}
                          alt={item.title}
                          fill
                          className="object-cover opacity-80 group-hover:opacity-100 group-hover:scale-105 transition-all duration-300"
                        />
                      </div>
                    ) : (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className={`p-6 rounded-full ${categoryColors[item.category]}`}>
                          <div className="w-12 h-12">{item.icon}</div>
                        </div>
                      </div>
                    )}
                    
                    <div className="absolute top-3 left-3 flex gap-2 z-10">
                      {item.badge && (
                        <Badge variant="secondary" className="bg-white/90 text-zinc-700">
                          {item.badge}
                        </Badge>
                      )}
                    </div>

                    <div className="absolute top-3 right-3 z-10">
                      <Badge 
                        variant="outline" 
                        className={`${categoryColors[item.category]} text-xs`}
                      >
                        {item.category.charAt(0).toUpperCase() + item.category.slice(1)}
                      </Badge>
                    </div>

                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300" />
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-foreground mb-2 group-hover:text-muted-foreground transition-colors">
                      {item.title}
                    </h3>
                    <p className="text-muted-foreground text-sm leading-relaxed">
                      {item.description}
                    </p>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
