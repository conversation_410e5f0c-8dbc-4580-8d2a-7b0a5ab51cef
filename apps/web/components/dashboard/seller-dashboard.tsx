"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Plus, Package, DollarSign, TrendingUp, Edit, Trash2, Clock } from "lucide-react";

interface SellerDashboardProps {
  user: any;
}

export function SellerDashboard({ user }: SellerDashboardProps) {
  const [showAddProduct, setShowAddProduct] = useState(false);

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <header className="border-b border-zinc-800 bg-black/95 backdrop-blur">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-bold">MODA Seller</h1>
            <Badge className="bg-blue-900 text-blue-300">Verified Seller</Badge>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-zinc-400">Welcome, {user.name}</span>
            <Button 
              onClick={() => setShowAddProduct(true)}
              className="bg-white text-black hover:bg-zinc-200"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Product
            </Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        {/* Stats Cards */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-zinc-400">Total Products</CardTitle>
              <Package className="h-4 w-4 text-zinc-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">24</div>
              <p className="text-xs text-zinc-400">+2 from last month</p>
            </CardContent>
          </Card>
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-zinc-400">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-zinc-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">$45,231</div>
              <p className="text-xs text-zinc-400">+12% from last month</p>
            </CardContent>
          </Card>
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-zinc-400">Avg. Sale Price</CardTitle>
              <TrendingUp className="h-4 w-4 text-zinc-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">$1,885</div>
              <p className="text-xs text-zinc-400">+5% from last month</p>
            </CardContent>
          </Card>
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-zinc-400">Pending Offers</CardTitle>
              <Clock className="h-4 w-4 text-amber-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">3</div>
              <p className="text-xs text-amber-400">Awaiting response</p>
            </CardContent>
          </Card>
        </div>

        {/* Inventory Table */}
        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader>
            <CardTitle className="text-white">Your Inventory</CardTitle>
            <CardDescription className="text-zinc-400">
              Manage your luxury product listings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {sellerInventory.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-4 border border-zinc-800 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-zinc-800 rounded-lg flex items-center justify-center">
                      <Package className="w-6 h-6 text-zinc-400" />
                    </div>
                    <div>
                      <h3 className="font-medium text-white">{item.title}</h3>
                      <p className="text-sm text-zinc-400">{item.brand}</p>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge 
                          variant="outline" 
                          className={`border-${item.status === 'active' ? 'green' : 'yellow'}-700 text-${item.status === 'active' ? 'green' : 'yellow'}-400`}
                        >
                          {item.status}
                        </Badge>
                        <span className="text-sm text-zinc-400">{item.category}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className="font-semibold text-white">{item.price}</div>
                      <div className="text-sm text-zinc-400">{item.views} views</div>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="icon" variant="ghost" className="text-zinc-400 hover:text-white">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button size="icon" variant="ghost" className="text-zinc-400 hover:text-red-400">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Add Product Modal */}
      {showAddProduct && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div className="absolute inset-0 bg-black/80 backdrop-blur-sm" onClick={() => setShowAddProduct(false)} />
          <Card className="relative w-full max-w-2xl mx-4 bg-zinc-900 border-zinc-800 max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle className="text-white">Add New Product</CardTitle>
              <CardDescription className="text-zinc-400">
                List a new luxury item in your inventory
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-white">Product Title</Label>
                  <Input className="bg-zinc-800 border-zinc-700 text-white" placeholder="Hermès Birkin 35..." />
                </div>
                <div className="space-y-2">
                  <Label className="text-white">Brand</Label>
                  <Input className="bg-zinc-800 border-zinc-700 text-white" placeholder="Hermès" />
                </div>
              </div>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-white">Price</Label>
                  <Input className="bg-zinc-800 border-zinc-700 text-white" placeholder="$12,500" />
                </div>
                <div className="space-y-2">
                  <Label className="text-white">Category</Label>
                  <Input className="bg-zinc-800 border-zinc-700 text-white" placeholder="Handbags" />
                </div>
              </div>
              <div className="flex justify-end space-x-2 pt-4">
                <Button variant="outline" onClick={() => setShowAddProduct(false)} className="border-zinc-700 text-white hover:bg-zinc-800">
                  Cancel
                </Button>
                <Button className="bg-white text-black hover:bg-zinc-200">
                  Add Product
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

const sellerInventory = [
  {
    title: "Vintage Hermès Birkin 35",
    brand: "Hermès",
    price: "$12,500",
    category: "Handbags",
    status: "active",
    views: 234
  },
  {
    title: "Chanel Classic Flap Medium",
    brand: "Chanel", 
    price: "$8,900",
    category: "Handbags",
    status: "pending",
    views: 156
  },
  {
    title: "Rolex Submariner Date",
    brand: "Rolex",
    price: "$15,800", 
    category: "Watches",
    status: "active",
    views: 445
  }
];