"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Textarea } from "@repo/ui/components/textarea";
import { CheckCircle2, ShieldCheck, Upload, X } from "lucide-react";

const warrantySchema = z.object({
  // Product Information
  productName: z.string().min(1, "Product name is required"),
  productBrand: z.string().min(1, "Product brand is required"),
  productModel: z.string().optional(),
  serialNumber: z.string().optional(),
  purchaseDate: z.string().optional(),
  purchasePrice: z.string().optional(),
  
  // Verification Request
  verificationType: z.enum(["authenticity", "warranty_status", "both"], {
    required_error: "Please select verification type",
  }),
  
  // Documentation
  hasOriginalReceipt: z.boolean().default(false),
  hasWarrantyCard: z.boolean().default(false),
  hasOriginalBox: z.boolean().default(false),
  hasCertificates: z.boolean().default(false),
  
  // Contact Information
  contactName: z.string().min(1, "Contact name is required"),
  contactEmail: z.string().email("Valid email is required"),
  contactPhone: z.string().optional(),
  
  // Additional Information
  concernsDescription: z.string().optional(),
  urgencyLevel: z.enum(["low", "medium", "high"], {
    required_error: "Please select urgency level",
  }),
  
  // Agreement
  agreementTerms: z.boolean().refine((val) => val === true, {
    message: "You must agree to the terms and conditions",
  }),
});

type WarrantyFormData = z.infer<typeof warrantySchema>;

const verificationTypeOptions = [
  { value: "authenticity", label: "Product Authenticity Verification" },
  { value: "warranty_status", label: "Warranty Status Check" },
  { value: "both", label: "Both Authenticity & Warranty Verification" },
];

const urgencyOptions = [
  { value: "low", label: "Low - Standard processing (5-7 business days)" },
  { value: "medium", label: "Medium - Priority processing (3-5 business days)" },
  { value: "high", label: "High - Express processing (1-2 business days)" },
];

export function WarrantyForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  
  const { user, isLoading } = useAuth();
  // const submitWarrantyCheck = useMutation(api.warranty.submitWarrantyCheck);

  const form = useForm<WarrantyFormData>({
    resolver: zodResolver(warrantySchema) as any,
    defaultValues: {
      productName: "",
      productBrand: "",
      productModel: "",
      serialNumber: "",
      purchaseDate: "",
      purchasePrice: "",
      hasOriginalReceipt: false,
      hasWarrantyCard: false,
      hasOriginalBox: false,
      hasCertificates: false,
      contactName: user?.name || "",
      contactEmail: user?.email || "",
      contactPhone: "",
      concernsDescription: "",
      urgencyLevel: "medium",
      agreementTerms: false,
    },
  });

  const { handleSubmit, formState: { errors }, reset, watch, setValue } = form;

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      // Handle image upload logic here
      // For now, just show placeholder
      toast.info("Image upload functionality will be implemented");
    }
  };

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (data: WarrantyFormData) => {
    if (!user) {
      toast.error("Please sign in to submit a warranty check request");
      return;
    }

    setIsSubmitting(true);
    
    try {
      // TODO: Implement API call when backend is ready
      // await submitWarrantyCheck({
      //   productName: data.productName,
      //   productBrand: data.productBrand,
      //   productModel: data.productModel,
      //   serialNumber: data.serialNumber,
      //   purchaseDate: data.purchaseDate,
      //   purchasePrice: data.purchasePrice,
      //   verificationType: data.verificationType,
      //   hasOriginalReceipt: data.hasOriginalReceipt,
      //   hasWarrantyCard: data.hasWarrantyCard,
      //   hasOriginalBox: data.hasOriginalBox,
      //   hasCertificates: data.hasCertificates,
      //   contactName: data.contactName,
      //   contactEmail: data.contactEmail,
      //   contactPhone: data.contactPhone,
      //   concernsDescription: data.concernsDescription,
      //   urgencyLevel: data.urgencyLevel,
      //   productImages: uploadedImages,
      // });
      
      // Simulate API call for now
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success("Warranty check request submitted successfully!", {
        description: "Our authentication team will review your request and contact you soon.",
      });
      
      setIsSubmitted(true);
      reset();
      setUploadedImages([]);
    } catch (error: any) {
      console.error("Submission error:", error);
      toast.error("Failed to submit warranty check request", {
        description: error?.message || "Please try again or contact support.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
          <CardContent className="p-8 text-center">
            <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading form...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
          <CardContent className="p-8 text-center">
            <ShieldCheck className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">Authentication Required</h3>
            <p className="text-muted-foreground mb-6">
              Please sign in to submit a warranty check request.
            </p>
            <Button onClick={() => window.location.href = "/login"}>
              Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isSubmitted) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
          <CardContent className="p-8 text-center">
            <CheckCircle2 className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">Request Submitted Successfully!</h3>
            <p className="text-muted-foreground mb-6">
              Thank you for submitting your warranty check request. Our authentication experts 
              will review the details and contact you with results and next steps.
            </p>
            <Button onClick={() => setIsSubmitted(false)}>
              Submit Another Request
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <Card className="border-border bg-card backdrop-blur-sm shadow-xl rounded-2xl">
        <CardHeader>
          <CardTitle className="text-2xl font-light tracking-wide text-foreground">
            Warranty Check Request
          </CardTitle>
          <p className="text-muted-foreground font-light">
            Submit your luxury product for professional authentication and warranty verification. 
            All fields marked with * are required.
          </p>
        </CardHeader>

        <CardContent className="pt-0">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            
            {/* Product Information Section */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-foreground border-b border-border pb-2">
                Product Information
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="productName" className="text-sm font-medium text-foreground">
                    Product Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="productName"
                    {...form.register("productName")}
                    placeholder="e.g., Submariner Date"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                  {errors.productName && (
                    <p className="text-sm text-red-600 font-medium">{errors.productName.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="productBrand" className="text-sm font-medium text-foreground">
                    Brand <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="productBrand"
                    {...form.register("productBrand")}
                    placeholder="e.g., Rolex"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                  {errors.productBrand && (
                    <p className="text-sm text-red-600 font-medium">{errors.productBrand.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="productModel" className="text-sm font-medium text-foreground">
                    Model/Reference Number
                  </Label>
                  <Input
                    id="productModel"
                    {...form.register("productModel")}
                    placeholder="e.g., 126610LN"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="serialNumber" className="text-sm font-medium text-foreground">
                    Serial Number
                  </Label>
                  <Input
                    id="serialNumber"
                    {...form.register("serialNumber")}
                    placeholder="Enter serial number if available"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="purchaseDate" className="text-sm font-medium text-foreground">
                    Purchase Date
                  </Label>
                  <Input
                    id="purchaseDate"
                    type="date"
                    {...form.register("purchaseDate")}
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="purchasePrice" className="text-sm font-medium text-foreground">
                    Purchase Price (USD)
                  </Label>
                  <Input
                    id="purchasePrice"
                    type="number"
                    {...form.register("purchasePrice")}
                    placeholder="e.g., 12000"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                </div>
              </div>
            </div>

            {/* Verification Type Section */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-foreground border-b border-border pb-2">
                Verification Type
              </h3>
              
              <div className="space-y-2">
                <Label className="text-sm font-medium text-foreground">
                  What type of verification do you need? <span className="text-red-500">*</span>
                </Label>
                <div className="space-y-3">
                  {verificationTypeOptions.map((option) => (
                    <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                      <input
                        type="radio"
                        {...form.register("verificationType")}
                        value={option.value}
                        className="mt-1 h-4 w-4 text-primary focus:ring-primary border-border"
                      />
                      <div className="text-sm">
                        <div className="font-medium text-foreground">{option.label}</div>
                      </div>
                    </label>
                  ))}
                </div>
                {errors.verificationType && (
                  <p className="text-sm text-red-600 font-medium">{errors.verificationType.message}</p>
                )}
              </div>
            </div>

            {/* Documentation Section */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-foreground border-b border-border pb-2">
                Available Documentation
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="checkbox"
                    {...form.register("hasOriginalReceipt")}
                    className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                  />
                  <span className="text-sm text-foreground">Original Purchase Receipt</span>
                </label>

                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="checkbox"
                    {...form.register("hasWarrantyCard")}
                    className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                  />
                  <span className="text-sm text-foreground">Warranty Card</span>
                </label>

                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="checkbox"
                    {...form.register("hasOriginalBox")}
                    className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                  />
                  <span className="text-sm text-foreground">Original Box/Packaging</span>
                </label>

                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="checkbox"
                    {...form.register("hasCertificates")}
                    className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                  />
                  <span className="text-sm text-foreground">Certificates of Authenticity</span>
                </label>
              </div>
            </div>

            {/* Product Images Section */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-foreground border-b border-border pb-2">
                Product Images
              </h3>
              
              <div className="space-y-4">
                <div className="border-2 border-dashed border-border rounded-xl p-8 text-center">
                  <Upload className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground mb-4">
                    Upload clear, high-resolution photos of the product, serial numbers, and documentation
                  </p>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="warranty-image-upload"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => document.getElementById('warranty-image-upload')?.click()}
                  >
                    Choose Images
                  </Button>
                </div>

                {uploadedImages.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {uploadedImages.map((image, index) => (
                      <div key={index} className="relative">
                        <img
                          src={image}
                          alt={`Product ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg"
                        />
                        <button
                          type="button"
                          onClick={() => removeImage(index)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Contact Information Section */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-foreground border-b border-border pb-2">
                Contact Information
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="contactName" className="text-sm font-medium text-foreground">
                    Contact Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="contactName"
                    {...form.register("contactName")}
                    placeholder="Your full name"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                  {errors.contactName && (
                    <p className="text-sm text-red-600 font-medium">{errors.contactName.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contactEmail" className="text-sm font-medium text-foreground">
                    Email Address <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    {...form.register("contactEmail")}
                    placeholder="<EMAIL>"
                    className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                  />
                  {errors.contactEmail && (
                    <p className="text-sm text-red-600 font-medium">{errors.contactEmail.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="contactPhone" className="text-sm font-medium text-foreground">
                  Phone Number
                </Label>
                <Input
                  id="contactPhone"
                  {...form.register("contactPhone")}
                  placeholder="+****************"
                  className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 h-12 px-4"
                />
              </div>
            </div>

            {/* Additional Information Section */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-foreground border-b border-border pb-2">
                Additional Information
              </h3>
              
              <div className="space-y-2">
                <Label htmlFor="concernsDescription" className="text-sm font-medium text-foreground">
                  Specific Concerns or Questions
                </Label>
                <Textarea
                  id="concernsDescription"
                  {...form.register("concernsDescription")}
                  placeholder="Describe any specific concerns about authenticity, condition, or warranty status..."
                  rows={4}
                  className="bg-primary/5 border-border focus:ring-2 focus:ring-primary/20 focus:border-primary rounded-xl text-foreground transition-all duration-300 font-light hover:ring-2 hover:ring-primary/20 p-4 resize-none"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-foreground">
                  Urgency Level <span className="text-red-500">*</span>
                </Label>
                <div className="space-y-3">
                  {urgencyOptions.map((option) => (
                    <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                      <input
                        type="radio"
                        {...form.register("urgencyLevel")}
                        value={option.value}
                        className="mt-1 h-4 w-4 text-primary focus:ring-primary border-border"
                      />
                      <div className="text-sm">
                        <div className="font-medium text-foreground">{option.label}</div>
                      </div>
                    </label>
                  ))}
                </div>
                {errors.urgencyLevel && (
                  <p className="text-sm text-red-600 font-medium">{errors.urgencyLevel.message}</p>
                )}
              </div>
            </div>

            {/* Agreement Section */}
            <div className="space-y-6">
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  id="agreementTerms"
                  {...form.register("agreementTerms")}
                  className="mt-1 h-4 w-4 text-primary focus:ring-primary border-border rounded"
                />
                <Label htmlFor="agreementTerms" className="text-sm text-foreground leading-relaxed">
                  I agree to the warranty check service terms and conditions. I understand that 
                  authentication fees apply and that results are final. I authorize MODA to examine 
                  the submitted product information and images. <span className="text-red-500">*</span>
                </Label>
              </div>
              {errors.agreementTerms && (
                <p className="text-sm text-red-600 font-medium">{errors.agreementTerms.message}</p>
              )}
            </div>

            {/* Submit Button */}
            <div className="pt-6">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-primary hover:bg-primary/90 text-primary-foreground font-medium py-3 px-6 rounded-xl transition-all duration-300 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed h-12"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full"></div>
                    <span>Submitting Request...</span>
                  </div>
                ) : (
                  "Submit Warranty Check Request"
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
