"use client";

import { useState, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Textarea } from "@repo/ui/components/textarea";
import { Label } from "@repo/ui/components/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Separator } from "@repo/ui/components/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Switch } from "@repo/ui/components/switch";
import { useAuth } from "@/hooks/useBetterAuth";
import { toast } from "sonner";
import { 
  Building2, 
  Globe, 
  MapPin, 
  User,
  Plus,
  X,
  Instagram,
  Twitter,
  Linkedin,
  Facebook,
  MessageSquare,
  ThumbsUp,
  TrendingUp
} from "lucide-react";

const SPECIALTY_OPTIONS = [
  "CLOTHING",
  "SNEAKERS", 
  "ACCESSORIES",
  "HANDBAGS",
  "COLLECTIBLES",
  "JEWELRY",
  "WATCHES",
  "ELECTRONICS",
  "ART",
  "VINTAGE"
];

export function MemberProfileSettings() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  
  // Form state
  const [displayName, setDisplayName] = useState("");
  const [bio, setBio] = useState("");
  const [company, setCompany] = useState("");
  const [website, setWebsite] = useState("");
  const [location, setLocation] = useState("");
  const [yearsExperience, setYearsExperience] = useState("");
  const [specialties, setSpecialties] = useState<string[]>([]);
  const [socialLinks, setSocialLinks] = useState({
    instagram: "",
    twitter: "",
    linkedin: "",
    facebook: ""
  });
  const [isPublic, setIsPublic] = useState(true);
  const [showEmail, setShowEmail] = useState(false);
  const [showPhone, setShowPhone] = useState(false);
  const [allowMessages, setAllowMessages] = useState(true);

  // Debug authentication
  const debugAuth = useQuery(api.members.debugUserAuth, {});

  // Queries and mutations
  const memberProfile = useQuery(api.members.getMemberProfileForUser, 
    user && user.userId ? { userId: user.userId } : "skip"
  );
  const calculatedActivity = useQuery(api.members.getCalculatedForumActivity,
    user && user.userId ? { userId: user.userId } : "skip"
  );
  const updateProfile = useMutation(api.members.updateMemberProfile);

  // Load existing profile data
  useEffect(() => {
    if (memberProfile) {
      setDisplayName(memberProfile.displayName || user?.name || "");
      setBio(memberProfile.bio || "");
      setCompany(memberProfile.company || "");
      setWebsite(memberProfile.website || "");
      setLocation(memberProfile.location || "");
      setYearsExperience(memberProfile.yearsExperience?.toString() || "");
      setSpecialties(memberProfile.specialty || []);
      setSocialLinks({
        instagram: memberProfile.socialLinks?.instagram || "",
        twitter: memberProfile.socialLinks?.twitter || "",
        linkedin: memberProfile.socialLinks?.linkedin || "",
        facebook: memberProfile.socialLinks?.facebook || ""
      });
      setIsPublic(memberProfile.isPublic ?? true);
      setShowEmail(memberProfile.showEmail ?? false);
      setShowPhone(memberProfile.showPhone ?? false);
      setAllowMessages(memberProfile.allowMessages ?? true);
    }
  }, [memberProfile, user]);

  const handleAddSpecialty = (specialty: string) => {
    if (!specialties.includes(specialty)) {
      setSpecialties([...specialties, specialty]);
    }
  };

  const handleRemoveSpecialty = (specialty: string) => {
    setSpecialties(specialties.filter(s => s !== specialty));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setIsLoading(true);
    try {
      await updateProfile({
        displayName: displayName.trim() || undefined,
        bio: bio.trim() || undefined,
        company: company.trim() || undefined,
        website: website.trim() || undefined,
        location: location.trim() || undefined,
        yearsExperience: yearsExperience ? parseInt(yearsExperience) : undefined,
        specialty: specialties.length > 0 ? specialties : undefined,
        socialLinks: Object.values(socialLinks).some(link => link.trim()) ? {
          instagram: socialLinks.instagram.trim() || undefined,
          twitter: socialLinks.twitter.trim() || undefined,
          linkedin: socialLinks.linkedin.trim() || undefined,
          facebook: socialLinks.facebook.trim() || undefined,
        } : undefined,
        isPublic,
        showEmail,
        showPhone,
        allowMessages,
      });
      toast.success("Profile updated successfully!");
    } catch (error: any) {
      toast.error(error.message || "Failed to update profile");
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return <div>Please sign in to edit your profile.</div>;
  }

  if (!user._id) {
    return (
      <div className="space-y-4">
        <div>Error: Invalid user session. Please refresh and try again.</div>
        {debugAuth && (
          <div className="p-4 bg-gray-100 rounded">
            <h4>Debug Info:</h4>
            <pre>{JSON.stringify({ user, debugAuth }, null, 2)}</pre>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Activity Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Forum Activity Overview
          </CardTitle>
          <CardDescription>
            Your community engagement statistics (calculated in real-time)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-muted/50 rounded-lg p-4 text-center">
              <MessageSquare className="h-8 w-8 mx-auto mb-2 text-blue-600" />
              <div className="text-2xl font-bold">{calculatedActivity?.forumPosts || 0}</div>
              <div className="text-sm text-muted-foreground">Forum Posts</div>
            </div>
            <div className="bg-muted/50 rounded-lg p-4 text-center">
              <MessageSquare className="h-8 w-8 mx-auto mb-2 text-green-600" />
              <div className="text-2xl font-bold">{calculatedActivity?.comments || 0}</div>
              <div className="text-sm text-muted-foreground">Comments</div>
            </div>
            <div className="bg-muted/50 rounded-lg p-4 text-center">
              <ThumbsUp className="h-8 w-8 mx-auto mb-2 text-purple-600" />
              <div className="text-2xl font-bold">-</div>
              <div className="text-sm text-muted-foreground">Helpful Votes</div>
              <div className="text-xs text-muted-foreground mt-1">Coming soon</div>
            </div>
          </div>
          <div className="mt-4 text-sm text-muted-foreground">
            💡 These statistics are calculated dynamically from your actual forum activity and update automatically.
          </div>
        </CardContent>
      </Card>

      {/* Profile Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Basic Information
            </CardTitle>
            <CardDescription>
              Your public profile information visible to other members
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="displayName">Display Name</Label>
              <Input
                id="displayName"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                placeholder="How you want to be known in the community"
              />
            </div>

            <div>
              <Label htmlFor="bio">Bio</Label>
              <Textarea
                id="bio"
                value={bio}
                onChange={(e) => setBio(e.target.value)}
                placeholder="Tell the community about yourself..."
                rows={4}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="company" className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  Company
                </Label>
                <Input
                  id="company"
                  value={company}
                  onChange={(e) => setCompany(e.target.value)}
                  placeholder="Your company or business"
                />
              </div>

              <div>
                <Label htmlFor="location" className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Location
                </Label>
                <Input
                  id="location"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  placeholder="City, Country"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="website" className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  Website
                </Label>
                <Input
                  id="website"
                  type="url"
                  value={website}
                  onChange={(e) => setWebsite(e.target.value)}
                  placeholder="https://your-website.com"
                />
              </div>

              <div>
                <Label htmlFor="yearsExperience">Years of Experience</Label>
                <Input
                  id="yearsExperience"
                  type="number"
                  min="0"
                  max="50"
                  value={yearsExperience}
                  onChange={(e) => setYearsExperience(e.target.value)}
                  placeholder="0"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Specialties */}
        <Card>
          <CardHeader>
            <CardTitle>Specialties & Interests</CardTitle>
            <CardDescription>
              Select the categories you're most interested in or knowledgeable about
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label>Add Specialty</Label>
                <Select onValueChange={handleAddSpecialty}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a specialty to add" />
                  </SelectTrigger>
                  <SelectContent>
                    {SPECIALTY_OPTIONS.filter(option => !specialties.includes(option)).map((option) => (
                      <SelectItem key={option} value={option}>
                        {option}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {specialties.length > 0 && (
                <div>
                  <Label>Selected Specialties</Label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {specialties.map((specialty) => (
                      <Badge key={specialty} variant="secondary" className="flex items-center gap-1">
                        {specialty}
                        <button
                          type="button"
                          onClick={() => handleRemoveSpecialty(specialty)}
                          className="ml-1 hover:bg-destructive/20 rounded-full p-0.5"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Social Links */}
        <Card>
          <CardHeader>
            <CardTitle>Social Media Links</CardTitle>
            <CardDescription>
              Connect your social media profiles (optional)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="instagram" className="flex items-center gap-2">
                  <Instagram className="h-4 w-4" />
                  Instagram
                </Label>
                <Input
                  id="instagram"
                  value={socialLinks.instagram}
                  onChange={(e) => setSocialLinks({...socialLinks, instagram: e.target.value})}
                  placeholder="username (without @)"
                />
              </div>

              <div>
                <Label htmlFor="twitter" className="flex items-center gap-2">
                  <Twitter className="h-4 w-4" />
                  Twitter
                </Label>
                <Input
                  id="twitter"
                  value={socialLinks.twitter}
                  onChange={(e) => setSocialLinks({...socialLinks, twitter: e.target.value})}
                  placeholder="username (without @)"
                />
              </div>

              <div>
                <Label htmlFor="linkedin" className="flex items-center gap-2">
                  <Linkedin className="h-4 w-4" />
                  LinkedIn
                </Label>
                <Input
                  id="linkedin"
                  value={socialLinks.linkedin}
                  onChange={(e) => setSocialLinks({...socialLinks, linkedin: e.target.value})}
                  placeholder="username"
                />
              </div>

              <div>
                <Label htmlFor="facebook" className="flex items-center gap-2">
                  <Facebook className="h-4 w-4" />
                  Facebook
                </Label>
                <Input
                  id="facebook"
                  value={socialLinks.facebook}
                  onChange={(e) => setSocialLinks({...socialLinks, facebook: e.target.value})}
                  placeholder="username"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Privacy Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Privacy & Visibility</CardTitle>
            <CardDescription>
              Control who can see your profile and contact you
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>Public Profile</Label>
                <p className="text-sm text-muted-foreground">
                  Make your profile visible in the member directory
                </p>
              </div>
              <Switch
                checked={isPublic}
                onCheckedChange={setIsPublic}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <Label>Show Email</Label>
                <p className="text-sm text-muted-foreground">
                  Display your email address on your profile
                </p>
              </div>
              <Switch
                checked={showEmail}
                onCheckedChange={setShowEmail}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label>Show Phone</Label>
                <p className="text-sm text-muted-foreground">
                  Display your phone number on your profile
                </p>
              </div>
              <Switch
                checked={showPhone}
                onCheckedChange={setShowPhone}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label>Allow Messages</Label>
                <p className="text-sm text-muted-foreground">
                  Let other members send you direct messages
                </p>
              </div>
              <Switch
                checked={allowMessages}
                onCheckedChange={setAllowMessages}
              />
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Saving..." : "Save Profile"}
          </Button>
        </div>
      </form>
    </div>
  );
}
