"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Separator } from "@repo/ui/components/separator";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@repo/ui/components/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { 
  CreditCard, 
  Plus, 
  Trash2, 
  Edit, 
  Shield,
  Calendar,
  DollarSign,
  FileText,
  Download,
  Loader2
} from "lucide-react";
import { cn } from "@repo/ui/lib/utils";
import { toast } from "sonner";
import { useStripeBilling, PaymentMethod, Invoice, Subscription } from "@/hooks/useStripeBilling";
import { loadStripe } from "@stripe/stripe-js";
import { Elements, CardElement, useStripe, useElements } from "@stripe/react-stripe-js";

// Load Stripe
const stripePromise = loadStripe("pk_test_51RyPnrAaIZO0txgLdR0ZbTCEZ01Me3O9pXVKTJhgssSwSav7EWdFXQhdJD8ejBaVPHa2VKVTO9ocFsY0sRDQFgkT00RsgVnKGQ"!);

// Card Element component for adding new payment methods
function AddCardForm({ onSuccess, onCancel }: { onSuccess: () => void; onCancel: () => void }) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [name, setName] = useState("");
  const [billingAddress, setBillingAddress] = useState({
    street: "",
    city: "",
    state: "",
    zip: "",
    country: "US"
  });

  const { handleCreateSetupIntent, handleAddPaymentMethod } = useStripeBilling();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!stripe || !elements) return;

    console.log('Stripe:', stripe);
    console.log('Elements:', elements);

    console.log('Stripe Promise:', stripePromise);
    console.log('Stripe Key:', process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);

    setIsLoading(true);
    try {
      // Create setup intent
      const setupIntent = await handleCreateSetupIntent();
      if (!setupIntent) return;

      // Confirm card setup
      const { error, setupIntent: confirmedSetupIntent } = await stripe.confirmCardSetup(
        setupIntent.clientSecret as string,
        {
          payment_method: {
            card: elements.getElement(CardElement)!,
            billing_details: {
              name,
              address: {
                line1: billingAddress.street,
                city: billingAddress.city,
                state: billingAddress.state,
                postal_code: billingAddress.zip,
                country: billingAddress.country,
              },
            },
          },
        }
      );

      if (error) {
        toast.error(error.message || "Failed to add payment method");
        return;
      }

      if (confirmedSetupIntent.payment_method) {
        // Add payment method to customer
        const success = await handleAddPaymentMethod(confirmedSetupIntent.payment_method as string);
        if (success) {
          onSuccess();
        }
      }
    } catch (error) {
      console.error("Error adding payment method:", error);
      toast.error("Failed to add payment method");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 py-4">
      <div className="space-y-2">
        <Label htmlFor="cardName">Cardholder Name</Label>
        <Input
          id="cardName"
          placeholder="John Doe"
          value={name}
          onChange={(e) => setName(e.target.value)}
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label>Card Details</Label>
        <div className="border rounded-md p-3">
          <CardElement
            options={{
              style: {
                base: {
                  fontSize: '16px',
                  color: '#424770',
                  '::placeholder': {
                    color: '#aab7c4',
                  },
                },
              },
            }}
          />
        </div>
      </div>
      
      <Separator />
      
      <div className="space-y-4">
        <h4 className="font-medium">Billing Address</h4>
        
        <div className="space-y-2">
          <Label htmlFor="street">Street Address</Label>
          <Input
            id="street"
            placeholder="123 Main St"
            value={billingAddress.street}
            onChange={(e) => setBillingAddress(prev => ({ ...prev, street: e.target.value }))}
            required
          />
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="city">City</Label>
            <Input
              id="city"
              placeholder="New York"
              value={billingAddress.city}
              onChange={(e) => setBillingAddress(prev => ({ ...prev, city: e.target.value }))}
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="state">State</Label>
            <Input
              id="state"
              placeholder="NY"
              value={billingAddress.state}
              onChange={(e) => setBillingAddress(prev => ({ ...prev, state: e.target.value }))}
              required
            />
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="zip">ZIP Code</Label>
            <Input
              id="zip"
              placeholder="10001"
              value={billingAddress.zip}
              onChange={(e) => setBillingAddress(prev => ({ ...prev, zip: e.target.value }))}
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label>Country</Label>
            <Select value={billingAddress.country} onValueChange={(value) => setBillingAddress(prev => ({ ...prev, country: value }))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="US">United States</SelectItem>
                <SelectItem value="CA">Canada</SelectItem>
                <SelectItem value="GB">United Kingdom</SelectItem>
                <SelectItem value="FR">France</SelectItem>
                <SelectItem value="DE">Germany</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
      
      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
          {isLoading ? "Adding..." : "Add Card"}
        </Button>
      </div>
    </form>
  );
}

export function BillingSettings() {
  const [isAddingCard, setIsAddingCard] = useState(false);
  const { 
    isLoading, 
    isLoadingBillingHistory,
    isLoadingPaymentMethods,
    isInitialized,
    paymentMethods, 
    billingHistory, 
    billingHistoryError,
    paymentMethodsError,
    initializeCustomer, 
    handleRemovePaymentMethod, 
    handleSetDefaultPaymentMethod,
    refreshBillingHistory,
    refreshPaymentMethods
  } = useStripeBilling();

  useEffect(() => {
    // Only initialize if not already initialized
    if (!isInitialized) {
      initializeCustomer();
    }
  }, [isInitialized, initializeCustomer]);

  const handleRemoveCard = async (id: string) => {
    try {
      await handleRemovePaymentMethod(id);
    } catch (error) {
      toast.error("Failed to remove payment method");
    }
  };

  const handleSetDefault = async (id: string) => {
    try {
      await handleSetDefaultPaymentMethod(id);
    } catch (error) {
      toast.error("Failed to update default payment method");
    }
  };

  const handleRefreshBillingHistory = async () => {
    if (isLoadingBillingHistory) return;
    
    try {
      await refreshBillingHistory();
      toast.success("Billing history refreshed");
    } catch (error) {
      toast.error("Failed to refresh billing history");
    }
  };

  const handleRefreshPaymentMethods = async () => {
    if (isLoadingPaymentMethods) return;
    
    try {
      await refreshPaymentMethods();
      toast.success("Payment methods refreshed");
    } catch (error) {
      toast.error("Failed to refresh payment methods");
    }
  };

  const handleManualRefresh = async () => {
    if (isLoading) return;
    
    try {
      await initializeCustomer();
      toast.success("Billing data refreshed");
    } catch (error) {
      toast.error("Failed to refresh billing data");
    }
  };

  const getCardIcon = (brand: string) => {
    switch (brand?.toLowerCase()) {
      case 'visa':
        return '💳';
      case 'mastercard':
        return '💳';
      case 'amex':
        return '💳';
      default:
        return '💳';
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString();
  };

  if (isLoading || isLoadingPaymentMethods || isLoadingBillingHistory) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header with refresh button */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-black dark:text-white">
          Billing Settings
        </h2>
        <Button 
          variant="outline" 
          onClick={handleManualRefresh}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Download className="w-4 h-4 mr-2" />
          )}
          {isLoading ? "Refreshing..." : "Refresh All"}
        </Button>
      </div>

      {/* Payment Methods */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-black dark:text-white">
            Payment Methods
          </h3>
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleRefreshPaymentMethods}
              disabled={isLoadingPaymentMethods}
            >
              {isLoadingPaymentMethods ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Download className="w-4 h-4 mr-2" />
              )}
              {isLoadingPaymentMethods ? "Refreshing..." : "Refresh"}
            </Button>
            <Dialog open={isAddingCard} onOpenChange={setIsAddingCard}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Payment Method
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Add New Payment Method</DialogTitle>
                  <DialogDescription>
                    Add a new credit or debit card to your account
                  </DialogDescription>
                </DialogHeader>
                
                <Elements stripe={stripePromise}>
                  <AddCardForm 
                    onSuccess={() => {
                      setIsAddingCard(false);
                      refreshPaymentMethods();
                    }} 
                    onCancel={() => setIsAddingCard(false)} 
                  />
                </Elements>
              </DialogContent>
            </Dialog>
          </div>
        </div>
        
        <div className="space-y-4">
          {isLoadingPaymentMethods ? (
            <Card>
              <CardContent className="p-6 text-center">
                <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2" />
                <p className="text-neutral-500">Loading payment methods...</p>
              </CardContent>
            </Card>
          ) : paymentMethodsError ? (
            <Card className="border-red-200 dark:border-red-800">
              <CardContent className="p-6 text-center">
                <div className="text-red-600 dark:text-red-400 mb-4">
                  <p className="font-medium mb-2">Failed to load payment methods</p>
                  <p className="text-sm">{paymentMethodsError}</p>
                </div>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleRefreshPaymentMethods}
                >
                  Try Again
                </Button>
              </CardContent>
            </Card>
          ) : paymentMethods.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center text-neutral-500">
                No payment methods added yet.
              </CardContent>
            </Card>
          ) : (
            paymentMethods.map((method) => (
              <Card key={method.id} className={cn(
                "relative",
                method.isDefault && "ring-2 ring-blue-500 dark:ring-blue-400"
              )}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="text-2xl">
                        {getCardIcon(method.card?.brand || '')}
                      </div>
                      <div>
                        <p className="font-medium text-black dark:text-white">
                          {method.card?.brand?.toUpperCase()} ending in {method.card?.last4}
                        </p>
                        <p className="text-sm text-neutral-600 dark:text-neutral-400">
                          Expires {method.card?.exp_month}/{method.card?.exp_year}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {method.isDefault && (
                        <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                          Default
                        </Badge>
                      )}
                      
                      {!method.isDefault && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSetDefault(method.id)}
                        >
                          Set Default
                        </Button>
                      )}
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveCard(method.id)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>

      <Separator />

      {/* Billing History */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-black dark:text-white">
            Billing History
          </h3>
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleRefreshBillingHistory}
              disabled={isLoadingBillingHistory}
            >
              {isLoadingBillingHistory ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Download className="w-4 h-4 mr-2" />
              )}
              {isLoadingBillingHistory ? "Refreshing..." : "Refresh"}
            </Button>
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export All
            </Button>
          </div>
        </div>
        
        <div className="space-y-4">
          {isLoadingBillingHistory ? (
            <Card>
              <CardContent className="p-6 text-center">
                <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2" />
                <p className="text-neutral-500">Loading billing history...</p>
              </CardContent>
            </Card>
          ) : billingHistoryError ? (
            <Card className="border-red-200 dark:border-red-800">
              <CardContent className="p-6 text-center">
                <div className="text-red-600 dark:text-red-400 mb-4">
                  <p className="font-medium mb-2">Failed to load billing history</p>
                  <p className="text-sm">{billingHistoryError}</p>
                </div>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleRefreshBillingHistory}
                >
                  Try Again
                </Button>
              </CardContent>
            </Card>
          ) : billingHistory.invoices.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center text-neutral-500">
                No billing history available yet.
              </CardContent>
            </Card>
          ) : (
            billingHistory.invoices.map((invoice) => (
              <Card key={invoice.id}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="font-medium text-black dark:text-white">
                        {invoice.description || `Invoice ${invoice.number}`}
                      </p>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">
                        {invoice.number} • {formatDate(invoice.created)}
                      </p>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="font-semibold text-black dark:text-white">
                          {formatCurrency(invoice.amount, invoice.currency)}
                        </p>
                        <Badge className={cn(
                          "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
                          invoice.status === 'paid' && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
                          invoice.status === 'open' && "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
                          invoice.status === 'void' && "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                        )}>
                          {invoice.status}
                        </Badge>
                      </div>
                      
                      {invoice.pdf && (
                        <Button variant="outline" size="sm" asChild>
                          <a href={invoice.pdf} target="_blank" rel="noopener noreferrer">
                            <FileText className="w-4 h-4 mr-2" />
                            View
                          </a>
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>

      <Separator />

      {/* Security Notice */}
      <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
        <CardContent className="p-6">
          <div className="flex items-start space-x-3">
            <Shield className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
            <div>
              <h4 className="font-semibold text-blue-800 dark:text-blue-300 mb-2">
                Secure Payment Processing
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-400">
                All payment information is encrypted and processed securely through Stripe. 
                We never store your complete card details on our servers.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
