"use client";

import { useState, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Button } from "@repo/ui/components/button";
import { Label } from "@repo/ui/components/label";
import { Separator } from "@repo/ui/components/separator";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Switch } from "@repo/ui/components/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Slider } from "@repo/ui/components/slider";
import { RadioGroup, RadioGroupItem } from "@repo/ui/components/radio-group";
import { 
  Bell, 
  Mail, 
  Smartphone, 
  Globe, 
  Palette, 
  Volume2,
  Eye,
  Star,
  ShoppingBag,
  TrendingUp,
  Users,
  Gift,
  MessageSquare,
  Save
} from "lucide-react";
import { toast } from "sonner";

export function PreferencesSettings() {
  const preferences = useQuery(api.userPreferences.getUserPreferences);
  const updatePreferences = useMutation(api.userPreferences.updateUserPreferences);
  const [isLoading, setIsLoading] = useState(false);
  
  const [notifications, setNotifications] = useState({
    // Email Notifications
    orderUpdates: true,
    promotions: true,
    newProducts: false,
    priceAlerts: true,
    newslettter: true,
    sellerUpdates: false,
    communityActivity: true,
    skuAlerts: true,
    
    // Push Notifications
    pushEnabled: true,
    orderStatus: true,
    bidUpdates: true,
    wishlistAlerts: true,
    marketingPush: false,
    
    // SMS Notifications
    smsEnabled: false,
    orderSms: false,
    securitySms: true,
  });

  const [localPreferences, setLocalPreferences] = useState({
    // Display Preferences
    theme: 'system' as 'light' | 'dark' | 'system',
    currency: 'USD',
    language: 'en',
    timezone: 'America/New_York',
    
    // Privacy Preferences
    profileVisibility: 'public' as 'public' | 'private',
    showPurchaseHistory: false,
    showWishlist: true,
    allowMessages: true,
    
    // Marketplace Preferences
    defaultView: 'grid' as 'grid' | 'list',
    itemsPerPage: 24,
    autoplayVideos: true,
    showSimilarItems: true,
    enableRecommendations: true,
    
    // Search & Discovery
    saveSearchHistory: true,
    personalizedResults: true,
    trendingCategories: true,
    locationBasedResults: true,
  });

  // Initialize preferences when loaded from backend
  useEffect(() => {
    if (preferences) {
      if (preferences.emailNotifications) {
        setNotifications(prev => ({
          ...prev,
          orderUpdates: preferences.emailNotifications.orderUpdates,
          promotions: preferences.emailNotifications.promotions,
          newProducts: preferences.emailNotifications.newProducts,
          priceAlerts: preferences.emailNotifications.priceAlerts,
          newslettter: preferences.emailNotifications.newsletter,
          sellerUpdates: preferences.emailNotifications.sellerUpdates,
          communityActivity: preferences.emailNotifications.communityActivity,
          skuAlerts: preferences.emailNotifications.skuAlerts ?? true,
          pushEnabled: preferences.pushNotifications.enabled,
          orderStatus: preferences.pushNotifications.orderStatus,
          bidUpdates: preferences.pushNotifications.bidUpdates,
          wishlistAlerts: preferences.pushNotifications.wishlistAlerts,
          marketingPush: preferences.pushNotifications.marketing,
          smsEnabled: preferences.smsNotifications.enabled,
          orderSms: preferences.smsNotifications.orderUpdates,
          securitySms: preferences.smsNotifications.security,
        }));
      }

      if (preferences.display) {
        setLocalPreferences(prev => ({
          ...prev,
          theme: preferences.display.theme as 'light' | 'dark' | 'system',
          currency: preferences.display.currency,
          language: preferences.display.language,
          timezone: preferences.display.timezone,
        }));
      }

      if (preferences.privacy) {
        setLocalPreferences(prev => ({
          ...prev,
          profileVisibility: preferences.privacy.profileVisibility as 'public' | 'private',
          showPurchaseHistory: preferences.privacy.showPurchaseHistory,
          showWishlist: preferences.privacy.showWishlist,
          allowMessages: preferences.privacy.allowMessages,
        }));
      }

      if (preferences.marketplace) {
        setLocalPreferences(prev => ({
          ...prev,
          defaultView: preferences.marketplace.defaultView as 'grid' | 'list',
          itemsPerPage: preferences.marketplace.itemsPerPage,
          autoplayVideos: preferences.marketplace.autoplayVideos,
          showSimilarItems: preferences.marketplace.showSimilarItems,
          enableRecommendations: preferences.marketplace.enableRecommendations,
        }));
      }

      if (preferences.search) {
        setLocalPreferences(prev => ({
          ...prev,
          saveSearchHistory: preferences.search.saveSearchHistory,
          personalizedResults: preferences.search.personalizedResults,
          trendingCategories: preferences.search.trendingCategories,
          locationBasedResults: preferences.search.locationBasedResults,
        }));
      }
    }
  }, [preferences]);

  const handleSavePreferences = async () => {
    setIsLoading(true);
    try {
      await updatePreferences({
        emailNotifications: {
          orderUpdates: notifications.orderUpdates,
          promotions: notifications.promotions,
          newProducts: notifications.newProducts,
          priceAlerts: notifications.priceAlerts,
          newsletter: notifications.newslettter,
          sellerUpdates: notifications.sellerUpdates,
          communityActivity: notifications.communityActivity,
          skuAlerts: notifications.skuAlerts,
        },
        pushNotifications: {
          enabled: notifications.pushEnabled,
          orderStatus: notifications.orderStatus,
          bidUpdates: notifications.bidUpdates,
          wishlistAlerts: notifications.wishlistAlerts,
          marketing: notifications.marketingPush,
        },
        smsNotifications: {
          enabled: notifications.smsEnabled,
          orderUpdates: notifications.orderSms,
          security: notifications.securitySms,
        },
        display: {
          theme: localPreferences.theme,
          currency: localPreferences.currency,
          language: localPreferences.language,
          timezone: localPreferences.timezone,
        },
        privacy: {
          profileVisibility: localPreferences.profileVisibility,
          showPurchaseHistory: localPreferences.showPurchaseHistory,
          showWishlist: localPreferences.showWishlist,
          allowMessages: localPreferences.allowMessages,
        },
        marketplace: {
          defaultView: localPreferences.defaultView,
          itemsPerPage: localPreferences.itemsPerPage,
          autoplayVideos: localPreferences.autoplayVideos,
          showSimilarItems: localPreferences.showSimilarItems,
          enableRecommendations: localPreferences.enableRecommendations,
        },
        search: {
          saveSearchHistory: localPreferences.saveSearchHistory,
          personalizedResults: localPreferences.personalizedResults,
          trendingCategories: localPreferences.trendingCategories,
          locationBasedResults: localPreferences.locationBasedResults,
        },
      });
      
      toast.success("Preferences updated successfully!");
    } catch (error) {
      console.error("Failed to update preferences:", error);
      toast.error("Failed to update preferences");
    } finally {
      setIsLoading(false);
    }
  };

  const currencies = [
    { value: 'USD', label: 'US Dollar ($)' },
    { value: 'EUR', label: 'Euro (€)' },
    { value: 'GBP', label: 'British Pound (£)' },
    { value: 'JPY', label: 'Japanese Yen (¥)' },
    { value: 'CAD', label: 'Canadian Dollar (C$)' },
    { value: 'AUD', label: 'Australian Dollar (A$)' },
  ];

  const languages = [
    { value: 'en', label: 'English' },
    { value: 'es', label: 'Español' },
    { value: 'fr', label: 'Français' },
    { value: 'de', label: 'Deutsch' },
    { value: 'it', label: 'Italiano' },
    { value: 'ja', label: '日本語' },
  ];

  const timezones = [
    { value: 'America/New_York', label: 'Eastern Time (ET)' },
    { value: 'America/Chicago', label: 'Central Time (CT)' },
    { value: 'America/Denver', label: 'Mountain Time (MT)' },
    { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
    { value: 'Europe/London', label: 'London (GMT)' },
    { value: 'Europe/Paris', label: 'Paris (CET)' },
    { value: 'Asia/Tokyo', label: 'Tokyo (JST)' },
    { value: 'Australia/Sydney', label: 'Sydney (AEDT)' },
  ];

  return (
    <div className="space-y-8">
      {/* Notification Preferences */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-black dark:text-white">
          Notification Preferences
        </h3>
        
        {/* Email Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="w-5 h-5" />
              Email Notifications
            </CardTitle>
            <CardDescription>
              Choose what email notifications you'd like to receive
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="font-medium text-black dark:text-white">Order Updates</p>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Notifications about your orders and shipping
                  </p>
                </div>
                <Switch
                  checked={notifications.orderUpdates}
                  onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, orderUpdates: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="font-medium text-black dark:text-white">Price Alerts</p>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    When items in your wishlist go on sale
                  </p>
                </div>
                <Switch
                  checked={notifications.priceAlerts}
                  onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, priceAlerts: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="font-medium text-black dark:text-white">New Products</p>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    When new products match your interests
                  </p>
                </div>
                <Switch
                  checked={notifications.newProducts}
                  onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, newProducts: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="font-medium text-black dark:text-white">Promotions & Deals</p>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Special offers and promotional content
                  </p>
                </div>
                <Switch
                  checked={notifications.promotions}
                  onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, promotions: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="font-medium text-black dark:text-white">Community Activity</p>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Comments, reviews, and social interactions
                  </p>
                </div>
                <Switch
                  checked={notifications.communityActivity}
                  onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, communityActivity: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="font-medium text-black dark:text-white">SKU Alerts</p>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Get notified when specific SKUs become available
                  </p>
                </div>
                <Switch
                  checked={notifications.skuAlerts}
                  onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, skuAlerts: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="font-medium text-black dark:text-white">Newsletter</p>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Weekly digest and luxury market insights
                  </p>
                </div>
                <Switch
                  checked={notifications.newslettter}
                  onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, newslettter: checked }))}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Push Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="w-5 h-5" />
              Push Notifications
            </CardTitle>
            <CardDescription>
              Manage mobile app and browser push notifications
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="font-medium text-black dark:text-white">Enable Push Notifications</p>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Allow the app to send you push notifications
                </p>
              </div>
              <Switch
                checked={notifications.pushEnabled}
                onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, pushEnabled: checked }))}
              />
            </div>
            
            {notifications.pushEnabled && (
              <div className="space-y-4 pl-6 border-l-2 border-neutral-200 dark:border-neutral-700">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-black dark:text-white">Order Status Updates</p>
                  <Switch
                    checked={notifications.orderStatus}
                    onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, orderStatus: checked }))}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-black dark:text-white">Bid & Auction Updates</p>
                  <Switch
                    checked={notifications.bidUpdates}
                    onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, bidUpdates: checked }))}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-black dark:text-white">Wishlist Alerts</p>
                  <Switch
                    checked={notifications.wishlistAlerts}
                    onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, wishlistAlerts: checked }))}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-black dark:text-white">Marketing Messages</p>
                  <Switch
                    checked={notifications.marketingPush}
                    onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, marketingPush: checked }))}
                  />
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Separator />

      {/* Display Preferences */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-black dark:text-white">
          Display & Localization
        </h3>
        
        <Card>
          <CardContent className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label>Theme</Label>
                <Select value={localPreferences.theme} onValueChange={(value) => setLocalPreferences(prev => ({ ...prev, theme: value as 'light' | 'dark' | 'system' }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">🌞 Light</SelectItem>
                    <SelectItem value="dark">🌙 Dark</SelectItem>
                    <SelectItem value="system">💻 System</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label>Currency</Label>
                <Select value={localPreferences.currency} onValueChange={(value) => setLocalPreferences(prev => ({ ...prev, currency: value as 'USD' | 'EUR' | 'GBP' | 'JPY' | 'CAD' | 'AUD' }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {currencies.map(currency => (
                      <SelectItem key={currency.value} value={currency.value}>
                        {currency.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label>Language</Label>
                <Select value={localPreferences.language} onValueChange={(value) => setLocalPreferences(prev => ({ ...prev, language: value as 'en' | 'es' | 'fr' | 'de' | 'it' | 'ja' }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {languages.map(language => (
                      <SelectItem key={language.value} value={language.value}>
                        {language.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label>Timezone</Label>
                <Select value={localPreferences.timezone} onValueChange={(value) => setLocalPreferences(prev => ({ ...prev, timezone: value as 'America/New_York' | 'America/Chicago' | 'America/Denver' | 'America/Los_Angeles' | 'Europe/London' | 'Europe/Paris' | 'Asia/Tokyo' | 'Australia/Sydney' }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {timezones.map(timezone => (
                      <SelectItem key={timezone.value} value={timezone.value}>
                        {timezone.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Separator />

      {/* Privacy Preferences */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-black dark:text-white">
          Privacy & Profile
        </h3>
        
        <Card>
          <CardContent className="p-6 space-y-6">
            <div className="space-y-4">
              <div className="space-y-3">
                <Label>Profile Visibility</Label>
                <RadioGroup 
                  value={localPreferences.profileVisibility} 
                  onValueChange={(value) => setLocalPreferences(prev => ({ ...prev, profileVisibility: value as 'public' | 'private' }))}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="public" id="public" />
                    <Label htmlFor="public">Public - Anyone can see your profile</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="private" id="private" />
                    <Label htmlFor="private">Private - Only you can see your profile</Label>
                  </div>
                </RadioGroup>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="font-medium text-black dark:text-white">Show Purchase History</p>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Let others see what you've bought
                  </p>
                </div>
                <Switch
                  checked={localPreferences.showPurchaseHistory}
                  onCheckedChange={(checked) => setLocalPreferences(prev => ({ ...prev, showPurchaseHistory: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="font-medium text-black dark:text-white">Show Wishlist</p>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Let others see your wishlist items
                  </p>
                </div>
                <Switch
                  checked={localPreferences.showWishlist}
                  onCheckedChange={(checked) => setLocalPreferences(prev => ({ ...prev, showWishlist: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="font-medium text-black dark:text-white">Allow Messages</p>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Let other users send you messages
                  </p>
                </div>
                <Switch
                  checked={localPreferences.allowMessages}
                  onCheckedChange={(checked) => setLocalPreferences(prev => ({ ...prev, allowMessages: checked }))}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Separator />

      {/* Marketplace Preferences */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-black dark:text-white">
          Marketplace Experience
        </h3>
        
        <Card>
          <CardContent className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label>Default View</Label>
                <Select value={localPreferences.defaultView} onValueChange={(value) => setLocalPreferences(prev => ({ ...prev, defaultView: value as 'grid' | 'list' }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="grid">🔲 Grid View</SelectItem>
                    <SelectItem value="list">📋 List View</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label>Items Per Page: {localPreferences.itemsPerPage}</Label>
                <Slider
                  value={[localPreferences.itemsPerPage]}
                  onValueChange={([value]) => setLocalPreferences(prev => ({ ...prev, itemsPerPage: value as number }))}
                  max={48}
                  min={12}
                  step={12}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-neutral-500">
                  <span>12</span>
                  <span>24</span>
                  <span>36</span>
                  <span>48</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="font-medium text-black dark:text-white">Autoplay Videos</p>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Automatically play product videos
                  </p>
                </div>
                <Switch
                  checked={localPreferences.autoplayVideos}
                  onCheckedChange={(checked) => setLocalPreferences(prev => ({ ...prev, autoplayVideos: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="font-medium text-black dark:text-white">Show Similar Items</p>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Display related products on item pages
                  </p>
                </div>
                <Switch
                  checked={localPreferences.showSimilarItems}
                  onCheckedChange={(checked) => setLocalPreferences(prev => ({ ...prev, showSimilarItems: checked }))}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="font-medium text-black dark:text-white">Personalized Recommendations</p>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Get product suggestions based on your activity
                  </p>
                </div>
                <Switch
                  checked={localPreferences.enableRecommendations}
                  onCheckedChange={(checked) => setLocalPreferences(prev => ({ ...prev, enableRecommendations: checked }))}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Separator />

      {/* Search & Discovery */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-black dark:text-white">
          Search & Discovery
        </h3>
        
        <Card>
          <CardContent className="p-6 space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="font-medium text-black dark:text-white">Save Search History</p>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Remember your searches for better suggestions
                </p>
              </div>
              <Switch
                checked={localPreferences.saveSearchHistory}
                onCheckedChange={(checked) => setLocalPreferences(prev => ({ ...prev, saveSearchHistory: checked }))}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="font-medium text-black dark:text-white">Personalized Results</p>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Customize search results based on your preferences
                </p>
              </div>
              <Switch
                checked={localPreferences.personalizedResults}
                onCheckedChange={(checked) => setLocalPreferences(prev => ({ ...prev, personalizedResults: checked }))}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="font-medium text-black dark:text-white">Show Trending Categories</p>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Display popular categories on the homepage
                </p>
              </div>
              <Switch
                checked={localPreferences.trendingCategories}
                onCheckedChange={(checked) => setLocalPreferences(prev => ({ ...prev, trendingCategories: checked }))}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="font-medium text-black dark:text-white">Location-Based Results</p>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Show items available in your area first
                </p>
              </div>
              <Switch
                checked={localPreferences.locationBasedResults}
                onCheckedChange={(checked) => setLocalPreferences(prev => ({ ...prev, locationBasedResults: checked }))}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      <Separator />

      {/* Save Button */}
      <div className="flex justify-end">
        <Button onClick={handleSavePreferences} disabled={isLoading} className="px-8">
          <Save className="w-4 h-4 mr-2" />
          {isLoading ? "Saving..." : "Save All Preferences"}
        </Button>
      </div>
    </div>
  );
}
