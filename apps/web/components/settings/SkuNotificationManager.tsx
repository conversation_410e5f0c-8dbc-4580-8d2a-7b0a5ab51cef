"use client";

import React, { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Trash2, Bell, BellOff, Plus, Search } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@repo/ui/components/select";
import { Switch } from "@repo/ui/components/switch";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@repo/ui/components/dialog";
import { Id } from "@repo/backend/convex/_generated/dataModel";

interface SkuSubscription {
  _id: string;
  _creationTime: number;
  sku: string;
  brand?: string;
  category?: string;
  maxPrice?: number;
  minCondition?: "new" | "like_new" | "excellent" | "very_good" | "good" | "fair";
  isActive: boolean;
  updatedAt: number;
  lastTriggered?: number;
  triggerCount: number;
}

const conditionOptions = [
  { value: "new", label: "New" },
  { value: "like_new", label: "Like New" },
  { value: "excellent", label: "Excellent" },
  { value: "very_good", label: "Very Good" },
  { value: "good", label: "Good" },
  { value: "fair", label: "Fair" },
];

export function SkuNotificationManager() {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newSku, setNewSku] = useState("");
  const [newBrand, setNewBrand] = useState("");
  const [newCategory, setNewCategory] = useState("");
  const [newMaxPrice, setNewMaxPrice] = useState("");
  const [newMinCondition, setNewMinCondition] = useState<string>("");
  
  // Queries
  const subscriptions = useQuery(api.skuNotifications.getUserSkuSubscriptions, {});
  
  // Mutations
  const createSubscription = useMutation(api.skuNotifications.createSkuSubscription);
  const updateSubscription = useMutation(api.skuNotifications.updateSkuSubscription);
  const deleteSubscription = useMutation(api.skuNotifications.deleteSkuSubscription);

  const handleCreateSubscription = async () => {
    if (!newSku.trim()) {
      toast.error("SKU is required");
      return;
    }

    try {
      const result = await createSubscription({
        sku: newSku.trim(),
        brand: newBrand.trim() || undefined,
        category: newCategory || undefined,
        maxPrice: newMaxPrice ? parseFloat(newMaxPrice) : undefined,
        minCondition: newMinCondition as any || undefined,
      });

      if (result.success) {
        toast.success(result.message);
        setIsAddDialogOpen(false);
        setNewSku("");
        setNewBrand("");
        setNewCategory("");
        setNewMaxPrice("");
        setNewMinCondition("");
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to create SKU subscription");
    }
  };

  const handleToggleSubscription = async (subscriptionId: string, currentState: boolean) => {
    try {
      const result = await updateSubscription({
        subscriptionId: subscriptionId as Id<"skuSubscriptions">,
        isActive: !currentState,
      });

      if (result.success) {
        toast.success(result.message);
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to update subscription");
    }
  };

  const handleDeleteSubscription = async (subscriptionId: string) => {
    try {
      const result = await deleteSubscription({ subscriptionId: subscriptionId as Id<"skuSubscriptions"> });

      if (result.success) {
        toast.success(result.message);
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to delete subscription");
    }
  };

  const formatCondition = (condition: string) => {
    return condition.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">SKU Notifications</h3>
          <p className="text-sm text-muted-foreground">
            Get notified when specific SKUs become available in the marketplace
          </p>
        </div>
        
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add SKU Alert
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Create SKU Alert</DialogTitle>
              <DialogDescription>
                Get notified when this SKU becomes available. You can add filters to narrow down your alerts.
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="sku">SKU *</Label>
                <Input
                  id="sku"
                  placeholder="Enter SKU (e.g., ABC-123-XYZ)"
                  value={newSku}
                  onChange={(e) => setNewSku(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="brand">Brand (Optional)</Label>
                <Input
                  id="brand"
                  placeholder="Filter by brand"
                  value={newBrand}
                  onChange={(e) => setNewBrand(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="maxPrice">Max Price (Optional)</Label>
                <Input
                  id="maxPrice"
                  type="number"
                  placeholder="Maximum price you're willing to pay"
                  value={newMaxPrice}
                  onChange={(e) => setNewMaxPrice(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="minCondition">Minimum Condition (Optional)</Label>
                <Select value={newMinCondition} onValueChange={setNewMinCondition}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select minimum condition" />
                  </SelectTrigger>
                  <SelectContent>
                    {conditionOptions.map((condition) => (
                      <SelectItem key={condition.value} value={condition.value}>
                        {condition.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateSubscription}>
                Create Alert
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {subscriptions === undefined ? (
        <div className="flex items-center justify-center py-8">
          <div className="text-sm text-muted-foreground">Loading...</div>
        </div>
      ) : subscriptions.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8 text-center">
            <Search className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="font-semibold text-lg mb-2">No SKU Alerts</h3>
            <p className="text-muted-foreground mb-4">
              You haven't set up any SKU alerts yet. Create your first alert to get notified when specific items become available.
            </p>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Your First SKU Alert
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {subscriptions.map((subscription) => (
            <Card key={subscription._id}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <code className="bg-muted px-2 py-1 rounded text-sm font-mono">
                        {subscription.sku}
                      </code>
                      <Badge variant={subscription.isActive ? "default" : "secondary"}>
                        {subscription.isActive ? "Active" : "Paused"}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                      {subscription.brand && (
                        <div>
                          <span className="font-medium">Brand:</span> {subscription.brand}
                        </div>
                      )}
                      {subscription.maxPrice && (
                        <div>
                          <span className="font-medium">Max Price:</span> ${subscription.maxPrice}
                        </div>
                      )}
                      {subscription.minCondition && (
                        <div>
                          <span className="font-medium">Min Condition:</span> {formatCondition(subscription.minCondition)}
                        </div>
                      )}
                      <div>
                        <span className="font-medium">Created:</span> {formatDate(subscription._creationTime)}
                      </div>
                      <div>
                        <span className="font-medium">Triggered:</span> {subscription.triggerCount} times
                      </div>
                      {subscription.lastTriggered && (
                        <div>
                          <span className="font-medium">Last Triggered:</span> {formatDate(subscription.lastTriggered)}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleToggleSubscription(subscription._id, subscription.isActive)}
                    >
                      {subscription.isActive ? (
                        <BellOff className="h-4 w-4" />
                      ) : (
                        <Bell className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteSubscription(subscription._id)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
      
      <Card>
        <CardHeader>
          <CardTitle className="text-base">How SKU Alerts Work</CardTitle>
        </CardHeader>
        <CardContent className="text-sm text-muted-foreground space-y-2">
          <p>• You'll receive an email notification when a matching SKU is posted for sale</p>
          <p>• Notifications respect your email preferences in settings</p>
          <p>• Free users can have up to 5 SKU alerts, paid subscribers get 100</p>
          <p>• Filters help you get more targeted notifications (brand, price, condition)</p>
          <p>• You can pause/resume or delete alerts at any time</p>
        </CardContent>
      </Card>
    </div>
  );
}
