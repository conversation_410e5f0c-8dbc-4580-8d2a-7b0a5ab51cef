"use client";

import { useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Card, CardContent } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";
import { Badge } from "@repo/ui/components/badge";
import { Skeleton } from "@repo/ui/components/skeleton";
import { Check, Trash2, ExternalLink, Bell } from "lucide-react";
import { toast } from "sonner";
import { formatDistanceToNow } from "date-fns";

interface NotificationsListProps {
  notifications: any[];
  isLoading: boolean;
  activeFilterCount: number;
}

export function NotificationsList({
  notifications,
  isLoading,
  activeFilterCount,
}: NotificationsListProps) {
  const markAsRead = useMutation(api.notifications.markNotificationAsRead);
  const deleteNotification = useMutation(api.notifications.deleteNotification);

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAsRead({ notificationId: notificationId as any });
      toast.success("Marked as read");
    } catch (error) {
      toast.error("Failed to mark as read");
    }
  };

  const handleDeleteNotification = async (notificationId: string) => {
    try {
      await deleteNotification({ notificationId: notificationId as any });
      toast.success("Notification deleted");
    } catch (error) {
      toast.error("Failed to delete notification");
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "sku_available":
        return "🔔";
      case "auction_won":
        return "🏆";
      case "auction_ended":
        return "⏰";
      case "auction_outbid":
        return "📈";
      case "system":
        return "⚙️";
      case "shipping":
        return "📦";
      case "payment":
        return "💳";
      default:
        return "📢";
    }
  };

  const getNotificationLink = (notification: any) => {
    if (notification.type === "sku_available" && notification.data?.productId) {
      return `/marketplace/product/${notification.data.productId}`;
    }
    return "#";
  };

  const getNotificationTypeLabel = (type: string) => {
    switch (type) {
      case "sku_available":
        return "SKU Alert";
      case "auction_won":
        return "Auction Won";
      case "auction_ended":
        return "Auction Ended";
      case "auction_outbid":
        return "Outbid";
      case "system":
        return "System";
      case "shipping":
        return "Shipping";
      case "payment":
        return "Payment";
      default:
        return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  const getNotificationTypeColor = (type: string) => {
    switch (type) {
      case "sku_available":
        return "bg-blue-100 text-blue-800";
      case "auction_won":
        return "bg-green-100 text-green-800";
      case "auction_ended":
        return "bg-orange-100 text-orange-800";
      case "auction_outbid":
        return "bg-red-100 text-red-800";
      case "system":
        return "bg-gray-100 text-gray-800";
      case "shipping":
        return "bg-purple-100 text-purple-800";
      case "payment":
        return "bg-emerald-100 text-emerald-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i} className="rounded-xl border-border">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <Skeleton className="w-12 h-12 rounded-xl" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (notifications.length === 0) {
    return (
      <Card className="rounded-xl border-border">
        <CardContent className="p-12 text-center">
          <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
            <Bell className="w-8 h-8 text-primary" />
          </div>
          <h3 className="text-lg font-light text-primary mb-2">
            {activeFilterCount > 0 ? "No notifications match your filters" : "No notifications yet"}
          </h3>
          <p className="text-muted-foreground font-light">
            {activeFilterCount > 0 
              ? "Try adjusting your filters to see more notifications"
              : "You'll see notifications here when you receive alerts and updates"
            }
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {notifications.map((notification) => (
        <Card 
          key={notification._id} 
          className={`rounded-xl border-border transition-all duration-200 hover:shadow-md ${
            !notification.read ? 'bg-primary/5 border-primary/20' : ''
          }`}
        >
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              {/* Icon */}
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                  <span className="text-xl">{getNotificationIcon(notification.type)}</span>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-4 mb-2">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="text-sm font-medium text-foreground line-clamp-1">
                        {notification.title}
                      </h3>
                      {!notification.read && (
                        <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0" />
                      )}
                    </div>
                    <Badge 
                      variant="secondary" 
                      className={`text-xs ${getNotificationTypeColor(notification.type)}`}
                    >
                      {getNotificationTypeLabel(notification.type)}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-1">
                    {!notification.read && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleMarkAsRead(notification._id)}
                        className="h-8 w-8 p-0 hover:bg-muted/50"
                      >
                        <Check className="w-4 h-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteNotification(notification._id)}
                      className="h-8 w-8 p-0 hover:bg-muted/50 text-muted-foreground hover:text-destructive"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                  {notification.message}
                </p>

                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">
                    {formatDistanceToNow(notification._creationTime, { addSuffix: true })}
                  </span>
                  
                  {getNotificationLink(notification) !== "#" && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(getNotificationLink(notification), "_blank")}
                      className="rounded-xl font-light text-xs"
                    >
                      View Details
                      <ExternalLink className="w-3 h-3 ml-1" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
