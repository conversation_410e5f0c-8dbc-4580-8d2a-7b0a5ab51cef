"use client";

import { useState } from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@repo/ui/components/card";
import { Badge } from "@repo/ui/components/badge";
import { Checkbox } from "@repo/ui/components/checkbox";
import { Label } from "@repo/ui/components/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";
import { Filter, X, ChevronDown, ChevronUp } from "lucide-react";
import { NotificationFilters } from "./NotificationsContent";

interface NotificationsSidebarProps {
  filters: NotificationFilters;
  notificationTypes: Array<{
    value: string;
    label: string;
    count: number;
  }>;
  onFilterChange: (filters: Partial<NotificationFilters>) => void;
  onClearFilters: () => void;
  activeFilterCount: number;
  isMobile?: boolean;
  isOpen?: boolean;
  onToggle?: () => void;
}

export function NotificationsSidebar({
  filters,
  notificationTypes,
  onFilterChange,
  onClearFilters,
  activeFilterCount,
  isMobile = false,
  isOpen = false,
  onToggle,
}: NotificationsSidebarProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(["types", "status", "date"])
  );

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const handleTypeChange = (type: string, checked: boolean) => {
    const newTypes = checked
      ? [...filters.types, type]
      : filters.types.filter(t => t !== type);
    onFilterChange({ types: newTypes });
  };

  const handleReadStatusChange = (status: "all" | "unread" | "read") => {
    onFilterChange({ readStatus: status });
  };

  const handleDateRangeChange = (range: "all" | "today" | "week" | "month") => {
    onFilterChange({ dateRange: range });
  };

  const sidebarContent = (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-light text-primary">Filters</h2>
        {activeFilterCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="text-xs text-muted-foreground hover:text-foreground"
          >
            Clear all
          </Button>
        )}
      </div>

      {/* Notification Types */}
      <Card className="rounded-xl border-border">
        <CardHeader className="pb-3">
          <button
            onClick={() => toggleSection("types")}
            className="flex items-center justify-between w-full text-left"
          >
            <CardTitle className="text-sm font-light text-primary">
              Notification Types
            </CardTitle>
            {expandedSections.has("types") ? (
              <ChevronUp className="w-4 h-4 text-muted-foreground" />
            ) : (
              <ChevronDown className="w-4 h-4 text-muted-foreground" />
            )}
          </button>
        </CardHeader>
        {expandedSections.has("types") && (
          <CardContent className="pt-0 space-y-3">
            {notificationTypes.map((type) => (
              <div key={type.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`type-${type.value}`}
                  checked={filters.types.includes(type.value)}
                  onCheckedChange={(checked) => 
                    handleTypeChange(type.value, checked as boolean)
                  }
                />
                <Label
                  htmlFor={`type-${type.value}`}
                  className="text-sm font-light cursor-pointer flex-1"
                >
                  {type.label}
                </Label>
                <Badge variant="secondary" className="text-xs">
                  {type.count}
                </Badge>
              </div>
            ))}
          </CardContent>
        )}
      </Card>

      {/* Read Status */}
      <Card className="rounded-xl border-border">
        <CardHeader className="pb-3">
          <button
            onClick={() => toggleSection("status")}
            className="flex items-center justify-between w-full text-left"
          >
            <CardTitle className="text-sm font-light text-primary">
              Read Status
            </CardTitle>
            {expandedSections.has("status") ? (
              <ChevronUp className="w-4 h-4 text-muted-foreground" />
            ) : (
              <ChevronDown className="w-4 h-4 text-muted-foreground" />
            )}
          </button>
        </CardHeader>
        {expandedSections.has("status") && (
          <CardContent className="pt-0">
            <Select
              value={filters.readStatus}
              onValueChange={handleReadStatusChange}
            >
              <SelectTrigger className="rounded-xl bg-primary/5 border-border font-light">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Notifications</SelectItem>
                <SelectItem value="unread">Unread Only</SelectItem>
                <SelectItem value="read">Read Only</SelectItem>
              </SelectContent>
            </Select>
          </CardContent>
        )}
      </Card>

      {/* Date Range */}
      <Card className="rounded-xl border-border">
        <CardHeader className="pb-3">
          <button
            onClick={() => toggleSection("date")}
            className="flex items-center justify-between w-full text-left"
          >
            <CardTitle className="text-sm font-light text-primary">
              Date Range
            </CardTitle>
            {expandedSections.has("date") ? (
              <ChevronUp className="w-4 h-4 text-muted-foreground" />
            ) : (
              <ChevronDown className="w-4 h-4 text-muted-foreground" />
            )}
          </button>
        </CardHeader>
        {expandedSections.has("date") && (
          <CardContent className="pt-0">
            <Select
              value={filters.dateRange}
              onValueChange={handleDateRangeChange}
            >
              <SelectTrigger className="rounded-xl bg-primary/5 border-border font-light">
                <SelectValue placeholder="Select date range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
              </SelectContent>
            </Select>
          </CardContent>
        )}
      </Card>
    </div>
  );

  if (isMobile) {
    return (
      <div className="md:hidden">
        <Button
          variant="outline"
          onClick={onToggle}
          className="w-full justify-between rounded-xl font-light"
        >
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4" />
            Filters
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFilterCount}
              </Badge>
            )}
          </div>
          {isOpen ? (
            <ChevronUp className="w-4 h-4" />
          ) : (
            <ChevronDown className="w-4 h-4" />
          )}
        </Button>

        {isOpen && (
          <div className="mt-4 p-4 bg-card rounded-xl border border-border">
            {sidebarContent}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="w-72 flex-shrink-0">
      {sidebarContent}
    </div>
  );
}
