"use client";

import { useState, useMemo } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { MarketplaceHeader } from "@/components/marketplace/MarketplaceHeader";
import { NotificationsList } from "./NotificationsList";
import { NotificationsSidebar } from "./NotificationsSidebar";
import { Button } from "@repo/ui/components/button";
import { Bell, Check, Trash2, Filter } from "lucide-react";
import { toast } from "sonner";

export interface NotificationFilters {
  types: string[];
  readStatus: "all" | "unread" | "read";
  dateRange: "all" | "today" | "week" | "month";
}

const initialFilters: NotificationFilters = {
  types: [],
  readStatus: "all",
  dateRange: "all",
};

export function NotificationsContent() {
  const [filters, setFilters] = useState<NotificationFilters>(initialFilters);
  const [showSidebar, setShowSidebar] = useState(false);

  // Queries
  const notifications = useQuery(api.notifications.getUserNotifications, {
    limit: 50,
  });
  const unreadCount = useQuery(api.notifications.getUnreadNotificationCount);

  // Mutations
  const markAllAsRead = useMutation(api.notifications.markAllNotificationsAsRead);
  const deleteAllRead = useMutation(api.notifications.deleteAllReadNotifications);

  const handleFilterChange = (newFilters: Partial<NotificationFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const clearFilters = () => {
    setFilters(initialFilters);
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead({});
      toast.success("All notifications marked as read");
    } catch (error) {
      toast.error("Failed to mark all as read");
    }
  };

  const handleDeleteAllRead = async () => {
    try {
      await deleteAllRead({});
      toast.success("All read notifications deleted");
    } catch (error) {
      toast.error("Failed to delete read notifications");
    }
  };

  // Filter notifications based on current filters
  const filteredNotifications = useMemo(() => {
    if (!notifications?.notifications) return [];

    let filtered = notifications.notifications;

    // Filter by type
    if (filters.types.length > 0) {
      filtered = filtered.filter(notification => 
        filters.types.includes(notification.type)
      );
    }

    // Filter by read status
    if (filters.readStatus === "unread") {
      filtered = filtered.filter(notification => !notification.read);
    } else if (filters.readStatus === "read") {
      filtered = filtered.filter(notification => notification.read);
    }

    // Filter by date range
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000;
    const oneWeek = 7 * oneDay;
    const oneMonth = 30 * oneDay;

    if (filters.dateRange === "today") {
      filtered = filtered.filter(notification => 
        now - notification._creationTime < oneDay
      );
    } else if (filters.dateRange === "week") {
      filtered = filtered.filter(notification => 
        now - notification._creationTime < oneWeek
      );
    } else if (filters.dateRange === "month") {
      filtered = filtered.filter(notification => 
        now - notification._creationTime < oneMonth
      );
    }

    return filtered;
  }, [notifications?.notifications, filters]);

  const activeFilterCount = useMemo(() => {
    return (
      filters.types.length +
      (filters.readStatus !== "all" ? 1 : 0) +
      (filters.dateRange !== "all" ? 1 : 0)
    );
  }, [filters]);

  const notificationTypes = useMemo(() => {
    if (!notifications?.notifications) return [];
    
    const types = new Set(notifications.notifications.map(n => n.type));
    return Array.from(types).map(type => ({
      value: type,
      label: type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      count: notifications.notifications.filter(n => n.type === type).length,
    }));
  }, [notifications?.notifications]);

  return (
    <div className="bg-card min-h-screen">
      <MarketplaceHeader />
      
      <div className="">
        {/* Hero Section */}
        <div className="bg-gradient-to-br from-primary/5 to-accent/5 border-b border-border">
          <div className="container mx-auto px-6 py-12">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-light text-primary tracking-wide mb-2">
                  Notifications
                </h1>
                <p className="text-muted-foreground font-light">
                  Stay updated with your latest alerts and updates
                </p>
              </div>
              <div className="flex items-center gap-3">
                {unreadCount && unreadCount > 0 && (
                  <Button
                    variant="outline"
                    onClick={handleMarkAllAsRead}
                    className="rounded-xl font-light"
                  >
                    <Check className="w-4 h-4 mr-2" />
                    Mark All Read
                  </Button>
                )}
                <Button
                  variant="outline"
                  onClick={handleDeleteAllRead}
                  className="rounded-xl font-light"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete Read
                </Button>
              </div>
            </div>
            
            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              <div className="bg-card rounded-xl p-6 border border-border">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                    <Bell className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <p className="text-2xl font-light text-primary">
                      {notifications?.total || 0}
                    </p>
                    <p className="text-sm text-muted-foreground font-light">
                      Total Notifications
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-card rounded-xl p-6 border border-border">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-accent/10 rounded-xl flex items-center justify-center">
                    <Check className="w-6 h-6 text-accent-foreground" />
                  </div>
                  <div>
                    <p className="text-2xl font-light text-accent-foreground">
                      {unreadCount || 0}
                    </p>
                    <p className="text-sm text-muted-foreground font-light">
                      Unread
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-card rounded-xl p-6 border border-border">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-secondary/10 rounded-xl flex items-center justify-center">
                    <Filter className="w-6 h-6 text-secondary-foreground" />
                  </div>
                  <div>
                    <p className="text-2xl font-light text-secondary-foreground">
                      {activeFilterCount}
                    </p>
                    <p className="text-sm text-muted-foreground font-light">
                      Active Filters
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex gap-6 container mx-auto px-6 py-6">
          {/* Filter Sidebar - Desktop */}
          <div className="hidden md:block w-72 flex-shrink-0">
            <NotificationsSidebar
              filters={filters}
              notificationTypes={notificationTypes}
              onFilterChange={handleFilterChange}
              onClearFilters={clearFilters}
              activeFilterCount={activeFilterCount}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 min-w-0">
            {/* Mobile Filter Button */}
            <div className="md:hidden mb-6">
              <NotificationsSidebar
                filters={filters}
                notificationTypes={notificationTypes}
                onFilterChange={handleFilterChange}
                onClearFilters={clearFilters}
                activeFilterCount={activeFilterCount}
                isMobile={true}
                isOpen={showSidebar}
                onToggle={() => setShowSidebar(!showSidebar)}
              />
            </div>
            
            <NotificationsList
              notifications={filteredNotifications}
              isLoading={notifications === undefined}
              activeFilterCount={activeFilterCount}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
