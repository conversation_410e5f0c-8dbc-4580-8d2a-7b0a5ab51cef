import { NextRequest, NextResponse } from "next/server";
import { requireAdminMiddleware } from "@repo/backend/better-auth/middleware";
import { getSessionCookie } from "better-auth/cookies";
import { auth } from "@repo/backend/better-auth/server";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Auth pages that should redirect authenticated users
  const authPages = ["/login", "/register"];
  
  // Public routes that don't require authentication
  const publicRoutes = [
    "/",
    "/login",
    "/register",
    "/forgot-password",
    "/verify",
    "/reset-password",
    "/unauthorized",
    "/marketplace",
    "/landing"
  ];

  // API routes and static assets - always allow
  if (
    pathname.startsWith("/api/") ||
    pathname.startsWith("/_next/") ||
    pathname.startsWith("/favicon.ico") ||
    pathname.startsWith("/images/") ||
    pathname.startsWith("/icons/")
  ) {
    return NextResponse.next();
  }

  // Check if user is already authenticated for auth pages
  if (authPages.includes(pathname)) {
    const sessionCookie = getSessionCookie(request);
    
    if (sessionCookie) {
      try {
        // Verify session exists and is valid
        const authInstance = auth({} as any);
        const session = await authInstance.api.getSession({
          headers: request.headers,
        });

        // If user has valid session, redirect to home
        if (session?.user) {
          return NextResponse.redirect(new URL("/", request.url));
        }
      } catch (error) {
        // If session verification fails, allow access to auth pages
        console.error("Session verification failed for auth page:", error);
      }
    }
  }

  // Admin routes - let client-side handle authentication
  // The RequireAdmin component will handle role-based access control
  if (pathname.startsWith("/admin")) {
    return NextResponse.next();
  }

  // Public routes - allow without authentication
  if (publicRoutes.includes(pathname)) {
    return NextResponse.next();
  }

  // For other protected routes, we can add additional middleware logic here
  // For now, let's allow other routes to work
  return NextResponse.next();
}

export const config = {
  matcher: [
    // Match all routes except static files and API routes
    "/((?!_next/static|_next/image|favicon.ico|images|icons).*)",
  ],
};
