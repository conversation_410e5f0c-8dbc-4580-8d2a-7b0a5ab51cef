/* Transparent Image Utilities */

/* Base class for transparent image containers */
.transparent-image-container {
  background: transparent;
  background-image: none;
}

/* Subtle background for luxury transparent images */
.luxury-transparent-bg {
  background: linear-gradient(135deg, 
    rgba(248, 250, 252, 0.3) 0%, 
    rgba(241, 245, 249, 0.4) 50%, 
    rgba(248, 250, 252, 0.3) 100%
  );
}

/* Dark mode luxury transparent background */
.dark .luxury-transparent-bg {
  background: linear-gradient(135deg, 
    rgba(15, 23, 42, 0.3) 0%, 
    rgba(30, 41, 59, 0.4) 50%, 
    rgba(15, 23, 42, 0.3) 100%
  );
}

/* Ensure transparent PNGs maintain their transparency */
.preserve-transparency {
  background-color: transparent !important;
  background-image: none !important;
  mix-blend-mode: normal;
}

/* Chanel bag specific styling (since it was mentioned) */
.chanel-bag-container {
  background: radial-gradient(ellipse at center, 
    rgba(255, 255, 255, 0.1) 0%, 
    transparent 70%
  );
}

.dark .chanel-bag-container {
  background: radial-gradient(ellipse at center, 
    rgba(0, 0, 0, 0.2) 0%, 
    transparent 70%
  );
}

/* PNG image specific handling */
img[src*=".png"], 
img[src*="PNG"],
.next-image[src*=".png"],
.next-image[src*="PNG"] {
  background-color: transparent;
  background-image: none;
}

/* WebP with transparency */
img[src*=".webp"], 
.next-image[src*=".webp"] {
  background-color: transparent;
  background-image: none;
}

/* SVG handling */
img[src*=".svg"], 
.next-image[src*=".svg"] {
  background-color: transparent;
  background-image: none;
}

/* Animation for transparent images */
.transparent-image-hover {
  transition: all 0.3s ease;
}

.transparent-image-hover:hover {
  transform: scale(1.02);
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1));
}

/* Checkerboard pattern for development/testing transparent images */
.checkerboard-bg {
  background-image: 
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
    linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.dark .checkerboard-bg {
  background-image: 
    linear-gradient(45deg, #2a2a2a 25%, transparent 25%), 
    linear-gradient(-45deg, #2a2a2a 25%, transparent 25%), 
    linear-gradient(45deg, transparent 75%, #2a2a2a 75%), 
    linear-gradient(-45deg, transparent 75%, #2a2a2a 75%);
}

/* Mobile-friendly utilities */

/* Hide scrollbars while maintaining functionality */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Touch optimization */
.touch-manipulation {
  touch-action: manipulation;
}
