import { useQuery, useMutation } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api";
import { Id } from "@repo/backend/convex/_generated/dataModel";
import { toast } from "sonner";

interface UseWatchedAuctionsOptions {
  limit?: number;
  offset?: number;
  status?: "all" | "active" | "ended" | "sold";
}

export function useWatchedAuctions(options: UseWatchedAuctionsOptions = {}) {
  const { limit = 12, offset = 0, status = "all" } = options;
  
  const data = useQuery(api.auctions.getUserWatchedAuctions, {
    limit,
    offset,
    status,
  });

  return {
    watchedAuctions: data?.auctions || [],
    total: data?.total || 0,
    hasMore: data?.hasMore || false,
    isLoading: data === undefined,
  };
}

export function useAuctionWatchingActions() {
  const toggleWatch = useMutation(api.auctions.toggleAuctionWatch);

  const addToWatchlist = async (auctionId: Id<"auctions">) => {
    try {
      await toggleWatch({
        auctionId,
        watch: true,
      });
      toast.success("Added to watchlist");
      return true;
    } catch (error) {
      toast.error("Failed to add to watchlist");
      return false;
    }
  };

  const removeFromWatchlist = async (auctionId: Id<"auctions">) => {
    try {
      await toggleWatch({
        auctionId,
        watch: false,
      });
      toast.success("Removed from watchlist");
      return true;
    } catch (error) {
      toast.error("Failed to remove from watchlist");
      return false;
    }
  };

  const toggleWatchlist = async (auctionId: Id<"auctions">, isWatching: boolean) => {
    if (isWatching) {
      return await removeFromWatchlist(auctionId);
    } else {
      return await addToWatchlist(auctionId);
    }
  };

  return {
    addToWatchlist,
    removeFromWatchlist,
    toggleWatchlist,
  };
}
