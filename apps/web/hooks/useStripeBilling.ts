import { useAction, useQuery } from "convex/react";
import { api } from "@repo/backend/convex/_generated/api"
import { useState, useEffect, useRef, useCallback } from "react";
import { toast } from "sonner";

export interface PaymentMethod {
  id: string;
  type: string;
  card?: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
  };
  isDefault: boolean;
}

export interface Invoice {
  id: string;
  number: string;
  amount: number;
  currency: string;
  status: string;
  created: number;
  dueDate?: number;
  description?: string;
  pdf?: string;
}

export interface Subscription {
  id: string;
  status: string;
  currentPeriodStart: number;
  currentPeriodEnd: number;
  amount: number;
  currency: string;
  interval?: string;
}

export function useStripeBilling() {
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingBillingHistory, setIsLoadingBillingHistory] = useState(false);
  const [isLoadingPaymentMethods, setIsLoadingPaymentMethods] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const billingHistoryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const paymentMethodsTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Actions
  const getOrCreateCustomer = useAction(api.stripeCustomers.getOrCreateCustomer);
  const getPaymentMethods = useAction(api.stripeCustomers.getPaymentMethods);
  const addPaymentMethod = useAction(api.stripeCustomers.addPaymentMethod);
  const deletePaymentMethod = useAction(api.stripeCustomers.deletePaymentMethod);
  const setDefaultPaymentMethod = useAction(api.stripeCustomers.setDefaultPaymentMethod);
  const getBillingHistory = useAction(api.stripeCustomers.getBillingHistory);
  const createSetupIntent = useAction(api.stripeCustomers.createSetupIntent);

  // State
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [billingHistory, setBillingHistory] = useState<{
    invoices: Invoice[];
    subscriptions: Subscription[];
  }>({ invoices: [], subscriptions: [] });
  const [billingHistoryError, setBillingHistoryError] = useState<string | null>(null);
  const [paymentMethodsError, setPaymentMethodsError] = useState<string | null>(null);

  // Debounced billing history loader
  const debouncedLoadBillingHistory = useCallback(async () => {
    // Clear any existing timeout
    if (billingHistoryTimeoutRef.current) {
      clearTimeout(billingHistoryTimeoutRef.current);
    }

    // Set a new timeout to delay the actual call
    billingHistoryTimeoutRef.current = setTimeout(async () => {
      await loadBillingHistory();
    }, 1500); // 1.5 second delay to be more conservative
  }, []);

  // Debounced payment methods loader
  const debouncedLoadPaymentMethods = useCallback(async () => {
    // Clear any existing timeout
    if (paymentMethodsTimeoutRef.current) {
      clearTimeout(paymentMethodsTimeoutRef.current);
    }

    // Set a new timeout to delay the actual call
    paymentMethodsTimeoutRef.current = setTimeout(async () => {
      await loadPaymentMethods();
    }, 1000); // 1 second delay for payment methods
  }, []);

  // Initialize customer and load data
  const initializeCustomer = useCallback(async () => {
    // Prevent multiple initializations
    if (isInitialized) {
      return;
    }

    setIsLoading(true);
    try {
      await getOrCreateCustomer({});
      await debouncedLoadPaymentMethods();
      await debouncedLoadBillingHistory();
      setIsInitialized(true);
    } catch (error) {
      console.error("Failed to initialize customer:", error);
      toast.error("Failed to initialize billing account");
    } finally {
      setIsLoading(false);
    }
  }, [getOrCreateCustomer, debouncedLoadPaymentMethods, debouncedLoadBillingHistory, isInitialized]);

  // Load payment methods
  const loadPaymentMethods = useCallback(async () => {
    // Prevent multiple simultaneous calls
    if (isLoadingPaymentMethods) {
      return;
    }

    setIsLoadingPaymentMethods(true);
    setPaymentMethodsError(null); // Clear previous errors
    
    try {
      const result = await getPaymentMethods({});
      if (result.success) {
        setPaymentMethods(result.paymentMethods);
        setPaymentMethodsError(null);
      }
    } catch (error: any) {
      console.error("Failed to load payment methods:", error);
      
      // Handle specific error types
      if (error.message?.includes("rate limit")) {
        const errorMsg = "Too many requests. Please wait a moment and try again.";
        setPaymentMethodsError(errorMsg);
        toast.error(errorMsg);
      } else {
        const errorMsg = "Failed to load payment methods";
        setPaymentMethodsError(errorMsg);
        toast.error(errorMsg);
      }
    } finally {
      setIsLoadingPaymentMethods(false);
    }
  }, [getPaymentMethods, isLoadingPaymentMethods]);

  // Load billing history
  const loadBillingHistory = useCallback(async () => {
    // Prevent multiple simultaneous calls
    if (isLoadingBillingHistory) {
      return;
    }

    setIsLoadingBillingHistory(true);
    setBillingHistoryError(null); // Clear previous errors
    
    try {
      const result = await getBillingHistory({ limit: 50 });
      if (result.success) {
        setBillingHistory({
          invoices: result.invoices,
          subscriptions: result.subscriptions,
        });
        setBillingHistoryError(null);
      }
    } catch (error: any) {
      console.error("Failed to load billing history:", error);
      
      // Handle specific error types
      if (error.message?.includes("rate limit")) {
        const errorMsg = "Too many requests. Please wait a moment and try again.";
        setBillingHistoryError(errorMsg);
        toast.error(errorMsg);
      } else {
        const errorMsg = "Failed to load billing history";
        setBillingHistoryError(errorMsg);
        toast.error(errorMsg);
      }
    } finally {
      setIsLoadingBillingHistory(false);
    }
  }, [getBillingHistory, isLoadingBillingHistory]);

  // Add payment method
  const handleAddPaymentMethod = useCallback(async (paymentMethodId: string) => {
    setIsLoading(true);
    try {
      const result = await addPaymentMethod({ paymentMethodId });
      if (result.success) {
        toast.success("Payment method added successfully");
        await loadPaymentMethods();
        return true;
      }
    } catch (error) {
      console.error("Failed to add payment method:", error);
      toast.error("Failed to add payment method");
      return false;
    } finally {
      setIsLoading(false);
    }
    return false;
  }, [addPaymentMethod, loadPaymentMethods]);

  // Remove payment method
  const handleRemovePaymentMethod = useCallback(async (paymentMethodId: string) => {
    try {
      const result = await deletePaymentMethod({ paymentMethodId });
      if (result.success) {
        toast.success("Payment method removed successfully");
        await loadPaymentMethods();
        return true;
      }
    } catch (error) {
      console.error("Failed to remove payment method:", error);
      toast.error("Failed to remove payment method");
      return false;
    }
    return false;
  }, [deletePaymentMethod, loadPaymentMethods]);

  // Set default payment method
  const handleSetDefaultPaymentMethod = useCallback(async (paymentMethodId: string) => {
    try {
      const result = await setDefaultPaymentMethod({ paymentMethodId });
      if (result.success) {
        toast.success("Default payment method updated");
        await loadPaymentMethods();
        return true;
      }
    } catch (error) {
      console.error("Failed to set default payment method:", error);
      toast.error("Failed to update default payment method");
      return false;
    }
    return false;
  }, [setDefaultPaymentMethod, loadPaymentMethods]);

  // Create setup intent for adding new payment method
  const handleCreateSetupIntent = useCallback(async () => {
    try {
      const result = await createSetupIntent({});
      if (result.success) {
        return result;
      }
    } catch (error) {
      console.error("Failed to create setup intent:", error);
      toast.error("Failed to create setup intent");
      return null;
    }
    return null;
  }, [createSetupIntent]);

  // Manual refresh billing history
  const refreshBillingHistory = useCallback(async () => {
    await loadBillingHistory();
  }, [loadBillingHistory]);

  // Manual refresh payment methods
  const refreshPaymentMethods = useCallback(async () => {
    await loadPaymentMethods();
  }, [loadPaymentMethods]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (billingHistoryTimeoutRef.current) {
        clearTimeout(billingHistoryTimeoutRef.current);
      }
      if (paymentMethodsTimeoutRef.current) {
        clearTimeout(paymentMethodsTimeoutRef.current);
      }
    };
  }, []);

  return {
    // State
    isLoading,
    isLoadingBillingHistory,
    isLoadingPaymentMethods,
    isInitialized,
    paymentMethods,
    billingHistory,
    billingHistoryError,
    paymentMethodsError,
    
    // Actions
    initializeCustomer,
    loadPaymentMethods,
    loadBillingHistory,
    refreshBillingHistory,
    refreshPaymentMethods,
    handleAddPaymentMethod,
    handleRemovePaymentMethod,
    handleSetDefaultPaymentMethod,
    handleCreateSetupIntent,
  };
}
