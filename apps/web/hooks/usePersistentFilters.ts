"use client";

import { useState, useEffect, useCallback } from "react";

export interface DashboardFilters {
  productTitle: string;
  productSku: string;
  storeLocation: string;
  category: string;
  soldStartDate: string;
  soldEndDate: string;
  ownershipType: "all" | "owned" | "consigned";
  listingStatus: "all" | "listed" | "unlisted";
}

const STORAGE_KEY = "MODA_dashboard_filters";

const getDefaultFilters = (): DashboardFilters => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  
  return {
    productTitle: "",
    productSku: "",
    storeLocation: "all",
    category: "all",
    soldStartDate: "",
    soldEndDate: `${year}-${month}-${day}`,
    ownershipType: "all",
    listingStatus: "all",
  };
};

export function usePersistentFilters() {
  const [filters, setFilters] = useState<DashboardFilters>(getDefaultFilters);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load filters from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedFilters = JSON.parse(stored);
        // Validate that the stored filters have all required keys
        const defaultFilters = getDefaultFilters();
        const validFilters = { ...defaultFilters, ...parsedFilters };
        setFilters(validFilters);
      }
    } catch (error) {
      console.warn("Failed to load dashboard filters from localStorage:", error);
      // Fall back to default filters if parsing fails
      setFilters(getDefaultFilters());
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Save filters to localStorage whenever they change (but only after initial load)
  useEffect(() => {
    if (isLoaded) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(filters));
      } catch (error) {
        console.warn("Failed to save dashboard filters to localStorage:", error);
      }
    }
  }, [filters, isLoaded]);

  const updateFilters = useCallback((newFilters: Partial<DashboardFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    const defaultFilters = getDefaultFilters();
    setFilters(defaultFilters);
  }, []);

  const resetFilterVisibility = useCallback(() => {
    try {
      localStorage.removeItem(`${STORAGE_KEY}_visibility`);
    } catch (error) {
      console.warn("Failed to reset filter visibility state:", error);
    }
  }, []);

  return {
    filters,
    updateFilters,
    clearFilters,
    resetFilterVisibility,
    isLoaded
  };
}

// Separate hook for managing filter panel visibility
export function useFilterVisibility() {
  const [showFilters, setShowFilters] = useState(false);
  const VISIBILITY_KEY = `${STORAGE_KEY}_visibility`;

  // Load visibility state from localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(VISIBILITY_KEY);
      if (stored !== null) {
        setShowFilters(JSON.parse(stored));
      }
    } catch (error) {
      console.warn("Failed to load filter visibility from localStorage:", error);
    }
  }, []);

  // Save visibility state to localStorage
  useEffect(() => {
    try {
      localStorage.setItem(VISIBILITY_KEY, JSON.stringify(showFilters));
    } catch (error) {
      console.warn("Failed to save filter visibility to localStorage:", error);
    }
  }, [showFilters]);

  return {
    showFilters,
    setShowFilters
  };
}
